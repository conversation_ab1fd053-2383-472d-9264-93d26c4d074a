<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Embed test</title>
</head>
<body>
<div>Hello</div>
<div style="width:100%;  height:310px">
      <div id="iframe-container" style="width: 100%; height: 310px; overflow: auto; position: relative;">
        <iframe
            src="https://dario.easyflatbcn.com/embed/search?lang=ca&website-id=eyJpdiI6IkJjanJYS0xEVEl1V3dNcFZqam00WEE9PSIsInZhbHVlIjoiYjFxUXd3eC96bE1SclI1UFplVTkvZz09IiwibWFjIjoiOWY1YmJmMzRkNjYwYzE3ZDhhOGY5ZTkyNmI1NzUwOWE5ZGUzMTdlMzYwZGEzMTJjZDQxNzU3ZmY3NWZkOWZlYSIsInRhZyI6IiJ9"
            frameborder="0" title="Rental Ninja" width="100%" height="310px">
        </iframe>
    </div>
    <script src="http://localhost/js/embed_search.js"></script>
<!-- <iframe width="100%" height="100%" src="http://localhost/web/1" frameborder="0" title="Embedded Content"></iframe> -->
<script>
window.addEventListener("message", function(event) {
    console.log({event})
  let data = event.data;
  console.log('From real url: ' + {data})
    if (data.type === "updateUrl") {
        // update query params
        console.log('From real url: ' + data.url);
        // replace hash with query params
        window.location.hash = data.url;
    }

});
// send query params  to iframe
const hash = window.location.hash;
const iframe = document.querySelector("iframe");
if (hash) {
    // on iframe load send query params
    iframe.onload = function() {
        iframe.contentWindow.postMessage({ type: "updateQuery", hash: hash }, "*");
    };
}
</script>
</div>
</body>
</html>
