# APP Configuration
RN_DOMAIN=""
APP_DOMAIN=""
APP_NAME="Rental Ninja Local"
APP_ENV=local
APP_KEY=base64:5RLJ/KNDr+aVsMFCzxiJk18nVdLrBo96lQR2dAnx+7k=
APP_DEBUG=true
APP_URL=${RN_DOMAIN}
APP_LOGO=img/logo/Iso-RGB-regular.png
LANDING_PAGE=${RN_DOMAIN}/login

USE_LAMBDA_STORAGE=false
LARAVEL_ENV_ENCRYPTION_KEY=
SHOULD_POST_TO_PROVIDER=false

RESPONSE_CACHE_ENABLED=false

#Composer Memory Limit
COMPOSER_MEMORY_LIMIT=-1

# Vapor Deployments
VAPOR_API_TOKEN=
XDEBUG_CONFIG=PHPSTORM
XDEBUG_MODE=debug
POSTMAN_COLLECTIONS_ENABLED=false

# PERSONAL and PASSWORD settings
PERSONAL_CLIENT_ID=1
PERSONAL_CLIENT_SECRET=xHw344HvoVUm9Me7HmUTuSpgJUfW6hSdtZVcIDFe
PASSWORD_CLIENT_ID=2
PASSWORD_CLIENT_SECRET=Kxv4uwG9JChpGaL9te2ZHjNIrgggCcYhaMuv5vX3

# LOGGING
LOG_CHANNEL=single
PAPERTRAIL_URL=logs4.papertrailapp.com
PAPERTRAIL_PORT=10199
PAPERTRAIL_LOG_LEVEL=notice

PROXY_CONFIG=

################################################################
# PROVIDERS
ICAL_PROVIDER_ENABLED=true

BOOKINGSYNC_CLIENT_ID=
BOOKINGSYNC_CLIENT_SECRET=
BOOKINGSYNC_REDIRECT_URI=https://${RN_DOMAIN}/register
BOOKINGSYNC_APP_REDIRECT_URI=https://${RN_DOMAIN}/app-register
BOOKINGSYNC_REDIRECT_RECONNECT_URI=https://${RN_DOMAIN}/settings/oauth-reconnected-app

LODGIFY_ENABLED=false
LODGIFY_API_URL=https://api.lodgify.com/

SMOOBU_REGISTER_REDIRECT_URL=https://${RN_DOMAIN}/register
SMOOBU_APP_REGISTER_REDIRECT_URL=https://${RN_DOMAIN}/app-register
SMOOBU_REDIRECT_LOCAL_URI=https://${RN_DOMAIN}/settings/oauth-reconnected-app

RENTALS_UNITED_ENABLED=true
RENTALS_UNITED_PASSWORD=

TOKEET_CLIENT_ID=
TOKEET_CLIENT_SECRET=
TOKEET_API_URL=
TOKEET_REGISTER_REDIRECT_URL=https://${RN_DOMAIN}/tokeet/register
TOKEET_REDIRECT_LOCAL_URI=https://${RN_DOMAIN}/settings/oauth-reconnected-app

CHANNEL_MANAGER_PASSWORD=TBD
RENTALS_UNITED_BACKDOOR_PW=user|pw

PDF_CREATOR_API_KEY=
PDF_CREATOR_URL=https://pdf-generator.pol-fae.workers.dev

################################################################
# EXTERNAL SERVICES
# DATABASE
DB_CONNECTION=mysql
DB_HOST=ninja_db
DB_PORT=3306
DB_DATABASE=vapor
DB_USERNAME=root
DB_PASSWORD=secret

# Please, use only @replica and read only values
DB_PROD_HOST=
DB_PROD_DATABASE=
DB_PROD_USERNAME=
DB_PROD_PASSWORD=
PROD_MYSQL_ATTR_SSL_CA=

# CACHE
CACHE_DRIVER=redis
SESSION_DRIVER=file
RESPONSE_CACHE_DRIVER=redis

# QUEUE
QUEUE_CONNECTION=sync
QUEUE_RETRY_AFTER=300

# REDIS
REDIS_CLIENT=predis
REDIS_HOST=ninja_redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# MAIL
MAIL_MAILER=smtp
MAIL_HOST="host.docker.internal"
MAIL_PORT=2525
MAIL_USERNAME="Rental Ninja"
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# CASHIER and STRIPE
CASHIER_CURRENCY=eur
STRIPE_KEY=
STRIPE_SECRET=
STRIPE_CLIENT_ID=

# AWS S3 Filesystem
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=eu-west-1
AWS_BUCKET=rental-ninja

# Vonage
VONAGE_KEY=
VONAGE_SECRET=

# Firebase
FIREBASE_API_KEY="AIzaSyDDlveYtqCsaaZipnONG346ET4m_Y0gJbo"
FIREBASE_AUTH_DOMAIN="rental-ninja.firebaseapp.com"
FIREBASE_DATABASE_URL="https://rental-ninja.firebaseio.com"
FIREBASE_STORAGE_BUCKET="rental-ninja.appspot.com"
FIREBASE_IMGIX_URL="https://rental-ninja-firebase.imgix.net/"
FIREBASE_MAPS_API_KEY="AIzaSyAM1Qmulb74gOEHMY9ucZ2JnaxIaxfbC8E"
FIREBASE_MEASUREMENT_ID="G-Z1CMXSL0HN"
FIREBASE_APP_ID="1:180832074713:web:1fab2583f0a94b2910f7c8"
FIREBASE_MESSAGING_SENDER_ID="180832074713"

# Google Cloud
GOOGLE_VISION_PRIVATE_KEY_ID=
GOOGLE_VISION_PRIVATE_KEY=

# Wheelhouse
WHEELHOUSE_API_KEY=
WHEELHOUSE_API_PASSWORD=

# Other ONE KEY services
SENTRY_FRONTEND_DSN="https://<EMAIL>/1259334"
APPROXIMATED_KEY=
FATHOM_KEY=

################################################################
# PULSE
PULSE_ENABLED=false

# TELESCOPE
TELESCOPE_ENABLED=true
TELESCOPE_DUMP_WATCHER=false
TELESCOPE_CACHE_WATCHER=true
TELESCOPE_SCHEDULE_WATCHER=true
TELESCOPE_EVENT_WATCHER=true
TELESCOPE_COMMAND_WATCHER=true
TELESCOPE_EXCEPTION_WATCHER=true
TELESCOPE_JOB_WATCHER=true
TELESCOPE_LOG_WATCHER=true
TELESCOPE_MAIL_WATCHER=true
TELESCOPE_MODEL_WATCHER=false
TELESCOPE_NOTIFICATION_WATCHER=true
TELESCOPE_QUERY_WATCHER=true
TELESCOPE_REDIS_WATCHER=false
TELESCOPE_REQUEST_WATCHER=true
TELESCOPE_GATE_WATCHER=true
TELESCOPE_VIEW_WATCHER=true
# This can be either a boolean or a string with a list of space separated words (eg. 'webhook/booking-sync smoobu lodgify')
TELESCOPE_STORE_REQUESTS=true

# SELENIUM GRID
SELENIUM_GRID_URL=
SELENIUM_GRID_USERNAME=
SELENIUM_GRID_PASSWORD=
