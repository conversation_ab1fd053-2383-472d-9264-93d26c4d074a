name: Download Crowdin translations
on:
  workflow_dispatch:  # Allows you to manually trigger the workflow from GitHub Actions
  schedule:
    - cron: '0 7 * * 2,4' # Every Tuesday and Thursday at 9am UTC

jobs:
  download-translations:
    name: Download Crowdin Translations
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: crowdin action
        uses: crowdin/github-action@v2
        with:
          upload_sources: false
          upload_translations: false
          push_translations: false
          download_translations: true
          download_translations_args: --exclude-language=en
          create_pull_request: false
        env:
          # Visit https://crowdin.com/settings#api-key to create this token
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_DIDAC_TOKEN }}

      - name: Commit changes
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git add .
          git commit -m "Download Crowdin Translations" || echo "No changes to commit"
          git push || echo "Nothing to push"
        env:
          # A classic GitHub Personal Access Token with the 'repo' scope selected (the user should have write access to the repository).
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}