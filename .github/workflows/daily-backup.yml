name: Daily backup PlanetScale
on:
  workflow_dispatch:
  schedule:
    - cron: '15 6 * * *'
# PS maintenance window 4:00 to 6:00 utc

jobs:
  new-backup-production:
    name: Build
    runs-on: ubuntu-latest
    steps:

      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install go-mydumper
        run: |
          git clone https://github.com/aquarapid/go-mydumper.git
          cd go-mydumper
          git checkout jacques_vitess
          make build

      - name: Copy Files
        run: |
          cp bin/backups/export/daily_config.cnf go-mydumper/config/config.cnf
          cp bin/backups/export/backup.sh go-mydumper

      - name: Run backup process
        run: |
          INPUT_DB_USER=80zwaak64k0nw6ky59vk
          INPUT_DB_PASSWORD=${{ secrets.PLANET_SCALE_DB_PASSWORD }}

          INPUT_S3_KEY=${{ secrets.AWS_KEY_PRODUCTION }}
          INPUT_S3_SECRET=${{ secrets.AWS_SECRET_PRODUCTION }}
          INPUT_AWS_FOLDER_PATH="backups/daily"
          cd go-mydumper
          source backup.sh

