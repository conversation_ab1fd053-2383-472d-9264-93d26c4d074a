<?php

use App\Actions\Guests\Upsales\GetBookingFeeDataForUpsaleAction;
use App\Actions\Notifications\SendBookingNotificationAction;
use App\Actions\PaymentGateway\StartBookingEngineAction;
use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Domains\GuestsRegistrationAuthorities\Models\SesHospedajesCommunication;
use App\Domains\HomeAutomation\Actions\SendSmartlockAuthorisationEmailAction;
use App\Events\Booking\BookingConfirmedEvent;
use App\Http\Controllers\NinjaInvoiceController;
use App\Mail\GuestNotifications\BookingConfirmedEmail;
use App\Mail\GuestNotifications\BookingRejectedEmail;
use App\Mail\GuestNotifications\UpsalePurchasedEmail;
use App\Models\Booking;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

Route::middleware(['web', 'novaAccess'])->group(function () {
    Route::get('download-invoice/{id}', [NinjaInvoiceController::class, 'downloadInvoice'])->name('devDownloadInvoice');
});

// Development routes: for local tests
Route::middleware(['web', 'isLocalEnv'])->group(function () {
    Route::get('/test', function () {
        return Booking::query()
            ->whereIn('team_id', [5])
            ->whereStatusBooked()
            ->whereBetween('created_at', [now()->subDay()->timestamp, now()->timestamp])
            ->whereHas('rental.guestRegistrationAuthorities', function (Builder $q) {
                $q->where('authority', GuestsRegistrationAuthorityEnum::SesHospedajes)
                    ->whereJsonContains('sources', DB::raw('CAST(booking.source_id AS JSON)'));
            })
            ->get();
    });
    Route::get('/test-push-notification', function () {
        $team = Team::find(5);
        $booking = Booking::query()
            ->onTeam($team)
            ->whereNotUnavailable()
            ->orderByDesc('created_at')
            ->first();
        event(new BookingConfirmedEvent($team, $booking->id));
    });
    Route::get('/booking-engine', function () {
        $team = Team::find(5);
        $rental = Rental::find(3825745363);
        $from = Carbon::parse('2025-01-20');
        $to = Carbon::parse('2025-01-25');
        $sourceId = 12926;

        $result = StartBookingEngineAction::run($team, $rental, $from, $to, 2, $sourceId, config('ninja.app_domain'), 'es', 0);
        if ($result['is_error']) {
            return $result['result'];
        } else {
            return redirect($result['result']);
        }
    });
    Route::get('/booking-engine/mail/{type}', function () {
        $team = Team::find(1);
        $booking = Booking::getBookingModel($team, 19275647);
        $type = intval(request()->query('type'));

        if ($type === 1) {
            SendBookingNotificationAction::run($team, $booking, new BookingConfirmedEmail($team, $booking->id), '<EMAIL>');
        } else {
            SendBookingNotificationAction::run($team, $booking, new BookingRejectedEmail($team, $booking->id), '<EMAIL>');
        }
    });

    Route::get('/upsales/mail', function () {
        $team = Team::find(5);
        $booking = Booking::getBookingModel($team, 1662967658); // Select a booking which has an upsale
        $feeIds = [1009, 2302]; // select the fee_id from the BookingFee of the upsale
        $bookingFeesData = $booking->bookingFees
            ->whereIn('fee_id', $feeIds)
            ->map(fn (App\Models\BookingFee $BookingFee) => GetBookingFeeDataForUpsaleAction::run($booking, $BookingFee->fee, $BookingFee->times_booked, $BookingFee->price));

        SendBookingNotificationAction::run($team, $booking, new UpsalePurchasedEmail($team, $booking->id, $bookingFeesData), '<EMAIL>');
    });

    Route::get('/authorisations/mail', function () {
        $team = Team::find(5);
        $booking = Booking::getBookingModel($team, *********);

        SendSmartlockAuthorisationEmailAction::run($team, $booking);
    });

    Route::get('/nuki', function () {
        //\App\Domains\HomeAutomation\Models\HomeAutomationAccount::find(1)->dispatchSync();
        App\Domains\HomeAutomation\Models\HomeAutomationDevice::find(1)->sync();
        //\App\Domains\HomeAutomation\Actions\OpenDoorAction::run(\App\Domains\HomeAutomation\Models\HomeAutomationDevice::find(1));
        //\App\Domains\HomeAutomation\Actions\LockDoorAction::run(\App\Domains\HomeAutomation\Models\HomeAutomationDevice::find(1));
        /*$booking = Booking::getBookingModel(5, *********);
        \App\Domains\HomeAutomation\Actions\CreateSmartlockBookingAccessAction::run($booking);*/
    });

    Route::get('/ses-hospedajes/reserva', function () {
        Booking::find(*********)
            ->reportBookingCreationToAuthorities();
    });

    Route::get('/ses-hospedajes/checkin', function () {
        Booking::find(**********)
            ->reportAtCheckInTimeToAuthorities();
    });

    Route::get('/ses-hospedajes/consulta', function () {
        GuestsRegistrationAuthorityEnum::SesHospedajes
            ->getService()
            ->consultaLotesPendientes(SesHospedajesCommunication::find(9));
    });

    Route::get('/ses-hospedajes/cancelacion', function () {
        Booking::find(*********)
            ->reportBookingCancellationToAuthorities();
    });

    Route::get('/ses-hospedajes/consulta-catalogo', function () {
        return GuestsRegistrationAuthorityEnum::SesHospedajes->getService()
            ->consultaCatalogo('TIPO_DOCUMENTO');
    });
});
