<?php

use App\Actions\Accounting\AdvancedSettings\GetOrRefreshAccountingAdvancedSettingsAction;
use App\Actions\Accounting\AdvancedSettings\UpdateAccountingAdvancedSettingsAction;
use App\Actions\Accounting\ExportSettlements\ExportExcelSettlementAction;
use App\Actions\Accounting\Invoices\CreatePayeeInvoiceAction;
use App\Actions\Accounting\Payees\CreatePayeesWithRentalOwnersAction;
use App\Actions\Accounting\Payees\DestroyPayeeAction;
use App\Actions\Accounting\Payees\DownloadPayeeAction;
use App\Actions\Accounting\Payees\DuplicatePayeeAction;
use App\Actions\Accounting\Payees\GetPayeeAction;
use App\Actions\Accounting\Payees\GetPayeeInvoicesIssuedAction;
use App\Actions\Accounting\Payees\GetPayeeInvoicesReceivedAction;
use App\Actions\Accounting\Payees\GetPayeesAction;
use App\Actions\Accounting\Payees\GetSlimPayeesAction;
use App\Actions\Accounting\Payees\GetSuperSlimPayeesAction;
use App\Actions\Accounting\Payees\StorePayeeAction;
use App\Actions\Accounting\Payees\UpdatePayeeAction;
use App\Actions\Accounting\Payouts\Attachments\DestroyPayoutAttachmentAction;
use App\Actions\Accounting\Payouts\Attachments\GetPayoutAttachmentsAction;
use App\Actions\Accounting\Payouts\Attachments\StorePayoutAttachmentAction;
use App\Actions\Accounting\Payouts\DestroyPayoutAction;
use App\Actions\Accounting\Payouts\EmailOrDownloadPayoutAction;
use App\Actions\Accounting\Payouts\ExportAccountingExcelAction;
use App\Actions\Accounting\Payouts\ExportPayoutAction;
use App\Actions\Accounting\Payouts\GetAllPayoutsAction;
use App\Actions\Accounting\Payouts\GetOnePayoutAction;
use App\Actions\Accounting\Payouts\GetSettlementEmailsListAction;
use App\Actions\Accounting\Payouts\GetSettlementPayoutsAction;
use App\Actions\Accounting\Payouts\PartialPayouts\CreatePartialPayoutAction;
use App\Actions\Accounting\Payouts\PartialPayouts\DestroyPartialPayoutAction;
use App\Actions\Accounting\Payouts\PartialPayouts\MarkPayoutAsPaidAction;
use App\Actions\Accounting\Payouts\PayoutDetails\CalculatePayoutDetailsAction;
use App\Actions\Accounting\Payouts\PayoutDetails\CalculatePayoutDetailsByTypeAction;
use App\Actions\Accounting\Payouts\StoreMultiplePayoutsAction;
use App\Actions\Accounting\Payouts\StorePayoutAction;
use App\Actions\Accounting\Payouts\UpdatePayoutAction;
use App\Actions\Accounting\ScheduledSettlements\DeleteScheduledSettlementAction;
use App\Actions\Accounting\ScheduledSettlements\GetTeamScheduledSettlementsAction;
use App\Actions\Accounting\ScheduledSettlements\StoreScheduledSettlementAction;
use App\Actions\Accounting\ScheduledSettlements\UpdateScheduledSettlementAction;
use App\Actions\Accounting\Settlements\DestroySettlementAction;
use App\Actions\Accounting\Settlements\EmailOrDownloadSettlementAction;
use App\Actions\Accounting\Settlements\GetCurrenciesForTeamAction;
use App\Actions\Accounting\Settlements\GetOneSettlementResourceAction;
use App\Actions\Accounting\Settlements\GetTeamSettlementsAction;
use App\Actions\Accounting\Settlements\StoreSettlementAction;
use App\Actions\Accounting\Settlements\UpdateSettlementAction;
use App\Actions\Accounting\Settlements\UpdateSettlementToReviewAction;
use App\Actions\Alerts\GetUserAlertsAction;
use App\Actions\Alerts\SnoozeAlertAction;
use App\Actions\Alerts\UnSnoozeAlertAction;
use App\Actions\BillableAddons\BillAddonAction;
use App\Actions\BillableAddons\GetAddonsUsageAction;
use App\Actions\Bookings\Actions\AcceptBookingRequestAction;
use App\Actions\Bookings\Actions\ArchiveBookingAction;
use App\Actions\Bookings\Actions\CancelBookingByIdAction;
use App\Actions\Bookings\Actions\ConfirmBookingLeadOrTentativeAction;
use App\Actions\Bookings\Actions\GetModalForRejectBookingRequestAction;
use App\Actions\Bookings\Actions\LeadToTentativeAction;
use App\Actions\Bookings\Actions\RejectBookingRequestAction;
use App\Actions\Bookings\BookingFee\AddBookingFeeAction;
use App\Actions\Bookings\BookingFee\DeleteBookingFeeAction;
use App\Actions\Bookings\BookingFee\UpdateBookingFeeAction;
use App\Actions\Bookings\BookingPicture\AddBookingPictureAction;
use App\Actions\Bookings\BookingPicture\DeleteBookingPictureAction;
use App\Actions\Bookings\BookingPicture\GetBookingPicturesAction;
use App\Actions\Bookings\BookingPicture\GetBookingPicturesWithTasksAction;
use App\Actions\Bookings\BookingPicture\UpdateBookingPictureAction;
use App\Actions\Bookings\Calendar\BookingsCalendarAction;
use App\Actions\Bookings\CheckIns\AddBookingCheckInTimeAction;
use App\Actions\Bookings\CheckOuts\AddBookingCheckOutTimeAction;
use App\Actions\Bookings\Comments\AddBookingCommentAction;
use App\Actions\Bookings\Comments\DeleteBookingCommentWebAction;
use App\Actions\Bookings\Fees\EditFeeBookingByIdAction;
use App\Actions\Bookings\GetBookingWithRelationsAction;
use App\Actions\Bookings\Guide\GetGuideLocalesBookingAction;
use App\Actions\Bookings\ListsAndExport\ExportBookingsForRequestAction;
use App\Actions\Bookings\ListsAndExport\GetBookingsForRequestAction;
use App\Actions\Bookings\Notes\EditBookingNotesAction;
use App\Actions\Bookings\OldUpdateBookingAction;
use App\Actions\Bookings\Payment\AddBookingPaymentAction;
use App\Actions\Bookings\Payment\CancelBookingPaymentAction;
use App\Actions\Bookings\Payment\CaptureDamageDepositAction;
use App\Actions\Bookings\Payment\GetBookingPaymentAction;
use App\Actions\Bookings\Payment\SendDownPaymentEmailAction;
use App\Actions\Bookings\Rates\GetBookingPriceAction;
use App\Actions\Bookings\Rates\OldGetBookingRateAction;
use App\Actions\Bookings\SearchBookingByIdOrRefsAction;
use App\Actions\Bookings\StoreBookingAction;
use App\Actions\Bookings\Tax\EditBookingTaxAction;
use App\Actions\Bookings\UpdateBookingAction;
use App\Actions\BookingTags\AttachTagToBookingAction;
use App\Actions\BookingTags\CreateBookingTagAction;
use App\Actions\BookingTags\DeleteBookingTagAction;
use App\Actions\BookingTags\GetAllBookingTagAction;
use App\Actions\BookingTags\GetOneBookingTagAction;
use App\Actions\BookingTags\RemoveTagFromBookingAction;
use App\Actions\BookingTags\SetTagsToBookingAction;
use App\Actions\BookingTags\UpdateBookingTagAction;
use App\Actions\Calendar\GlobalCalendarAction;
use App\Actions\ChannelManager\Distribution\GetRentalDistributionDataAction;
use App\Actions\ChannelManager\Distribution\GetTeamDistributionRentalsAction;
use App\Actions\ChannelManager\Distribution\HideRentalFromChannelManagerAction;
use App\Actions\ChannelManager\Distribution\PublishRentalToChannelManagerAction;
use App\Actions\ChannelManager\Fetchers\FetchDistributionChannelsStatsAction;
use App\Actions\Chat\NotifyUsersNewChatMessageAction;
use App\Actions\Clients\DeleteClientAction;
use App\Actions\Clients\GetOneClientAction;
use App\Actions\Clients\GetOrSearchClientsAction;
use App\Actions\Clients\StoreClientAction;
use App\Actions\Clients\UpdateClientAction;
use App\Actions\Fees\DeleteFeeAction;
use App\Actions\Fees\GetAllFeesAction;
use App\Actions\Fees\GetOneFeeAction;
use App\Actions\Fees\StoreNewFeeAction;
use App\Actions\Fees\UpdateFeeAction;
use App\Actions\GenCore\ExecuteAIUserRequestAction;
use App\Actions\Guests\GuestsAppSettings\GetRentalGuestsAppSettingsAction;
use App\Actions\Guests\GuestsAppSettings\GetTeamGuestsAppSettingsAction;
use App\Actions\Guests\GuestsAppSettings\UpdateRentalGuestsAppSettingsAction;
use App\Actions\Guests\GuestsAppSettings\UpdateTeamGuestsAppSettingsAction;
use App\Actions\Guests\Guides\DownloadRentalGuideByRentalAction;
use App\Actions\Guests\Guides\GetRentalGuideAction;
use App\Actions\Guests\Guides\UpdateRentalGuideAction;
use App\Actions\Guests\PreCheckInSettings\GetRentalPreCheckInSettingsAction;
use App\Actions\Guests\PreCheckInSettings\GetTeamPreCheckInSettingsAction;
use App\Actions\Guests\PreCheckInSettings\UpdateRentalPreCheckInSettingsAction;
use App\Actions\Guests\PreCheckInSettings\UpdateTeamPreCheckInSettingsAction;
use App\Actions\Guests\RentalSettings\GetGuestAppRentalSettingsByUserAction;
use App\Actions\ICal\AttachInputICalToRentalAction;
use App\Actions\ICal\GenerateNewOutputICalAction;
use App\Actions\ICal\GetRentalICalAction;
use App\Actions\ICal\RemoveICalAction;
use App\Actions\ICal\SyncRentalICalAction;
use App\Actions\ICal\UpdateInputICalAction;
use App\Actions\ICal\UpdateOutputICalAction;
use App\Actions\Inbox\GetAllInboxMessages;
use App\Actions\Inbox\GetBookingConversationAction;
use App\Actions\Inbox\GetContextForBookingAction;
use App\Actions\Inbox\GetMessageAttachmentTempUrlAction;
use App\Actions\Inbox\MarkBookingAsReadOrArchiveAction;
use App\Actions\Inbox\MarkManyBookingsAsRead;
use App\Actions\Inbox\PostGuestMessageAction;
use App\Actions\Inbox\TranslateMessageAction;
use App\Actions\Model179\DownloadModel179Action;
use App\Actions\Model179\GetDropdownDataModel179Action;
use App\Actions\Model179\GetOneLegalDetailsModel179Action;
use App\Actions\Model179\GetRentalLegalDetailsModel179Action;
use App\Actions\Model179\GetTeamLegalDetailsModel179Action;
use App\Actions\Model179\UpdateLegalDetailsModel179Action;
use App\Actions\Model179\UpdateModel179PassportsAction;
use App\Actions\Model179\ValidateModel179Action;
use App\Actions\Model179\ValidateModel179ResponseAction;
use App\Actions\PaymentGateway\DeletePaymentGatewayAccountAction;
use App\Actions\PaymentGateway\GetAllPaymentGatewayAccountsAction;
use App\Actions\PaymentGateway\GetOnePaymentGatewayAccountAction;
use App\Actions\PaymentGateway\StorePaymentGatewayAccountAction;
use App\Actions\PaymentGateway\Stripe\DeauthorizeStripeConnectAction;
use App\Actions\PaymentGateway\Stripe\GetStripeConnectOnboardingUrlAction;
use App\Actions\PaymentGateway\UpdatePaymentGatewayAccountAction;
use App\Actions\Pictures\AddRentalNinjaPictureAction;
use App\Actions\Pictures\DeletePictureAction;
use App\Actions\Pictures\GetPicturesAsPerFilterAction;
use App\Actions\PreCheckIn\DeletePreCheckInBookingAction;
use App\Actions\PreCheckIn\DocumentMrzParser\IdentityParserAction;
use App\Actions\PreCheckIn\GetPreCheckInBookingAction;
use App\Actions\PreCheckIn\GetPreCheckInFormsAction;
use App\Actions\PreCheckIn\SendPreCheckInBookingSmsAction;
use App\Actions\PreCheckIn\SendPreCheckInFormBookingEmailAction;
use App\Actions\PreCheckIn\UpdatePreCheckInBookingAction;
use App\Actions\ProviderEvents\GetProviderEventsAction;
use App\Actions\Providers\ChannelManager\GetWhiteLabelHtmlAction;
use App\Actions\Providers\Oauth\GetOauthReconnectUrlAction;
use App\Actions\Rentals\BlockRentalAction;
use App\Actions\Rentals\CopyRentalCustomAction;
use App\Actions\Rentals\DailyDetails\GetRatesCalendarAction;
use App\Actions\Rentals\DailyDetails\ManualDailyDetails\DeleteManualRentalDailyDetailsAction;
use App\Actions\Rentals\DailyDetails\ManualDailyDetails\PushManualRentalDailyDetailsAction;
use App\Actions\Rentals\DailyDetails\OldGetRatesCalendarAction;
use App\Actions\Rentals\DeleteRentalAction;
use App\Actions\Rentals\DuplicateEverythingFromRatesAction;
use App\Actions\Rentals\GetContextForRentalAction;
use App\Actions\Rentals\GetExtendedRentalAction;
use App\Actions\Rentals\GetExtendedRentalDataAction;
use App\Actions\Rentals\GetRentalPricingModelAction;
use App\Actions\Rentals\GetRentalWithRelationsAction;
use App\Actions\Rentals\GetTeamRentalsAction;
use App\Actions\Rentals\Owners\DestroyRentalOwnerAction;
use App\Actions\Rentals\Owners\GetAllRentalOwnersAction;
use App\Actions\Rentals\Owners\GetOneRentalOwnerAction;
use App\Actions\Rentals\Owners\GetOrCreateRentalOwnerAction;
use App\Actions\Rentals\Owners\UpdateRentalOwnerAction;
use App\Actions\Rentals\RentalDiscounts\DeleteRentalDiscountAction;
use App\Actions\Rentals\RentalDiscounts\GetOneRentalDiscountAction;
use App\Actions\Rentals\RentalDiscounts\GetRentalDiscountsAction;
use App\Actions\Rentals\RentalDiscounts\SortRentalDiscountAction;
use App\Actions\Rentals\RentalDiscounts\StoreNewRentalDiscountAction;
use App\Actions\Rentals\RentalDiscounts\UpdateRentalDiscountAction;
use App\Actions\Rentals\RentalFees\DeleteRentalFeeAction;
use App\Actions\Rentals\RentalFees\GetRentalFeesAction;
use App\Actions\Rentals\RentalFees\SortRentalFeesAction;
use App\Actions\Rentals\RentalFees\StoreNewRentalFeeAction;
use App\Actions\Rentals\RentalFees\UpdateRentalFeeAction;
use App\Actions\Rentals\RentalPictures\DeleteRentalPictureAction;
use App\Actions\Rentals\RentalPictures\GetRentalPicturesAction;
use App\Actions\Rentals\RentalPictures\SortRentalPictureAction;
use App\Actions\Rentals\RentalPictures\StoreNewRentalPictureAction;
use App\Actions\Rentals\RentalPictures\UpdateRentalPictureAction;
use App\Actions\Rentals\RentalPictures\UpscalePicsartRentalPictureAction;
use App\Actions\Rentals\RestoreRentalAction;
use App\Actions\Rentals\SeasonalPrices\CalendarRentalPricesAction;
use App\Actions\Rentals\SeasonalPrices\CleanRentalSeasonsAction;
use App\Actions\Rentals\SeasonalPrices\DeleteSeasonalRuleAction;
use App\Actions\Rentals\SeasonalPrices\DuplicateSeasonalRulesAction;
use App\Actions\Rentals\SeasonalPrices\GetOneSeasonalPriceAction;
use App\Actions\Rentals\SeasonalPrices\GetRentalSeasonalPricesAction;
use App\Actions\Rentals\SeasonalPrices\SortSeasonalRulesAction;
use App\Actions\Rentals\SeasonalPrices\StoreNewSeasonalRuleAction;
use App\Actions\Rentals\SeasonalPrices\UpdateSeasonalRuleAction;
use App\Actions\Rentals\StoreNewRentalAction;
use App\Actions\Rentals\UpdateCollectDownPaymentAction;
use App\Actions\Rentals\UpdateExtendedRentalAction;
use App\Actions\Rentals\UpdateRentalAction;
use App\Actions\Rentals\UpdateRentalPricingModelAction;
use App\Actions\Sources\DeleteSourceAction;
use App\Actions\Sources\GetOneSourceAction;
use App\Actions\Sources\GetTeamSourcesAction;
use App\Actions\Sources\StoreNewProviderSourceAction;
use App\Actions\Sources\UpdateSourceAction;
use App\Actions\Stats\AccountingCategoryBreakdown\GetCategoryBreakdownAccountingStatsAction;
use App\Actions\Stats\AccountingPaymentsOverTime\ExportPaymentsOverTimeAccountingStatsAction;
use App\Actions\Stats\AccountingPaymentsOverTime\GetCompactPaymentsOverTimeAccountingStatsAction;
use App\Actions\Stats\AccountingPaymentsOverTime\GetDetailsPaymentsOverTimeAccountingStatsAction;
use App\Actions\Stats\AdvanceBookings\ExportAdvanceBookingsStatsAction;
use App\Actions\Stats\AdvanceBookings\GetCompactAdvanceBookingsStatsAction;
use App\Actions\Stats\AdvanceBookings\GetDetailsAdvanceBookingsStatsAction;
use App\Actions\Stats\AverageBookingLength\ExportAverageBookingLengthStatsAction;
use App\Actions\Stats\AverageBookingLength\GetCompactAverageBookingLengthStatsAction;
use App\Actions\Stats\AverageBookingLength\GetDetailsAverageBookingLengthStatsAction;
use App\Actions\Stats\AverageBookingPrice\ExportAverageBookingPriceStatsAction;
use App\Actions\Stats\AverageBookingPrice\GetCompactAverageBookingPriceStatsAction;
use App\Actions\Stats\AverageBookingPrice\GetDetailsAverageBookingPriceStatsAction;
use App\Actions\Stats\AveragePricePerNight\ExportAveragePricePerNightStatsAction;
use App\Actions\Stats\AveragePricePerNight\GetCompactAveragePricePerNightStatsAction;
use App\Actions\Stats\AveragePricePerNight\GetDetailsAveragePricePerNightStatsAction;
use App\Actions\Stats\BookingsCancelled\ExportBookingsCancelledStatsAction;
use App\Actions\Stats\BookingsCancelled\GetCompactBookingsCancelledStatsAction;
use App\Actions\Stats\BookingsCancelled\GetDetailsBookingsCancelledStatsAction;
use App\Actions\Stats\BookingsReceived\ExportBookingsReceivedStatsAction;
use App\Actions\Stats\BookingsReceived\GetCompactBookingsReceivedStatsAction;
use App\Actions\Stats\BookingsReceived\GetDetailsBookingsReceivedStatsAction;
use App\Actions\Stats\ChannelCommission\ExportChannelCommissionStatsAction;
use App\Actions\Stats\ChannelCommission\GetCompactChannelCommissionStatsAction;
use App\Actions\Stats\ChannelCommission\GetDetailsChannelCommissionStatsAction;
use App\Actions\Stats\ChannelNights\ExportChannelNightsStatsAction;
use App\Actions\Stats\ChannelNights\GetCompactChannelNightsStatsAction;
use App\Actions\Stats\ChannelNights\GetDetailsChannelNightsStatsAction;
use App\Actions\Stats\ChannelRevenue\ExportChannelRevenueStatsAction;
use App\Actions\Stats\ChannelRevenue\GetCompactChannelRevenueStatsAction;
use App\Actions\Stats\ChannelRevenue\GetDetailsChannelRevenueStatsAction;
use App\Actions\Stats\GuestsHosted\ExportGuestsHostedStatsAction;
use App\Actions\Stats\GuestsHosted\GetCompactGuestsHostedStatsAction;
use App\Actions\Stats\GuestsHosted\GetDetailsGuestsHostedStatsAction;
use App\Actions\Stats\NightsBookedAhead\ExportNightsBookedAheadStatsAction;
use App\Actions\Stats\NightsBookedAhead\GetCompactNightsBookedAheadStatsAction;
use App\Actions\Stats\NightsBookedAhead\GetDetailsNightsBookedAheadStatsAction;
use App\Actions\Stats\NightsCancelled\ExportNightsCancelledStatsAction;
use App\Actions\Stats\NightsCancelled\GetCompactNightsCancelledStatsAction;
use App\Actions\Stats\NightsCancelled\GetDetailsNightsCancelledStatsAction;
use App\Actions\Stats\NightsFilled\ExportNightsFilledStatsAction;
use App\Actions\Stats\NightsFilled\GetCompactNightsFilledStatsAction;
use App\Actions\Stats\NightsFilled\GetDetailsNightsFilledStatsAction;
use App\Actions\Stats\NightsReceived\ExportNightsReceivedStatsAction;
use App\Actions\Stats\NightsReceived\GetCompactNightsReceivedStatsAction;
use App\Actions\Stats\NightsReceived\GetDetailsNightsReceivedStatsAction;
use App\Actions\Stats\OccupancyRates\ExportOccupancyRatesStatsAction;
use App\Actions\Stats\OccupancyRates\GetCompactOccupancyRatesStatsAction;
use App\Actions\Stats\OccupancyRates\GetDetailsOccupancyRatesStatsAction;
use App\Actions\Stats\PaymentMethods\ExportPaymentMethodsStatsAction;
use App\Actions\Stats\PaymentMethods\GetCompactPaymentMethodsStatsAction;
use App\Actions\Stats\PaymentMethods\GetDetailsPaymentMethodsStatsAction;
use App\Actions\Stats\PaymentsReceived\ExportPaymentsReceivedStatsAction;
use App\Actions\Stats\PaymentsReceived\GetCompactPaymentsReceivedStatsAction;
use App\Actions\Stats\PaymentsReceived\GetDetailsPaymentsReceivedStatsAction;
use App\Actions\Stats\RentalCount\GetCompactRentalCountStatsAction;
use App\Actions\Stats\RentalNinjaUsers\GetCompactRentalNinjaUsersStatsAction;
use App\Actions\Stats\RevenueFromBookings\ExportRevenueFromBookingsStatsAction;
use App\Actions\Stats\RevenueFromBookings\GetCompactRevenueFromBookingsStatsAction;
use App\Actions\Stats\RevenueFromBookings\GetDetailsRevenueFromBookingsStatsAction;
use App\Actions\Stats\RevenueGenerated\ExportRevenueGeneratedStatsAction;
use App\Actions\Stats\RevenueGenerated\GetCompactRevenueGeneratedStatsAction;
use App\Actions\Stats\RevenueGenerated\GetDetailsRevenueGeneratedStatsAction;
use App\Actions\Support\GetAllCountriesAction;
use App\Actions\Support\GetAllCurrenciesAction;
use App\Actions\Support\GetAllLanguagesAction;
use App\Actions\Support\GetGooglePlaceAction;
use App\Actions\Support\GetGooglePlacePredictionsAction;
use App\Actions\Support\SearchRULocationAction;
use App\Actions\Tasks\Checklists\DestroyChecklistAction;
use App\Actions\Tasks\Checklists\GetChecklistAction;
use App\Actions\Tasks\Checklists\GetTeamChecklistsAction;
use App\Actions\Tasks\Checklists\StoreChecklistAction;
use App\Actions\Tasks\Checklists\UpdateChecklistAction;
use App\Actions\Tasks\CompleteOrUncompleteTaskAction;
use App\Actions\Tasks\DestroyTaskAction;
use App\Actions\Tasks\ExportTasksToExcelAction;
use App\Actions\Tasks\GetAdminTasksAction;
use App\Actions\Tasks\GetTasksForRentalAction;
use App\Actions\Tasks\GetTasksForRentalsQueryAction;
use App\Actions\Tasks\GetTasksForUsersQueryAction;
use App\Actions\Tasks\GetTaskWithRelationsAction;
use App\Actions\Tasks\GetUserCompletedTasksListAction;
use App\Actions\Tasks\GetUserCurrentTasksListAction;
use App\Actions\Tasks\GetUserFutureTasksListAction;
use App\Actions\Tasks\Inspect\GetCurrentTasksToInspectAction;
use App\Actions\Tasks\Inspect\GetFutureTasksToInspectAction;
use App\Actions\Tasks\Inspect\GetInspectedTasksAction;
use App\Actions\Tasks\Inspect\MarkTaskInspectedOrUninspectedAction;
use App\Actions\Tasks\Picture\AddTaskPictureAction;
use App\Actions\Tasks\Picture\DeleteTaskPictureAction;
use App\Actions\Tasks\RecurrentTask\Crud\DestroyRecurrentTaskAction;
use App\Actions\Tasks\RecurrentTask\Crud\GetRecurrentTaskWithRelationsAction;
use App\Actions\Tasks\RecurrentTask\Crud\GetTeamRecurrentTasksAction;
use App\Actions\Tasks\RecurrentTask\Crud\StoreRecurrentTaskAction;
use App\Actions\Tasks\RecurrentTask\Crud\UpdateRecurrentTaskAction;
use App\Actions\Tasks\ScheduledTask\DestroyScheduledTaskAction;
use App\Actions\Tasks\ScheduledTask\GetScheduledTaskWithRelationsAction;
use App\Actions\Tasks\ScheduledTask\GetTeamScheduledTasksAction;
use App\Actions\Tasks\ScheduledTask\StoreScheduledTaskAction;
use App\Actions\Tasks\ScheduledTask\UpdateScheduleTaskAction;
use App\Actions\Tasks\StoreTaskAction;
use App\Actions\Tasks\TaskItems\CompleteOrUncompleteTaskItemAction;
use App\Actions\Tasks\Team\GetTeamCompletedNoUsersTasksAction;
use App\Actions\Tasks\Team\GetTeamCompletedTasksAction;
use App\Actions\Tasks\Team\GetTeamCurrentNoUsersTasksAction;
use App\Actions\Tasks\Team\GetTeamCurrentTasksAction;
use App\Actions\Tasks\Team\GetTeamFutureNoUsersTasksAction;
use App\Actions\Tasks\Team\GetTeamFutureTasksAction;
use App\Actions\Tasks\UpdateTaskAction;
use App\Actions\Teams\GetContextForTeamAction;
use App\Actions\Teams\GetTeamVerificationErrorsAction;
use App\Actions\Teams\StartTeamVerificationAction;
use App\Actions\Teams\Subscription\GetStripePortalUrlAction;
use App\Actions\Teams\TeamDetails\GetTeamDetailsAction;
use App\Actions\Teams\TeamDetails\UpdateTeamDetailsAction;
use App\Actions\Teams\TeamMembers\CheckMembersPermissionsAction;
use App\Actions\Teams\TeamMembers\GetTeamMemberAction;
use App\Actions\Teams\TeamMembers\RemoveTeamMemberAction;
use App\Actions\Teams\TeamMembers\SetTeamMemberRoleAction;
use App\Actions\Teams\TeamMembers\UpdateTeamMemberAction;
use App\Actions\Teams\TeamSettings\CustomizeTeamAction;
use App\Actions\Teams\TeamSettings\GetIs179Enabled;
use App\Actions\Teams\TeamSettings\GetTeamSettingsAction;
use App\Actions\Teams\TeamSettings\UpdateSlackNotificationSettingsAction;
use App\Actions\Users\GetUsersWithAccessToRentalsAction;
use App\Actions\Users\Invitations\DeleteMailedInvitationAction;
use App\Actions\Users\Invitations\GetAllMailedInvitationsByTeamAction;
use App\Actions\Users\Invitations\SendMailedInvitationAction;
use App\Actions\WebsiteBuilder\CheckDomainWebsiteAction;
use App\Actions\WebsiteBuilder\DeleteWebsiteAction;
use App\Actions\WebsiteBuilder\DuplicateWebsiteAction;
use App\Actions\WebsiteBuilder\GetAllWebsitesAction;
use App\Actions\WebsiteBuilder\GetOneWebsiteAction;
use App\Actions\WebsiteBuilder\SortDistributionWebsiteRentalAction;
use App\Actions\WebsiteBuilder\StoreNewWebsiteAction;
use App\Actions\WebsiteBuilder\UpdateDomainWebsiteAction;
use App\Actions\WebsiteBuilder\UpdateWebsiteAction;
use App\Domains\Automations\Actions\API\AttachTriggerActionAction;
use App\Domains\Automations\Actions\API\CreateAutomationAction;
use App\Domains\Automations\Actions\API\DeleteOneAutomationAction;
use App\Domains\Automations\Actions\API\DeleteTriggerActionAction;
use App\Domains\Automations\Actions\API\FetchBookingAutomationLogsAction;
use App\Domains\Automations\Actions\API\FetchTriggerActionLogsAction;
use App\Domains\Automations\Actions\API\GetAllAutomationsAction;
use App\Domains\Automations\Actions\API\GetAutomationsFormAction;
use App\Domains\Automations\Actions\API\GetOneAutomationAction;
use App\Domains\Automations\Actions\API\UpdateAutomationAction;
use App\Domains\Automations\Actions\API\UpdateTriggerActionAction;
use App\Domains\GuestsRegistrationAuthorities\Actions\DestroyGuestsRegistrationAuthorityAction;
use App\Domains\GuestsRegistrationAuthorities\Actions\GetAuthoritiesListAction;
use App\Domains\GuestsRegistrationAuthorities\Actions\GetGuestsRegistrationAuthoritiesAction;
use App\Domains\GuestsRegistrationAuthorities\Actions\GetGuestsRegistrationAuthoritiesFormDataAction;
use App\Domains\GuestsRegistrationAuthorities\Actions\IndexGuestsRegistrationAuthoritiesAction;
use App\Domains\GuestsRegistrationAuthorities\Actions\StoreGuestsRegistrationAuthorityAction;
use App\Domains\GuestsRegistrationAuthorities\Actions\UpdateGuestsRegistrationAuthorityAction;
use App\Domains\HomeAutomation\Actions\ConnectHomeAutomationAccountAction;
use App\Domains\HomeAutomation\Actions\CreatePermanentSmartlockAuthorisationAction;
use App\Domains\HomeAutomation\Actions\CreateSmartlockBookingAuthorisationAction;
use App\Domains\HomeAutomation\Actions\DeleteHomeAutomationDeviceAction;
use App\Domains\HomeAutomation\Actions\DeleteSmartlockAuthorisationAction;
use App\Domains\HomeAutomation\Actions\DisconnectHomeAutomationAccountAction;
use App\Domains\HomeAutomation\Actions\GetHomeAutomationAccountsAction;
use App\Domains\HomeAutomation\Actions\GetHomeAutomationDevicesAction;
use App\Domains\HomeAutomation\Actions\GetHomeAutomationPricingAction;
use App\Domains\HomeAutomation\Actions\GetSingleHomeAutomationDeviceAction;
use App\Domains\HomeAutomation\Actions\GetUserSmartlockAuthorisationsAction;
use App\Domains\HomeAutomation\Actions\LockDoorAction;
use App\Domains\HomeAutomation\Actions\MapDeviceRentalAction;
use App\Domains\HomeAutomation\Actions\OpenDoorAction;
use App\Domains\HomeAutomation\Actions\UpdateHomeAutomationAccountAction;
use App\Domains\ManageBookings\Actions\CreateBookingAction;
use App\Domains\ManageBookings\Actions\GetBookingManagementAction;
use App\Domains\ManageBookings\Actions\ManageBookingAction;
use App\Domains\ManageBookings\Actions\ModifyBookingAction;
use App\Domains\TrainingSessions\Actions\GetOneTeamTrainingSessionAction;
use App\Domains\TrainingSessions\Actions\GetTeamTrainingSessionsAction;
use App\Domains\Wheelhouse\Actions\PreviewRatesFromWheelhouseAction;
use App\Http\Middleware\CanManageExtraRentalsMiddleware;
use App\Http\Middleware\VerifyCanEditAutomationsMiddleware;
use App\Http\Middleware\VerifyCanSeeAutomationsMiddleware;
use App\Http\Middleware\VerifyTeamIsVerified;
use App\Http\Middleware\VerifyUserCanAccessInbox;
use App\Http\Middleware\VerifyUserCanManageBooking;
use App\Http\Middleware\VerifyUserCanManageHomeAutomationSettings;
use App\Http\Middleware\VerifyUserCanUseHomeAutomationDevices;
use App\Http\Middleware\VerifyUserIsAdmin;

Route::get('/settings/countries', GetAllCountriesAction::class);
Route::get('/settings/currencies', GetAllCurrenciesAction::class);
Route::get('/settings/languages', GetAllLanguagesAction::class);
Route::get('/settings/locations', SearchRULocationAction::class);

Route::middleware(['checkRequestIsSameTeam']) // Routes without team param can also go through this Middleware
->group(function () {
    Route::post('/settings/teams/{team}/invite', SendMailedInvitationAction::class);
    Route::get('/settings/teams/{team}/invitations', GetAllMailedInvitationsByTeamAction::class)
        ->middleware('verifyUserIsAdmin');
    Route::delete('/settings/invitations/{invitation}', DeleteMailedInvitationAction::class)
        ->middleware('verifyUserIsAdmin');

    Route::get('/team/{team}/verify', StartTeamVerificationAction::class)
         ->middleware('throttle:ninja-nano');
    Route::get('/team/{team}/verify/errors', GetTeamVerificationErrorsAction::class);

    Route::post('/team/{team}/chat/notify', NotifyUsersNewChatMessageAction::class);

    /// Slim Rental Related Routes
    Route::get('/slim-rentals/{team}/for-user', [GetTeamRentalsAction::class, 'asUserSlimRentals']);
    Route::get('/slim-rentals/{team}', [GetTeamRentalsAction::class, 'asSlimRentals']);
    Route::get('/slim-rentals/{team}/deleted', [GetTeamRentalsAction::class, 'asOnlyLastYearDeletedSlimRentals']);
    Route::get('/slim-rentals/{team}/all-deleted', [GetTeamRentalsAction::class, 'asAllDeletedSlimRentals']);
    Route::get('/slim-rentals/{team}/for-rentals', GetUsersWithAccessToRentalsAction::class)
        ->middleware('userIsRentalManagerOrAbove');
    Route::get('/slim-rentals/{team}/{id}', [GetTeamRentalsAction::class, 'asSingleSlimRental']);

    // Geo location helper from google.
    Route::post('/team/{team}/places', GetGooglePlacePredictionsAction::class);
    Route::post('/team/{team}/place', GetGooglePlaceAction::class);

    // Team Settings
    Route::get('/team/{team}/is-179-enabled', GetIs179Enabled::class);
    Route::put('/team/{team}/is-179-enabled', GetIs179Enabled::class); // Check that PUT is not used anywhere

    Route::get('/team/{team}/context', GetContextForTeamAction::class);

    Route::middleware(['verifyUserIsAdmin'])
        ->group(function () {
            // Team related
            Route::get('/team/{team}/customization', CustomizeTeamAction::class);
            Route::put('/team/{team}/customization', CustomizeTeamAction::class);
            // Team settings
            Route::get('/team/{team}/settings', GetTeamSettingsAction::class);
            Route::put('/slack/{team}/settings', UpdateSlackNotificationSettingsAction::class)
                ->middleware('verifyTeamIsProfessional');
            // Team legal details
            Route::get('/team/{team}/details', GetTeamDetailsAction::class);
            Route::put('/team/{team}/details', UpdateTeamDetailsAction::class);

            // This should be accessible even if there is no active subscription.
            Route::get('/team/{team}/settings/subscription-portal-url', GetStripePortalUrlAction::class)
                ->withoutMiddleware('teamSubscribed');

            // Oauth Reconnect
            Route::get('/team/{team}/settings/oauth-reconnect', GetOauthReconnectUrlAction::class)
                ->withoutMiddleware('teamSubscribed');

            // Payment Gateway
            Route::get('/team/{team}/payment-gateway', GetAllPaymentGatewayAccountsAction::class);
            Route::get('/team/{team}/payment-gateway/{paymentGatewayAccount:id}', GetOnePaymentGatewayAccountAction::class);
            Route::put('/team/{team}/payment-gateway/{paymentGatewayAccount:id}', UpdatePaymentGatewayAccountAction::class);
            Route::post('/team/{team}/payment-gateway', StorePaymentGatewayAccountAction::class);
            Route::delete('/team/{team}/payment-gateway/{paymentGatewayAccount:id}', DeletePaymentGatewayAccountAction::class);
            Route::get('/team/{team}/payment-gateway/{paymentGatewayAccount:id}/authorize', GetStripeConnectOnboardingUrlAction::class);
            Route::get('/team/{team}/payment-gateway/{paymentGatewayAccount:id}/deauthorize', DeauthorizeStripeConnectAction::class);

            // Website builder
            Route::middleware([VerifyTeamIsVerified::class])
                 ->group(function () {
                     Route::get('/team/{team}/website-builder/', GetAllWebsitesAction::class);
                     Route::post('/team/{team}/website-builder/', StoreNewWebsiteAction::class);
                     Route::post('/team/{team}/website-builder/duplicate/', DuplicateWebsiteAction::class);
                     Route::get('/team/{team}/website-builder/{distributionWebsite:id}', GetOneWebsiteAction::class);
                     Route::get('/team/{team}/website-builder/{distributionWebsite:id}/check-domain', CheckDomainWebsiteAction::class);
                     Route::post('/team/{team}/website-builder/{distributionWebsite:id}/update-domain', UpdateDomainWebsiteAction::class);
                     Route::put('/team/{team}/website-builder/{distributionWebsite:id}', UpdateWebsiteAction::class);
                     Route::put('/team/{team}/website-builder/{distributionWebsite:id}/sort', SortDistributionWebsiteRentalAction::class);
                     Route::delete('/team/{team}/website-builder/{distributionWebsite:id}', DeleteWebsiteAction::class);
                 });
        });

    // Global calendar view
    Route::post('/team/{team}/calendar', GlobalCalendarAction::class);

    // Event Related
    Route::post('/team/{team}/events', GetProviderEventsAction::class);

    // Booking Related
    Route::post('/team/{team}/bookings/calendar', BookingsCalendarAction::class);

    Route::post('/team/{team}/bookings/{requestType}/web', GetBookingsForRequestAction::class);
    Route::post('/team/{team}/bookings/{requestType}/web/download', ExportBookingsForRequestAction::class);
    Route::post('/team/{team}/bookings/{requestType}', GetBookingsForRequestAction::class);
    Route::post('/team/{team}/bookings/{requestType}/download', ExportBookingsForRequestAction::class);

    // Requests
    Route::middleware([VerifyUserCanManageBooking::class])
        ->group(function () {
            Route::put('/team/{team}/bookings/confirm/{booking:id}', ConfirmBookingLeadOrTentativeAction::class);
            Route::put('/team/{team}/bookings/accept/{booking:id}', AcceptBookingRequestAction::class);
            Route::put('/team/{team}/bookings/lead-to-tentative/{booking:id}', LeadToTentativeAction::class);
            Route::put('/team/{team}/bookings/reject/{booking:id}', RejectBookingRequestAction::class);
            Route::delete('/team/{team}/bookings/cancel/{booking:id}', CancelBookingByIdAction::class);
            Route::put('/team/{team}/bookings/archive/{booking:id}', ArchiveBookingAction::class);

            // Deprecated
            Route::put('/team/{team}/bookings/{booking:id}/accept-request', AcceptBookingRequestAction::class);
            Route::put('/team/{team}/bookings/{booking:id}/reject-request', RejectBookingRequestAction::class);
            Route::get('/team/{team}/bookings/{booking:id}/reject-request', GetModalForRejectBookingRequestAction::class);
        });
    Route::delete('/team/{team}/bookings/{booking:id}', CancelBookingByIdAction::class);

    Route::get('/team/{team}/properties/{teamRental:id}/fees', GetRentalFeesAction::class);

    Route::get('/team/{team}/bookings/search', SearchBookingByIdOrRefsAction::class);
    Route::get('/team/{team}/bookings/{booking:id}', GetBookingWithRelationsAction::class);
    Route::put('/team/{team}/bookings/{booking:id}/check-out-time', AddBookingCheckOutTimeAction::class);
    Route::put('/team/{team}/bookings/{booking:id}/check-in-time', AddBookingCheckInTimeAction::class);
    Route::put('/team/{team}/bookings/{booking:id}/fees/{fee}', EditFeeBookingByIdAction::class);
    Route::put('/team/{team}/bookings/{booking:id}/taxes/{tax}', EditBookingTaxAction::class);
    Route::delete('/team/{team}/bookings/{booking:id}/pictures/{picture}', DeleteBookingPictureAction::class);
    Route::post('/team/{team}/bookings/{booking:id}/pictures', AddBookingPictureAction::class);
    Route::put('/team/{team}/bookings/{booking:id}/pictures/{picture}', UpdateBookingPictureAction::class);
    Route::post('/team/{team}/bookings/{booking:id}/comments', AddBookingCommentAction::class);
    Route::delete('/team/{team}/bookings/{booking:id}/comments/{comment}', DeleteBookingCommentWebAction::class);
    Route::put('/team/{team}/bookings/{booking:id}/notes', EditBookingNotesAction::class);
    Route::get('/team/{team}/bookings/{booking:id}/payments/{payId}', GetBookingPaymentAction::class);
    Route::post('/team/{team}/bookings/{booking:id}/payments', AddBookingPaymentAction::class);
    Route::delete('/team/{team}/bookings/{booking:id}/payments/{bookingPayment:id}', CancelBookingPaymentAction::class);
    Route::put('/team/{team}/bookings/{booking:id}/payments/{bookingPayment:id}/capture-damage-deposit', CaptureDamageDepositAction::class);

    Route::get('/team/{team}/bookings/{booking:id}/guide/locales', GetGuideLocalesBookingAction::class);

    Route::get('/team/{team}/bookings/{booking:id}/pre-check-in', GetPreCheckInBookingAction::class);
    Route::put('/team/{team}/bookings/{booking:id}/pre-check-in', UpdatePreCheckInBookingAction::class);
    Route::delete('/team/{team}/bookings/{booking:id}/pre-check-in', DeletePreCheckInBookingAction::class);
    Route::get('/team/{team}/bookings/{booking:id}/pre-check-in/sms', SendPreCheckInBookingSmsAction::class);
    Route::post('/team/{team}/bookings/{booking:id}/pre-check-in/send', SendPreCheckInFormBookingEmailAction::class);
    Route::post('/team/{team}/bookings/{booking:id}/down-payment-email/send', SendDownPaymentEmailAction::class);
    Route::post('/team/{team}/bookings/{booking:id}/home-automation/smartlock-authorisation', CreateSmartlockBookingAuthorisationAction::class)
        ->middleware(VerifyUserCanUseHomeAutomationDevices::class);

    Route::post('/team/{teamId}/bookings/{bookingId}/pre-check-in/document', IdentityParserAction::class);

    Route::post('/team/{team}/pre-check-ins', GetPreCheckInFormsAction::class);

    Route::get('/team/{team}/clients/', GetOrSearchClientsAction::class);

    // Conversations and messages
    Route::get('/inbox/{team}/booking/{booking:id}', GetBookingConversationAction::class);
    Route::get('/inbox/{team}/attachments/{messageAttachment:id}', GetMessageAttachmentTempUrlAction::class)
        ->middleware(VerifyUserCanAccessInbox::class);

    // Bookings and clients routes
    Route::middleware([CanManageExtraRentalsMiddleware::class])
        ->group(function () {
            Route::get('/team/{team}/clients/{client:id}', GetOneClientAction::class);

            // Booking creation and edit flow
            Route::get('/team/{team}/booking-manager/', GetBookingManagementAction::class);
            Route::get('/team/{team}/booking-manager/{allBooking:id}', GetBookingManagementAction::class);

            Route::patch('/team/{team}/booking-manager/', ManageBookingAction::class);

            Route::post('/team/{team}/booking-manager/submit', CreateBookingAction::class);
            Route::put('/team/{team}/booking-manager/{allBooking:id}', ModifyBookingAction::class);
            Route::put('/team/{team}/booking-manager/{allBooking:id}/submit', ModifyBookingAction::class);

            // Require can_manage_bookings permission
            Route::post('/team/{team}/properties/{teamRental:id}/booking', StoreBookingAction::class);
            Route::put('/team/{team}/properties/{teamRental:id}/booking/{booking:id}', OldUpdateBookingAction::class);
            Route::put('/team/{team}/properties/{teamRental:id}/booking/{booking:id}/update', UpdateBookingAction::class);
            Route::post('/team/{team}/properties/{teamRental:id}/booking/{booking:id}/fee', AddBookingFeeAction::class);
            Route::put('/team/{team}/properties/{teamRental:id}/booking/{booking:id}/fee/{bookingFee:id}', UpdateBookingFeeAction::class);
            Route::delete('/team/{team}/properties/{teamRental:id}/booking/{booking:id}/fee/{bookingFee:id}', DeleteBookingFeeAction::class);

            // Delete if not used
//            Route::post('/team/{team}/properties/{teamRental:id}/booking/{booking:id}/rates', BookingRateSimulatorForUpdateAction::class);
//            Route::post('/team/{team}/properties/{teamRental:id}/booking/{booking:id}/fees', BookingFeesSimulatorForUpdateAction::class);

            Route::post('/team/{team}/clients', StoreClientAction::class);
            Route::put('/team/{team}/clients/{client:id}', UpdateClientAction::class);
            Route::delete('/team/{team}/clients/{client:id}', DeleteClientAction::class);
        });

    // NOTIFICATION CENTER
    Route::middleware([VerifyCanSeeAutomationsMiddleware::class])
        ->group(function () {
            // Logs
            Route::get('/team/{team}/automations/log', FetchBookingAutomationLogsAction::class);
            Route::get('/team/{team}/automations/actions/log', FetchTriggerActionLogsAction::class);

            // Getters
            Route::get('/team/{team}/automations/form', GetAutomationsFormAction::class);
            Route::get('/team/{team}/automations/', GetAllAutomationsAction::class);
            Route::get('/team/{team}/automations/{automation:id}', GetOneAutomationAction::class);
        });
    Route::middleware([VerifyCanSeeAutomationsMiddleware::class, VerifyCanEditAutomationsMiddleware::class])
        ->group(function () {
            Route::post('/team/{team}/automations/', CreateAutomationAction::class);
            Route::put('/team/{team}/automations/{automation:id}', UpdateAutomationAction::class);
            Route::delete('/team/{team}/automations/{automation:id}', DeleteOneAutomationAction::class);

            Route::post('/team/{team}/automations/{automation:id}/actions', AttachTriggerActionAction::class);
            Route::put('/team/{team}/automations/{automation:id}/actions/{triggerAction:id}', UpdateTriggerActionAction::class);
            Route::delete('/team/{team}/automations/{automation:id}/actions/{triggerAction:id}', DeleteTriggerActionAction::class);
        });

    // BOOKING TAGS
    Route::post('/team/{team}/booking-tag', CreateBookingTagAction::class);
    Route::put('/team/{team}/booking-tag/{tag:id}', UpdateBookingTagAction::class);
    Route::delete('/team/{team}/booking-tag/{tag:id}', DeleteBookingTagAction::class);
    Route::get('/team/{team}/booking-tag/{tag:id}', GetOneBookingTagAction::class);
    Route::get('/team/{team}/booking-tag', GetAllBookingTagAction::class);
    Route::post('/team/{team}/bookings/{booking:id}/tag/{tag:id}', AttachTagToBookingAction::class);
    Route::delete('/team/{team}/bookings/{booking:id}/tag/{tag:id}', RemoveTagFromBookingAction::class);
    Route::post('/team/{team}/bookings/{booking:id}/set-tags', SetTagsToBookingAction::class);

    // SOURCES ROUTES
    Route::get('/team/{team}/booking-sources', GetTeamSourcesAction::class);
    Route::get('/team/{team}/booking-sources/{source:id}', GetOneSourceAction::class);
    Route::middleware([CanManageExtraRentalsMiddleware::class, VerifyUserIsAdmin::class])
        ->group(function () {
            Route::post('/team/{team}/booking-sources', StoreNewProviderSourceAction::class);
            Route::put('/team/{team}/booking-sources/{source:id}', UpdateSourceAction::class);
            Route::delete('/team/{team}/booking-sources/{source:id}', DeleteSourceAction::class);
        });

    // OWNERS TODO: we should protect these routes with user permissions + ensure rental routes do not return rental owner information
    Route::get('/team/{team}/owners', GetAllRentalOwnersAction::class);
    Route::get('/team/{team}/owners/{rentalOwner:id}', GetOneRentalOwnerAction::class);
    Route::post('/team/{team}/owners', GetOrCreateRentalOwnerAction::class);
    Route::put('/team/{team}/owners/{rentalOwner:id}', UpdateRentalOwnerAction::class);
    Route::delete('/team/{team}/owners/{rentalOwner:id}', DestroyRentalOwnerAction::class);

    // PROPERTIES ROUTES
    Route::get('/team/{team}/properties/{rentId}', GetRentalWithRelationsAction::class);

    Route::post('/team/{team}/properties/{rentId}/pictures', AddRentalNinjaPictureAction::class);
    Route::post('/team/{team}/properties/{rentId}/block', BlockRentalAction::class);
    Route::get('/team/{team}/properties/model-179/dropdown', GetDropdownDataModel179Action::class);

    // These routes are now also used for guest info. We need to improve the security of them, since we cannot use following middlewares.
    Route::get('/team/{team}/properties/extended/data', GetExtendedRentalDataAction::class);
    Route::get('/team/{team}/properties/context/{teamRental:id}', GetContextForRentalAction::class)
        ->preventsScopedBindings();
    Route::get('/team/{team}/properties/extended/{teamRental:id}', GetExtendedRentalAction::class)
        ->preventsScopedBindings(); // This removes query scopes (like uncompleted) but keeps the temRental:id to team_id route binding
    Route::put('/team/{team}/properties/extended/{teamRental:id}', UpdateExtendedRentalAction::class)
        ->preventsScopedBindings(); // This removes query scopes (like uncompleted) but keeps the temRental:id to team_id route binding
    Route::put('/team/{team}/properties/{teamRental:id}/down-payment', UpdateCollectDownPaymentAction::class) // Used for flavours without full rental view
    ->preventsScopedBindings();

    Route::middleware([CanManageExtraRentalsMiddleware::class])
        ->group(function () {
            Route::middleware([VerifyUserIsAdmin::class])
                ->group(function () {
                    Route::post('/team/{team}/properties/', StoreNewRentalAction::class);
                    Route::put('/team/{team}/properties/restore/{teamRental:id}', RestoreRentalAction::class)
                        ->withTrashed();

                    Route::put('/team/{team}/properties/{rentId}', UpdateRentalAction::class);

                    Route::get('/team/{team}/distribution/', GetTeamDistributionRentalsAction::class);
                    Route::post('/team/{team}/distribution/property/{teamRental:id}', PublishRentalToChannelManagerAction::class);
                    Route::delete('/team/{team}/distribution/property/{teamRental:id}', HideRentalFromChannelManagerAction::class);
                    Route::get('/team/{team}/distribution/refresh', FetchDistributionChannelsStatsAction::class);
                    Route::get('/team/{team}/distribution/data/{teamRental:id}', GetRentalDistributionDataAction::class);

                    Route::delete('/team/{team}/properties/{rentId}', DeleteRentalAction::class);

                    Route::post('/team/{team}/properties/{teamRental:id}/rates/duplicate', DuplicateEverythingFromRatesAction::class);

                    // PRICING MODEL
                    Route::get('/team/{team}/properties/pricing/{teamRental:id}', GetRentalPricingModelAction::class);
                    Route::put('/team/{team}/properties/pricing/{teamRental:id}', UpdateRentalPricingModelAction::class);

                    Route::get('/team/{team}/properties/{teamRental:id}/pricing', GetRentalPricingModelAction::class);
                    Route::put('/team/{team}/properties/{teamRental:id}pricing', UpdateRentalPricingModelAction::class);
                    Route::put('/team/{team}/properties/{teamRental:id}/smart-pricing-preview', PreviewRatesFromWheelhouseAction::class);

                    // SEASONAL PRICES
                    Route::get('/team/{team}/properties/{teamRental:id}/seasonal-price', GetRentalSeasonalPricesAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/seasonal-price', StoreNewSeasonalRuleAction::class);
                    Route::get('/team/{team}/properties/{teamRental:id}/seasonal-price/{seasonalPrice:id}', GetOneSeasonalPriceAction::class);
                    Route::put('/team/{team}/properties/{teamRental:id}/seasonal-price/{seasonalPrice:id}', UpdateSeasonalRuleAction::class);
                    Route::delete('/team/{team}/properties/{teamRental:id}/seasonal-price/{seasonalPrice:id}', DeleteSeasonalRuleAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/seasonal-price/sort', SortSeasonalRulesAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/seasonal-price/duplicate', DuplicateSeasonalRulesAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/seasonal-price/clean', CleanRentalSeasonsAction::class);

                    // DISCOUNTS
                    Route::get('/team/{team}/properties/{teamRental:id}/discounts/{type}', GetRentalDiscountsAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/discounts/{type}', StoreNewRentalDiscountAction::class);
                    Route::get('/team/{team}/properties/{teamRental:id}/discounts/{type}/{rentalDiscount:id}', GetOneRentalDiscountAction::class);
                    Route::put('/team/{team}/properties/{teamRental:id}/discounts/{type}/{rentalDiscount:id}', UpdateRentalDiscountAction::class);
                    Route::delete('/team/{team}/properties/{teamRental:id}/discounts/{type}/{rentalDiscount:id}', DeleteRentalDiscountAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/discounts/{type}/sort', SortRentalDiscountAction::class);

                    // RENTAL FEES
                    Route::post('/team/{team}/properties/{teamRental:id}/fees', StoreNewRentalFeeAction::class);
                    Route::put('/team/{team}/properties/{teamRental:id}/fees/{rentalFee:id}', UpdateRentalFeeAction::class);
                    Route::delete('/team/{team}/properties/{teamRental:id}/fees/{rentalFee:id}', DeleteRentalFeeAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/fees/sort', SortRentalFeesAction::class);

                    // BOOKING PRICE CALENDAR AND CALCULATOR
                    Route::post('/team/{team}/properties/{teamRental:id}/calculate-seasonal-prices', CalendarRentalPricesAction::class);
                    //Route::post('/team/{team}/properties/{teamRental:id}/booking-price-calculator', BookingRateSimulatorAction::class);
                    // RATE CALENDAR AND CALCULATOR
                    Route::post('/team/{team}/properties/{teamRental:id}/get-rates-calendar', OldGetRatesCalendarAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/booking-price-calculator', OldGetBookingRateAction::class);

                    Route::post('/team/{team}/properties/{teamRental:id}/rates-calendar', GetRatesCalendarAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/calculate-booking-price', GetBookingPriceAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/push-manual-rates', PushManualRentalDailyDetailsAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/delete-manual-rates', DeleteManualRentalDailyDetailsAction::class);

                    // PICTURES
                    Route::get('/team/{team}/properties/{teamRental:id}/rental-picture', GetRentalPicturesAction::class);
                    Route::get('/team/{team}/properties/{teamRental:id}/rental-picture-upscale/{photo:id}', UpscalePicsartRentalPictureAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/rental-picture', StoreNewRentalPictureAction::class);
                    Route::put('/team/{team}/properties/{teamRental:id}/rental-picture/sort', SortRentalPictureAction::class);
                    Route::put('/team/{team}/properties/{teamRental:id}/rental-picture/{photo:id}', UpdateRentalPictureAction::class);
                    Route::delete('/team/{team}/properties/{teamRental:id}/rental-picture/{photo:id}', DeleteRentalPictureAction::class);

                    // PROPERTIES ICAL
                    Route::post('/team/{team}/properties/{teamRental:id}/input-ical', AttachInputICalToRentalAction::class);
                    Route::post('/team/{team}/properties/{teamRental:id}/output-ical', GenerateNewOutputICalAction::class);
                    Route::put('/team/{team}/properties/{teamRental:id}/output-ical/{iCalOutput:id}', UpdateOutputICalAction::class);
                    Route::put('/team/{team}/properties/{teamRental:id}/input-ical/{iCalInput:id}', UpdateInputICalAction::class);
                    Route::delete('/team/{team}/properties/{teamRental:id}/output-ical/{iCalOutput:id}', RemoveICalAction::class);
                    Route::delete('/team/{team}/properties/{teamRental:id}/input-ical/{iCalInput:id}', RemoveICalAction::class);

                    Route::get('/team/{team}/properties/{teamRental:id}/input-ical/{iCalInput:id}/sync', SyncRentalICalAction::class)
                        ->middleware('throttle:ninja-micro');
                    Route::post('/team/{team}/properties/{teamRental:id}/ical/{iCalInput:id}/sync', SyncRentalICalAction::class)
                        ->middleware('throttle:ninja-micro');

                    // FEES
                    Route::post('/team/{team}/fees', StoreNewFeeAction::class);
                    Route::put('/team/{team}/fees/{fee:id}', UpdateFeeAction::class);
                    Route::delete('/team/{team}/fees/{fee:id}', DeleteFeeAction::class);

                    // COPY INFORMATION FROM RENTAL TO RENTALS
                    Route::get('/team/{team}/copy-rental-data', [CopyRentalCustomAction::class, 'getOptions']);
                    Route::post('/team/{team}/copy-rental-data', CopyRentalCustomAction::class);
                });
            Route::get('/team/{team}/properties/{teamRental:id}/input-ical', GetRentalICalAction::class);
            Route::get('/team/{team}/properties/{teamRental:id}/output-ical', GetRentalICalAction::class);

            Route::get('/team/{team}/fees', GetAllFeesAction::class);
            Route::get('/team/{team}/fees/{fee:id}', GetOneFeeAction::class);

            // WHITE LABEL CHANNEL MANAGER
            Route::get('/team/{team}/white-label-html', GetWhiteLabelHtmlAction::class)
                 ->middleware(VerifyTeamIsVerified::class);
        });

    // PICTURES RELATED
    Route::get('/team/{team}/pictures', GetPicturesAsPerFilterAction::class);
    Route::post('/team/{team}/pictures', GetPicturesAsPerFilterAction::class);
    Route::get('/team/{team}/pictures/booking/{booking:id}', GetBookingPicturesAction::class);
    Route::get('/team/{team}/pictures/booking/{booking:id}/with-tasks', GetBookingPicturesWithTasksAction::class);
    Route::delete('/team/{team}/pictures/{picture:id}', DeletePictureAction::class);

    // ALERT RELATED
    Route::post('/team/{team}/alerts', GetUserAlertsAction::class);
    Route::post('/team/{team}/alerts/{alert}/snooze', SnoozeAlertAction::class);
    Route::post('/team/{team}/alerts/{alert}/un-snooze', UnSnoozeAlertAction::class);

    // TEAM MEMBER MANAGEMENT
    Route::post('/team/{team}/members/permissions', CheckMembersPermissionsAction::class);
    Route::middleware(['verifyUserIsAdmin'])
        ->group(function () {
            Route::get('/team/{team}/members/{team_member}', GetTeamMemberAction::class);
            Route::put('/team/{team}/members/{team_member}', UpdateTeamMemberAction::class);
            Route::post('/team/{team}/members/{team_member}/role', SetTeamMemberRoleAction::class);
            Route::delete('/team/{team}/members/{team_member}', RemoveTeamMemberAction::class);
        });

    // ADDONS
    Route::post('team/{team}/addons/bill', BillAddonAction::class);
    Route::post('team/{team}/addons/usage', GetAddonsUsageAction::class);
    Route::post('team/{team}/gen-ai', ExecuteAIUserRequestAction::class);

    // STATS CONTROL CENTER
    Route::middleware(['cacheResponse'])
        ->group(function () {
            Route::post('/team/{team}/stats/revenue-from-bookings', GetCompactRevenueFromBookingsStatsAction::class);
            Route::post('/team/{team}/stats/revenue-generated', GetCompactRevenueGeneratedStatsAction::class);
            Route::post('/team/{team}/stats/payments-received', GetCompactPaymentsReceivedStatsAction::class);
            Route::post('/team/{team}/stats/guests-hosted', GetCompactGuestsHostedStatsAction::class);
            Route::post('/team/{team}/stats/active-rentals', GetCompactRentalCountStatsAction::class);
            Route::post('/team/{team}/stats/nights-booked-ahead', GetCompactNightsBookedAheadStatsAction::class);
            Route::post('/team/{team}/stats/bookings-created', GetCompactBookingsReceivedStatsAction::class);
            Route::post('/team/{team}/stats/nights-created', GetCompactNightsReceivedStatsAction::class);
            Route::post('/team/{team}/stats/bookings-cancelled', GetCompactBookingsCancelledStatsAction::class);
            Route::post('/team/{team}/stats/nights-cancelled', GetCompactNightsCancelledStatsAction::class);
            Route::post('/team/{team}/stats/active-users', GetCompactRentalNinjaUsersStatsAction::class);
            Route::post('/team/{team}/stats/channel-nights', GetCompactChannelNightsStatsAction::class);
            Route::post('/team/{team}/stats/channel-revenue', GetCompactChannelRevenueStatsAction::class);
            Route::post('/team/{team}/stats/channel-commission', GetCompactChannelCommissionStatsAction::class);
            Route::post('/team/{team}/stats/payment-methods', GetCompactPaymentMethodsStatsAction::class);
            Route::post('/team/{team}/stats/nights-filled', GetCompactNightsFilledStatsAction::class);
            Route::post('/team/{team}/stats/occupancy-rate', GetCompactOccupancyRatesStatsAction::class);
            Route::post('/team/{team}/stats/advance-booking-days', GetCompactAdvanceBookingsStatsAction::class);
            Route::post('/team/{team}/stats/average-booking-price', GetCompactAverageBookingPriceStatsAction::class);
            Route::post('/team/{team}/stats/average-booking-length', GetCompactAverageBookingLengthStatsAction::class);
            Route::post('/team/{team}/stats/average-night-price', GetCompactAveragePricePerNightStatsAction::class);
        });

    /// STATS DASHBOARDS
    Route::post('/team/{team}/stats/dashboard/revenue-from-bookings', GetDetailsRevenueFromBookingsStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/revenue-generated', GetDetailsRevenueGeneratedStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/payments-received', GetDetailsPaymentsReceivedStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/guests-hosted', GetDetailsGuestsHostedStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/bookings-created', GetDetailsBookingsReceivedStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/nights-created', GetDetailsNightsReceivedStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/nights-filled', GetDetailsNightsFilledStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/occupancy-rate', GetDetailsOccupancyRatesStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/nights-booked-ahead', GetDetailsNightsBookedAheadStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/channel-revenue', GetDetailsChannelRevenueStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/channel-nights', GetDetailsChannelNightsStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/channel-commission', GetDetailsChannelCommissionStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/payment-methods', GetDetailsPaymentMethodsStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/bookings-cancelled', GetDetailsBookingsCancelledStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/nights-cancelled', GetDetailsNightsCancelledStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/advance-booking-days', GetDetailsAdvanceBookingsStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/average-booking-price', GetDetailsAverageBookingPriceStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/average-booking-length', GetDetailsAverageBookingLengthStatsAction::class);
    Route::post('/team/{team}/stats/dashboard/average-night-price', GetDetailsAveragePricePerNightStatsAction::class);

    // STATS DOWNLOADS
    Route::post('/team/{team}/stats/download/revenue-from-bookings', ExportRevenueFromBookingsStatsAction::class);
    Route::post('/team/{team}/stats/download/revenue-generated', ExportRevenueGeneratedStatsAction::class);
    Route::post('/team/{team}/stats/download/payments-received', ExportPaymentsReceivedStatsAction::class);
    Route::post('/team/{team}/stats/download/guests-hosted', ExportGuestsHostedStatsAction::class);
    Route::post('/team/{team}/stats/download/bookings-created', ExportBookingsReceivedStatsAction::class);
    Route::post('/team/{team}/stats/download/nights-created', ExportNightsReceivedStatsAction::class);
    Route::post('/team/{team}/stats/download/nights-filled', ExportNightsFilledStatsAction::class);
    Route::post('/team/{team}/stats/download/occupancy-rate', ExportOccupancyRatesStatsAction::class);
    Route::post('/team/{team}/stats/download/nights-booked-ahead', ExportNightsBookedAheadStatsAction::class);
    Route::post('/team/{team}/stats/download/channel-revenue', ExportChannelRevenueStatsAction::class);
    Route::post('/team/{team}/stats/download/channel-nights', ExportChannelNightsStatsAction::class);
    Route::post('/team/{team}/stats/download/channel-commission', ExportChannelCommissionStatsAction::class);
    Route::post('/team/{team}/stats/download/payment-methods', ExportPaymentMethodsStatsAction::class);
    Route::post('/team/{team}/stats/download/bookings-cancelled', ExportBookingsCancelledStatsAction::class);
    Route::post('/team/{team}/stats/download/nights-cancelled', ExportNightsCancelledStatsAction::class);
    Route::post('/team/{team}/stats/download/advance-booking-days', ExportAdvanceBookingsStatsAction::class);
    Route::post('/team/{team}/stats/download/average-booking-price', ExportAverageBookingPriceStatsAction::class);
    Route::post('/team/{team}/stats/download/average-booking-length', ExportAverageBookingLengthStatsAction::class);
    Route::post('/team/{team}/stats/download/average-night-price', ExportAveragePricePerNightStatsAction::class);

    Route::middleware(['verifyTeamIsProfessional'])
        ->group(function () {
            // Tasks
            Route::post('/team/{team}/tasks-list', GetUserCurrentTasksListAction::class); // Mobile - Web
            Route::post('/team/{team}/tasks/future', GetUserFutureTasksListAction::class); // Mobile - Web
            Route::post('/team/{team}/tasks/complete', GetUserCompletedTasksListAction::class); // Mobile - Web

            Route::post('/team/{team}/tasks/team', GetTeamCurrentTasksAction::class); // Web
            Route::post('/team/{team}/tasks/team/future', GetTeamFutureTasksAction::class); // Web
            Route::post('/team/{team}/tasks/team/no-users', GetTeamCurrentNoUsersTasksAction::class); // Mobile
            Route::post('/team/{team}/tasks/team/no-users/future', GetTeamFutureNoUsersTasksAction::class); // Mobile
            Route::post('/team/{team}/tasks/team/complete', GetTeamCompletedTasksAction::class); // Web
            Route::post('/team/{team}/tasks/team/complete/no-users', GetTeamCompletedNoUsersTasksAction::class); // Mobile
            Route::post('/team/{team}/tasks/team/export-excel', ExportTasksToExcelAction::class)
                ->middleware('userIsRentalManagerOrAbove');

            Route::post('/team/{team}/tasks/to-inspect', GetCurrentTasksToInspectAction::class); // Mobile - Web
            Route::post('/team/{team}/tasks/to-inspect/future', GetFutureTasksToInspectAction::class); // Mobile - Web
            Route::post('/team/{team}/tasks/inspected', GetInspectedTasksAction::class); // Mobile - Web

            Route::post('/team/{team}/tasks/admin-tasks', GetAdminTasksAction::class);
            Route::post('/team/{team}/tasks/rental/{rentId}', GetTasksForRentalAction::class);
            Route::post('/team/{team}/tasks/for-rentals', GetTasksForRentalsQueryAction::class);
            Route::post('/team/{team}/tasks/for-users', GetTasksForUsersQueryAction::class);

            Route::get('/team/{team}/tasks/{task}', GetTaskWithRelationsAction::class);
            Route::post('/team/{team}/tasks', StoreTaskAction::class);

            Route::put('/team/{team}/tasks/{task}', UpdateTaskAction::class);
            Route::post('/team/{team}/tasks/{task}/complete', CompleteOrUncompleteTaskAction::class);
            Route::post('/team/{team}/tasks/{task}/inspect', MarkTaskInspectedOrUninspectedAction::class);
            Route::post('/team/{team}/tasks/{task}/{item}/complete', CompleteOrUncompleteTaskItemAction::class);
            Route::delete('/team/{team}/tasks/{task}', DestroyTaskAction::class)
                ->middleware('userIsRentalManagerOrAbove');
            Route::post('/team/{team}/tasks/{task}/{item}/pictures', AddTaskPictureAction::class);
            Route::delete('/team/{team}/tasks/{task}/{item}/pictures/{picture}', DeleteTaskPictureAction::class);

            // Scheduled Tasks
            Route::middleware('userIsRentalManagerOrAbove')
                ->group(function () {
                    Route::get('/team/{team}/auto', GetTeamScheduledTasksAction::class);
                    Route::get('team/{team}/auto/slim', [GetTeamScheduledTasksAction::class, 'asApiResponse']);
                    Route::post('/team/{team}/auto', StoreScheduledTaskAction::class);
                    Route::get('/team/{team}/auto/{task}', GetScheduledTaskWithRelationsAction::class);
                    Route::put('/team/{team}/auto/{task}', UpdateScheduleTaskAction::class);
                    Route::delete('/team/{team}/auto/{scheduled_task}', DestroyScheduledTaskAction::class);
                });

            // Recurrent Tasks
            Route::middleware('userIsRentalManagerOrAbove')
                ->scopeBindings()
                ->group(function () {
                    Route::get('/team/{team}/recurrent', GetTeamRecurrentTasksAction::class);
                    Route::get('team/{team}/recurrent/slim', [GetTeamRecurrentTasksAction::class, 'slimResponse']);
                    Route::post('/team/{team}/recurrent', StoreRecurrentTaskAction::class);
                    Route::get('/team/{team}/recurrent/{recurrent_task}', GetRecurrentTaskWithRelationsAction::class);
                    Route::put('/team/{team}/recurrent/{recurrent_task}', UpdateRecurrentTaskAction::class);
                    Route::delete('/team/{team}/recurrent/{recurrent_task}', DestroyRecurrentTaskAction::class);
                });

            // Checklists
            Route::middleware('userIsRentalManagerOrAbove')
                ->group(function () {
                    Route::get('/team/{team}/checklists', GetTeamChecklistsAction::class);
                    Route::get('/team/{team}/checklists/slim', [GetTeamChecklistsAction::class, 'slimTeamCheckLists']);
                    Route::post('/team/{team}/checklists', StoreChecklistAction::class);
                    Route::get('/team/{team}/checklists/{checklist}', GetChecklistAction::class);
                    Route::put('/team/{team}/checklists/{checklist}', UpdateChecklistAction::class);
                    Route::delete('/team/{team}/checklists/{checklist}', DestroyChecklistAction::class);
                });
        });

    // ACCOUNTING ROUTES
    Route::middleware(['verifyTeamIsProfessional', 'verifyUserHasAccessToAccounting'])
        ->group(function () {
            // Settlements related routes.
            Route::get('/currency-for/{team}', GetCurrenciesForTeamAction::class);
            Route::get('/accounting/{team}/settlements', GetTeamSettlementsAction::class);
            Route::post('/accounting/{team}/settlements/{settlement}', GetOneSettlementResourceAction::class);
            Route::post('/accounting/{team}/settlements', StoreSettlementAction::class);
            Route::put('/accounting/{team}/settlements/{settlement}', UpdateSettlementAction::class);
            Route::delete('/accounting/{team}/settlements/{settlement}', DestroySettlementAction::class);
            Route::post('/accounting/{team}/settlements/{settlement}/download', EmailOrDownloadSettlementAction::class);
            Route::post('/accounting/{team}/settlements/{settlement}/excel-export', ExportExcelSettlementAction::class);
            Route::post('/accounting/{team}/settlements/{settlement}/email', EmailOrDownloadSettlementAction::class);
            Route::get('/accounting/{team}/settlements/{settlement:id}/email-list', GetSettlementEmailsListAction::class);
            Route::post('/accounting/{team}/settlements/{settlement}/to-review', UpdateSettlementToReviewAction::class);

            // Payees Related
            Route::get('/accounting/{team}/payees', GetPayeesAction::class);
            Route::get('/accounting/{team}/payees/slim', GetSlimPayeesAction::class);
            Route::get('/accounting/{team}/payees/super-slim', GetSuperSlimPayeesAction::class);
            Route::get('/accounting/{team}/payees/fill', CreatePayeesWithRentalOwnersAction::class);
            Route::get('/accounting/{team}/payees/{payee:id}', GetPayeeAction::class);
            Route::post('/accounting/{team}/payees', StorePayeeAction::class);
            Route::put('/accounting/{team}/payees/{payee:id}', UpdatePayeeAction::class);
            Route::post('/accounting/{team}/payees/{payee:id}/duplicate/{payeeFrom}', DuplicatePayeeAction::class);
            Route::delete('/accounting/{team}/payees/{payee:id}', DestroyPayeeAction::class);
            Route::get('/accounting/{team}/payees/{payee:id}/invoices-issued', GetPayeeInvoicesIssuedAction::class);
            Route::get('/accounting/{team}/payees/{payee:id}/invoices-received', GetPayeeInvoicesReceivedAction::class);
            Route::get('/accounting/{team}/payees/{payee:id}/download-invoices', DownloadPayeeAction::class);

            // Payout Related
            Route::get('/accounting/{team}/payouts', GetAllPayoutsAction::class);
            Route::post('/accounting/{team}/payouts/mark-as-paid', [MarkPayoutAsPaidAction::class, 'asMultiple']);
            Route::post('/accounting/{team}/payouts/export', ExportAccountingExcelAction::class); // Delete after 2024-09-30
            Route::post('/accounting/{team}/exporter', ExportAccountingExcelAction::class);

            Route::get('/accounting/{team}/settlements/{settlement:id}/payouts', GetSettlementPayoutsAction::class);
            Route::get('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}', GetOnePayoutAction::class);
            Route::post('/accounting/{team}/settlements/{settlement:id}/payouts', StorePayoutAction::class);
            Route::post('/accounting/{team}/settlements/{settlement:id}/payouts/multiple', StoreMultiplePayoutsAction::class);
            Route::put('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}', UpdatePayoutAction::class);
            Route::delete('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}', DestroyPayoutAction::class);
            Route::post('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}/download', EmailOrDownloadPayoutAction::class);
            Route::post('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}/email', EmailOrDownloadPayoutAction::class);
            Route::post('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}/export', ExportPayoutAction::class);
            Route::get('/accounting/{team}/settlements/{settlement}/payouts/{payout}/mark-as-paid', MarkPayoutAsPaidAction::class);
            Route::delete('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}/partials/{partialPayoutId}', DestroyPartialPayoutAction::class);
            Route::post('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}/partials', CreatePartialPayoutAction::class);
            Route::post('/accounting/{team}/settlements/{settlement}/payouts/{payout}/invoice', CreatePayeeInvoiceAction::class);
            Route::get('/accounting/{team}/settlements/{settlement:id}/payout-details/{teamPayee:id}/create', CalculatePayoutDetailsAction::class);
            Route::get('/accounting/{team}/settlements/{settlement:id}/payout-details/{teamPayee:id}/create-by-type', CalculatePayoutDetailsByTypeAction::class);

            // Payout Attachment Related
            Route::get('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}/attachments', GetPayoutAttachmentsAction::class);
            Route::post('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}/attachments', StorePayoutAttachmentAction::class);
            Route::delete('/accounting/{team}/settlements/{settlement:id}/payouts/{payout:id}/attachments/{attachment:id}', DestroyPayoutAttachmentAction::class);

            // MODEL 179 related
            Route::post('/accounting/{team}/model-179/validate', ValidateModel179Action::class);
            Route::post('/accounting/{team}/model-179/validate-response', ValidateModel179ResponseAction::class);
            Route::post('/accounting/{team}/model-179/download', DownloadModel179Action::class);
            Route::get('/accounting/{team}/model-179/rentals/dropdown', GetDropdownDataModel179Action::class);
            Route::put('/accounting/{team}/model-179/rentals/booking/{booking:id}/passports', UpdateModel179PassportsAction::class);
            Route::get('/accounting/{team}/model-179/rentals', GetTeamLegalDetailsModel179Action::class);
            Route::put('/accounting/{team}/model-179/rentals/{settings}', UpdateLegalDetailsModel179Action::class);
            Route::get('/accounting/{team}/model-179/rentals/{settings}', GetOneLegalDetailsModel179Action::class);
            Route::get('/accounting/{team}/model-179/rentals/rental/{id}', GetRentalLegalDetailsModel179Action::class);

            // Schedule Accounting related
            Route::get('/accounting/{team}/scheduled-settlements', GetTeamScheduledSettlementsAction::class);
            Route::get('/accounting/{team}/scheduled-settlements/{scheduledSettlement:id}', [GetTeamScheduledSettlementsAction::class, 'getSingle']);
            Route::post('/accounting/{team}/scheduled-settlements', StoreScheduledSettlementAction::class);
            Route::put('/accounting/{team}/scheduled-settlements/{scheduledSettlement:id}', UpdateScheduledSettlementAction::class);
            Route::delete('/accounting/{team}/scheduled-settlements/{scheduledSettlement:id}', DeleteScheduledSettlementAction::class);

            // Advanced Accounting settings
            Route::get('/accounting/{team}/advanced-settings', GetOrRefreshAccountingAdvancedSettingsAction::class);
            Route::put('/accounting/{team}/advanced-settings', UpdateAccountingAdvancedSettingsAction::class);

            // Statistics Related
            // CACHED ROUTES
            Route::middleware(['cacheResponse'])
                ->group(function () {
                    Route::get('/accounting/{team}/stats/money-paid-over-time', GetCompactPaymentsOverTimeAccountingStatsAction::class);
                    Route::post('/accounting/{team}/stats/money-paid-over-time', GetCompactPaymentsOverTimeAccountingStatsAction::class);

                    Route::get('/accounting/{team}/stats/dashboard/money-paid-over-time', GetDetailsPaymentsOverTimeAccountingStatsAction::class);
                    Route::post('/accounting/{team}/stats/dashboard/money-paid-over-time', GetDetailsPaymentsOverTimeAccountingStatsAction::class);
                    Route::post('/accounting/{team}/stats/download/money-paid-over-time', ExportPaymentsOverTimeAccountingStatsAction::class);

                    Route::get('/accounting/{team}/stats/category-breakdown', GetCategoryBreakdownAccountingStatsAction::class);
                    Route::post('/accounting/{team}/stats/category-breakdown', GetCategoryBreakdownAccountingStatsAction::class);
                });
        });

    // GUESTS APPLICATION ROUTES
    Route::middleware(['verifyTeamIsProfessional', 'VerifyUserHasAccessToGuestApp'])
        ->group(function () {
            // Pre-CheckIn Settings
            Route::get('/guests/{team}/pre-check-in/settings', GetTeamPreCheckInSettingsAction::class);
            Route::put('/guests/{team}/pre-check-in/settings', UpdateTeamPreCheckInSettingsAction::class);
            // Pre-CheckIn Settings per rental
            Route::get('/guests/{team}/pre-check-in/rental/{teamRental:id}', GetRentalPreCheckInSettingsAction::class);
            Route::put('/guests/{team}/pre-check-in/rental/{teamRental:id}', UpdateRentalPreCheckInSettingsAction::class);
            // Guests Registration Authorities
            Route::get('/guests/{team}/rental/{teamRental:id}/guests-registration-authorities', IndexGuestsRegistrationAuthoritiesAction::class);
            Route::get('/guests/{team}/rental/{teamRental:id}/guests-registration-authorities/{guestsRegistrationAuthority}', GetGuestsRegistrationAuthoritiesAction::class);
            Route::post('/guests/{team}/rental/{teamRental:id}/guests-registration-authorities', StoreGuestsRegistrationAuthorityAction::class);
            Route::put('/guests/{team}/rental/{teamRental:id}/guests-registration-authorities/{guestsRegistrationAuthority}', UpdateGuestsRegistrationAuthorityAction::class);
            Route::delete('/guests/{team}/rental/{teamRental:id}/guests-registration-authorities/{guestsRegistrationAuthority}', DestroyGuestsRegistrationAuthorityAction::class);
            Route::get('/guests/{team}/rental/{teamRental:id}/guests-registration-authorities/authorities', GetAuthoritiesListAction::class);
            Route::post('/guests/{team}/rental/{teamRental:id}/guests-registration-authorities/form', GetGuestsRegistrationAuthoritiesFormDataAction::class);
            // Guest-app settings
            Route::get('/guests/{team}/guests-app/settings', GetTeamGuestsAppSettingsAction::class);
            Route::put('/guests/{team}/guests-app/settings', UpdateTeamGuestsAppSettingsAction::class);
            // Guest-app settings per rental
            Route::get('/guests/{team}/guests-app/rental/{teamRental:id}', GetRentalGuestsAppSettingsAction::class);
            Route::put('/guests/{team}/guests-app/rental/{teamRental:id}', UpdateRentalGuestsAppSettingsAction::class);
            // Guide endpoints per rental
            Route::get('/guests/{team}/guide/rentals', GetGuestAppRentalSettingsByUserAction::class);
            Route::get('/guests/{team}/guide/rental/{teamRental:id}', GetRentalGuideAction::class);
            Route::get('/guests/{team}/guide/rental/{teamRental:id}/pdf-url', [GetRentalGuideAction::class, 'pdfUrl']); // In case the user has uploaded a pdf as guide
            Route::put('/guests/{team}/guide/rental/{teamRental:id}', UpdateRentalGuideAction::class);
            Route::get('/guests/{team}/guide/rental/{teamRental:id}/download', DownloadRentalGuideByRentalAction::class);
            Route::get('/guests/{team}/guide/rental/{teamRental:id}/preview', [GetRentalGuideAction::class, 'getPreviewSecret']);
        });

    // Centralized Inbox
    Route::middleware([VerifyUserCanAccessInbox::class])
        ->group(function () {
            Route::post('/inbox/{team}', GetAllInboxMessages::class);
            Route::put('/inbox/{team}/booking/{booking:id}/message/{message:id}/translate', TranslateMessageAction::class);
            Route::post('/inbox/{team}/booking/{booking:id}/mark-as-read', MarkBookingAsReadOrArchiveAction::class);
            Route::post('/inbox/{team}/mark-as-read', MarkManyBookingsAsRead::class);
            Route::post('/inbox/{team}/booking/{booking:id}/message', PostGuestMessageAction::class);
            Route::get('/inbox/{team}/booking/{booking:id}/context', GetContextForBookingAction::class);
        });

    // Support
    Route::middleware([VerifyUserIsAdmin::class])
        ->group(function () {
            Route::get('/support/{team}/training-sessions/', GetTeamTrainingSessionsAction::class);
            Route::get('/support/{team}/training-sessions/{trainingSession:id}', GetOneTeamTrainingSessionAction::class);
        });

    // Home Automation
    Route::get('/team/{team}/home-automation/user/smartlock-authorisations', GetUserSmartlockAuthorisationsAction::class);
    Route::middleware([VerifyUserCanUseHomeAutomationDevices::class])
         ->group(function () {
             Route::post('/team/{team}/home-automation/devices', GetHomeAutomationDevicesAction::class);
             Route::get('/team/{team}/home-automation/devices/{homeAutomationDevice:id}', GetSingleHomeAutomationDeviceAction::class)
                 ->middleware(VerifyUserCanManageHomeAutomationSettings::class);
             Route::put('/team/{team}/home-automation/devices/{homeAutomationDevice:id}', MapDeviceRentalAction::class)
                ->middleware(VerifyUserCanManageHomeAutomationSettings::class);
             Route::delete('/team/{team}/home-automation/devices/{homeAutomationDevice:id}', DeleteHomeAutomationDeviceAction::class)
                 ->middleware(VerifyUserCanManageHomeAutomationSettings::class);
             Route::get('/team/{team}/home-automation/devices/{homeAutomationDevice:id}/open-door', OpenDoorAction::class);
             Route::get('/team/{team}/home-automation/devices/{homeAutomationDevice:id}/lock-door', LockDoorAction::class);
             Route::post('/team/{team}/home-automation/devices/{homeAutomationDevice:id}/authorisation', CreatePermanentSmartlockAuthorisationAction::class);
             Route::delete('/team/{team}/home-automation/devices/{homeAutomationDevice:id}/authorisation/{smartlockAuthorisation:id}', DeleteSmartlockAuthorisationAction::class);
         });

    Route::middleware([VerifyUserCanManageHomeAutomationSettings::class])
         ->group(function () {
             Route::get('/team/{team}/home-automation/accounts', GetHomeAutomationAccountsAction::class);
             Route::post('/team/{team}/home-automation/accounts', ConnectHomeAutomationAccountAction::class);
             Route::put('/team/{team}/home-automation/accounts/{homeAutomationAccount:id}', UpdateHomeAutomationAccountAction::class);
             Route::delete('/team/{team}/home-automation/accounts/{homeAutomationAccount:id}', DisconnectHomeAutomationAccountAction::class);
             Route::post('/team/{team}/home-automation/pricing', GetHomeAutomationPricingAction::class);
         });
});
