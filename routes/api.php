<?php

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register the API routes for your application as
| the routes are automatically authenticated using the API guard and
| loaded automatically by this application's RouteServiceProvider.
|
*/

use App\Actions\Auth\GetLoginQrCodeAction;
use App\Actions\Support\ApiPingAction;
use App\Actions\Support\Firebase\RemoveFirebaseTokenAction;
use App\Actions\Support\Firebase\SetFirebaseTokenAction;
use App\Actions\Support\Register\GetOauthRegistrationLinkAction;
use App\Actions\Support\Register\GetRegistrationDataAction;
use App\Actions\Teams\Subscription\GetRentalNinjaPlansAction;
use App\Actions\Teams\Subscription\GetTeamSubscriptionStripeCheckOutUrlAction;
use App\Actions\Users\GetApiUserAction;
use App\Actions\Users\GetCompleteUserAction;
use App\Actions\Users\Invitations\AcceptInvitationForExistingUserAction;
use App\Actions\Users\Invitations\GetMailedInvitationAction;
use App\Actions\Users\Invitations\RejectInvitationAction;
use App\Actions\Users\Password\ResetPasswordFromEmailAction;
use App\Actions\Users\Password\SendResetPasswordLinkEmailAction;
use App\Actions\Users\RegisterUserAction;
use App\Actions\Users\SaveUserLocaleAction;
use App\Actions\Users\UpdateUserEmailAction;
use App\Actions\Users\UpdateUserNameAction;
use App\Actions\Users\UpdateUserPasswordAction;
use App\Actions\Users\UpdateUserProfilePictureFromFirebaseAction;
use App\Actions\Users\UpdateUserSettingsAction;
use App\Domains\PublicApi\Actions\ApiGetOneRentalAction;
use App\Domains\PublicApi\Actions\ApiGetRentalDailyDetailsAction;
use App\Domains\PublicApi\Actions\ApiGetRentalsAction;
use App\Domains\PublicApi\Actions\ApiPushRentalDailyDetailsAction;
use App\Domains\PublicApi\Middleware\OpenApiPricesMiddleware;

Route::middleware(['throttle:ninja'])
    ->group(function () {
        // Ping checker.
        Route::get('/ping', ApiPingAction::class);
        Route::get('/sentry-ping', [ApiPingAction::class, 'forSentry']);

        // Extra public API calls. Non team related
        Route::prefix('/v3')->group(function ($router) {
            // Forgot password routes
            Route::post('/password/email', SendResetPasswordLinkEmailAction::class);
            Route::post('/password/reset', ResetPasswordFromEmailAction::class);
            Route::get('/invitations/{invitation}', GetMailedInvitationAction::class);
            Route::post('/invitations/accept', AcceptInvitationForExistingUserAction::class);
            Route::post('/invitations/reject', RejectInvitationAction::class);

            // Team and user Registration
            Route::post('/registration/user', RegisterUserAction::class);
            Route::get('/registration/data', GetRegistrationDataAction::class);
            Route::get('/registration/oauth-link/{providerId}', GetOauthRegistrationLinkAction::class);
        });
    });

// These routes can be accessed by not subscribed teams.
Route::middleware(['auth:api', 'hasTeam', 'throttle:ninja'])
    ->withoutMiddleware('teamSubscribed')
    ->prefix('/v3')->group(function () {
        Route::get('plans', GetRentalNinjaPlansAction::class);
        Route::get('user/me', GetCompleteUserAction::class);

        Route::get('team/{team}/get-checkout-url', GetTeamSubscriptionStripeCheckOutUrlAction::class);
    });

Route::middleware([
    'auth:api',
    'hasTeam',
    'teamSubscribed',
    'setApiRequestLocale',
    'throttle:ninja',
])
    ->group(function () {
        Route::prefix('/v3')->group(function ($router) {
            Route::get('/user', GetApiUserAction::class);
            Route::get('/user/qr-password', GetLoginQrCodeAction::class);
            Route::put('/user/{user}/email', UpdateUserEmailAction::class);
            Route::put('/user/{user}/name', UpdateUserNameAction::class);
            Route::put('/user/{user}/locale', SaveUserLocaleAction::class);
            Route::put('/user/{id}/password', UpdateUserPasswordAction::class);
            Route::put('/user/{user}/settings', UpdateUserSettingsAction::class);
            Route::post('/user/{id}/firebase-token', SetFirebaseTokenAction::class);
            Route::post('/user/{id}/profile-picture', UpdateUserProfilePictureFromFirebaseAction::class);
            Route::delete('/user/{id}/firebase-token/{token?}', RemoveFirebaseTokenAction::class);

            require base_path('routes/ninja_routes.php');
        });
    });

// Open API
Route::middleware(['throttle:ninja', OpenApiPricesMiddleware::class])
    ->prefix('/pricing/v0')
    ->group(function () {
        Route::get('/team/{team}/rentals', ApiGetRentalsAction::class);
        Route::get('/team/{team}/rentals/{teamRental:id}', ApiGetOneRentalAction::class);
        Route::get('/team/{team}/rentals/{teamRental:id}/rates', ApiGetRentalDailyDetailsAction::class);
        Route::post('/team/{team}/rentals/{teamRental:id}/rates', ApiPushRentalDailyDetailsAction::class);
    });
