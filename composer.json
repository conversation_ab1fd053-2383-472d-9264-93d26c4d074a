{"name": "rental-ninja/ninja", "description": "Rental Ninja Project.", "keywords": ["rental-ninja"], "license": "MIT", "type": "project", "require": {"php": "8.2.*", "ext-PDO": "*", "ext-curl": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mailparse": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-zip": "*", "aaronfrancis/pulse-outdated": "^0.1.2", "artesaos/seotools": "^1.3", "ashallendesign/laravel-config-validator": "^2.4", "awobaz/compoships": "^2.2", "barryvdh/laravel-ide-helper": "^2.13", "doctrine/dbal": "^3.5", "dompdf/dompdf": "^3.0", "fakerphp/faker": "^1.17", "google/cloud-ai-platform": "^1.12", "google/cloud-vision": "^1.6", "guzzlehttp/guzzle": "^7.4.0", "intervention/image": "^2.7", "kreait/firebase-php": "^7.0", "kreait/laravel-firebase": "^5.1", "laravel-notification-channels/fcm": "^4.0.0", "laravel/cashier": "^15.0", "laravel/framework": "^10.48.23", "laravel/horizon": "^5.21", "laravel/nova": "4.24.2", "laravel/passport": "^11.5", "laravel/pulse": "^1.0@beta", "laravel/slack-notification-channel": "^2.5", "laravel/telescope": "^4.12", "laravel/tinker": "^2.8", "laravel/ui": "^4.2", "laravel/vapor-core": "^2.27", "laravel/vonage-notification-channel": "^3.1", "league/commonmark": "^2.5", "league/flysystem-aws-s3-v3": "^3.0", "league/html-to-markdown": "^5.1", "livewire/flux": "^1.0", "livewire/flux-pro": "^1.0", "livewire/livewire": "^3.4", "lorisleiva/laravel-actions": "^2.5", "maantje/pulse-database": "^0.1.0", "maatwebsite/excel": "^3.1", "maatwebsite/laravel-nova-excel": "^1.3", "mavinoo/laravel-batch": "^2.3", "mews/purifier": "^3.4", "mpociot/vat-calculator": "^3.1", "mtownsend/xml-to-array": "^2.0", "mustache/mustache": "^2.14", "nadar/quill-delta-parser": "^3.4", "nova-kit/nova-on-vapor": "^1.0", "owenvoke/blade-fontawesome": "^2.2", "php-mime-mail-parser/php-mime-mail-parser": "^8.0", "php-webdriver/webdriver": "^1.15", "predis/predis": "^2.0", "psr/simple-cache": "^2.0", "realrashid/sweet-alert": "^7.0", "robsontenorio/mary": "^1.41", "segmentio/analytics-php": "^3.0", "sentry/sentry-laravel": "^4.2", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/array-to-xml": "^3.1", "spatie/enum": "^3.12", "spatie/icalendar-generator": "^2.5", "spatie/laravel-data": "^3.4", "spatie/laravel-markdown": "^2.5", "spatie/laravel-responsecache": "^7.4", "spatie/laravel-sitemap": "^7.2", "spatie/once": "^3.0", "spatie/regex": "^3.1", "spatie/simple-excel": "^3.0", "spatie/temporary-directory": "^2.1", "staudenmeir/eloquent-json-relations": "^1.8", "zoon/commonmark-ext-youtube-iframe": "^2.0"}, "require-dev": {"filp/whoops": "^2.14", "insolita/unused-scanner": "^2.1", "jakeasmith/http_build_url": "0.1.3", "larastan/larastan": "^2.4", "laravel/pint": "^1.0", "laravel/vapor-cli": "^1.51", "madewithlove/license-checker": "^1.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-faker": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "spatie/laravel-cronless-schedule": "^1.1", "spatie/laravel-ignition": "^2.0", "spatie/laravel-ray": "^1.32", "spatie/pest-plugin-test-time": "^2.0", "styleci/cli": "^1.2", "thedoctor0/laravel-factory-generator": "^1.2", "wulfheart/laravel-actions-ide-helper": "^0.7.0"}, "autoload": {"classmap": ["database"], "files": ["app/Support/Helper.php", "app/Support/NinjaRequestHasher.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"files": ["app/Support/Helper.php", "app/Support/NinjaRequestHasher.php"], "psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "@php artisan nova:publish", "@php artisan telescope:publish || true", "@php artisan horizon:publish", "@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "COMPOSER_MIRROR_PATH_REPOS": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}, "0": {"type": "composer", "url": "https://nova.laravel.com"}}, "minimum-stability": "beta", "prefer-stable": true}