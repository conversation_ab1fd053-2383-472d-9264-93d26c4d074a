<?xml version="1.0" encoding="UTF-8"?>
<!-- editado con XMLSpy v2015 rel. 4 sp1 (x64) (http://www.altova.com) por AEAT (Agencia Estatal de Administración Tributaria) -->
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by PC Corporativo (AGENCIA TRIBUTARIA) -->
<schema xmlns="http://www.w3.org/2001/XMLSchema" xmlns:ddiiRB="https://www2.agenciatributaria.gob.es/static_files/common/internet/dep/aplicaciones/es/aeat/ddii/enol/ws/RespuestaBajaDI.xsd" xmlns:ddii="https://www2.agenciatributaria.gob.es/static_files/common/internet/dep/aplicaciones/es/aeat/ddii/enol/ws/DeclaracionInformativa.xsd" targetNamespace="https://www2.agenciatributaria.gob.es/static_files/common/internet/dep/aplicaciones/es/aeat/ddii/enol/ws/RespuestaBajaDI.xsd" elementFormDefault="qualified">
	<import namespace="https://www2.agenciatributaria.gob.es/static_files/common/internet/dep/aplicaciones/es/aeat/ddii/enol/ws/DeclaracionInformativa.xsd" schemaLocation="DeclaracionInformativa.xsd"/>
	<element name="RespuestaBajaDI" type="ddiiRB:RespuestaBajaType"/>
	<annotation>
		<documentation>Servicio de baja de Declaraciones Informativas</documentation>
	</annotation>
	<complexType name="RespuestaComunBajaType">
		<sequence>
			<element name="CSV" type="string" minOccurs="0">
				<annotation>
					<documentation xml:lang="es"> CSV asociado al envío generado por AEAT. Solo se genera si no hay rechazo del envio</documentation>
				</annotation>
			</element>
			<element name="DatosPresentacion" type="ddii:DatosPresentacionType" minOccurs="0">
				<annotation>
					<documentation xml:lang="es"> Se devuelven datos de la presentacion realizada. Solo se genera si no hay rechazo del envio </documentation>
				</annotation>
			</element>
			<element name="Cabecera" type="ddii:CabeceraConsultaDI">
				<annotation>
					<documentation xml:lang="es"> Se devuelve la cabecera que se incluyó en el envío. </documentation>
				</annotation>
			</element>
			<element name="Declarado" type="ddii:DeclaradoType2">
				<annotation>
					<documentation xml:lang="es"> Se devuelve el IdRegistroDeclarado que se incluyó en el envío. </documentation>
				</annotation>
			</element>
			<element name="EstadoEnvio" type="ddiiRB:EstadoEnvioType">
				<annotation>
					<documentation xml:lang="es"> 
						Estado del envío en conjunto. 
						Si los datos de cabecera y todos los registros son correctos,el estado es correcto. 
						En caso de estructura y cabecera correctos donde todos los registros son incorrectos, el estado es incorrecto
						En caso de estructura y cabecera correctos con al menos un registro incorrecto o aceptado con errores, el estado global es parcialmente correcto.						
					</documentation>
				</annotation>
			</element>
		</sequence>
	</complexType>
	<complexType name="RespuestaBajaType">
		<annotation>
			<documentation xml:lang="es"> Respuesta a un envío Ddii </documentation>
		</annotation>
		<complexContent>
			<extension base="ddiiRB:RespuestaComunBajaType">
				<sequence>
					<element name="RespuestaLinea" type="ddiiRB:RespuestaOperacionesType" minOccurs="0">
						<annotation>
							<documentation xml:lang="es"> 
							Estado detallado de cada línea del suministro.
						</documentation>
						</annotation>
					</element>
				</sequence>
			</extension>
		</complexContent>
	</complexType>
	<complexType name="RespuestaOperacionesType">
		<annotation>
			<documentation xml:lang="es"> Respuesta a un envío Ddii </documentation>
		</annotation>
		<sequence>
			<element name="IDRegistroDeclarado" type="ddii:TextMax20Type"/>
			<element name="EstadoRegistro" type="ddiiRB:EstadoRegistroType">
				<annotation>
					<documentation xml:lang="es"> 
						Estado del registro. Correcto, erróneo
					</documentation>
				</annotation>
			</element>
			<element name="CodigoErrorRegistro" type="ddiiRB:ErrorDetalleType" minOccurs="0">
				<annotation>
					<documentation xml:lang="es"> 
						Código del error de registro, en su caso.
					</documentation>
				</annotation>
			</element>
			<element name="DescripcionErrorRegistro" type="ddii:TextMax500Type" minOccurs="0">
				<annotation>
					<documentation xml:lang="es"> 
						Descripción detallada del error de registro, en su caso.
					</documentation>
				</annotation>
			</element>
		</sequence>
	</complexType>
	<simpleType name="EstadoEnvioType">
		<restriction base="string">
			<enumeration value="Aceptacion Completa">
				<annotation>
					<documentation xml:lang="es">Aceptacion Completa</documentation>
				</annotation>
			</enumeration>
			<enumeration value="Aceptacion Parcial">
				<annotation>
					<documentation xml:lang="es">Aceptacion Parcial</documentation>
				</annotation>
			</enumeration>
			<enumeration value="Rechazo Completo">
				<annotation>
					<documentation xml:lang="es">Rechazo Completo</documentation>
				</annotation>
			</enumeration>
		</restriction>
	</simpleType>
	<simpleType name="EstadoRegistroType">
		<restriction base="string">
			<enumeration value="Aceptado">
				<annotation>
					<documentation xml:lang="es">Aceptado</documentation>
				</annotation>
			</enumeration>
			<enumeration value="Rechazado">
				<annotation>
					<documentation xml:lang="es">Rechazado</documentation>
				</annotation>
			</enumeration>
		</restriction>
	</simpleType>
	<simpleType name="ErrorEnvioType">
		<restriction base="string">
			<enumeration value="ERR01">
				<annotation>
					<documentation xml:lang="es">Error de validación contra esquema</documentation>
				</annotation>
			</enumeration>
			<enumeration value="ERR03">
				<annotation>
					<documentation xml:lang="es">Declarante desconocido</documentation>
				</annotation>
			</enumeration>
			<enumeration value="ERR04">
				<annotation>
					<documentation xml:lang="es">El declarante debe identificarse mediante un NIF español.</documentation>
				</annotation>
			</enumeration>
			<enumeration value="ERR05">
				<annotation>
					<documentation xml:lang="es">El NIF del representante es incorrecto.</documentation>
				</annotation>
			</enumeration>
		</restriction>
	</simpleType>
	<simpleType name="ErrorDetalleType">
		<restriction base="integer"/>
	</simpleType>
</schema>
