<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>API Reference</title>

    <link rel="stylesheet" href="css/style.css"/>
    <script src="js/all.js"></script>


    <script>
        $(function () {
            setupLanguages(["bash", "javascript", "python", "php"]);
        });
    </script>
</head>

<body class="">
<a href="#" id="nav-button">
      <span>
        NAV
        <img src="images/navbar.png"/>
      </span>
</a>
<div class="tocify-wrapper">
    <img src="images/logo.png"/>
    <div class="lang-selector">
        <a href="#" data-language-name="bash">bash</a>
        <a href="#" data-language-name="javascript">javascript</a>
        <a href="#" data-language-name="python">python</a>
        <a href="#" data-language-name="php">php</a>
    </div>
    <div class="search">
        <input type="text" class="search" id="input-search" placeholder="Search">
    </div>
    <ul class="search-results"></ul>
    <div id="toc">
    </div>
    <ul class="toc-footer">
        <li><a href='http://github.com/mpociot/documentarian'>Documentation Powered by Documentarian</a></li>
    </ul>
</div>
<div class="page-wrapper">
    <div class="dark-box"></div>
    <div class="content">
        <!-- START_INFO -->
        <h1>Info</h1>
        <p>Welcome to the generated API reference.</p>
        <!-- END_INFO -->
        <h1>User Management</h1>
        <!-- START_9d9bd3b1d5366fd9b3cf05167e87e228 -->
        <h2>register</h2>
        <p>Registers a user from lodgify into rental ninja.</p>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/register" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"user_id":16,"api_key":"aut","email":"et","company_name":"non","website_slug":"doloribus","trial_ends_at":"tempora","password":"iste"}'
</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/register"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "user_id": 16,
    "api_key": "aut",
    "email": "et",
    "company_name": "non",
    "website_slug": "doloribus",
    "trial_ends_at": "tempora",
    "password": "iste"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/register'
payload = {
    "user_id": 16,
    "api_key": "aut",
    "email": "et",
    "company_name": "non",
    "website_slug": "doloribus",
    "trial_ends_at": "tempora",
    "password": "iste"
}
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers, json=payload)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://pm.lodgifyintegration.com/lodgify/register',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'user_id' =&gt; 16,
            'api_key' =&gt; 'aut',
            'email' =&gt; 'et',
            'company_name' =&gt; 'non',
            'website_slug' =&gt; 'doloribus',
            'trial_ends_at' =&gt; 'tempora',
            'password' =&gt; 'iste',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "id": 1223,
    "access-token": "$2y$10$gmETmsptePtfEwWw15fwnuW1tiX4YYiSoOjO38f37y4YEdI0Xw.ru"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>POST lodgify/register</code></p>
        <h4>Body Parameters</h4>
        <table>
            <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Status</th>
                <th>Description</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><code>user_id</code></td>
                <td>integer</td>
                <td>required</td>
                <td>The user id from Lodgify.</td>
            </tr>
            <tr>
                <td><code>api_key</code></td>
                <td>string</td>
                <td>required</td>
                <td>The API Key to connect to Lodgify user</td>
            </tr>
            <tr>
                <td><code>email</code></td>
                <td>string</td>
                <td>required</td>
                <td>The email of the user. Will be used for login.</td>
            </tr>
            <tr>
                <td><code>company_name</code></td>
                <td>string</td>
                <td>optional</td>
                <td>The company name of the lodgify user if any. If not found we will generate one like:
                    Lodgify-{user_id}.
                </td>
            </tr>
            <tr>
                <td><code>website_slug</code></td>
                <td>string</td>
                <td>optional</td>
                <td>The company slug. If not found we will generate one with the company name.</td>
            </tr>
            <tr>
                <td><code>trial_ends_at</code></td>
                <td>date</td>
                <td>optional</td>
                <td>The trial ends at.</td>
            </tr>
            <tr>
                <td><code>password</code></td>
                <td>string</td>
                <td>optional</td>
                <td>An optional password for the created user. If not specified we will generate a random password.</td>
            </tr>
            <tr>
                <td><code>locale</code></td>
                <td>string</td>
                <td>optional</td>
                <td>The preferred locale of the new user (team). Allowed values are en, es, ca, fr, it for now.</td>
            </tr>
            </tbody>
        </table>
        <!-- END_9d9bd3b1d5366fd9b3cf05167e87e228 -->
        <!-- START_5b70df08c8aa8c90a08b81d26b818e81 -->
        <h2>account/{id}/refresh-token</h2>
        <p>Refreshes a token from an existing account. If the user isn&#039;t found, validation will fail.</p>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/account/1/refresh-token" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"user_id":1}'
</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/account/1/refresh-token"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "user_id": 1
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/account/1/refresh-token'
payload = {
    "user_id": 1
}
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers, json=payload)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://pm.lodgifyintegration.com/lodgify/account/1/refresh-token',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'user_id' =&gt; 1,
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "user_id": 1223,
    "access-token": "$2y$10$gmETmsptePtfEwWw15fwnuW1tiX4YYiSoOjO38f37y4YEdI0Xw.ru"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>POST lodgify/account/{id}/refresh-token</code></p>
        <h4>Body Parameters</h4>
        <table>
            <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Status</th>
                <th>Description</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><code>user_id</code></td>
                <td>integer</td>
                <td>required</td>
                <td>The user id from Lodgify.</td>
            </tr>
            </tbody>
        </table>
        <!-- END_5b70df08c8aa8c90a08b81d26b818e81 -->
        <!-- START_73dfe13fbac578cb8c07f681b0f6fc52 -->
        <h2>account/{id}</h2>
        <p>Returns current information about a Lodgify user inside rental ninja.</p>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X GET \
    -G "https://pm.lodgifyintegration.com/lodgify/account/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/account/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/account/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('GET', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://pm.lodgifyintegration.com/lodgify/account/1',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "data": {
        "id": 1,
        "api_key": "jc\/JGEe1GAirYMkBjxoCsJ5Xb4fzTBBbV7mvQbzvu6qm6IAdS9ONkD3+wY0lkZr4",
        "team_id": 1,
        "business_name": "Pol Test 2",
        "status": "enabled",
        "default_locale": "en",
        "email": "<EMAIL>",
        "default_arrival_time": 15,
        "default_departure_time": 10,
        "default_communication_locale": "en",
        "address1": null,
        "address2": null,
        "city": null,
        "zip": null,
        "state": null,
        "country_code": null,
        "website": null,
        "created_at": "2020-02-07 16:15:02",
        "updated_at": "2020-02-07 16:15:02",
        "canceled_at": null,
        "lodgify_user_id": 132516
    }
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>GET lodgify/account/{id}</code></p>
        <!-- END_73dfe13fbac578cb8c07f681b0f6fc52 -->
        <!-- START_1038786d3ad88e0ee1c20a094bb2864a -->
        <h2>account/{id}</h2>
        <p>Removes completely all account related information from our databases</p>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X DELETE \
    "https://pm.lodgifyintegration.com/lodgify/account/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"user_id":16}'
</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/account/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "user_id": 16
}

fetch(url, {
    method: "DELETE",
    headers: headers,
    body: body
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/account/1'
payload = {
    "user_id": 16
}
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('DELETE', url, headers=headers, json=payload)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;delete(
    'https://pm.lodgifyintegration.com/lodgify/account/1',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'user_id' =&gt; 16,
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>DELETE lodgify/account/{id}</code></p>
        <h4>Body Parameters</h4>
        <table>
            <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Status</th>
                <th>Description</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><code>user_id</code></td>
                <td>integer</td>
                <td>required</td>
                <td>The user id from Lodgify</td>
            </tr>
            </tbody>
        </table>
        <!-- END_1038786d3ad88e0ee1c20a094bb2864a -->
        <!-- START_6f2ffaa0713c91a35b06a824320c1aee -->
        <h2>account/{id}</h2>
        <p>Updates a Lodgify Account</p>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X PUT \
    "https://pm.lodgifyintegration.com/lodgify/account/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"api_key":"ut","status":"itaque","company_name":"velit","email":"voluptates","user_name":"enim","trial_ends_at":"enim","force_sync":false}'
</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/account/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "api_key": "ut",
    "status": "itaque",
    "company_name": "velit",
    "email": "voluptates",
    "user_name": "enim",
    "trial_ends_at": "enim",
    "force_sync": false
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/account/1'
payload = {
    "api_key": "ut",
    "status": "itaque",
    "company_name": "velit",
    "email": "voluptates",
    "user_name": "enim",
    "trial_ends_at": "enim",
    "force_sync": false
}
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('PUT', url, headers=headers, json=payload)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;put(
    'https://pm.lodgifyintegration.com/lodgify/account/1',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
        'json' =&gt; [
            'api_key' =&gt; 'ut',
            'status' =&gt; 'itaque',
            'company_name' =&gt; 'velit',
            'email' =&gt; 'voluptates',
            'user_name' =&gt; 'enim',
            'trial_ends_at' =&gt; 'enim',
            'force_sync' =&gt; false,
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <h3>HTTP Request</h3>
        <p><code>PUT lodgify/account/{id}</code></p>
        <h4>Body Parameters</h4>
        <table>
            <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Status</th>
                <th>Description</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><code>api_key</code></td>
                <td>string</td>
                <td>required</td>
                <td>The API Key to connect to the Lodgify User</td>
            </tr>
            <tr>
                <td><code>status</code></td>
                <td>string</td>
                <td>optional</td>
                <td>The status of the account. If enabled, the company will be considered subscribed.</td>
            </tr>
            <tr>
                <td><code>company_name</code></td>
                <td>string</td>
                <td>optional</td>
                <td>The company name of the lodgify user if any. If not found we will not update it.</td>
            </tr>
            <tr>
                <td><code>email</code></td>
                <td>string</td>
                <td>optional</td>
                <td>The email of the account and the user that is owner of it. It must be unique for the users.</td>
            </tr>
            <tr>
                <td><code>user_name</code></td>
                <td>string</td>
                <td>optional</td>
                <td>The name of the user.</td>
            </tr>
            <tr>
                <td><code>trial_ends_at</code></td>
                <td>date</td>
                <td>optional</td>
                <td>The date the trial ends at. If null, the trial will be expired. If not specified, it won't be
                    modified.
                </td>
            </tr>
            <tr>
                <td><code>force_sync</code></td>
                <td>boolean</td>
                <td>optional</td>
                <td>If set to &quot;true&quot;. We will issue a force sync.</td>
            </tr>
            <tr>
                <td><code>locale</code></td>
                <td>string</td>
                <td>optional</td>
                <td>The preferred locale of the new user (team). Allowed values are en, es, ca, fr, it for now.</td>
            </tr>
            </tbody>
        </table>
        <!-- END_6f2ffaa0713c91a35b06a824320c1aee -->
        <!-- START_e8e9bf85d3a13bd7a294d59d3c905610 -->
        <h2>accounts/active</h2>
        <p>Returns a paginated list of all ACTIVE accounts of lodgify inside rental ninja</p>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X GET \
    -G "https://pm.lodgifyintegration.com/lodgify/accounts/active" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/accounts/active"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/accounts/active'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('GET', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://pm.lodgifyintegration.com/lodgify/accounts/active',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "data": [
        {
            "id": 1,
            "api_key": "jc\/JGEe1GAirYMkBjxoCsJ5Xb4fzTBBbV7mvQbzvu6qm6IAdS9ONkD3+wY0lkZr4",
            "team_id": 1,
            "business_name": "Pol Test 2",
            "status": "enabled",
            "default_locale": "en",
            "email": "<EMAIL>",
            "default_arrival_time": 15,
            "default_departure_time": 10,
            "default_communication_locale": "en",
            "address1": null,
            "address2": null,
            "city": null,
            "zip": null,
            "state": null,
            "country_code": null,
            "website": null,
            "created_at": "2020-02-07 16:15:02",
            "updated_at": "2020-02-07 16:15:02",
            "canceled_at": null,
            "lodgify_user_id": 132516
        }
    ],
    "links": {
        "first": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "last": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "prev": null,
        "next": null
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 1,
        "path": "http:\/\/ninja.local\/lodgify\/accounts",
        "per_page": 15,
        "to": 1,
        "total": 1
    }
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>GET lodgify/accounts/active</code></p>
        <!-- END_e8e9bf85d3a13bd7a294d59d3c905610 -->
        <!-- START_63a9ecd708e2997a3d977df18fcec0ba -->
        <h2>accounts/not-active</h2>
        <p>Returns a paginated list of all accounts of lodgify inside rental ninja</p>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X GET \
    -G "https://pm.lodgifyintegration.com/lodgify/accounts/not-active" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/accounts/not-active"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/accounts/not-active'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('GET', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://pm.lodgifyintegration.com/lodgify/accounts/not-active',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "data": [
        {
            "id": 1,
            "api_key": "jc\/JGEe1GAirYMkBjxoCsJ5Xb4fzTBBbV7mvQbzvu6qm6IAdS9ONkD3+wY0lkZr4",
            "team_id": 1,
            "business_name": "Pol Test 2",
            "status": "enabled",
            "default_locale": "en",
            "email": "<EMAIL>",
            "default_arrival_time": 15,
            "default_departure_time": 10,
            "default_communication_locale": "en",
            "address1": null,
            "address2": null,
            "city": null,
            "zip": null,
            "state": null,
            "country_code": null,
            "website": null,
            "created_at": "2020-02-07 16:15:02",
            "updated_at": "2020-02-07 16:15:02",
            "canceled_at": null,
            "lodgify_user_id": 132516
        }
    ],
    "links": {
        "first": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "last": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "prev": null,
        "next": null
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 1,
        "path": "http:\/\/ninja.local\/lodgify\/accounts",
        "per_page": 15,
        "to": 1,
        "total": 1
    }
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>GET lodgify/accounts/not-active</code></p>
        <!-- END_63a9ecd708e2997a3d977df18fcec0ba -->
        <!-- START_80035d8cd6f0eb52e2c7271c72ed7ce1 -->
        <h2>accounts</h2>
        <p>Returns a paginated list of all accounts of lodgify inside rental ninja</p>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X GET \
    -G "https://pm.lodgifyintegration.com/lodgify/accounts" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/accounts"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/accounts'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('GET', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;get(
    'https://pm.lodgifyintegration.com/lodgify/accounts',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "data": [
        {
            "id": 1,
            "api_key": "jc\/JGEe1GAirYMkBjxoCsJ5Xb4fzTBBbV7mvQbzvu6qm6IAdS9ONkD3+wY0lkZr4",
            "team_id": 1,
            "business_name": "Pol Test 2",
            "status": "enabled",
            "default_locale": "en",
            "email": "<EMAIL>",
            "default_arrival_time": 15,
            "default_departure_time": 10,
            "default_communication_locale": "en",
            "address1": null,
            "address2": null,
            "city": null,
            "zip": null,
            "state": null,
            "country_code": null,
            "website": null,
            "created_at": "2020-02-07 16:15:02",
            "updated_at": "2020-02-07 16:15:02",
            "canceled_at": null,
            "lodgify_user_id": 132516
        }
    ],
    "links": {
        "first": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "last": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "prev": null,
        "next": null
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 1,
        "path": "http:\/\/ninja.local\/lodgify\/accounts",
        "per_page": 15,
        "to": 1,
        "total": 1
    }
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>GET lodgify/accounts</code></p>
        <!-- END_80035d8cd6f0eb52e2c7271c72ed7ce1 -->
        <h1>Web Hooks</h1>
        <!-- START_18efb4f1451a53b27836804a158519c7 -->
        <h2>Triggers the sync of a new Property</h2>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/property" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://pm.lodgifyintegration.com/lodgify/property',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>POST lodgify/property</code></p>
        <!-- END_18efb4f1451a53b27836804a158519c7 -->
        <!-- START_06e26461be78ba83fc384e8dcf802021 -->
        <h2>Triggers the sync of a an updated property</h2>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X PUT \
    "https://pm.lodgifyintegration.com/lodgify/property/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "PUT",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('PUT', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;put(
    'https://pm.lodgifyintegration.com/lodgify/property/1',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>PUT lodgify/property/{id}</code></p>
        <!-- END_06e26461be78ba83fc384e8dcf802021 -->
        <!-- START_9cfd323653d334435576c1482021e654 -->
        <h2>Triggers the sync of a deleted Property</h2>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X DELETE \
    "https://pm.lodgifyintegration.com/lodgify/property/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('DELETE', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;delete(
    'https://pm.lodgifyintegration.com/lodgify/property/1',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>DELETE lodgify/property/{id}</code></p>
        <!-- END_9cfd323653d334435576c1482021e654 -->
        <!-- START_f97f1c6cad5e8078f22fe0552de4f606 -->
        <h2>Triggers the sync of a new Room</h2>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/property/1/room" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1/room"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1/room'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://pm.lodgifyintegration.com/lodgify/property/1/room',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>POST lodgify/property/{id}/room</code></p>
        <!-- END_f97f1c6cad5e8078f22fe0552de4f606 -->
        <!-- START_52ef579bb00617d962f7eeb1c8c732c9 -->
        <h2>Triggers the sync of an updated Room</h2>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X PUT \
    "https://pm.lodgifyintegration.com/lodgify/property/1/room/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1/room/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "PUT",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1/room/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('PUT', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;put(
    'https://pm.lodgifyintegration.com/lodgify/property/1/room/1',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>PUT lodgify/property/{id}/room/{room_id}</code></p>
        <!-- END_52ef579bb00617d962f7eeb1c8c732c9 -->
        <!-- START_ba32f676ac6096763190274472d1744a -->
        <h2>Triggers the sync of a deleted Room</h2>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X DELETE \
    "https://pm.lodgifyintegration.com/lodgify/property/1/room/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1/room/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1/room/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('DELETE', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;delete(
    'https://pm.lodgifyintegration.com/lodgify/property/1/room/1',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>DELETE lodgify/property/{id}/room/{room_id}</code></p>
        <!-- END_ba32f676ac6096763190274472d1744a -->
        <!-- START_b2e9ac592cc94d35116d9d760a634511 -->
        <h2>Triggers the sync of a new Booking</h2>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/booking" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/booking"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/booking'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;post(
    'https://pm.lodgifyintegration.com/lodgify/booking',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>POST lodgify/booking</code></p>
        <!-- END_b2e9ac592cc94d35116d9d760a634511 -->
        <!-- START_2fcd2f8e383b9aca1b25cf7621c072d6 -->
        <h2>Triggers the sync of an updated Booking</h2>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X PUT \
    "https://pm.lodgifyintegration.com/lodgify/booking/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/booking/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "PUT",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/booking/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('PUT', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;put(
    'https://pm.lodgifyintegration.com/lodgify/booking/1',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>PUT lodgify/booking/{id}</code></p>
        <!-- END_2fcd2f8e383b9aca1b25cf7621c072d6 -->
        <!-- START_629e38288f12c0c9441b3cd3c16797c6 -->
        <h2>Triggers the sync of a Deleted / Cancelled booking</h2>
        <blockquote>
            <p>Example request:</p>
        </blockquote>
        <pre><code class="language-bash">curl -X DELETE \
    "https://pm.lodgifyintegration.com/lodgify/booking/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
        <pre><code class="language-javascript">const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/booking/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
        <pre><code class="language-python">import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/booking/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('DELETE', url, headers=headers)
response.json()</code></pre>
        <pre><code class="language-php">
$client = new \GuzzleHttp\Client();
$response = $client-&gt;delete(
    'https://pm.lodgifyintegration.com/lodgify/booking/1',
    [
        'headers' =&gt; [
            'Content-Type' =&gt; 'application/json',
            'Accept' =&gt; 'application/json',
        ],
    ]
);
$body = $response-&gt;getBody();
print_r(json_decode((string) $body));</code></pre>
        <blockquote>
            <p>Example response (200):</p>
        </blockquote>
        <pre><code class="language-json">{
    "success": "OK"
}</code></pre>
        <h3>HTTP Request</h3>
        <p><code>DELETE lodgify/booking/{id}</code></p>
        <!-- END_629e38288f12c0c9441b3cd3c16797c6 -->
    </div>
    <div class="dark-box">
        <div class="lang-selector">
            <a href="#" data-language-name="bash">bash</a>
            <a href="#" data-language-name="javascript">javascript</a>
            <a href="#" data-language-name="python">python</a>
            <a href="#" data-language-name="php">php</a>
        </div>
    </div>
</div>
</body>
</html>