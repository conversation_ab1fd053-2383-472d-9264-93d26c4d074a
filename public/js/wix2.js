"use strict";
console.info("Wix2.js loaded");
// we load the team id from the query params
const paramsLoaded = document.currentScript.src.split("?")[1].split("&");

const query = {};
paramsLoaded.forEach((e) => {
    const [key, value] = e.split("=");
    query[key] = value;
});

window.addEventListener("message", function (event) {
    let data = event.data;
    console.log("Called From LiveWire Event Type: ", data.type);
    if (data.type === "updateUrl") {
        if (data.url == window.parent.location.hash) {
            console.log("Same url");
            return;
        }
        // update query params
        console.log("From real url: " + data.url);
        // replace hash with query params
        window.parent.location.hash = data.url;
    }
    if (data.type == "updateHeight") {
        const iframe = window.document.getElementById("rental-ninja");
        const iframeParent = iframe.parentElement;
        console.log("Height: ", data.height);
        const height = data.height > 10000 ? "100vh;" : data.height + "px;";
        iframeParent.style =
            "height: " + height + "width: 100%; position:relative; z-index:1";
    }
});
const iframeRN = window.document.getElementById("iframe");
const iframe = window.document.createElement("iframe");

// Set attributes for the iframe
iframe.style =
    "height: 100%; width: 100%; position:relative; z-index:1; overflow: scroll;";
iframe.src = "https://dario-rn.eu.ngrok.io/web/" + query.t + "?external=true";
iframe.title = "Rental Ninja Embedded Content";
iframe.frameborder = "0";
iframe.id = "rental-ninja";
iframe.setAttribute("crossorigin", "anonymous");
iframeRN.appendChild(iframe);
iframe.onload = function () {
    const hash = window.location.hash;
    if (hash) {
        iframe.contentWindow.postMessage(
            { type: "updateQuery", hash: hash },
            "*"
        );
    }
};

window.navigation.addEventListener("navigate", (event) => {
    const hash = window.location.hash.split("#")[1];
    console.log({ event });
    const destinationUrl = event?.destination?.url;
    const destionationHash = destinationUrl.split("#")[1] ?? "";
    console.log({ destionationHash });
    console.log({ hash });
    if (hash != destionationHash) {
        console.log("Updating hash");
        console.log({ destionationHash });
        iframe.contentWindow.postMessage(
            { type: "updateQuery", hash: "#" + destionationHash },
            "*"
        );
    }
});
