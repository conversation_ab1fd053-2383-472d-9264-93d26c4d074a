(self["webpackChunk"] = self["webpackChunk"] || []).push([["/js/app"],{

/***/ "./resources/assets/js/app.js":
/*!************************************!*\
  !*** ./resources/assets/js/app.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

/* provided dependency */ var $ = __webpack_require__(/*! jquery */ "./node_modules/jquery/dist/jquery.js");
/*
 |--------------------------------------------------------------------------
 | Laravel Spark Bootstrap
 |--------------------------------------------------------------------------
 |
 | First, we will load all of the "core" dependencies for Spark which are
 | libraries such as Vue and jQuery. This also loads the Spark helpers
 | for things such as HTTP calls, forms, and form validation errors.
 |
 | Next, we'll create the root Vue application for Spark. This will start
 | the entire application and attach it to the DOM. Of course, you may
 | customize this script as you desire and load your own components.
 |
 */

__webpack_require__(Object(function webpackMissingModule() { var e = new Error("Cannot find module 'spark-bootstrap'"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));
__webpack_require__(/*! ./components/bootstrap */ "./resources/assets/js/components/bootstrap.js");
__webpack_require__(/*! ./components/auth/lodgify-registration */ "./resources/assets/js/components/auth/lodgify-registration.js");
window.Vapor = __webpack_require__(/*! laravel-vapor */ "./node_modules/laravel-vapor/dist/laravel-vapor.js");
// Initialize the application.
var app = new Vue({
  mixins: [__webpack_require__(Object(function webpackMissingModule() { var e = new Error("Cannot find module 'spark'"); e.code = 'MODULE_NOT_FOUND'; throw e; }()))]
});
Vue.config.devtools = false;
Vue.config.debug = false;
Vue.config.silent = true;
Vue.config.productionTip = false;
function inIframe() {
  try {
    return window.self !== window.top;
  } catch (e) {
    return true;
  }
}
$(function () {
  // Make sure the register button works as expected.
  $('.register-button').on("click", function (event) {
    event.preventDefault();
    var target = $(this).attr("href");
    inIframe() === true ? window.top.location.href = target : window.location = target;
  });
});

/***/ }),

/***/ "./resources/assets/js/components/auth/lodgify-registration.js":
/*!*********************************************************************!*\
  !*** ./resources/assets/js/components/auth/lodgify-registration.js ***!
  \*********************************************************************/
/***/ (() => {

Vue.component('lodgify-registration', {
  data: function data() {
    return {
      started: false,
      form: new SparkForm({
        api_key: '',
        name: '',
        email: '',
        team: '',
        team_slug: '',
        terms: false,
        password: '',
        password_confirmation: ''
      })
    };
  },
  watch: {
    /**
     * Watch the team name for changes.
     */
    'form.team': function formTeam(val, oldVal) {
      if (this.form.team_slug === '' || this.form.team_slug === oldVal.toLowerCase().replace(/[\s\W-]+/g, '-')) {
        this.form.team_slug = val.toLowerCase().replace(/[\s\W-]+/g, '-');
      }
    }
  },
  methods: {
    /**
     * Fetches the data from Lodgify of an account.
     */
    getAccount: function getAccount() {
      var self = this;
      this.started = false;
      Spark.post('/register-lf/get-account', this.form).then(function (response) {
        console.log(response);
        // OK
        if (response === 1) {
          self.started = true;
          self.busy = false;
        }
        // TEAM ALREADY EXISTS.
        else if (response === 2) {
          // TODO:
        }
        // API KEY NOT VALID.
        else if (response === 3) {
          // TODO
        }
      })["catch"](function (response) {
        console.log("Error!", response);
      });
      window.form = this.form;
    },
    /**
     * Do the actual registration...
     */
    register: function register() {
      Spark.post('/register-lf', this.form).then(function (response) {
        window.location = response.redirect;
      });
    }
  }
});

/***/ }),

/***/ "./resources/assets/js/components/bootstrap.js":
/*!*****************************************************!*\
  !*** ./resources/assets/js/components/bootstrap.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

/*
 |--------------------------------------------------------------------------
 | Laravel Spark Components
 |--------------------------------------------------------------------------
 |
 | Here we will load the Spark components which makes up the core client
 | application. This is also a convenient spot for you to load all of
 | your components that you write while building your applications.
 */

__webpack_require__(Object(function webpackMissingModule() { var e = new Error("Cannot find module './../spark-components/bootstrap'"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));

/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allows your team to easily build robust real-time web applications.
 */

Window.ninjaCurrencyFormat = function (value) {
  var currency = parseFloat(value);
  if (isNaN(currency)) {
    return value;
  }
  return currency.toLocaleString("en-US", {
    style: "currency",
    currency: this.currencyName,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};
Window.ninjaFormatNights = function (value) {
  var nights = parseInt(value);
  if (isNaN(nights)) {
    return value;
  }
  return nights;
};
window.unnamedChartColors = ["rgb(255, 99, 132)", "rgb(255, 159, 64)", "rgb(255, 205, 86)", "rgb(75, 192, 192)", "rgb(54, 162, 235)", "rgb(153, 102, 255)", "rgb(201, 203, 207)", "rgb(60, 180, 75)", "rgb(230, 25, 75)", "rgb(255, 225, 25)", "rgb(70, 240, 240)", "rgb(240, 50, 230)", "rgb(210, 245, 60)", "rgb(170, 110, 40)", "rgb(128, 0, 0)", "rgb(128, 128, 0)", "rgb(0, 0, 128)"];
window.chartColors = {
  red: "rgb(255, 99, 132)",
  orange: "rgb(255, 159, 64)",
  yellow: "rgb(255, 205, 86)",
  green: "rgb(75, 192, 192)",
  blue: "rgb(54, 162, 235)",
  purple: "rgb(153, 102, 255)",
  grey: "rgb(201, 203, 207)",
  darkGreen: "rgb(60, 180, 75)",
  fuchsia: "rgb(230, 25, 75)",
  strongYellow: "rgb(255, 225, 25)",
  cyan: "rgb(70, 240, 240)",
  magenta: "rgb(240, 50, 230)",
  lime: "rgb(210, 245, 60)",
  brown: "rgb(170, 110, 40)",
  maroon: "rgb(128, 0, 0)",
  olive: "rgb(128, 128, 0)",
  navy: "rgb(0, 0, 128)"
};
window.fadedColors = {
  red: "rgba(255, 99, 132, 0.7)",
  orange: "rgba(255, 159, 64, 0.7)",
  yellow: "rgba(255, 205, 86, 0.7)",
  green: "rgba(75, 192, 192, 0.7)",
  blue: "rgba(54, 162, 235, 0.7)",
  purple: "rgba(153, 102, 255, 0.7)",
  grey: "rgba(201, 203, 207, 0.7)",
  darkGreen: "rgba(60, 180, 75, 0.7)",
  fuchsia: "rgba(230, 25, 75, 0.7)",
  strongYellow: "rgba(255, 225, 25, 0.7)",
  cyan: "rgba(70, 240, 240, 0.7)",
  magenta: "rgba(240, 50, 230, 0.7)",
  lime: "rgba(210, 245, 60, 0.7)",
  brown: "rgba(170, 110, 40, 0.7)",
  maroon: "rgba(128, 0, 0, 0.7)",
  olive: "rgba(128, 128, 0, 0.7)",
  navy: "rgba(0, 0, 128,0.7)"
};
window.accentColors = {
  red: "rgb(255, 26, 75)",
  orange: "rgb(255, 128, 0)",
  yellow: "rgb(255, 179, 0)",
  green: "rgb(53, 151, 151)",
  blue: "	rgb(18, 119, 186)",
  purple: "rgb(102, 26, 255)",
  grey: "rgb(160, 164, 171)",
  darkGreen: "	rgb(45, 134, 57)",
  fuchsia: "	rgb(184, 20, 61)",
  strongYellow: "rgb(204, 177, 0)",
  cyan: "rgb(17, 212, 212)",
  magenta: "rgb(190, 14, 181)",
  lime: "rgb(180, 218, 11)",
  brown: "rgb(124, 80, 29)",
  maroon: "	rgb(77, 0, 0)",
  olive: "rgb(77, 77, 0)",
  navy: "rgb(0, 0, 77)"
};
window.dateRange = {
  RANGE_CURRENT_WEEK: 'current_week',
  RANGE_CURRENT_MONTH: 'current_month',
  RANGE_CURRENT_YEAR: 'current_year',
  RANGE_CURRENT_QUARTER: 'current_trimester',
  RANGE_LAST_7_DAYS: 'last_7_days',
  RANGE_LAST_WEEK: 'last_week',
  RANGE_LAST_30_DAYS: 'last_30_days',
  RANGE_LAST_MONTH: 'last_month',
  RANGE_LAST_60_DAYS: 'last_60_days',
  RANGE_LAST_90_DAYS: 'last_90_days',
  RANGE_LAST_180_DAYS: 'last_180_days',
  RANGE_LAST_365_DAYS: 'last_365_days',
  RANGE_LAST_YEAR: 'last_year',
  RANGE_TWO_YEARS_AGO: 'two_years_ago'
};
Vue.prototype.$http = axios;

/***/ }),

/***/ "./resources/assets/sass/app.scss":
/*!****************************************!*\
  !*** ./resources/assets/sass/app.scss ***!
  \****************************************/
/***/ (() => {

throw new Error("Module build failed (from ./node_modules/mini-css-extract-plugin/dist/loader.js):\nModuleBuildError: Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\nSassError: Can't find stylesheet to import.\n   ╷\n59 │ @import 'spark/spark';\n   │         ^^^^^^^^^^^^^\n   ╵\n  resources/assets/sass/app.scss 59:9  root stylesheet\n    at processResult (/Users/<USER>/MJ/ninja/node_modules/webpack/lib/NormalModule.js:764:19)\n    at /Users/<USER>/MJ/ninja/node_modules/webpack/lib/NormalModule.js:866:5\n    at /Users/<USER>/MJ/ninja/node_modules/loader-runner/lib/LoaderRunner.js:400:11\n    at /Users/<USER>/MJ/ninja/node_modules/loader-runner/lib/LoaderRunner.js:252:18\n    at context.callback (/Users/<USER>/MJ/ninja/node_modules/loader-runner/lib/LoaderRunner.js:124:13)\n    at Object.loader (/Users/<USER>/MJ/ninja/node_modules/sass-loader/dist/index.js:69:5)");

/***/ }),

/***/ "./resources/assets/sass/app_lodgify.scss":
/*!************************************************!*\
  !*** ./resources/assets/sass/app_lodgify.scss ***!
  \************************************************/
/***/ (() => {

throw new Error("Module build failed (from ./node_modules/mini-css-extract-plugin/dist/loader.js):\nModuleBuildError: Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\nSassError: Can't find stylesheet to import.\n   ╷\n63 │ @import 'spark/spark_lodgify';\n   │         ^^^^^^^^^^^^^^^^^^^^^\n   ╵\n  resources/assets/sass/app_lodgify.scss 63:9  root stylesheet\n    at processResult (/Users/<USER>/MJ/ninja/node_modules/webpack/lib/NormalModule.js:764:19)\n    at /Users/<USER>/MJ/ninja/node_modules/webpack/lib/NormalModule.js:866:5\n    at /Users/<USER>/MJ/ninja/node_modules/loader-runner/lib/LoaderRunner.js:400:11\n    at /Users/<USER>/MJ/ninja/node_modules/loader-runner/lib/LoaderRunner.js:252:18\n    at context.callback (/Users/<USER>/MJ/ninja/node_modules/loader-runner/lib/LoaderRunner.js:124:13)\n    at Object.loader (/Users/<USER>/MJ/ninja/node_modules/sass-loader/dist/index.js:69:5)");

/***/ }),

/***/ "./resources/assets/css/extra.css":
/*!****************************************!*\
  !*** ./resources/assets/css/extra.css ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
// extracted by mini-css-extract-plugin


/***/ }),

/***/ "./node_modules/process/browser.js":
/*!*****************************************!*\
  !*** ./node_modules/process/browser.js ***!
  \*****************************************/
/***/ ((module) => {

// shim for using process in browser
var process = module.exports = {};

// cached from whatever global is present so that test runners that stub it
// don't break things.  But we need to wrap it in a try catch in case it is
// wrapped in strict mode code which doesn't define any globals.  It's inside a
// function because try/catches deoptimize in certain engines.

var cachedSetTimeout;
var cachedClearTimeout;

function defaultSetTimout() {
    throw new Error('setTimeout has not been defined');
}
function defaultClearTimeout () {
    throw new Error('clearTimeout has not been defined');
}
(function () {
    try {
        if (typeof setTimeout === 'function') {
            cachedSetTimeout = setTimeout;
        } else {
            cachedSetTimeout = defaultSetTimout;
        }
    } catch (e) {
        cachedSetTimeout = defaultSetTimout;
    }
    try {
        if (typeof clearTimeout === 'function') {
            cachedClearTimeout = clearTimeout;
        } else {
            cachedClearTimeout = defaultClearTimeout;
        }
    } catch (e) {
        cachedClearTimeout = defaultClearTimeout;
    }
} ())
function runTimeout(fun) {
    if (cachedSetTimeout === setTimeout) {
        //normal enviroments in sane situations
        return setTimeout(fun, 0);
    }
    // if setTimeout wasn't available but was latter defined
    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {
        cachedSetTimeout = setTimeout;
        return setTimeout(fun, 0);
    }
    try {
        // when when somebody has screwed with setTimeout but no I.E. maddness
        return cachedSetTimeout(fun, 0);
    } catch(e){
        try {
            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally
            return cachedSetTimeout.call(null, fun, 0);
        } catch(e){
            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error
            return cachedSetTimeout.call(this, fun, 0);
        }
    }


}
function runClearTimeout(marker) {
    if (cachedClearTimeout === clearTimeout) {
        //normal enviroments in sane situations
        return clearTimeout(marker);
    }
    // if clearTimeout wasn't available but was latter defined
    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {
        cachedClearTimeout = clearTimeout;
        return clearTimeout(marker);
    }
    try {
        // when when somebody has screwed with setTimeout but no I.E. maddness
        return cachedClearTimeout(marker);
    } catch (e){
        try {
            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally
            return cachedClearTimeout.call(null, marker);
        } catch (e){
            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.
            // Some versions of I.E. have different rules for clearTimeout vs setTimeout
            return cachedClearTimeout.call(this, marker);
        }
    }



}
var queue = [];
var draining = false;
var currentQueue;
var queueIndex = -1;

function cleanUpNextTick() {
    if (!draining || !currentQueue) {
        return;
    }
    draining = false;
    if (currentQueue.length) {
        queue = currentQueue.concat(queue);
    } else {
        queueIndex = -1;
    }
    if (queue.length) {
        drainQueue();
    }
}

function drainQueue() {
    if (draining) {
        return;
    }
    var timeout = runTimeout(cleanUpNextTick);
    draining = true;

    var len = queue.length;
    while(len) {
        currentQueue = queue;
        queue = [];
        while (++queueIndex < len) {
            if (currentQueue) {
                currentQueue[queueIndex].run();
            }
        }
        queueIndex = -1;
        len = queue.length;
    }
    currentQueue = null;
    draining = false;
    runClearTimeout(timeout);
}

process.nextTick = function (fun) {
    var args = new Array(arguments.length - 1);
    if (arguments.length > 1) {
        for (var i = 1; i < arguments.length; i++) {
            args[i - 1] = arguments[i];
        }
    }
    queue.push(new Item(fun, args));
    if (queue.length === 1 && !draining) {
        runTimeout(drainQueue);
    }
};

// v8 likes predictible objects
function Item(fun, array) {
    this.fun = fun;
    this.array = array;
}
Item.prototype.run = function () {
    this.fun.apply(null, this.array);
};
process.title = 'browser';
process.browser = true;
process.env = {};
process.argv = [];
process.version = ''; // empty string to avoid regexp issues
process.versions = {};

function noop() {}

process.on = noop;
process.addListener = noop;
process.once = noop;
process.off = noop;
process.removeListener = noop;
process.removeAllListeners = noop;
process.emit = noop;
process.prependListener = noop;
process.prependOnceListener = noop;

process.listeners = function (name) { return [] }

process.binding = function (name) {
    throw new Error('process.binding is not supported');
};

process.cwd = function () { return '/' };
process.chdir = function (dir) {
    throw new Error('process.chdir is not supported');
};
process.umask = function() { return 0; };


/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["css/extra","/js/vendor"], () => (__webpack_exec__("./resources/assets/js/app.js"), __webpack_exec__("./resources/assets/sass/app.scss"), __webpack_exec__("./resources/assets/sass/app_lodgify.scss"), __webpack_exec__("./resources/assets/css/extra.css")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);