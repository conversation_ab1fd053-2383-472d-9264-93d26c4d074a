
<?php

use App\DataProviders\ApiResolvers\RentalsUnited\Bookings\CMBookingsResolver;
use App\DataProviders\ApiResolvers\RentalsUnited\Bookings\LeadsResolver;
use App\DataProviders\ApiResolvers\RentalsUnited\GuestCommunications\CMMessagesResolver;
use App\DataProviders\ApiResolvers\RentalsUnited\GuestCommunications\CMThreadsResolver;
use App\DataProviders\ApiResolvers\RentalsUnited\Rentals\CMRentalResolver;
use App\DataProviders\ProviderConstants;

return [
    'username' => '<EMAIL>',
    'password' => env('CHANNEL_MANAGER_PASSWORD'),

    'api_url' => env('RENTALS_UNITED_API_URL', 'https://rm.rentalsunited.com/api/Handler.ashx'),
    'new_api_url' => env('RENTALS_UNITED_NEW_API_URL', 'https://api.rentalsunited.com/api'),

    'pms-api-key' => env('RENTALS_UNITED_PMS_API_KEY'),

    'backdoor_password' => env('RENTALS_UNITED_BACKDOOR_PW'),
    'tech_backdoor_password' => env('RENTALS_UNITED_TECH_BACKDOOR_PW'),

    // API Configuration
    'api_method' => 'RAW_POST',
    'http_timeout' => 120,
    'http_connect_timeout' => 30,
    'http_max_retries' => 3,
    'can_process_concurrent_pages' => false,
    'api_concurrency_limit' => 0,

    'endpoints_configuration' => [
        ProviderConstants::ENDPOINT_RENTALS => [
            'url' => 'Pull_ListOwnerProp_RQ',
            'url_single' => 'Pull_ListSpecProp_RQ',
            'resolver' => CMRentalResolver::class,
            'uses_new_api' => false,
        ],
        ProviderConstants::ENDPOINT_BOOKINGS => [
            'url' => 'Pull_ListReservationsSubUsers_RQ',
            'url_single' => 'Pull_GetReservationByID_RQ',
            'resolver' => CMBookingsResolver::class,
            'uses_new_api' => false,
        ],
        ProviderConstants::ENDPOINT_LEADS => [
            'url' => 'Pull_GetLeads_RQ',
            'resolver' => LeadsResolver::class,
            'uses_new_api' => false,
        ],

        ProviderConstants::ENDPOINT_THREADS => [
            'method' => 'GET',
            'uses_new_api' => true,
            'url' => '/messaging/threads',
            'extra_params' => [
                'parentType' => 'RUReservation',
            ],
            'resolver' => CMThreadsResolver::class,
        ],
        ProviderConstants::ENDPOINT_MESSAGES => [
            'method' => 'GET',
            'uses_new_api' => true,
            'url' => '/messaging/messages',
            'extra_params' => [
                'parentType' => 'RUReservation',
            ],
            'resolver' => CMMessagesResolver::class,
        ],
        'notifications_subscription' => [
            'uses_new_api' => true,
            'url' => '/messaging/notifications/subscribe',
        ],
    ],

    'sync' => [
        'complete' => [
            ProviderConstants::ENDPOINT_BOOKINGS,
            ProviderConstants::ENDPOINT_LEADS,
        ],
        'normal' => [
            ProviderConstants::ENDPOINT_BOOKINGS,
            ProviderConstants::ENDPOINT_LEADS,
        ],
    ],
];
