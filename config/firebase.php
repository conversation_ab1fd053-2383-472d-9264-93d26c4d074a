<?php

return [
    /*
     * Documentation:
     * https://github.com/kreait/laravel-firebase/blob/main/config/firebase.php
     */
    'default' => env('FIREBASE_PROJECT', 'rental-ninja'),
    'api_key' => env('FIREBASE_API_KEY'),
    'auth_domain' => env('FIREBASE_AUTH_DOMAIN'),
    'database_url' => env('FIREBASE_DATABASE_URL'),
    'storage_bucket' => env('FIREBASE_STORAGE_BUCKET'),
    'firebase_imgix_url' => env('FIREBASE_IMGIX_URL', 'https://rental-ninja-firebase.imgix.net/'),
    // WARNING: if you change imgix url, change the imgix_url_identifier in ninja conf too
    'maps_api_key' => env('FIREBASE_MAPS_API_KEY'),
    'measurement_id' => env('FIREBASE_MEASUREMENT_ID'),
    'app_id' => env('FIREBASE_APP_ID'),
    'messaging_sender_id' => env('FIREBASE_MESSAGING_SENDER_ID'),

    'projects' => [
        'rental-ninja' => [
            'credentials' => [
                'file' => base_path(env('FIREBASE_CREDENTIALS', 'rental-ninja-firebase-adminsdk-ts1y5-a35439d557.json')),
                'auto_discovery' => true,
            ],

            /*'auth' => [
                'tenant_id' => env('FIREBASE_AUTH_TENANT_ID'),
            ],*/

            'database' => [
                'url' => env('FIREBASE_DATABASE_URL'),
            ],

            'dynamic_links' => [
                'default_domain' => env('FIREBASE_DYNAMIC_LINKS_DEFAULT_DOMAIN', 'https://z9adr.app.goo.gl'),
            ],

            'storage' => [
                'default_bucket' => env('FIREBASE_STORAGE_DEFAULT_BUCKET'),

            ],

            'cache_store' => env('FIREBASE_CACHE_STORE', 'redis'),

            'logging' => [
                'http_log_channel' => env('FIREBASE_HTTP_LOG_CHANNEL', null),
                'http_debug_log_channel' => env('FIREBASE_HTTP_DEBUG_LOG_CHANNEL', null),
            ],

            'debug' => env('FIREBASE_ENABLE_DEBUG', false),

            'http_client_options' => [
                'proxy' => env('FIREBASE_HTTP_CLIENT_PROXY'),
                'timeout' => env('FIREBASE_HTTP_CLIENT_TIMEOUT', 30),
                'guzzle_middlewares' => [],
            ],
        ],
    ],
];
