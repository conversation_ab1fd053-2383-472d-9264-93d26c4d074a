<?php

return [
    'enabled' => env('POSTMAN_COLLECTIONS_ENABLED', false),
    'mask_data' => env('POSTMAN_MASK_DATA', false),
    // The Header values allowed to be kept from incoming requests.
    'allowed_header_values_from_request' => [
        'content-type',
    ],
    'ignored_prefixes' => [
        'nova',
        'nova-api',
        'telescope',
        'dev',
        'horizon',
    ],
    'prefixes_wanted_even_if_no_json_is_expected' => [
        'smoobu',
        'lodgify',
        'bookingsync',
        'rentals-united',
    ],
    'ignore_routes_starting_with' => [
        'spark',
        'vapor-ui',
        'tax-rate',
    ],
    'ignored_routes' => [
        '/',
        'accounting',
        'tasks',
        'guest-app',
        'vapor/signed-storage-url',
        'terms',
        'stripe/token',
    ],
    'enforced_routes' => [
        'webhook/booking-sync',
        'webhook/ninja-stripe',
        'register-lf/get-account',
    ],
    'variables_to_env_variables' => [
        'team' => '{{TEAM}}',
        'team_id' => '{{TEAM}}',
        'rentals' => '{{RENTALS}}',
    ],
    'collection_variables' => [
        [
            'key' => 'SERVER',
            'value' => env('POSTMAN_SERVER_URL', 'localhost'),
        ],
        [
            'key' => 'ACCESS_TOKEN',
            'value' => '{{ACCESS_TOKEN}}',
        ],
        [
            'key' => 'REFRESH_TOKEN',
            'value' => '{{REFRESH_TOKEN}}',
        ],
        [
            'key' => 'CLIENT_ID',
            'value' => env('PASSWORD_CLIENT_ID', '2'),
        ],
        [
            'key' => 'CLIENT_SECRET',
            'value' => '{{CLIENT_SECRET}}',
        ],
        [
            'key' => 'USERNAME',
            'value' => '{{USERNAME}}',
        ],
        [
            'key' => 'PASSWORD',
            'value' => '{{PASSWORD}}',
        ],
        [
            'key' => 'TEAM',
            'value' => '{{TEAM}}',
        ],
        [
            'key' => 'RENTALS',
            'value' => '{{RENTALS}}',
        ],
    ],
    'collection_auth' => [
        'type' => 'bearer',
        'bearer' => [
            'type' => 'bearer',
            'key' => 'Authorization',
            'value' => 'Bearer {{ACCESS_TOKEN}}',
        ],
    ],
];
