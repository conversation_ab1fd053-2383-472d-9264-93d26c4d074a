<?php

use App\DataProviders\Providers\BookingSync;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\DataProviders\Providers\Lodgify;
use App\DataProviders\Providers\NoProvider;
use App\DataProviders\Providers\RentalsUnited;
use App\DataProviders\Providers\RentalsUnitedPms;
use App\DataProviders\Providers\Smoobu;
use App\DataProviders\Providers\Tokeet;

return [
    'force_sync_percent_every_twenty_minutes' => env('NINJA_FORCE_SYNC_PERCENT_EVERY_TWENTY_MINUTES', 0.2),
    'active_providers' => [
        '0' => NoProvider::class,
        '1' => BookingSync::class,
        '2' => Lodgify::class,
        '3' => Smoobu::class,
        '4' => RentalsUnited::class,
        '5' => Tokeet::class,
        '6' => ChannelManagerProvider::class,
        '7' => RentalsUnitedPms::class,
    ],
    'num_rental_photos' => 3,
];
