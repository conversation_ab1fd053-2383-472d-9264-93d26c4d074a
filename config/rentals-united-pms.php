<?php

use App\DataProviders\ApiResolvers\RentalsUnited\Bookings\RUPmsBookingsResolver;
use App\DataProviders\ApiResolvers\RentalsUnited\GuestCommunications\RUPmsMessagesResolver;
use App\DataProviders\ApiResolvers\RentalsUnited\GuestCommunications\RUPmsThreadsResolver;
use App\DataProviders\ApiResolvers\RentalsUnited\Rentals\RUPmsRentalResolver;
use App\DataProviders\ProviderConstants;

return [
    'enabled' => env('RENTALS_UNITED_PMS_ENABLED', true),
    'username' => env('RENTALS_UNITED_PMS_USERNAME', '<EMAIL>'),
    'password' => env('RENTALS_UNITED_PMS_PASSWORD', 'undefined'),
    'api_url' => env('RENTALS_UNITED_API_URL', 'https://rm.rentalsunited.com/api/Handler.ashx'),
    'new_api_url' => env('RENTALS_UNITED_NEW_API_URL', 'https://api.rentalsunited.com/api'),
    'enable-signup' => false,

    'max_rental_photos' => 3,

    // API Configuration
    'api_method' => 'RAW_POST',
    'http_timeout' => 180,
    'http_connect_timeout' => 30,
    'http_max_retries' => 2,
    'can_process_concurrent_pages' => false,
    'api_concurrency_limit' => 0,

    'endpoints_configuration' => [
        // GETTERS
        ProviderConstants::ENDPOINT_RENTALS => [
            'url' => 'Pull_ListOwnerProp_RQ',
            'url_single' => 'Pull_ListSpecProp_RQ',
            'resolver' => RUPmsRentalResolver::class,
            'uses_new_api' => false,
        ],
        ProviderConstants::ENDPOINT_BOOKINGS => [
            'url' => 'Pull_ListReservationsSubUsers_RQ',
            'url_single' => 'Pull_GetReservationByID_RQ',
            'resolver' => RUPmsBookingsResolver::class,
            'uses_new_api' => false,
        ],
        ProviderConstants::ENDPOINT_THREADS => [
            'method' => 'GET',
            'uses_new_api' => true,
            'url' => '/messaging/threads/by-service-for-user',
            'resolver' => RUPmsThreadsResolver::class,
        ],
        ProviderConstants::ENDPOINT_MESSAGES => [
            'method' => 'GET',
            'uses_new_api' => true,
            'url' => '/messaging/messages/by-service-for-user',
            'resolver' => RUPmsMessagesResolver::class,
        ],
    ],

    'sync' => [
        'complete' => [
            ProviderConstants::ENDPOINT_RENTALS,
            ProviderConstants::ENDPOINT_BOOKINGS,
        ],
        'normal' => [
            ProviderConstants::ENDPOINT_RENTALS,
            ProviderConstants::ENDPOINT_BOOKINGS,
        ],
    ],

    'allowed_ip_addresses' => [
        '*************',
        '**************',
        '*************',
        '**************',
    ],
];
