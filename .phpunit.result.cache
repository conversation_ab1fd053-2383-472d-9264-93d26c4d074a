{"version": 1, "defects": {"/Users/<USER>/MJ/ninja/tests/Feature/Http/BookingListsTest.php::it": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/ControlCenterTest.php::it": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/DownloadUrlTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/HomePageTest.php::it": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/ProviderEventsTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/ScheduledSettlementsTest.php::user": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/SlimRentalsTest.php::user": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/SlimRentalsTest.php::it": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/SourcesTest.php::it": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/TasksTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/TeamSettingsTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Query/TeamQueryTest.php::Teams": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Teams/TeamMembers/UpdateTeamMemberActionTest.php::UpdateTeamMemberAction": 5, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/TaskFromRecurrentActionTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/CreateFutureTasksForRecurrentTaskActionTest.php::it": 3, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/Crud/StoreRecurrentTaskActionTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/Crud/GetTeamRecurrentTasksActionTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/Crud/UpdateRecurrentTaskActionTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleBookingSyncWebhookTest.php::the": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleBookingSyncWebhookTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Accounting/Settlements/GetCurrenciesForTeamTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Alerts/GetAlertsActionTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Bookings/GetBookingWithRelationsActionTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/Crud/DestroyRecurrentTaskActionTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Tasks/CleanupCancelledBookingTasksActionTest.php::CleanupCancelledBookingTasksAction": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Tasks/GetTaskWithRelationsActionTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Tasks/TasksFromNonScheduledActionTest.php::that": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Middleware/VerifyRequestIsSameTeamTest.php::VerifyRequestIsSameTeam": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Middleware/VerifyUserIsRentalManagerOrAboveTest.php::VerifyUserIsRentalManagerOrAbove": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Models/PreCheckInFormTest.php::creating": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Models/UserTest.php::getRentalsForUser": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/GuestsFormAndPortalTest.php::it": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/LegalPagesTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/TeamCreatedPageTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleLodgifyWebhookTest.php::it": 1, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleRentalsUnitedWebhookTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleSmoobuWebhookTest.php::the": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleSmoobuWebhookTest.php::it": 4, "/Users/<USER>/MJ/ninja/tests/OnBackup/Actions/Accounting/GetExpandedSettlementActionTest.php::calculation": 4, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Teams/CreateTeamActionTest.php::Creates": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Register/CreateSMTeamActionTest.php::Creates": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Register/CreateRUTeamActionTest.php::Creates": 3, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Register/CreateBSTeamActionTest.php::Creates": 4, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Register/CreateBS_Simily_TeamActionTest.php::Creates": 3}, "times": {"/Users/<USER>/MJ/ninja/tests/Feature/Http/BookingListsTest.php::it": 0.135, "/Users/<USER>/MJ/ninja/tests/Feature/Http/ControlCenterTest.php::it": 0.166, "/Users/<USER>/MJ/ninja/tests/Feature/Http/DownloadUrlTest.php::it": 0.087, "/Users/<USER>/MJ/ninja/tests/Feature/Http/HomePageTest.php::it": 0.126, "/Users/<USER>/MJ/ninja/tests/Feature/Http/LegalPagesTest.php::it": 0.298, "/Users/<USER>/MJ/ninja/tests/Feature/Http/ProviderEventsTest.php::it": 0.209, "/Users/<USER>/MJ/ninja/tests/Feature/Http/ScheduledSettlementsTest.php::user": 0.232, "/Users/<USER>/MJ/ninja/tests/Feature/Http/SlimRentalsTest.php::user": 5.887, "/Users/<USER>/MJ/ninja/tests/Feature/Http/SlimRentalsTest.php::it": 0.097, "/Users/<USER>/MJ/ninja/tests/Feature/Http/SourcesTest.php::it": 0.155, "/Users/<USER>/MJ/ninja/tests/Feature/Http/TasksTest.php::it": 0.354, "/Users/<USER>/MJ/ninja/tests/Feature/Http/TeamSettingsTest.php::it": 0.108, "/Users/<USER>/MJ/ninja/tests/OnBackup/Actions/Accounting/GetExpandedSettlementActionTest.php::calculation": 5.132, "/Users/<USER>/MJ/ninja/tests/Unit/Query/TeamQueryTest.php::Teams": 17.908, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Accounting/Settlements/GetCurrenciesForTeamTest.php::it": 5.665, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Alerts/GetAlertsActionTest.php::it": 1.014, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Bookings/GetBookingWithRelationsActionTest.php::it": 0.166, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Tasks/GetTaskWithRelationsActionTest.php::it": 0.191, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Teams/TeamMembers/UpdateTeamMemberActionTest.php::UpdateTeamMemberAction": 0.033, "/Users/<USER>/MJ/ninja/tests/Unit/Middleware/VerifyRequestIsSameTeamTest.php::VerifyRequestIsSameTeam": 0.124, "/Users/<USER>/MJ/ninja/tests/Unit/Middleware/VerifyUserIsRentalManagerOrAboveTest.php::VerifyUserIsRentalManagerOrAbove": 0.083, "/Users/<USER>/MJ/ninja/tests/Unit/Models/PreCheckInFormTest.php::creating": 0.081, "/Users/<USER>/MJ/ninja/tests/Unit/Models/UserTest.php::getRentalsForUser": 0.077, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/TaskFromRecurrentActionTest.php::it": 0.108, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/CreateFutureTasksForRecurrentTaskActionTest.php::it": 0.105, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/Crud/StoreRecurrentTaskActionTest.php::it": 0.097, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/Crud/GetTeamRecurrentTasksActionTest.php::it": 0.202, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/Crud/UpdateRecurrentTaskActionTest.php::it": 37.726, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/RecurrentTasks/Crud/DestroyRecurrentTaskActionTest.php::it": 0.176, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleBookingSyncWebhookTest.php::the": 5.147, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleBookingSyncWebhookTest.php::it": 0.101, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Tasks/CleanupCancelledBookingTasksActionTest.php::CleanupCancelledBookingTasksAction": 0.126, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Tasks/TasksFromNonScheduledActionTest.php::that": 0.076, "/Users/<USER>/MJ/ninja/tests/Feature/Http/GuestsFormAndPortalTest.php::it": 0.15, "/Users/<USER>/MJ/ninja/tests/Feature/Http/TeamCreatedPageTest.php::it": 0.162, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleLodgifyWebhookTest.php::it": 0.072, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleRentalsUnitedWebhookTest.php::it": 0.091, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleSmoobuWebhookTest.php::the": 0.116, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Webhooks/HandleSmoobuWebhookTest.php::it": 0.158, "/Users/<USER>/MJ/ninja/tests/Unit/Actions/Teams/CreateTeamActionTest.php::Creates": 6.431, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Register/CreateSMTeamActionTest.php::Creates": 5.903, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Register/CreateRUTeamActionTest.php::Creates": 0.064, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Register/CreateBSTeamActionTest.php::Creates": 5.166, "/Users/<USER>/MJ/ninja/tests/Feature/Http/Register/CreateBS_Simily_TeamActionTest.php::Creates": 0.073}}