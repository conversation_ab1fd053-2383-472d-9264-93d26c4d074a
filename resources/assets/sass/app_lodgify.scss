$white: #ffffff;
$danger: #ff3b59;
$red: $danger;
$success: #03ebc3;
$green: $success;
$warning: #f1b53d;
$primary: #03ebc3;
$info: #67d1f8;
$purple: #7266ba;
$pink: #f76397;
$lightgrey: #f2f2f2;
$inverse: #4c5667;
$light: #eeeeee;
$dark: #343c49;
$lightdark3: #333333;
$lightdark4: #444444;
$lightdark5: #555555;
$lightdark6: #636363;
$lightdark7: #797979;
$lightdark-alt: #cccccc;
$light5: #f5f5f5;
$light3: #f3f3f3;
$light9: #f9f9f9;
$light-alt: #fafafa;
$greyish: #aaaaaa;
$brand-primary: #3097d1;
$brand-info: #8eb4cb;
$brand-success: #2ab27b;
$brand-warning: #cbb956;
$brand-danger: #bf5329;
$muted: #80898e;
$font-size-base: 0.875rem;
$breadcrumb-divider: url('data:image/svg+xml,%3Csvg width="4" height="8" viewBox="0 0 4 8" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath fill-rule="evenodd" clip-rule="evenodd" d="M3.30436 3.99997L5.33469e-05 0.833331L0.347875 0.5L4 3.99997L0.347821 7.5L0 7.16667L3.30436 3.99997Z" fill="%23666666"/%3E%3C/svg%3E%0A');

:root {
    --primary: $primary;
    --danger: $danger;
    --warning: $warning;
    --info: $info;
}

$grid-breakpoints: (
    xs: 0,
    sm: 1px,
    md: 2px,
    lg: 3px,
    xl: 1200px,
    xxl: 1500px,
    xxxl: 1900px,
    xxxxl: 2300px,
);

$container-max-widths: (
    sm: 540px,
    md: 720px,
    lg: 960px,
    xl: 1140px,
    xxl: 1440px,
    xxxl: 1600px,
    xxxxl: 1920px,
);

@import 'spark/spark_lodgify';
//@import "./../../../vendor/laravel/spark-aurelius/resources/assets/sass/spark";

.container {
    min-width: 992px !important;
}

.justify-content-evenly {
    justify-content: space-evenly !important;
}

@import 'ninja/functions';
@import 'ninja/user_or_team_created';
@import 'ninja/home';
@import 'ninja/rental';
@import 'ninja/rentals';
@import 'ninja/breadcrumbs';
@import 'ninja/datatables';
@import 'ninja/sync';
@import 'charist';

.navbar-fixed-top {
    height: 60px;
    min-width: max-content;
    background-color: $lightdark3;

    .navbar-brand {
        padding-bottom: 0;
        padding-top: 0;
    }
}

#spark-app {
    .nav {
        .nav-link {
            color: $lightdark6;
            padding: 8px 2px 8px 10px;
            height: auto;
            border-radius: 3px;
            position: relative;

            &:hover {
                color: $dark;
                font-weight: normal;

                svg {
                    fill: $dark;
                }
            }

            &.active {
                color: $dark;
                font-weight: 600;
                background-color: #e7e7e7;

                svg {
                    fill: $dark;
                }
            }
        }
    }
}

// Modal Notifications
#modal-notifications {
    .modal-dialog {
        .modal-footer {
            justify-content: flex-start;
        }
    }
}

.card-flush {
    padding: 0;

    .card-body {
        padding: 0;
    }
}

.grow {
    flex-grow: 1;
}

.shrink {
    flex-shrink: 1;
}

.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
    border-color: #ededed;
    background-color: #fff !important;
}

.btn {
    border-radius: 2px;
}

.btn:focus,
.btn:active {
    outline: none !important;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 2px;
}

.card {
    overflow: visible;
    width: 100%;
    background-color: #ffffff;
    border-radius: 3px;
    box-shadow: 0 3px 10px -5px rgba(0, 0, 0, 0.2);

    .card-header {
        background-color: #ffffff;
        padding: 0.9rem 1.25rem;
        font-size: 17px;
        border-bottom: 1px solid #dddddd;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }

    .m-b-md {
        padding-bottom: 20px;
        padding-top: 0;
        margin-top: -10px;
    }

    .left-icon {
        height: 80px;
        width: 80px;
        text-align: center;
        border-radius: 50%;
        float: left;

        .icon {
            font-size: 45px;
            color: #fff !important;
            line-height: 78px;
        }
    }

    .buttons {
        position: absolute;
        top: 7px;
        right: 10px;

        .btn-xs {
            font-size: 10px;
            padding: 0.25rem 0.45rem;
        }
    }

    .btn-xs {
        font-size: 10px;
        padding: 0.25rem 0.45rem;
    }

    .right-text {
        color: #797979 !important;
        outline: none !important;
        text-align: right !important;
        overflow: hidden;
        white-space: nowrap;

        h3 {
            margin-top: 20px !important;
            font-size: 20px;
            font-weight: bolder;
        }

        p {
            position: absolute;
            bottom: 7px;
            right: 10px;
            font-size: 13px;
            margin-bottom: 0;

            .btn-xs {
                font-size: 10px;
                padding: 0.25rem 0.45rem;
            }
        }
    }

    .info {
        background-color: fade-out($info, 0.3);
        border: 6px solid fade-in($info, 0.3);
    }

    .primary {
        background-color: fade-out($primary, 0.3);
        border: 6px solid fade-in($primary, 0.3);
    }

    .pink {
        background-color: fade-out($pink, 0.3);
        border: 6px solid fade-in($pink, 0.3);
    }

    .purple {
        background-color: fade-out($purple, 0.3);
        border: 6px solid fade-in($purple, 0.3);
    }

    .success {
        background-color: fade-out($success, 0.3);
        border: 6px solid fade-in($success, 0.3);
    }

    .warning {
        background-color: fade-out($warning, 0.3);
        border: 6px solid fade-in($warning, 0.3);
    }

    .danger {
        background-color: fade-out($danger, 0.3);
        border: 6px solid fade-in($danger, 0.3);
    }

    .inverse {
        background-color: fade-out($inverse, 0.3);
        border: 6px solid fade-in($inverse, 0.3);
    }
}

// Tooltip definitions.
.tooltip-inner {
    width: 220px;
}

.ninja-responsive-table {
    max-height: 550px;
    table {
        tbody {
            tr {
                th,
                td {
                    white-space: nowrap;
                }
            }
        }

        .variation {
            font-weight: bold;

            &.green {
                color: #4eb76e;
            }

            &.red {
                color: #bf5329;
            }
        }
    }
}

a {
    color: $lightdark3;

    &:hover {
        color: $dark;
        font-weight: bold;
    }
}

.lodgify-loading-spinner {
    position: absolute;
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    $anim-parameters: 0.8s infinite ease-in-out;
    $logo-square-size: 40px;

    .spinner-container {
        display: flex;
        align-items: center;

        .spinner-logo {
            display: grid;
            grid-template-columns: repeat(2, $logo-square-size);
            grid-template-rows: repeat(2, $logo-square-size);
        }

        .red1 {
            animation: red1-kf $anim-parameters;
            background: $red;
        }

        .green {
            width: 72%;
            height: 72%;
            animation: green-kf $anim-parameters;
            background: $success;
            justify-self: end;
        }

        .red2 {
            animation: red2-kf $anim-parameters;
            background: $red;
        }

        .red3 {
            animation: red3-kf $anim-parameters;
            background: $red;
            grid-column: 2/2;
            grid-row: 2/2;
        }

        .red4 {
            animation: red4-kf $anim-parameters;
            background: $red;
            grid-column: 2/2;
            grid-row: 2/2;
        }

        @keyframes red1-kf {
            100% {
                background: $green;
                transform: translateX(114%) translateY(-14%) rotate(90deg) scale(0.72);
            }
        }

        @keyframes green-kf {
            100% {
                transform: translateX(118%) translateY(-14%) rotate(90deg) scale(0);
            }
        }

        @keyframes red2-kf {
            100% {
                transform: translateY(-100%) rotate(90deg);
            }
        }

        @keyframes red3-kf {
            100% {
                transform: translateX(-100%);
            }
        }

        @keyframes red4-kf {
            0% {
                transform: translateX(118%) rotate(0deg) scale(0);
            }

            100% {
                transform: translateX(0%) rotate(-90deg) scale(1);
            }
        }
    }
}
