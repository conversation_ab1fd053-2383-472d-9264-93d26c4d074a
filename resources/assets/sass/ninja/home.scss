.home-page {
    .nav {
        margin-bottom: 0;
        padding-left: 0;
        list-style: none;
    }

    .home-page-stacked-tabs {
        border-radius: $border-radius;
        font-weight: 300;
        a {
            border-bottom: 1px solid lighten($border-color, 5%);
            border-left: 3px solid transparent;
            color: $body-color;

            i {
                color: lighten($body-color, 25%);
                position: relative;
            }
        }

        li:last-child a {
            border-bottom: 0;
        }

        li.active a {
            border-left: 3px solid $brand-primary;
        }

        li a:active,
        li a:hover,
        li a:link,
        li a:visited {
            background-color: white;
        }
    }
}
