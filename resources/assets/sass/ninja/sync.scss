.ninja-sync-figure {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-top: 50px;
    position: relative;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;

    .ninja-figure-image {
        position: relative;
        //top: -200px;
        left: 50%;
        z-index: -1;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        pointer-events: none;
    }

    .heading {
        text-align: center;
        max-width: 600px;

        .title {
            margin-bottom: 22px;
            color: #333333;
            font-size: 30px;
        }

        .subtitle {
            max-width: 770px;
            padding-right: 10px;
            padding-left: 10px;
            color: #919191;
            font-size: 12px;
            font-weight: 400;
            text-transform: uppercase;
        }
    }
}
