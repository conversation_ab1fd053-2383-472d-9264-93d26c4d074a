Vue.component('lodgify-registration', {
    data() {
        return {
            started: false,
            form: new SparkForm({
                api_key: '',
                name: '',
                email: '',
                team: '',
                team_slug: '',
                terms: false,
                password: '',
                password_confirmation: ''
            }),
        }
    },

    watch: {
        /**
         * Watch the team name for changes.
         */
        'form.team': function (val, oldVal) {
            if (this.form.team_slug === '' ||
                this.form.team_slug === oldVal.toLowerCase().replace(/[\s\W-]+/g, '-')
            ) {
                this.form.team_slug = val.toLowerCase().replace(/[\s\W-]+/g, '-');
            }
        },
    },


    methods: {
        /**
         * Fetches the data from Lodgify of an account.
         */
        getAccount() {
            let self = this;
            this.started = false;
            Spark.post('/register-lf/get-account', this.form)
                .then(response => {
                    console.log(response);
                    // OK
                    if (response === 1) {
                        self.started = true;
                        self.busy = false;
                    }
                    // TEAM ALREADY EXISTS.
                    else if (response === 2) {
                        // TODO:
                    }
                    // API KEY NOT VALID.
                    else if (response === 3) {
                        // TODO
                    }
                }).catch(response => {
                console.log(`Error!`, response);
            });

            window.form = this.form;
        },

        /**
         * Do the actual registration...
         */
        register() {
            Spark.post('/register-lf', this.form).then(response => {
                window.location = response.redirect;
            });
        },
    },
});