@extends('layout.app')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="message-card">
                <h2 class="message-title">{{ $title }}</h2>
                
                <div class="message-body">
                    <p class="message-text">{{ $message }}</p>
                    
                    @if(isset($button_url) && isset($button_text))
                        <a href="{{ $button_url }}" class="action-button">
                            {{ $button_text }}
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .message-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        text-align: center;
    }

    .message-title {
        color: #2c3e50;
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #eee;
    }

    .message-body {
        padding: 1rem;
    }

    .message-text {
        color: #505c6e;
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .action-button {
        display: inline-block;
        background: #3490dc;
        color: white;
        padding: 0.8rem 2rem;
        border-radius: 30px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .action-button:hover {
        background: #2779bd;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(52, 144, 220, 0.3);
    }
</style>
@endsection