<div>
    @if ($isPreviewMode)
        <div class="py-2 text-center text-white bg-yellow-500">
            <p>{{ __('booking_page.you_are_in_preview_mode') }}</p>
        </div>
    @endif
    <header class="bg-white shadow-md">
        <div class="flex justify-between px-6 py-4 mx-auto items center max-w-7xl">
            <a href="{{ $localeUrl }}" class="flex items-center text-lg font-bold text-cyan-600"
                title="{{ __('booking_page.about') }}">
                <img src="{{ $logo }}" alt="{{ $websiteSeoTitle }}" title="{{ $websiteSeoTitle }}"
                    class="w-auto max-h-16">
                {{-- <span class="ml-2">{{ $team->name }}</span> --}}
            </a>
            @if (!$isDirectStays)
            <nav class="flex items-center space-x-4 items center">
                <a href="{{ $localeUrl }}" class="hidden text-gray-500 md:block hover:text-gray-900"
                    title="{{ __('booking_page.home') }}">{{ __('booking_page.home') }}</a>
                <a href="{{ $localeUrl }}about" class="text-gray-500 hover:text-gray-900"
                    title="{{ __('booking_page.about') }}">{{ __('booking_page.about') }}</a>
                <a href="{{ $localeUrl }}contact" class="text-gray-500 hover:text-gray-900"
                    title="{{ __('booking_page.contact') }}">{{ __('booking_page.contact') }}</a>
                @if (count($locations) > 1)
                    <div class="hidden md:block">
                        <select class="text-gray-500 hover:text-gray-900" onchange="location = this.value;">
                            <option value="" disabled selected>{{ __('booking_page.location') }}</option>
                            @foreach ($locations as $location)
                                <option value="{{ $location['url'] }}">{{ $location['name'] }}</option>
                            @endforeach
                        </select>
                    </div>
                @endif
                <div class="z-10">
                    @livewire('language-menu', ['currentUrl' => $currentUrl, 'currentDomain' => $currentDomain])
                </div>
            </nav>
            @endif
            @if($isDirectStays)
            <div class="z-10">
                @livewire('language-menu-direct-stays')
            </div>
            @endif
        </div>
    </header>
</div>
