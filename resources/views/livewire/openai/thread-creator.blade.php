<div>
    <form wire:submit.prevent="createThread" class="space-y-4">
        <div>
            <flux:heading size="lg">Customize Assistant Behaviour</flux:heading>
            <flux:subheading>Before creating a thread, configure how it will behave based on customer status.</flux:subheading>
        </div>

        <!-- Customer Status Radio Group -->
        <flux:radio.group label="Customer Status" wire:model.live="cs_status">
            <flux:radio name="cs_status" value="lead" label="Lead" description="Customer is a Lead and has not yet signed up."/>
            <flux:radio name="cs_status" value="pm_customer" label="PM Customer" description="Customer is a PM Customer."/>
            <flux:radio name="cs_status" value="cm_customer" label="CM Customer" description="Customer is a CM Customer."/>
        </flux:radio.group>
        @error('cs_status')
        <span class="text-red-500 text-sm">{{ $message }}</span>
        @enderror

        @if($this->cs_status !== 'lead')
            <x-select label="Relate chat to the team..."
                      icon="o-user"
                      :options="$this->teams"
                      error-field="team"
                      wire:model="team"/>
        @endif


        <div class="flex justify-end">
            <flux:button type="submit" variant="primary">Create Thread</flux:button>
        </div>
    </form>
</div>

