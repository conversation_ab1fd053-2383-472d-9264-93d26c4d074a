<div class="min-h-screen flex flex-col bg-white dark:bg-zinc-800">
    <!-- Header -->
    <flux:header container class="bg-zinc-50 dark:bg-zinc-900 border-b border-zinc-200 dark:border-zinc-700">
        <flux:spacer/>
        <flux:navbar class="mr-4">
            <flux:navbar.item icon="trash" label="Delete" wire:click="deleteThread"/>
        </flux:navbar>
    </flux:header>

    <!-- Main content area -->
    <flux:main container class="flex-grow flex flex-col">
        <flux:heading size="lg" level="1">
            <a href="https://platform.openai.com/threads/{{$this->thread->thread_id}}" target="_blank">
                Thread {{$this->thread->thread_id}}
            </a>
        </flux:heading>
        <flux:subheading size="lg" class="mb-6">
            @if($this->thread->user_id)
                <span class="text-gray-500">
                    <a href="/nova/resources/teams/{{$this->thread->user_id}}" target="_blank">Team {{$this->thread->user_id}} - {{\App\Models\Team::find($this->thread->user_id)->name}}</a>
                </span>
            @else
                <span class="text-gray-500">Lead</span>
            @endif
        </flux:subheading>
        <flux:separator variant="subtle"/>

        <!-- Chatbox area -->
        <div class="flex flex-col flex-grow relative">
            <x-loading wire:loading/>
            <div class="flex-grow overflow-y-auto chatbox space-y-4" wire:loading.remove>
                @foreach($messages as $message)
                    <div class="{{ $message['role'] === 'user' ? 'text-right' : 'text-left' }} !mb-4" wire:key="{{$message['id']}}">
                        <div class="inline-block list-decimal {{$message['role'] === 'user'? 'max-w-md md:max-w-lg lg:max-w-4xl': 'w-full'}}">
                            <div class="{{ $message['role'] === 'user' ? 'bg-gray-200 text-gray-900' : 'bg-transparent text-gray-900' }} px-4 py-2 rounded-lg border-1">
                                @if($message['role'] === 'assistant')
                                    <flux:accordion transition>
                                        <flux:accordion.item heading="Thought">
                                            <div class="m-2">
                                                {!! data_get($message, 'thought') !!}
                                            </div>
                                        </flux:accordion.item>
                                        @if(!empty(data_get($message, 'search_results')))
                                            <flux:accordion.item heading="Search Results">
                                                <div class="m-2">
                                                    {!! data_get($message, 'search_results') !!}
                                                </div>
                                            </flux:accordion.item>
                                        @endif
                                        <flux:accordion.item heading="Answer" expanded disabled>
                                            <div class="m-2 answer-container" id="answer-{{$message['id']}}">
                                                {!! data_get($message, 'answer') !!}
                                            </div>
                                            <button class="copy-answer-btn" onclick="copyAnswer('answer-{{$message['id']}}')">Copy Answer</button>
                                        </flux:accordion.item>
                                    </flux:accordion>
                                @else
                                    <flux:accordion>
                                        <flux:accordion.item heading="Instructions">
                                            <div class="m-2">
                                                {!! data_get($message, 'instructions') !!}
                                            </div>
                                        </flux:accordion.item>
                                        <flux:accordion.item expanded disabled>
                                            <div class="m-2">
                                                {!! data_get($message, 'answer') !!}
                                            </div>
                                        </flux:accordion.item>
                                    </flux:accordion>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <form
                    wire:submit.prevent="sendMessage"
                    class="flex items-end w-full p-3 bg-[#f4f4f4] dark:bg-token-main-surface-secondary rounded-xl">
                <div class="flex items-center w-full gap-2">
                    <flux:textarea
                            wire:loading.attr="disabled"
                            wire:model="message"
                            rows="auto"
                            resize="none"
                            placeholder="Type your message..."
                    />
                    <button
                            wire:loading.attr="disabled"
                            type="submit"
                            class="flex items-center justify-center h-10 w-10 bg-blue-500 text-white rounded-full hover:bg-blue-700"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </flux:main>


    <script>
        async function copyAnswer(answerId) {
            const answerContainer = document.getElementById(answerId);
            if (answerContainer) {
                try {
                    // Convert HTML lists to plain text numbering with better formatting for nested lists
                    const clonedContainer = answerContainer.cloneNode(true);

                    function convertListItems(list, level = 1) {
                        const items = list.querySelectorAll(':scope > li');
                        items.forEach((item, index) => {
                            // Add only a single prefix to avoid double dashes
                            if (!item.dataset.processed) {
                                const prefix = list.tagName === 'OL' ? `${index + 1}. ` : `- `;
                                item.insertAdjacentText('afterbegin', `${' '.repeat((level - 1) * 2)}${prefix}`);
                                item.dataset.processed = true;
                            }
                            // Recursively process nested lists
                            const nestedLists = item.querySelectorAll(':scope > ul, :scope > ol');
                            nestedLists.forEach((nestedList) => convertListItems(nestedList, level + 1));
                        });
                    }

                    const lists = clonedContainer.querySelectorAll('ul, ol');
                    lists.forEach((list) => convertListItems(list));

                    // Remove redundant newlines and ensure proper spacing
                    const plainText = clonedContainer.innerText.replace(/\n{2,}/g, '\n').trim();

                    await navigator.clipboard.write([
                        new ClipboardItem({
                            'text/html': new Blob([answerContainer.innerHTML], {type: 'text/html'}),
                            'text/plain': new Blob([plainText], {type: 'text/plain'})
                        })
                    ]);
                    alert('Answer copied to clipboard!');
                } catch (err) {
                    console.error('Failed to copy text: ', err);
                }
            }
        }
    </script>

    <style>
        .copy-answer-btn {
            margin-top: 10px;
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .copy-answer-btn:hover {
            background-color: #45a049;
        }
    </style>
</div>