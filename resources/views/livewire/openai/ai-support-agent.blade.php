<div>
    <x-nav sticky class="lg:hidden">
        <x-slot:brand>
            <div class="flex items-center">
                <img src="/img/logo/rentalninja.png" class="h-10" alt="Logo">
                <div class="ml-5 font-bold">AI Support Helper</div>
            </div>
        </x-slot:brand>
        <x-slot:actions>
            <label for="main-drawer" class="lg:hidden mr-3">
                <x-icon name="o-bars-3" class="cursor-pointer"/>
            </label>
        </x-slot:actions>
    </x-nav>

    <x-main full-width>
        <x-slot:sidebar drawer="main-drawer" class="bg-base-100 lg:bg-inherit border">
            <div class="flex flex-col items-start p-2 space-y-1">
                <div class="h-10">
                    <img src="/img/logo/logo-regular-color.png" class="h-full w-auto object-contain" alt="Logo">
                </div>
                <div class="pl-3 font-bold">AI Support Helper</div>
            </div>
            <div class="px-2">
                <x-button
                        label="New Thread"
                        icon="o-plus"
                        class="btn-primary btn-sm w-full"
                        wire:click="createThread"
                />
                <ul>
                    <x-menu-separator/>
                    @forelse($chats as $thread)
                        <x-button
                                class="content-start justify-start w-full p-4 my-1 cursor-pointer text-left {{ $selectedThread?->id === $thread->id ? 'bg-blue-200' : 'hover:bg-blue-100' }}"
                                link="?threadId={{ $thread->id }}"
                                :label="$this->getThreadTitle($thread)"
                        />
                    @empty
                        <li class="p-4 text-center text-gray-500">No chats available</li>
                    @endforelse
                </ul>
            </div>
        </x-slot:sidebar>
        <x-slot:content class="bg-white !p-0">
            @if(empty($selectedThread))
                <x-alert title="No Chat Selected" icon="o-exclamation-triangle"/>
            @else
                <livewire:openai.thread-chat :thread="$selectedThread"/>
            @endif
        </x-slot:content>
    </x-main>


    <flux:modal name="create-thread-modal" wire:model.self="showThreadCreatorModal" class="md:w-300 space-y-6">
        <livewire:openai.thread-creator/>
    </flux:modal>

    @push('scripts')
        <script>
            window.addEventListener('close-thread-creator-modal', () => {
                @this.
                set('showThreadCreatorModal', false);
            });
        </script>
    @endpush
</div>
