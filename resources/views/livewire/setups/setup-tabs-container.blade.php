<div>
    @section('page-title')
        {{$company?->team?->name ?? $company?->company?->company_name ?? 'Onboarding'}}
    @endsection

    @if ($showOverview || $onboardingId)
        <livewire:setups.setup-fetch-on-boarding-button
                :setup-id="$company->id"
                :onboardingId="$onboardingId"
                :showOverview="$showOverview"
                :hash="$company->login_hash"
                :showInfo="$onboardingId != null"
                title="Overview"
        />
    @else
        {{-- NAVBAR mobile only --}}
        <x-nav sticky class="lg:hidden">
            <x-slot:brand>
                <div class="flex items-center">
                    <img src="/img/logo/rentalninja.png" class="h-10">
                    <div class="ml-5 font-bold">{{$company?->team?->name ?? $company?->company?->company_name ?? 'Onboarding'}}</div>
                </div>
            </x-slot:brand>
            <x-slot:actions>
                <label for="main-drawer" class="lg:hidden mr-3">
                    <x-icon name="o-bars-3" class="cursor-pointer"/>
                </label>
            </x-slot:actions>
        </x-nav>

        {{-- MAIN --}}
        <x-main full-width>
            {{-- SIDEBAR --}}
            <x-slot:sidebar drawer="main-drawer" class="bg-base-100 lg:bg-inherit border">
                {{-- BRAND --}}
                <div class="flex flex-col items-start p-2 space-y-1">
                    <div class="h-10">
                        <img src="/img/logo/logo-regular-color.png" class="h-full w-auto object-contain" alt="Logo">
                    </div>
                    <div class="pl-3 font-bold">{{$company?->team?->name ?? $company?->company?->company_name ?? 'Onboarding'}}</div>
                </div>

                {{-- MENU --}}
                <x-menu activate-by-route>
                    <x-menu-separator/>
                    @foreach ($navigation as $item)

                        <x-menu-item
                                :title="$item['name']"
                                :link="$item['href']"
                                :icon="$item['icon']"
                                :active="$activeTab == $item['tab']"
                                @click="$wire.setActiveTab('{{ $item['name'] }}')"
                                :external="$item['external']"
                        />

                        @if(array_key_exists('separator', $item) && $item['separator'])
                            <x-menu-separator/>
                        @endif
                    @endforeach
                </x-menu>
            </x-slot:sidebar>

            {{-- CONTENT --}}
            <x-slot:content class="bg-white">
                @if ($activeTab === 'profile')
                    <livewire:setups.setup-profile-tab :company="$company"/>
                @elseif ($activeTab === 'accounts')
                    <livewire:setups.setup-services-and-markups-tab :company="$company"/>
                @elseif ($activeTab === 'rentals')
                    <livewire:setups.setup-rentals-tab :company="$company"/>
                @elseif($activeTab === 'confirm-rentals')
                    <livewire:setups.setup-confirm-rentals-tab :company="$company"/>
                @elseif($activeTab === 'actions')
                    @if (\App\Models\RentalNinjaTeam::userHasNovaAccess(request()->user))
                        <livewire:setups.setup-actions-tab :company="$company"/>
                    @else
                        <div class="text-center text-red-500">You do not have access to this page</div>
                    @endif
                @endif
            </x-slot:content>
        </x-main>
    @endif
</div>