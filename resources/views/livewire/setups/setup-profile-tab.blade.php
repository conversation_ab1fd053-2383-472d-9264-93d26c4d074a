<div>
    <x-mary-header title="Profiles" separator progress-indicator>
        <x-slot:actions>
            <x-mary-button wire:click="save" label="Save" spinner="save" class="btn btn-success btn-sm"/>
        </x-slot:actions>
    </x-mary-header>

    @if (session()->has('message'))
        <x-alert icon="o-check-circle" class="mt-4">
            {{ session('message') }}
        </x-alert>
    @endif

    <?php
        $timezoneOptions = [
            'UTC-12:00', 'UTC-11:00', 'UTC-10:00', 'UTC-09:30', 'UTC-09:00', 'UTC-08:00', 'UTC-07:00',
            'UTC-06:00', 'UTC-05:00', 'UTC-04:30', 'UTC-04:00', 'UTC-03:30', 'UTC-03:00', 'UTC-02:00',
            'UTC-01:00', 'UTC±00:00', 'UTC+01:00', 'UTC+02:00', 'UTC+03:00', 'UTC+03:30', 'UTC+04:00',
            'UTC+04:30', 'UTC+05:00', 'UTC+05:30', 'UTC+05:45', 'UTC+06:00', 'UTC+06:30', 'UTC+07:00',
            'UTC+08:00', 'UTC+08:30', 'UTC+08:45', 'UTC+09:00', 'UTC+09:30', 'UTC+10:00', 'UTC+10:30',
            'UTC+11:00', 'UTC+12:00', 'UTC+12:45', 'UTC+13:00', 'UTC+14:00'
        ];
        // Transform the array into id => value, name => value array
        $timezoneOptions = collect($timezoneOptions)->map(function ($item) {
            return ['id' => $item, 'name' => $item];
        })->toArray();
    ?>

    <form wire:submit="save" class="space-y-8">
        <x-mary-card title="Contact Information">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                <x-mary-input label="First name" wire:model="contact_first_name"/>
                <x-mary-input label="Last name" wire:model="contact_last_name"/>
                <x-mary-input label="Email address" wire:model="contact_email" type="email"/>
                <x-select label="Phone Prefix" :options="\App\Models\Country::all()" wire:model="contact_prefix_phone"/>
                <x-mary-input label="Phone Number" wire:model="contact_phone"/>
                <x-mary-input label="City" wire:model="contact_city"/>
                <x-select label="Country" :options="\App\Models\Country::all()"  wire:model="contact_country"/>
                <x-mary-input label="Address" wire:model="contact_address"/>
                <x-mary-input label="ZIP / Postal code" wire:model="contact_zip"/>
                <x-mary-input label="Birthday" wire:model="contact_birthday" type="date"/>
                <x-select label="Preferred Language" :options="\App\Models\Locale::all()" wire:model="contact_language"/>
            </div>
        </x-mary-card>

        <x-mary-card title="Company Information">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                <x-mary-input label="Company name" wire:model="company_name"/>
                <x-mary-input label="Website" wire:model="company_website" type="url"/>
                <x-mary-input label="City" wire:model="company_city"/>
                <x-select label="Country" :options="\App\Models\Country::all()" wire:model="company_country"/>
                <x-mary-input label="ZIP / Postal code" wire:model="company_zip"/>
                <x-select label="Timezone" :options="$timezoneOptions" wire:model="company_timezone"/>
                <x-mary-input label="Region" wire:model="company_region"/>
                <x-mary-input label="Address" wire:model="company_address"/>
                <x-select label="Company Phone Prefix" :options="\App\Models\Country::all()" wire:model="company_prefix_phone"/>
                <x-mary-input label="Company Phone" wire:model="company_phone"/>
                <x-mary-input label="Company ID" wire:model="company_id"/>
                <x-mary-input label="VAT Number" wire:model="company_vat"/>
                <x-mary-input label="Merchant Name" wire:model="company_merchant_name"/>
                <x-mary-input label="Number of Rentals" wire:model="company_num_rentals" type="number"/>
                <x-mary-input label="Number of Employees" wire:model="company_num_employees" type="number"/>
                <x-mary-input label="Year of Creation" wire:model="company_creation_year" type="number"/>
            </div>
            <div class="mt-6">
                <x-textarea label="Company Description" wire:model="company_description"/>
            </div>
            <x-slot:actions>
            </x-slot:actions>
        </x-mary-card>
    </form>
</div>
