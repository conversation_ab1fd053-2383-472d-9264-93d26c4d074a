<div>
    @if (count($navigationLanguages) > 1)
        <x-button label="{{ $localeName }}" wire:click="$toggle('languageMenu')" />
        <x-drawer wire:model="languageMenu" class="w-11/12 lg:w-1/3 bg-slate-50" right>
            <div class="absolute top-0 right-0 m-2 cursor-pointer" @click="$wire.languageMenu = false">
                <span class="text-2xl material-symbols-outlined">cancel</span>
            </div>
            @foreach ($navigationLanguages as $id => $name)
                <option value="{{ $id }}" {{ $id === app()->getLocale() ? 'selected' : '' }}
                    class="px-4 py-2 text-gray-800 cursor-pointer hover:bg-gray-200"
                    @click="$wire.updateLocale('{{ $id }}')">
                    {{ __("$name") }}
                </option>
            @endforeach
            <div wire:loading wire:target="updateLocale"
                class="absolute top-0 left-0 w-full h-full bg-gray-200 bg-opacity-50">
                <div class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
                    <x-loading size="24" />
                </div>
            </div>
        </x-drawer>
    @endif
</div>
@script
    <script>
        document.addEventListener('livewire:initialized', () => {
            function setCookie(name, value, domain, days) {
                console.log('Setting cookie: ', name, value, domain, days);
                const date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                const expires = "; expires=" + date.toUTCString();
                document.cookie = name + "=" + value + expires + "; path=/;"
                console.log('Cookie set: ', name, value, domain, days);
            }

            Livewire.on('setLocaleCookie', (data) => {
                data = data[0];
                setCookie('locale', data['locale'], data.domain, 365);
                setTimeout(() => {
                    window.location.reload();
                }, 150);
            });
        });
    </script>
@endscript
