<div>
    <div class="bg-white shadow-lg rounded-b-md hover:shadow-xl " wire:key="$rental">
        @livewire('ninja-image-gallery', [
        'images' => $rental->pictures,
        'class' => 'object-cover w-full h-64',
        'withLightbox' => false,
        'link' => $this->getRentalUrl($rental) ])
        <div wire:navigate.hover href="{{ $this->getRentalUrl($rental) }}" title="{{ $rental->headline }}"
            class="cursor-pointer">
            <div class="p-2">
                <div class="flex justify-between text-center">
                    <div class="items-center content-center w-full h-32">
                        <h2 class="ml-2 text-xl font-medium text-gray-700">
                            {{ trim($rental->headline) }}
                        </h2>
                    </div>
                </div>
                <div class="flex justify-between">
                    @if ($rental->surface)
                    <div class="grid items-center grid-rows-2">
                        <div class="justify-self-center">
                            <img src="https://img.icons8.com/ios-glyphs/24/null/expand--v1.png"
                                alt="{{ __('booking_page.surface') }}" title="{{ __('booking_page.surface') }}" />
                        </div>
                        <div>
                            <p class="ml-2 text-sm font-medium text-gray-700">
                                {{ $rental->surface }} {{ $rental->surfaceUnit === 'imperial' ? 'ft²' : 'm²' }}
                            </p>
                        </div>
                    </div>
                    @endif
                    @if ($rental->bathrooms_count)
                    <div class="grid items-center grid-rows-2">
                        <div class="justify-self-center">
                            <img src="https://img.icons8.com/pastel-glyph/24/null/bath--v2.png"
                                alt="{{ __('booking_page.bathroom_plural') }}"
                                title="{{ __('booking_page.bathroom_plural') }}" />
                        </div>
                        <div>
                            <p class="ml-2 text-sm font-medium text-gray-700">
                                {{ $rental->bathrooms_count }} {{ __('booking_page.bathroom_plural')
                                }}
                            </p>
                        </div>
                    </div>
                    @endif
                    @if ($rental->bedrooms_count)
                    <div class="grid grid-rows-2 items center">
                        <div class="justify-self-center">
                            <img src="https://img.icons8.com/ios-glyphs/24/null/bedroom.png"
                                alt="{{ __('booking_page.bedroom_plural') }}"
                                title="{{ __('booking_page.bedroom_plural') }}" />
                        </div>
                        <div>
                            <p class="ml-2 text-sm font-medium text-gray-700">
                                {{ $rental->bedrooms_count }} {{ __('booking_page.bedroom_plural') }}
                            </p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @if ($rental->hasQuote)
            <div class="flex items-center justify-between pb-2 m-2">
                <div>
                    <div>
                        <b>{{ $this->getAmount($rental->avgNightPrice,
                            $rental->currency) }}</b>
                        {{ __('booking_page.per_night') }}
                    </div>
                    <div class="ml-1 text-sm text-gray-500">
                        {{ $this->getAmount($rental->finalPrice, $rental->currency) }}
                        {{ __('booking_page.total') }}
                    </div>
                </div>
                <div class="flex items center">
                    <x-button wire:navigate href="{{ $this->getRentalUrl($rental) }}"
                        class="flex items-center justify-center w-full h-12 px-2 font-medium text-center text-white bg-blue-600 rounded-lg min-w-fit hover:bg-blue-400">
                        {{ __('booking_page.view_details') }}
                    </x-button>
                </div>
            </div>
            @endif
            @if ($rental->error)
            <div class="flex items-center justify-between pb-2 m-2">
                {{ $rental->error}}
            </div>
            @endif

        </div>
    </div>
</div>
