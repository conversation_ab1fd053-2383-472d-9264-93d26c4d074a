<div>
    <div class="text-black/[0.5] text-xs font-medium uppercase">
        {{ $rental->city }}, {{ $rental->countryName }}
    </div>

    <h1 class="mb-4 text-2xl font-medium leading-none md:text-4xl">
        {{ $rental->headline }}
    </h1>

    <div class="flex flex-wrap items-center mb-4 text-sm text-center">
        <div class="flex items-center gap-6">
            @if ($rental->bathroomsCount > 0)
                <div class="items-center gap-2">
                    <span class="material-symbols-outlined notranslate">
                        groups
                    </span>
                    <div>{{ $rental->sleeps }} - {{ $rental->sleepsMax }}</div>
                </div>
            @endif

            @if ($rental->bathroomsCount > 0)
                <div class="items-center gap-2">
                    <span class="material-symbols-outlined notranslate">
                        bathroom
                    </span>
                    <div>{{ $rental->bathroomsCount }}</div>
                </div>
            @endif

            @if ($rental->bedroomsCount > 0)
                <div class="items-center gap-2">
                    <span class="material-symbols-outlined notranslate">
                        bedroom_parent
                    </span>
                    <div>{{ $rental->bedroomsCount }}</div>
                </div>
            @endif
        </div>

        @if ($licenseNumber != null)
            <div class="items-center gap-2 ml-auto">
                <span class="material-symbols-outlined notranslate">
                    verified
                </span>
                <div>{{ $licenseNumber }}</div>
            </div>
        @endif
    </div>

    <div class="mb-16" style="color: #475569">
        {!! clean($description) !!}
    </div>
</div>
