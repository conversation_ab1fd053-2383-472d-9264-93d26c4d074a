<div>
    <div class="flow-root">
        <div class="flex items-center gap-2 mb-2">
            <span class="material-symbols-outlined notranslate">shelf_position</span>
            <h2 class="text-2xl font-semibold text-gray-900">{{ __('booking_page.distribution') }}</h2>
        </div>
    </div>
    <div class="pl-4 notranslate">
        @if ($rentalSurface != null)
        <x-list-item :item="[$rentalSurface]" no-separator no-hover>
            <x-slot:avatar>
                <span class="material-symbols-outlined notranslate">square_foot</span>
            </x-slot:avatar>
            <x-slot:value>
                @if ($rentalSurfaceUnit == null)
                <span class="text-sm font-semibold text-gray-900 notranslate">{{ $rentalSurface }}</span>
                @else
                <span class="text-sm text-gray-600 notranslate">{{ $rentalSurface }} {{ $rentalSurfaceUnit ?? 'm2' }}</span>
                @endif
            </x-slot:value>
        </x-list-item>
        @endif
        @if($roomAmenities != null && count($roomAmenities) > 0)
        @foreach (array_slice($roomAmenities,0,3, true) as $room)
        <x-list-item :item="$roomAmenities" no-separator no-hover>
            <x-slot:avatar>
                <span class="material-symbols-outlined notranslate">{{ $room['icon'] ?? 'check' }}</span>
            </x-slot:avatar>
            <x-slot:value>
                <h3>{{ __('channel_manager.amenity.' . $room['key']) }}</h3>
            </x-slot:value>
            <x-slot:sub-value>
                @foreach ($room['amenities'] as $index => $amenity)
                <span class="text-sm text-stone-400 notranslate">{{ __('channel_manager.amenity.' .
                    $amenity['key']) }}</span>@if ($index != count($room['amenities']) - 1),@endif
                @endforeach
            </x-slot:sub-value>
        </x-list-item>
        @endforeach
        @endif
        @if ( $expanded)
        @foreach ($roomAmenities as $room)
        <x-list-item :item="$roomAmenities" no-separator no-hover>
            <x-slot:avatar>
                <span class="material-symbols-outlined notranslate">{{ $room['icon'] ?? 'check' }}</span>
            </x-slot:avatar>
            <x-slot:value>
                <h3>{{ __('channel_manager.amenity.' . $room['key']) }}</h3>
            </x-slot:value>
            <x-slot:sub-value>
                @foreach ($room['amenities'] as $index => $amenity)
                <span class="text-sm text-stone-400 notranslate">{{ __('channel_manager.amenity.' .
                    $amenity['key']) }}</span>@if ($index != count($room['amenities']) - 1),@endif
                </span>
                @endforeach
            </x-slot:sub-value>
        </x-list-item>
        @endforeach
        @endif
        @if (count($roomAmenities) > 3)
        <div class="flex items-center gap-2 mb-4 ml-4">
            <button wire:click="toggleExpand" class="text-sm font-semibold text-gray-900">
                {{$expanded ? __('booking_page.show_less') : __('booking_page.show_all')}}
                <span class="mt-2 material-symbols-outlined notranslate">expand_more</span>
            </button>
        </div>
        @endif
    </div>
    @livewire('property-amenities', ['allAmenities' => $allAmenities, 'amenities' => $rentalAmenities])
</div>
