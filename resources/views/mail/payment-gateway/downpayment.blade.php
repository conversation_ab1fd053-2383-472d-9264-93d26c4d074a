@extends('mail.layout.guests_mail_layout')

@section('title', $sub)

@section('content')
    <table border="0" cellpadding="0" cellspacing="0"
           style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;">
        @if ($photo != null)
            <tr>
                <td cellpadding="10"
                    style="text-align: center; vertical-align: top; margin-bottom: 15px">
                    <img style="padding-top: 20px; padding-bottom: 15px; max-width: 600px"
                         src="{{ $photo }}">
                </td>
            </tr>
        @endif
        <tr>
            <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;">
                <p style="text-align: center; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; Margin-bottom: 45px;">
                    {{ __('messages.email.intro.dear_name', ['name' => $clientName]) }}
                </p>
                <p style="text-align: center; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; Margin-bottom: 15px;">
                {{ __('messages.downpayment_email.intro', ['property_name' => $propertyName, 'on' => $on, 'booking_source' => $bookingSource]) }}
                <p>
                <p style="text-align: center; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; Margin-bottom: 15px;">
                    {{ __('messages.downpayment_email.instruction_intro') }} <br>
                </p>
                <table border="0" cellpadding="0" cellspacing="0" class="btn btn-primary"
                       style="margin-bottom: 5px; margin-top: 25px; border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; box-sizing: border-box;">
                    <tbody>
                    <tr>
                        <td align="center"
                            style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding-bottom: 0px;">
                            <table border="0" cellpadding="0" cellspacing="0"
                                   style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;">
                                <tbody>
                                <tr>
                                    <td style="font-family: sans-serif; font-size: 14px; vertical-align: top; background-color: #3498db; border-radius: 5px; text-align: center;">
                                        <a href="{{ $paymentUrl }}"
                                           target="_blank"
                                           style="display: inline-block; color: #ffffff; background-color: #3498db; border: solid 1px #3498db; border-radius: 5px; box-sizing: border-box; cursor: pointer; text-decoration: none; font-size: 18px; font-weight: bold; margin: 0; padding: 12px 25px; text-transform: capitalize; border-color: #3498db;">
                                            {{ __('messages.downpayment_email.button') }}
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p style="text-align: center; font-family: sans-serif; font-size: 10px; font-weight: normal; margin: 0; Margin-bottom: 25px;">
                    {{ __('messages.downpayment_email.button.if_cant_click_button') }}
                    <br>{{ $paymentUrl }}
                </p>
                <p style="text-align: center; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; Margin-bottom: 15px;">
                    {{ __('messages.downpayment_email.important_note', ['booking_reference' => $bookingReference, 'final_payment_date' => $finalPaymentDate]) }}
                </p>
                <p style="text-align: center; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; Margin-bottom: 15px;">
                    {{ __('messages.downpayment_email.assistance_offer', ['contact_email' => $contactEmail, 'contact_phone' => $contactPhone ]) }}
                </p>
                <p style="text-align: center; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; Margin-bottom: 25px;">
                    {{ __('messages.downpayment_email.closing', ['property_name' => $propertyName]) }}
                </p>
                <p style="text-align: center; font-family: sans-serif; font-size: 10px; font-weight: normal; margin: 0; Margin-bottom: 15px;">
                    {{ __('messages.email.note')  }}
                </p>
                <p style="text-align: center; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; Margin-top: 45px;">
                    {{ __('messages.pre_check-in_email.see_you_soon') }}
                    <br>{{$teamName}}
                </p>
            </td>
        </tr>
    </table>
@endsection