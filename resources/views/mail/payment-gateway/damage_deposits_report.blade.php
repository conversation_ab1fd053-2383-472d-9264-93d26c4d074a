@php
    use App\Actions\PaymentGateway\InformGatewayDamageDepositsToReturnAction;
    use App\Models\BookingPayment;

    $ddReportStart = InformGatewayDamageDepositsToReturnAction::DD_REPORT_WINDOW_START;
    $ddReportEnd = InformGatewayDamageDepositsToReturnAction::DD_REPORT_WINDOW_END;
@endphp
@component('mail::message', ['url' => $url, 'name' => $name])
# {{__('messages.payment_gateway.damage_deposit.report_email.title')}}

{{__('messages.payment_gateway.damage_deposit.report_email.intro', ['start' => $ddReportStart, 'end' => $ddReportEnd])}}
@foreach ($payments as $payment)
@php
    /** @var BookingPayment $payment */
    $booking = $payment->booking;
    $rental = $booking->rental;
@endphp

## {{__('teams.booking')}}: {{$payment->booking_id}} / {{$booking->reference}}

{{__('teams.rental')}}: {{$rental->name}}

{{$booking->getCheckInDay()}} -> {{$booking->getCheckOutDay()}}

{{__('messages.guest')}}: {{$booking->client->name()}}

@component('mail::table')
| {{__('messages.payment_gateway.damage_deposit.report_email.table_header')}} ||
| :------------ | ----------------------------: |
| {{__('messages.payment_gateway.damage_deposit.report_email.deposit_amount')}} | {{$payment->getAmountInUnits()}} {{$payment->currency}} |
| {{__('messages.payment_gateway.damage_deposit.report_email.return_date')}} | {{$payment->getHoldUntilStringDate()}} |
| {{__('messages.payment_gateway.damage_deposit.report_email.payment_intent')}} | {{$payment->stripe_payment_intent_id}} |
@endcomponent

@component('mail::button', ['url' => $config->rnAppTargetDomain().'/page/bookings/'.$payment->booking_id])
{{__('teams.view_booking')}}
@endcomponent
@component('mail::button', ['url' => config('ninja-stripe.stripe-dashboard-url').'/payments/'.$payment->stripe_payment_intent_id])
{{__('messages.payment_gateway.damage_deposit.report_email.view_in_stripe')}}
@endcomponent
@endforeach

{{__('teams.email.regards')}}<br>
{{ $config->rnAppName() }}
@endcomponent