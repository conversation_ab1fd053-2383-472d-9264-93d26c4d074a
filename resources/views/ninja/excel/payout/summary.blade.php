<table>
    <tbody>
    <tr></tr>
    <tr>
        <td><b>{{ __('accounting_pdfs.payment.title') }}</b></td>
    </tr>
    <tr></tr>
    <tr>
        <td>{{$payout['settlement_name']}} - {{ $payout['payee_name'] }}</td>
    </tr>
    <tr>
        <td>{{ __('accounting_pdfs.bookings_between', [
    'start' => Carbon\Carbon::parse($payout['settlement']['start'])->format('d M Y'),
    'finish' => Carbon\Carbon::parse($payout['settlement']['end'])->format('d M Y'),
    ]) }}</td>
    </tr>
    <tr></tr>
    <tr>
        <td><b>{{ __('accounting_pdfs.payment.rentals_included') }}</b></td>
    </tr>
    @php
        $rentals_in_groups = array_chunk($rentals, 4, false);
    @endphp
    @foreach ($rentals_in_groups as $rental_group)
        <tr>
            @foreach($rental_group as $rental_name)
                <td>{{ $rental_name }}</td>
            @endforeach
        </tr>
    @endforeach
    <tr></tr>
    </tbody>
</table>

{{--Totals table:--}}
@include('ninja.components.payout.totals_table', ['payout' => $payout])
{{--Rental Price table. Do not show unless there are Rental Prices to show--}}
@includeWhen(count($payout['bookings']) > 0, 'ninja.components.payout.bookings_table', ['payout' => $payout, 'rentals' => $rentals])
{{--Fees table. Do not show unless there are Fees to show--}}
@includeWhen(count($payout['fees']) > 0, 'ninja.components.payout.fees_table', ['payout' => $payout, 'rentals' => $rentals])
{{--Taxes table. Do not show unless there are Taxes to show--}}
@includeWhen(count($payout['taxes']) > 0, 'ninja.components.payout.taxes_table', ['payout' => $payout, 'rentals' => $rentals])
{{--Others table. Do not show unless there are Others to show--}}
@includeWhen(count($payout['others']) > 0, 'ninja.components.payout.others_table', ['payout' => $payout, 'rentals' => $rentals])
{{--Expenses table. Do not show unless there are Expenses to show--}}
@includeWhen(count($payout['expenses']) > 0, 'ninja.components.payout.expenses_table', ['payout' => $payout, 'rentals' => $rentals])
