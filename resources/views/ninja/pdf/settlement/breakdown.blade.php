<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">

    <title>Booking Breakdown</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background: #fff none;
            font-family: <PERSON><PERSON><PERSON><PERSON>, sans-serif, serif;
            font-size: 10px;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        /* Center tables for demo */
        table {
            margin-bottom: 30px;
        }

        /* Default Table Style */
        table {
            width: 100%;
            color: #333;
            background: white;
            border: 1px solid grey;
            font-size: 10px;
            border-collapse: collapse;
        }

        table thead th,
        table tfoot th {
            color: #777;
            background: rgba(0, 0, 0, .1);
        }

        table caption {
            padding: 3px;
        }

        table th,
        table td {
            padding: 3px;
            border: 1px solid lightgrey;
            width: auto;
        }

        table th.min, table td.min {
            width: 1%;
            white-space: nowrap;
            padding-left: 10px;
            padding-right: 10px;
        }

        h1 {
            border-top: 1px solid #5D6975;
            border-bottom: 1px solid #5D6975;
            color: #5D6975;
            font-size: 2.4em;
            line-height: 1.4em;
            font-weight: normal;
            text-align: center;
            margin: 0 0 20px 0;
        }

        /* Zebra Table Style */
        [data-table-theme*=zebra] tbody tr:nth-of-type(odd) {
            background: rgba(0, 0, 0, .05);
        }

        [data-table-theme*=zebra][data-table-theme*=dark] tbody tr:nth-of-type(odd) {
            background: rgba(255, 255, 255, .05);
        }

        /* Dark Style */
        [data-table-theme*=dark] {
            color: #ddd;
            background: #333;
            font-size: 12px;
            border-collapse: collapse;
        }

        [data-table-theme*=dark] thead th,
        [data-table-theme*=dark] tfoot th {
            color: #aaa;
            background: rgba(0255, 255, 255, .15);
        }

        [data-table-theme*=dark] caption {
            padding: .5em;
        }

        [data-table-theme*=dark] th,
        [data-table-theme*=dark] td {
            padding: .5em;
            border: 1px solid grey;
        }

        .container {
            padding: 30px;
        }

    </style>
</head>

<body>
<div class="container">
    <h1>{{ __('accounting_pdfs.booking_breakdown.title') }}</h1>

    <table style="width: 100%; padding: 0; margin:0 auto;border-style: none;" cellspacing="0">
        <tr>
            <td style="padding: 10px; color: #000000; text-align: left; border-style: none;" align="left" valign="top">
                @if($team->hasValidImgixPhoto())
                    <div style="margin-top: 10px;">
                        <img src="{{$team->imgixPhoto()}}" style="width: 100px; height: 100px;" alt="Team photo">
                    </div>
                @endif
                <p>{{$team->email}}</p>
                <h3>{{ $team->name }}</h3>
            </td>
            <td style="padding: 10px; color: #000000; text-align: right; border-style: none;" align="right"
                valign="top">
                <h3>{{$settlement['name']}}</h3>
                <p>
                    @if(!$settlement['on_check_out'])
                        {{  __('accounting_pdfs.bookings_between', [
                            'start' => Carbon\Carbon::parse($settlement['start'])->format('d M Y'),
                            'finish' => Carbon\Carbon::parse($settlement['end'])->format('d M Y')]) }}
                    @else
                        {{  __('accounting_pdfs.bookings_between_checkout', [
                            'start' => Carbon\Carbon::parse($settlement['start'])->format('d M Y'),
                            'finish' => Carbon\Carbon::parse($settlement['end'])->format('d M Y')]) }}
                    @endif
                        <br>
                        {{ __('accounting_pdfs.income_calculation_type', ['type' => $settlement['on_net_income'] ? __('accounting_pdfs.net_income') : __('accounting_pdfs.gross_income')]) }}
                </p>
            </td>
        </tr>
    </table>

    <h4>{{ __('accounting_pdfs.rentals_included') }}</h4>
    <p style="text-transform: uppercase">{{implode(", ", array_values(array_merge($settlement['sorted_rentals'],$settlement['rentals_with_no_bookings'])))}}</p>

    <h4>{{__('accounting_pdfs.sources_included')}}</h4>
    <p>{{  empty($settlement['sources']) ? ucfirst(__('booking_page.all')) : implode(", ", array_values($settlement['included_channel_names'])) }}</p>

    <h3 style="margin-bottom: 20px">{{ __('accounting_pdfs.booking_breakdown.booking_breakdown') }}</h3>

    <!-- Booking breakdown -->
    @foreach($settlement['sorted_rentals'] as $rental)
        @foreach($settlement['bookings_map_by_rental'][$rental] as $booking_id)
            @php
                $booking = collect($settlement['bookings'])->firstWhere('id', $booking_id);
                $status = App\Enum\BookingStatusEnum::tryFrom($booking['status'])->getStatusPublicName();
                $start = Carbon\Carbon::parse($booking['start_at'])->format('d M Y');
                $end = Carbon\Carbon::parse($booking['end_at'])->format('d M Y');
                $nights = $booking['nights'];

                $client = '-';
                if(isset($booking['client']) && isset($booking['client']['fullname'])) $client = $booking['client']['fullname'];

                $hasNoMoney = !isset($booking['final_price']) || $booking['final_price'] == 0.0;
                $final_price = $booking['final_price'] ?? 0.0;
                $commission = $booking['commission'] ?? 0.0;
                $initial_price = $booking['initial_price'] ?? 0.0;
                $final_rental_price = $booking['final_rental_price'] ?? 0.0;

                $discount_amount = 0.0;
                if(isset($booking['discount']) && !empty($booking['discount']))
                {
                    $disc_value =$booking['discount'];
                    if(ninja_str_contains($booking['discount'], "%")){
                        $disc = doubleval(str_replace("%", '',$disc_value ));
                        if($disc > 0) $discount_amount = $booking['initial_price'] * $disc / 100;
                        else $discount_amount = 0.0;
                    }
                    else $discount_amount = $disc_value;
                }
                $discount_amount = abs(doubleval($discount_amount));

                $taxes_value = array_reduce($booking['taxes'], function ($prev, $curr) {
                    return doubleval($prev) + ($curr['tax_included_in_price'] ? 0.0 : doubleval($curr['amount']));
                }, 0.0);

                $income = $final_price - $taxes_value;
                $net_income = $final_price - $commission;
            @endphp

            <h4>{{ __('accounting_pdfs.booking_breakdown.booking.title', [
            'rental' => strtoupper($rental),
            'status' => $status,
            'start' => $start,
            'end' => $end,
            ]) }}</h4>

            <p>{{ __('accounting_pdfs.booking_breakdown.booking.reference', [
            'reference' => $booking['reference'],
            'source' => $booking['source'],
            'client' => $client,
            'guests' => $booking['guests'],
            'nights' => $nights,
            ]) }}</p>

            @if($hasNoMoney)
                @if(App\Enum\BookingStatusEnum::tryFrom($booking['status'])->isUnavailable())
                    <p>{{ __('accounting_pdfs.booking_breakdown.booking.blocking') }}</p>
                @else
                    <p>{{ __('accounting_pdfs.booking_breakdown.booking.no_money') }}</p>
                @endif
            @else
                <table class="">
                    <thead class="table-dark">
                    <tr>
                        <th class="min">{{ __('accounting_pdfs.concept') }}</th>
                        <th>{{ __('accounting_pdfs.description') }}</th>
                        <th class="min" style="text-align: right;">{{ __('accounting_pdfs.amount') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="min"
                            style="font-weight: bold;">{{ __('accounting_pdfs.booking_breakdown.booking.initial_price') }}</td>
                        <td>{{ __('accounting_pdfs.rental_price') }}</td>
                        <td class="min"
                            style="text-align: right;"><x-format-amount :currency="$settlement['currency']" :amount="$initial_price"/></td>
                    </tr>

                    @if($discount_amount != null && $discount_amount != 0.0)
                        <tr>
                            <td class="min"
                                style="font-weight: bold;">{{ __('accounting_pdfs.booking_breakdown.booking.discount') }}</td>
                            <td>{{ __('accounting_pdfs.booking_breakdown.booking.discount_applied') }}</td>
                            <td class="min"
                                style="text-align: right;"><x-format-amount :currency="$settlement['currency']" :amount="$discount_amount * -1"/></td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: right; font-weight: bold;">{{ __('accounting_pdfs.booking_breakdown.booking.final_rental_price') }}</td>
                            <td class="min"
                                style="text-align: right; font-weight: bold;"><x-format-amount :currency="$settlement['currency']" :amount="$final_rental_price"/></td>
                        </tr>
                    @endif

                    @if(!empty($booking['fees']))
                        @foreach($booking['fees'] as $fee)
                            <tr>
                                <td class="min"
                                    style="font-weight: bold;">{{ __('accounting_pdfs.booking_breakdown.booking.fee') }}</td>
                                @if($fee['times_booked'] > 1)
                                    <td>{{ucwords(strtolower($fee['name']))}} {{$fee['times_booked']}}
                                        x <x-format-amount :currency="$settlement['currency']" :amount="$fee['price']"/></td>
                                @endif
                                @if($fee['times_booked'] <= 1)
                                    <td>{{ucwords(strtolower($fee['name']))}}</td>
                                @endif
                                <td class="min"
                                    style="text-align: right;"><x-format-amount :currency="$settlement['currency']" :amount="$fee['price'] * $fee['times_booked']"/></td>
                            </tr>
                        @endforeach
                    @endif

                    @if(!empty($booking['taxes']))
                        <tr>
                            <td colspan="2" style="text-align: right; font-weight: bold;">{{ __('accounting_pdfs.booking_breakdown.booking.price_before_taxes') }}</td>
                            <td class="min"
                                style="text-align: right; font-weight: bold;"><x-format-amount :currency="$settlement['currency']" :amount="$income"/></td>
                        </tr>
                        @foreach($booking['taxes'] as $tax)
                            <tr>
                                <td class="min"
                                    style="font-weight: bold;">{{ __('accounting_pdfs.booking_breakdown.booking.tax') }}
                                </td>
                                <td>
                                    @if(empty(data_get($tax, 'percentage', 0)))
                                        {{ucwords(strtolower($tax['name']))}}
                                    @else
                                        {{__('accounting_pdfs.booking_breakdown.booking.tax.excluded', [
                                    'tax' => ucwords(strtolower($tax['name'])),
                                    'percentage' => data_get($tax, 'percentage', 0),
                                    ])}}
                                    @endif
                                    @if($tax['tax_included_in_price'])
                                        {{" " . __('accounting_pdfs.booking_breakdown.booking.tax.included')}}
                                    @endif
                                </td>
                                <td class="min"
                                    style="text-align: right;"><x-format-amount :currency="$settlement['currency']" :amount="$tax['amount']"/>
                                </td>
                            </tr>
                        @endforeach
                    @endif

                    <tr>
                        <td colspan="2" style="text-align: right; font-weight: bold;">{{ __('accounting_pdfs.booking_breakdown.booking.final_price') }}</td>
                        <td class="min"
                            style="text-align: right; font-weight: bold;"><x-format-amount :currency="$settlement['currency']" :amount="$final_price"/></td>
                    </tr>
                    @if($settlement['on_net_income'])
                        <tr>
                            <td colspan="2" style="text-align: right; font-weight: bold;">{{ __('accounting_pdfs.commission') }}</td>
                            <td class="min"
                                style="text-align: right; font-weight: bold;"><x-format-amount :currency="$settlement['currency']" :amount="$commission * -1"/></td>
                        </tr>

                        <tr>
                            <td colspan="2" style="text-align: right; font-weight: bold;">{{ __('accounting_pdfs.income') }}</td>
                            <td class="min"
                                style="text-align: right; font-weight: bold;"><x-format-amount :currency="$settlement['currency']" :amount="$net_income"/></td>
                        </tr>
                    @endif
                    </tbody>
                </table>

            @endif
        @endforeach
    @endforeach
</div>
</body>
</html>