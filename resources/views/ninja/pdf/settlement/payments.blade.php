<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">

    <title>Payments Sent Out</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background: #fff none;
            font-family: <PERSON><PERSON><PERSON><PERSON>, sans-serif, serif;
            font-size: 10px;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        /* Center tables for demo */
        table {
            margin-bottom: 30px;
        }

        /* Default Table Style */
        table {
            width: 100%;
            color: #333;
            background: white;
            border: 1px solid grey;
            font-size: 10px;
            border-collapse: collapse;
        }

        table thead th,
        table tfoot th {
            color: #777;
            background: rgba(0, 0, 0, .1);
        }

        table caption {
            padding: 3px;
        }

        table th,
        table td {
            padding: 3px;
            /* border: 1px solid lightgrey; */
            width: auto;
        }

        table th.min, table td.min {
            width: 1%;
            white-space: nowrap;
            padding-left: 10px;
            padding-right: 10px;
        }

        h1 {
            color: #5D6975;
            font-size: 2.4em;
            line-height: 1.4em;
            font-weight: normal;
        }

        h4 {
            border-top: 1px solid #5D6975;
            border-bottom: 1px solid #5D6975;
            color: #5D6975;
            font-size: 1.4em;
            font-weight: normal;
            text-align: center;
            text-transform: uppercase;
            margin: 0 0 10px 0;
        }

        /* Zebra Table Style */
        [data-table-theme*=zebra] tbody tr:nth-of-type(odd) {
            background: rgba(0, 0, 0, .05);
        }

        [data-table-theme*=zebra][data-table-theme*=dark] tbody tr:nth-of-type(odd) {
            background: rgba(255, 255, 255, .05);
        }

        /* Dark Style */
        [data-table-theme*=dark] {
            color: #ddd;
            background: #333;
            font-size: 12px;
            border-collapse: collapse;
        }

        [data-table-theme*=dark] thead th,
        [data-table-theme*=dark] tfoot th {
            color: #aaa;
            background: rgba(0255, 255, 255, .15);
        }

        [data-table-theme*=dark] caption {
            padding: .5em;
        }

        [data-table-theme*=dark] th,
        [data-table-theme*=dark] td {
            padding: .5em;
            border: 1px solid grey;
        }


        table tr:nth-child(2n-1) td {
            background: #F5F5F5;
        }

    </style>
</head>

<body>
<div>
    <header>
        <table style="width: 100%; padding: 0; border: none; margin:0 auto;border-style: none;border-bottom: 1px solid #5D6975; margin: 0 0 20px 0;" cellspacing="0">
            <tbody>
            <tr>
                <td style="color: #000000; text-align: left; background-color: white; vertical-align: middle;" valign="top">
                    <h1>{{ __('accounting_pdfs.payments.title') }}</h1>
                </td>
                <td style="color: #000000; text-align: right; background-color: white;" valign="top">
                    @if($team->hasValidImgixPhoto())
                        <div>
                            <img src="{{$team->imgixPhoto()}}" style="width: 100px; height: 100px;" alt="Team photo">
                        </div>
                    @endif
                </td>
            </tr>
            </tbody>
        </table>

    </header>
    <!-- <div class="header">
        <h1>{{ __('accounting_pdfs.payments.title') }}</h1>
        @if($team->hasValidImgixPhoto())
        <img src="{{$team->imgixPhoto()}}" style="width: 100px; height: 100px;" alt="Team photo">

    @endif
    </div> -->
    <p>{{ __('accounting_pdfs.payments.intro', [
    'statement' => strtoupper($settlement['settlement_name']),
    ]) }}</p>

    <!-- Rental Income Section -->
    @foreach($settlement['payout_data'] as $payment)
        @php
            $bookings = doubleval($payment['bookings_value']);
            $value = doubleval($payment['value']);
            $taxes = doubleval($payment['taxes_value']);
            $fees = doubleval($payment['fees_value']);
            $others = doubleval($payment['others_value']);
            $expenses = doubleval($payment['expenses_value']);
            $currency = $payment['currency'];
            $payee_name = $payment['payee_name'];
            $paid_amount = doubleval($payment['paid_amount']);
            $payee_email = $payment['payee_email'];
            $payee_notes = new Illuminate\Support\HtmlString(nl2br(e($payment['payee_notes'])));
            $payee_information = new Illuminate\Support\HtmlString(nl2br(e($payment['payee_information'])));
            $partial_payments = $payment['partial_payouts'];
            $left_to_pay = (doubleval($value) - doubleval($paid_amount));
        @endphp
        <h4>{{strtoupper($payee_name)}}:
            <x-format-amount :currency="$currency" :amount="$value"/>
        </h4>
        <p>
            Email: {{$payee_email}}
            @if($payee_notes != null && strlen($payee_notes) > 0)
                <br>
                <b>{{ __('accounting_pdfs.payments.legal_info') }}</b><br>
                {{new Illuminate\Support\HtmlString($payee_notes)}}
            @endif
            @if($payee_information != null && strlen($payee_information) > 0)
                <br>
                <b>{{ __('accounting_pdfs.payments.extra_info') }}</b><br>
                {{new Illuminate\Support\HtmlString($payee_information)}}
            @endif
        </p>
</div>

<div>
    <table class="">
        <thead class="table-dark">
        <tr>
            <th class="min">{{ __('accounting_pdfs.concept') }}</th>
            <th>{{ __('accounting_pdfs.description') }}</th>
            <th class="min" style="text-align: right;">{{ __('accounting_pdfs.amount') }}</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td class="min"
                style="font-weight: bold;">{{ __('accounting_pdfs.payments.table.bookings.concept') }}</td>
            <td>{{ __('accounting_pdfs.payments.table.bookings.description') }}</td>
            <td class="min" style="text-align: right;">
                <x-format-amount :currency="$currency" :amount="$bookings"/>
            </td>
        </tr>

        @if($fees && $fees > 0)
            <tr>
                <td class="min" style="font-weight: bold;">{{ __('accounting_pdfs.fees') }}</td>
                <td>{{ __('accounting_pdfs.payments.table.fees.description') }}</td>
                <td class="min" style="text-align: right;">
                    <x-format-amount :currency="$currency" :amount="$fees"/>
                </td>
            </tr>
        @endif

        @if($taxes && $taxes > 0)
            <tr>
                <td class="min" style="font-weight: bold;">{{ __('accounting_pdfs.taxes') }}</td>
                <td>{{ __('accounting_pdfs.payments.table.taxes.description') }}</td>
                <td class="min" style="text-align: right;">
                    <x-format-amount :currency="$currency" :amount="$taxes"/>
                </td>
            </tr>
        @endif

        @if($others && $others > 0)
            <tr>
                <td class="min"
                    style="font-weight: bold;">{{ __('accounting_pdfs.payments.table.others.concept') }}</td>
                <td>{{ __('accounting_pdfs.payments.table.others.description') }}</td>
                <td class="min" style="text-align: right;">
                    <x-format-amount :currency="$currency" :amount="$others"/>
                </td>
            </tr>
        @endif

        @if($expenses && $expenses < 0)
            <tr>
                <td class="min">{{ __('accounting_pdfs.payments.table.expenses.concept') }}</td>
                <td>{{ __('accounting_pdfs.payments.table.expenses.description') }}</td>
                <td class="min"
                    style="text-align: right; font-weight: bold;">
                    <x-format-amount :currency="$currency" :amount="$expenses"/>
                </td>
            </tr>
        @endif

        <tr>
            <td class="min"></td>
            <td style="text-align: right; font-weight: bold;">{{ __('accounting_pdfs.TOTAL') }}</td>
            <td class="min"
                style="text-align: right; font-weight: bold;">
                <x-format-amount :currency="$currency" :amount="$value"/>
            </td>
        </tr>

        @if($paid_amount && $paid_amount > 0)
            <tr>
                <td class="min"></td>
                <td style="text-align: right;">{{ __('accounting_pdfs.payments.table.already_paid') }}</td>
                <td class="min"
                    style="text-align: right; font-weight: bold;">
                    <x-format-amount :currency="$currency" :amount="$paid_amount"/>
                </td>
            </tr>
        @endif

        @if($left_to_pay && $left_to_pay > 0)
            <tr>
                <td class="min"></td>
                <td style="text-align: right;">{{ __('accounting_pdfs.payments.table.left_pay') }}</td>
                <td class="min"
                    style="text-align: right; font-weight: bold;">
                    <x-format-amount :currency="$currency" :amount="$left_to_pay"/>
                </td>
            </tr>
        @endif
        </tbody>
    </table>
</div>

@if(!empty($partial_payments))
    <div>
        <p>{{ __('accounting_pdfs.payments.sent_payments.text') }}</p>
        <ol>
            @foreach($partial_payments as $partial)
                <li>{{ __('accounting_pdfs.payments.sent_payments.amount', [
                    'amount' => \App\Actions\Support\Currencies\FormatCurrencyAction::run($currency, $partial['amount']),
                    'date' => Carbon\Carbon::parse($partial['paid_at'])->format('d M Y'),
                    ]) }}</li>
            @endforeach
        </ol>
    </div>
@endif
@endforeach
</body>
</html>