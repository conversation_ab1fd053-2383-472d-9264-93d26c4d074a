<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">

    <title>{{__('accounting_pdfs.statement_summary.title')}}</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background: #fff none;
            font-family: <PERSON>ja<PERSON><PERSON>, sans-serif, serif;
            font-size: 10px;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        /* Center tables for demo */
        table {
            margin-bottom: 30px;
        }

        /* Default Table Style */
        table {
            width: 100%;
            color: #333;
            background: white;
            border: 1px solid grey;
            font-size: 9px;
            border-collapse: collapse;
        }

        table thead th,
        table tfoot th {
            color: #777;
            background: rgba(0, 0, 0, .1);
        }

        table caption {
            padding: .5em;
        }

        table th,
        table td {
            padding: .5em;
            border: 1px solid lightgrey;
            width: auto;
        }

        table th.min, table td.min {
            width: 1%;
            white-space: nowrap;
            padding-left: 10px;
            padding-right: 10px;
        }

        .rental-table th, .rental-table td {
            /* width: 1%; */
            /* white-space: nowrap; */
            padding-left: 10px;
            padding-right: 10px;
        }

        h1 {
            border-top: 1px solid #5D6975;
            border-bottom: 1px solid #5D6975;
            color: #5D6975;
            font-size: 2.4em;
            line-height: 1.4em;
            font-weight: normal;
            text-align: center;
            margin: 0 0 20px 0;
        }

        /* Zebra Table Style */
        [data-table-theme*=zebra] tbody tr:nth-of-type(odd) {
            background: rgba(0, 0, 0, .05);
        }

        [data-table-theme*=zebra][data-table-theme*=dark] tbody tr:nth-of-type(odd) {
            background: rgba(255, 255, 255, .05);
        }

        /* Dark Style */
        [data-table-theme*=dark] {
            color: #ddd;
            background: #333;
            font-size: 12px;
            border-collapse: collapse;
        }

        [data-table-theme*=dark] thead th,
        [data-table-theme*=dark] tfoot th {
            color: #aaa;
            background: rgba(0255, 255, 255, .15);
        }

        [data-table-theme*=dark] caption {
            padding: .5em;
        }

        [data-table-theme*=dark] th,
        [data-table-theme*=dark] td {
            padding: .5em;
            border: 1px solid grey;
        }

        .container {
            padding: 30px;
        }

        .font-weight-bold {
            font-weight: bold;
        }
    </style>
</head>

<body>
<div class="container">
    <h1>{{__('accounting_pdfs.statement_summary.title')}}</h1>

    <table style="width: 100%; padding: 0; margin:0 auto;border-style: none;" cellspacing="0">
        <tr>
            <td style="padding: 10px; color: #000000; text-align: left; border-style: none;" align="left" valign="top">
                @if($team->hasValidImgixPhoto())
                    <div style="margin-top: 10px;">
                        <img src="{{$team->imgixPhoto()}}" style="width: 100px; height: 100px;" alt="Team photo">
                    </div>
                @endif
                <p>{{$team->email}}</p>
                <h3>{{ $team->name }}</h3>
            </td>
            <td style="padding: 10px; color: #000000; text-align: right; border-style: none;" align="right" valign="top">
                <h3>{{$settlement['name']}}</h3>
                <p>
                    @if(!$settlement['on_check_out'])
                        {{  __('accounting_pdfs.bookings_between', [
                            'start' => Carbon\Carbon::parse($settlement['start'])->format('d M Y'),
                            'finish' => Carbon\Carbon::parse($settlement['end'])->format('d M Y')]) }}
                    @else
                        {{  __('accounting_pdfs.bookings_between_checkout', [
                            'start' => Carbon\Carbon::parse($settlement['start'])->format('d M Y'),
                            'finish' => Carbon\Carbon::parse($settlement['end'])->format('d M Y')]) }}
                    @endif
                    <br>
                    {{ __('accounting_pdfs.income_calculation_type', ['type' => $settlement['on_net_income'] ? __('accounting_pdfs.net_income') : __('accounting_pdfs.gross_income')]) }}
                </p>
            </td>
        </tr>
    </table>

    <h4>{{__('accounting_pdfs.rentals_included')}}</h4>
    <p style="text-transform: uppercase">{{implode(", ", array_values(array_merge($settlement['sorted_rentals'],$settlement['rentals_with_no_bookings'])))}}</p>

    <h4>{{__('accounting_pdfs.sources_included')}}</h4>
    <p>{{  empty($settlement['sources']) ? ucfirst(__('booking_page.all')) : implode(", ", array_values($settlement['included_channel_names'])) }}</p>

    <h4 style="margin-bottom: 20px">{{__('accounting_pdfs.statement_summary.details')}}</h4>
    <ul>
        <li>{{__('accounting_pdfs.statement_summary.details.headings.income_rentals')}}
            <x-format-amount :currency="$settlement['currency']" :amount="$settlement['rentals_income']"/>
        </li>
        <li>{{__('accounting_pdfs.statement_summary.details.headings.income_fees')}}
            <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_fees']"/>
        </li>
        <li>{{__('accounting_pdfs.statement_summary.details.headings.income_taxes')}}
            <x-format-amount :currency="$settlement['currency']" :amount="$settlement['taxes_income']"/>
        </li>
        @if($settlement['on_net_income'])
            <li>{{__('accounting_pdfs.statement_summary.details.headings.commissions_paid')}}
                <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_commissions']"/>
            </li>
        @endif
        <li>{{__('accounting_pdfs.statement_summary.details.headings.income')}}  {{--Could be net or gross--}}
            <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_income']"/>
        </li>
    </ul>

    <table class="rental-table" style="width: 100%;">
        <thead class="table-dark">
        <tr>
            <th style="white-space: normal; word-wrap: break-word; width: 80px; max-width: 80px;">{{__('accounting_pdfs.statement_summary.details.table.rental_name')}}</th>
            <th style="white-space: normal; width: 40px; word-wrap: break-word;  max-width: 40px; text-align: right;"
                >{{__('accounting_pdfs.statement_summary.details.table.bookings')}}</th>
            <th style="white-space: normal; width: 40px; word-wrap: break-word;  max-width: 40px; text-align: right;"
                >{{__('accounting_pdfs.statement_summary.details.table.nights')}}</th>
            <th style="white-space: normal; width: 25px; word-wrap: break-word;  max-width: 25px; text-align: right;"
                >{{__('accounting_pdfs.statement_summary.details.table.guests')}}</th>
            <th class="" style="text-align: right;">{{__('accounting_pdfs.rental_price')}}</th>
            <th class="" style="text-align: right;">{{__('accounting_pdfs.fees')}}</th>
            <th class="" style="text-align: right;">{{__('accounting_pdfs.taxes')}}</th>
            @if($settlement['on_net_income'])
                <th class=""
                    style="text-align: right;">{{__('accounting_pdfs.statement_summary.details.table.channels')}}</th>
            @endif
            <th class="" style="text-align: right;">{{__('accounting_pdfs.income')}}</th>
        </tr>
        </thead>
        <tbody>
        <!-- Rental Income Section -->
        @foreach($settlement['sorted_rentals'] as $rental)
            <tr class="{{$settlement['income_by_rental'][$rental]['final_rental_prices_total'] == 0? 'unavailable': ''}}">
                @php
                    $income = $settlement['income_by_rental'][$rental];
                @endphp
                <td style="">{{ ucwords(strtolower(trim($rental))) }}</td>
                <td style="text-align: right;">{{$income['bookings']}}</td>
                <td style="text-align: right;">{{$income['nights']}}</td>
                <td style="text-align: right;">{{$income['guests']}}</td>
                <td class=""
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$income['final_rental_prices_total']"/>
                </td>
                <td class=""
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$income['fees']"/>
                </td>
                <td class=""
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$income['taxes']"/>
                </td>
                @if($settlement['on_net_income'])
                    <td class=""
                        style="text-align: right;">
                        <x-format-amount :currency="$settlement['currency']" :amount=" $income['commissions']"/>
                    </td>
                @endif
                <td class=""
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount=" $income['bookings_income']"/>
                </td>
            </tr>
        @endforeach
        <!-- Include rentals with no bookings here -->
        @if (!empty($settlement['rentals_with_no_bookings']))
            @foreach($settlement['rentals_with_no_bookings'] as $rental)
                <tr>
                    <td style="">{{ucwords(strtolower(trim($rental)))}}</td>
                    <td style="text-align: right;">-</td>
                    <td style="text-align: right;">-</td>
                    <td style="text-align: right;">-</td>
                    <td class="" style="text-align: right;">-</td>
                    <td class="" style="text-align: right;">-</td>
                    <td class="" style="text-align: right;">-</td>
                    <td class="" style="text-align: right;">-</td>
                    <td class="" style="text-align: right;">-</td>
                </tr>
            @endforeach
        @endif
        <tr class="table-warning font-weight-bold">
            <td style="">{{__('accounting_pdfs.TOTAL')}}</td>
            <td style="text-align: right;">{{$settlement['total_bookings']}}</td>
            <td style="text-align: right;">{{$settlement['nights']}}</td>
            <td style="text-align: right;">{{$settlement['guests']}}</td>
            <td class=""
                style="text-align: right;">
                <x-format-amount :currency="$settlement['currency']" :amount="$settlement['rentals_income']"/>
            </td>
            <td class=""
                style="text-align: right;">
                <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_fees']"/>
            </td>
            <td class=""
                style="text-align: right;">
                <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_taxes']"/>
            </td>
            @if($settlement['on_net_income'])
                <td class=""
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_commissions']"/>
                </td>
            @endif
            <td class=""
            style="text-align: right;">
                <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_income']"/>
            </td>
        </tr>
        </tbody>
    </table>

    @if ( !empty($settlement['fees']) )
        <h4>{{__('accounting_pdfs.statement_summary.fees_breakdown')}}</h4>
        <table class="table table-striped table-sm accounting-table">
            <thead class="table-dark">
            <tr>
                <th>{{__('accounting_pdfs.name')}}</th>
                <th class="min"
                    style="text-align: right;">{{__('accounting_pdfs.statement_summary.fees_breakdown.times_booked')}}</th>
                <th class="min"
                    style="text-align: right;">{{__('accounting_pdfs.statement_summary.fees_breakdown.average_price')}}</th>
                <th class="min" style="text-align: right;">{{__('accounting_pdfs.income')}}</th>
            </tr>
            </thead>
            <tbody>
            @foreach ($settlement['fees'] as $fee)
                <tr>
                    <td class="font-weight-bold">{{ ucwords(strtolower(trim($fee['name']))) }}</td>
                    <td class="min" style="text-align: right;">{{ $fee['times_booked'] }}</td>
                    <td class="min"
                        style="text-align: right;">
                        <x-format-amount :currency="$settlement['currency']" :amount=" $fee['price']/$fee['times_booked']"/>
                    </td>
                    <td class="min"
                        style="text-align: right;">
                        <x-format-amount :currency="$settlement['currency']" :amount=" $fee['price']"/>
                    </td>
                </tr>
            @endforeach
            <tr class="table-warning font-weight-bold">
                <td>{{__('accounting_pdfs.TOTAL')}}</td>
                <td class="min" style="text-align: right;">{{ $settlement['fees_times_booked'] }}</td>
                <td class="min"
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$settlement['fees_average_price']"/>
                </td>
                <td class="min"
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_fees']"/>
                </td>
            </tr>
            </tbody>
        </table>
    @endif
    @if ( !empty($settlement['taxes']) )
        <h4>{{__('accounting_pdfs.statement_summary.taxes_breakdown')}}</h4>
        <table class="table table-striped table-sm accounting-table">
            <thead class="table-dark">
            <tr>
                <th>{{__('accounting_pdfs.name')}}</th>
                <th class="min"
                    style="text-align: right;">{{__('accounting_pdfs.statement_summary.taxes_breakdown.included_in_price')}}</th>
                <th class="min"
                    style="text-align: right;">{{__('accounting_pdfs.statement_summary.taxes_breakdown.tax_price')}}</th>
                <th class="min" style="text-align: right;">{{__('accounting_pdfs.income')}}</th>
            </tr>
            </thead>
            <tbody>
            @foreach( $settlement['taxes'] as $tax )
                <tr>
                    <td class="font-weight-bold">{{ ucwords(strtolower(trim($tax['name']))) }}</td>
                    <td class="min"
                        style="text-align: right;">{{ ($tax['tax_included_in_price']) ? 'YES': 'NO'}}</td>
                    <td class="min"
                        style="text-align: right;">
                        <x-format-amount :currency="$settlement['currency']" :amount=" $tax['amount']"/>
                    </td>
                    <td class="min"
                        style="text-align: right;">{{ ($tax['tax_included_in_price']) ? 0 : \App\Actions\Support\Currencies\FormatCurrencyAction::run($settlement['currency'],  $tax['amount']) }}</td>
                </tr>
            @endforeach
            <tr class="table-warning font-weight-bold">
                <td class="font-weight-bold">{{__('accounting_pdfs.TOTAL')}}</td>
                <td class="min" style="text-align: right;">-</td>
                <td class="min"
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$settlement['taxes_prices']"/>
                </td>
                <td class="min"
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$settlement['taxes_income']"/>
                </td>
            </tr>
            </tbody>
        </table>
    @endif
    @if ( !empty($settlement['commissions']) )
        <h4>{{__('accounting_pdfs.statement_summary.commissions_breakdown')}}</h4>
        <table class="table table-striped table-sm accounting-table">
            <thead class="table-dark">
            <tr>
                <th>{{__('accounting_pdfs.statement_summary.commissions_breakdown.imposed_by')}}</th>
                <th class="min"
                    style="text-align: right;">{{__('accounting_pdfs.statement_summary.commissions_breakdown.commission_price')}}</th>
            </tr>
            </thead>
            <tbody>
            @foreach($settlement['commissions'] as $commission => $value)
                <tr>
                    <td class="font-weight-bold">{{ ucwords($commission) }}</td>
                    <td class="min"
                        style="text-align: right;">
                        <x-format-amount :currency="$settlement['currency']" :amount="$value"/>
                    </td>
                </tr>
            @endforeach
            <tr class="table-warning font-weight-bold">
                <td class="font-weight-bold">{{__('accounting_pdfs.TOTAL')}}</td>
                <td class="min"
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_commissions']"/>
                </td>
            </tr>
            </tbody>
        </table>
    @endif

    @if ( !empty($settlement['payment_methods']) )
        <h4>{{__('accounting_pdfs.statement_summary.client_payment_methods')}}</h4>
        <table class="table table-striped table-sm accounting-table">
            <thead class="table-dark">
            <tr>
                <th>{{__('accounting_pdfs.name')}}</th>
                <th class="min"
                    style="text-align: right;">{{__('accounting_pdfs.amount')}}</th>
            </tr>
            </thead>
            <tbody>
            @foreach($settlement['payment_methods'] as $method => $value)
                <tr>
                    <td class="font-weight-bold">{{ $method }}</td>
                    <td class="min"
                        style="text-align: right;">
                        <x-format-amount :currency="$settlement['currency']" :amount="$value"/>
                    </td>
                </tr>
            @endforeach
            <tr class="table-warning font-weight-bold">
                <td class="font-weight-bold">{{__('accounting_pdfs.statement_summary.client_payment_methods.total_paid_by_clients')}}</td>
                <td class="min"
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_payed']"/>
                </td>
            </tr>
            </tbody>
        </table>
    @endif

    @if ( !empty($settlement['income_by_channel']) )
        <h4>{{__('accounting_pdfs.statement_summary.income_by_channel')}}</h4>
        <table class="table table-striped table-sm accounting-table">
            <thead class="table-dark">
            <tr>
                <th>{{__('accounting_pdfs.name')}}</th>
                <th class="min"
                    style="text-align: right;">{{$settlement['on_net_income'] ?  __('accounting_pdfs.net_income') : __('accounting_pdfs.gross_income')}}</th>
            </tr>
            </thead>
            <tbody>
            @foreach($settlement['income_by_channel'] as $channel => $value)
                <tr>
                    <td class="font-weight-bold">{{ $channel }}</td>
                    <td class="min"
                        style="text-align: right;">
                        <x-format-amount :currency="$settlement['currency']" :amount="$value['channelIncome']"/>
                    </td>
                </tr>
            @endforeach
            <tr class="table-warning font-weight-bold">
                <td class="font-weight-bold">{{__('accounting_pdfs.TOTAL')}}</td>
                <td class="min"
                    style="text-align: right;">
                    <x-format-amount :currency="$settlement['currency']" :amount="$settlement['total_channels_income']"/>
                </td>
            </tr>
            @if ($team->flavor_id != \App\Flavors\TokeetFlavor::FLAVOR_ID)
                <tr>
                    <td class="font-weight-bold">{{__('accounting_pdfs.statement_summary.income_by_channel.pending_payments_by_clients')}}</td>
                    <td class="min"
                        style="text-align: right;">
                        <x-format-amount :currency="$settlement['currency']" :amount="$settlement['un_payed']"/>
                    </td>
                </tr>
            @endif
            </tbody>
        </table>
    @endif
</div>
</body>
</html>