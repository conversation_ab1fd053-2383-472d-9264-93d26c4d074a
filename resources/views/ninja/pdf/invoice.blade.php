<!DOCTYPE html>
<html lang="en">
@php
/** @var App\Models\PayeeInvoice $invoice */
/** @var App\Models\Payee $receiver */
/** @var App\Models\Payee $issuer */

$issuer_notes = new Illuminate\Support\HtmlString(nl2br(e($issuer->payout_notes)));
$receiver_notes = new Illuminate\Support\HtmlString(nl2br(e($receiver->payout_notes)));

@endphp

<head>
    <meta charset="utf-8">
    <title>Invoice {{$invoice->invoice_id}}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .clearfix:after {
            content: "";
            display: table;
            clear: both;
        }

        a {
            color: #5D6975;
            text-decoration: underline;
        }

        body {
            position: relative;
            color: #001028;
            background: #FFFFFF;
            font-family: Deja<PERSON>u Sans, sans-serif, serif;
            font-size: 10px;
        }

        header {
            padding: 0px 0 10px 0;
        }

        h1 {
            color: #5D6975;
            font-size: 2.4em;
            line-height: 1.4em;
            font-weight: normal;
            text-align: left;
            margin: 0 0 0px 0;
        }


        .issue-date {
            margin-top: 2em;
            margin-bottom: 2em;
        }

        #project div,
        #company div {
            white-space: nowrap;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            margin-bottom: 20px;
            position: relative;
        }

        table th,
        table td {
            text-align: center;
        }

        table th {
            padding: 10px;
            color: #5D6975;
            background-color: #D9E1F2;
            white-space: nowrap;
            font-weight: bold;
        }

        table .service,
        table .desc {
            text-align: left;
        }

        table td {
            padding: 10px;
            text-align: right;
        }

        table td.service,
        table td.desc {
            vertical-align: top;
        }

        table th.unit,
        table td.unit {
            text-align: right;
        }

        table td.total,
        table th.total {
            font-weight: bold;
        }

        table td.grand {
            border-top: 1px solid #5D6975;
        }

        .rental-name {
            font-weight: bold;
            text-align: left;
        }

        table tr:nth-child(2n-1) {
            background: #F5F5F5;
        }

        th:first-child {
            border-top-left-radius: 6px;
        }

        th:last-child {
            border-top-right-radius: 6px;
        }

        tr:last-child td:first-child {
            border-bottom-left-radius: 6px;
        }

        tr:last-child td:last-child {
            border-bottom-right-radius: 6px;
        }

        tr:last-child td {
            background-color: rgb(248, 217, 154, .25);
        }

        #notices .notice {
            font-size: 12px;
            margin-top: 10px;
        }
    </style>
</head>

<body>
    <header class="clearfix" style="position:relative;">
        <div style="position: absolute;top: -110px;bottom:0;background:#eef4f9;left:-110px;right:-110px;height: 220px;"></div>
        <table style="width: 100%; padding: 0; margin:0 auto;border-style: none;border-bottom: 1px solid #5D6975; margin: 0 0 20px 0;" cellspacing="0">
            <tbody>
                <tr>
                    <td style="padding: 0 0 10px 0; color: #000000; text-align: left; vertical-align: middle;background: transparent;" align="left" valign="top">
                        <h1>{{ __('accounting_pdfs.invoice.title', ['number' => $invoice->invoice_id]) }}</h1>
                    </td>
                    <td style="padding: 0 0 10px 0; color: #000000; text-align: right;background: transparent;" align="right" valign="top">
                        @if($team->hasValidImgixPhoto())
                        <div>
                            <img src="{{$team->imgixPhoto()}}" style="width: 100px; height: 100px;" alt="Team photo">
                        </div>
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>

    </header>
    <table style="width: 100%; padding: 0; margin:0 auto;border-style: none;" cellspacing="0">
        <tr>
            <td style="width:60%; padding: 10px; color: #000000; text-align: left; background-color: white;" align="left" valign="top">
                <h3 style="margin-top: 0; margin-bottom: 10px;">{{ __('accounting_pdfs.invoice.client', ['client' => $receiver->name]) }}</h3>
                <div>{{$receiver_notes}}</div>
            </td>

            <td style="width:40%; padding: 10px; color: #000000; text-align: left; background-color: white;" align="left" valign="top">
                <h3 style="margin-top: 0; margin-bottom: 10px;">{{ __('accounting_pdfs.invoice.issuer', ['issuer' => $issuer->name]) }}</h3>
                <div>{{$issuer_notes}}</div>
                <h4 class="issue-date">{{ __('accounting_pdfs.invoice.date', ['date' => $invoice->issued_at->toFormattedDateString()]) }}</h4>
            </td>
        </tr>
    </table>

    {{--Rental Price table. Do not show unless there are Rental Prices to show--}}
    @includeWhen(count($payout['bookings']) > 0, 'ninja.components.invoice.bookings_table', ['payout' => $payout,])

    {{--Fees table. Do not show unless there are Fees to show--}}
    @includeWhen(count($payout['fees']) > 0, 'ninja.components.invoice.fees_table', ['payout' => $payout,])

    {{--Taxes table. Do not show unless there are Taxes to show--}}
    @includeWhen(count($payout['taxes']) > 0, 'ninja.components.invoice.taxes_table', ['payout' => $payout,])

    {{--Total table--}}
    @include('ninja.components.invoice.totals_table', ['payout' => $payout,])

    {{--Others table. Do not show unless there are Others to show--}}
    @includeWhen(count($payout['others']) > 0, 'ninja.components.invoice.others_table', ['payout' => $payout])

    @if($payout['payee_invoices_footer_information'] !== null || trim($payout['payee_invoices_footer_information']) !== '')
    <div id="notices">
        <div class="notice">{{ $payout['payee_invoices_footer_information'] }}</div>
    </div>
    @endif

    <div id="notices">
        <div class="notice">{{ __('accounting_pdfs.invoice.thank_you') }}</div>
    </div>
</body>

</html>