<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">

    <title>Payout {{$payout['settlement_name']}}</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background: #fff;
            background-image: none;
            font-family: <PERSON>ja<PERSON><PERSON>, sans-serif, serif;
            font-size: 10px;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        h1 {
            border-top: 1px solid #5D6975;
            border-bottom: 1px solid #5D6975;
            color: #5D6975;
            font-size: 2.4em;
            line-height: 1.4em;
            font-weight: normal;
            text-align: center;
            margin: 0 0 20px 0;
        }

        /* Center tables for demo */
        table {
            margin-bottom: 30px;
        }

        /* Default Table Style */
        table {
            width: 100%;
            color: #333;
            background: white;
            font-size: 10px;
            border-collapse: collapse;
        }

        th {
            color: #ffffff;
            background: #0000CC;
        }

        table tr {
            background: #ffffff;
        }

        table tr:nth-child(2n-1) {
            background: #f5f5f5;
        }

        th:first-child {
            border-top-left-radius: 6px;
        }

        th:last-child {
            border-top-right-radius: 6px;
        }

        tr:last-child td:first-child {
            border-bottom-left-radius: 6px;
        }

        tr:last-child td:last-child {
            border-bottom-right-radius: 6px;
        }

        tr:last-child td {
            background-color: rgb(248, 217, 154, .5);
        }

        table caption {
            padding: .5em;
        }

        table th,
        table td {
            padding: .5em;
            padding-left: 10px;
            width: auto;
            border: none;
        }

        table th.min,
        table td.min {
            width: 1%;
            white-space: nowrap;
            padding-left: 20px;
            padding-right: 10px;
        }

        /* Zebra Table Style */
        [data-table-theme*=zebra] tbody tr:nth-of-type(odd) {
            background: rgba(0, 0, 0, .05);
        }

        [data-table-theme*=zebra][data-table-theme*=dark] tbody tr:nth-of-type(odd) {
            background: rgba(255, 255, 255, .05);
        }

        /* Dark Style */
        [data-table-theme*=dark] {
            color: #ddd;
            background: #333;
            font-size: 12px;
            border-collapse: collapse;
        }

        [data-table-theme*=dark] thead th,
        [data-table-theme*=dark] tfoot th {
            color: #aaa;
            background: rgba(0255, 255, 255, .15);
        }

        [data-table-theme*=dark] caption {
            padding: .5em;
        }

        [data-table-theme*=dark] th,
        [data-table-theme*=dark] td {
            padding: .5em;
        }

        .container {
            padding: 30px;
        }

        .font-weight-bold {
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>{{ __('accounting_pdfs.payment.title') }}</h1>

        <h3 style="text-align: center;">{{$payout['settlement_name']}} - {{ $payout['payee_name'] }}</h3>

        <p style="text-align: center;">
            @if(!$payout['settlement']['on_check_out'])
                {{ __('accounting_pdfs.bookings_between', [
                        'start' => Carbon\Carbon::parse($payout['settlement']['start'])->format('d M Y'),
                        'finish' => Carbon\Carbon::parse($payout['settlement']['end'])->format('d M Y'),]) }}
            @else
                {{ __('accounting_pdfs.bookings_between_checkout', [
                        'start' => Carbon\Carbon::parse($payout['settlement']['start'])->format('d M Y'),
                        'finish' => Carbon\Carbon::parse($payout['settlement']['end'])->format('d M Y'),]) }}
            @endif
            <br>
        </p>

        <table style="width: 100%; padding: 0; margin:0 auto;border-style: none;" cellspacing="0">
            <tr>
                <td style="width:60%; padding: 10px; color: #000000; background-color: #ffffff; text-align: left; border-style: none;" align="left" valign="top">
                    @if($team->hasValidImgixPhoto())
                    <div style="margin-top: 10px;">
                        <img src="{{$team->imgixPhoto()}}" style="width: 100px; height: 100px;" alt="Team photo">
                    </div>
                    @endif
                    <p>{{$team->email}}</p>
                    <h3>{{ $team->name }}</h3>
                </td>
                <td style="width:40%; padding: 10px; color: #000000; background-color: #ffffff; text-align: left; border-style: none;" align="left" valign="top">
                    <h4>{{ __('accounting_pdfs.payment.rentals_included') }}</h4>
                    <p>{{implode(", ", array_values(array_merge($rentals)))}}</p>
                    @if(($payout['payee_notes'] != null && strlen($payout['payee_notes']) > 0) || ($payout['payee_information'] != null && strlen($payout['payee_information']) > 0))
                    <h5>{{ __('accounting_pdfs.payment.payee_information') }}</h5>
                    @if(($payout['payee_notes'] != null && strlen($payout['payee_notes']) > 0))
                    <p>{{new Illuminate\Support\HtmlString(nl2br(e($payout['payee_notes'])))}}</p>
                    @endif
                    @endif
                </td>
            </tr>
        </table>

        {{--Rental Price table. Do not show unless there are Rental Prices to show--}}
        @includeWhen(count($payout['bookings']) > 0, 'ninja.components.payout.bookings_table', ['payout' => $payout, 'rentals' => $rentals])

        {{--Fees table. Do not show unless there are Fees to show--}}
        @includeWhen(count($payout['fees']) > 0, 'ninja.components.payout.fees_table', ['payout' => $payout, 'rentals' => $rentals])

        {{--Taxes table. Do not show unless there are Taxes to show--}}
        @includeWhen(count($payout['taxes']) > 0, 'ninja.components.payout.taxes_table', ['payout' => $payout, 'rentals' => $rentals])

        {{--Others table. Do not show unless there are Others to show--}}
        @includeWhen(count($payout['others']) > 0, 'ninja.components.payout.others_table', ['payout' => $payout, 'rentals' => $rentals])

        {{--Expenses table. Do not show unless there are Expenses to show--}}
        @includeWhen(count($payout['expenses']) > 0, 'ninja.components.payout.expenses_table', ['payout' => $payout, 'rentals' => $rentals])

        {{--Totals table:--}}
        @include('ninja.components.payout.totals_table', ['payout' => $payout])
    </div>
</body>

</html>