<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Tokeet accounting</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .clearfix:after {
            content: "";
            display: table;
            clear: both;
        }

        .clearfix {
            margin-bottom: 15px;
        }

        a {
            color: #5D6975;
            text-decoration: underline;
        }

        body {
            position: relative;
            color: #001028;
            background: #FFFFFF;
            font-family: Helvetica;
            font-size: 10px;
        }

        header {
            padding: 10px 0;
            margin-bottom: 30px;
        }

        h1 {
            border-top: 1px solid #5D6975;
            border-bottom: 1px solid #5D6975;
            color: #5D6975;
            font-size: 2.4em;
            line-height: 1.4em;
            font-weight: normal;
            text-align: center;
            margin: 0 0 20px 0;
        }

        #project {
            display: inline-block;
            text-align: left;
            margin-top: 30px;
        }

        #project span {
            color: #5D6975;
            text-align: right;
            width: 52px;
            margin-right: 10px;
            display: inline-block;
            font-size: 0.8em;
        }

        #project div,
        #company div {
            white-space: nowrap;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            margin-bottom: 20px;
        }

        table tr:nth-child(2n-1) td {
            background: #F5F5F5;
        }

        table th,
        table td {
            text-align: center;
        }

        table th {
            padding: 5px 20px;
            color: #5D6975;
            border-bottom: 1px solid #C1CED9;
            white-space: nowrap;
            font-weight: normal;
        }

        table .service {
            text-align: left;
        }

        table td {
            padding: 12px;
            text-align: right;
        }

        table td.service {
            vertical-align: top;
        }

        table th.unit,
        table th.total,
        table td.unit,
        table td.total {
            text-align: right;
        }

        table td.total,
        table th.total {
            font-weight: bold;
        }

        table td.grand {
            border-top: 1px solid #5D6975;;
        }

        #notices .notice {
            font-size: 12px;
            margin-top: 10px;
        }

        footer {
            color: #5D6975;
            width: 100%;
            height: 30px;
            position: absolute;
            bottom: 0;
            border-top: 1px solid #C1CED9;
            padding: 8px 0;
            text-align: center;
        }

        .right_align {
            text-align: right;
        }
    </style>
</head>
<body>
<header class="clearfix">
    <h1>TOKEET - RENTAL NINJA ACCOUNTING</h1>
    <div id="project">
        <h3 style="margin-top: 0; margin-bottom: 10px;">Billed by: Rental Ninja SL</h3>
        <div><p>
                Address: Carrer de Sant Pere Més Alt 66, local 1, 08003 Barcelona, Spain<br>
                VAT number: B67572370
            </p>
        </div>
        <h2 style="margin-bottom: 0;">ACCOUNTANCY PERIOD: {{$start}} TO {{$end}}</h2>
    </div>
</header>
<main>
    <table class="table">
        <thead>
        <tr style="background-color: #ddd;">
            <th class="service">Company Name</th>
            <th class="unit">Account ID</th>
            <th class="unit">Days active</th>
            <th class="unit">Average active rentals per day</th>
            <th class="unit">Daily rental count</th>
        </tr>
        </thead>
        <tbody>
        @if($teams != null)
            @foreach($teams as $team)
                <tr>
                    <td class="service">{{ $team['name'] }}</td>
                    <td class="unit">{{ $team['account_id'] }}</td>
                    <td class="unit">{{ $team['days'] . ' days' }}</td>
                    <td class="unit">{{ $team['averaged_rentals'] }}</td>
                    <td class="unit">{{  $team['daily_rental_count'] }}</td>
                </tr>
            @endforeach
        @endif
        </tbody>
    </table>
    <table class="table">
        <thead>
        <tr style="background-color: #ddd;">
            <th class="service">Date</th>
            <th class="unit">Rental Count</th>
            <th class="unit">Active teams</th>
            <th class="unit">Daily price</th>
        </tr>
        </thead>
        <tbody>
        <br />
        @if($dates != null)
            @foreach($dates as $date)
                <tr>
                    <td class="service">{{ $date['date'] }}</td>
                    <td class="unit">{{ $date['rentals'] }}</td>
                    <td class="unit">{{ $date['active_teams'] }}</td>
                    <td class="unit"><x-format-amount currency="EUR" :amount="doubleval($date['price'])"/></td>
                </tr>
            @endforeach
        @endif

        <tr>
            <td colspan="3" class="grand total">TOTAL</td>
            <td class="grand total"><x-format-amount currency="EUR" :amount="doubleval($finalPrice)"/></td>
        </tr>

        <tr>
            <td colspan="3" class="grand total">NUM ACCOUNTS</td>
            <td class="grand total">{{  intval($numAccounts) }}</td>
        </tr>
        <tr>
            <td colspan="3" class="grand total">NUM DAYS</td>
            <td class="grand total">{{  intval($numDays) }}</td>
        </tr>
        </tbody>
    </table>

    <div id="notices">
        <div class="notice">
            <p class="right_align"><b>Thank you,</b></p>
            <p class="right_align"><b>Rental Ninja team</b></p>
        </div>
    </div>
</main>
</body>
</html>