<table>
    <thead class="table-dark">
        <tr>
            <th style="text-align: left;"><b>{{ __('accounting_pdfs.rental_price') }}</b></th>
            <th class="min" style="text-align: right;"><b>{{ __('accounting_pdfs.AMOUNT') }}</b></th>
        </tr>
    </thead>
    <tbody>
        @foreach($payout['bookings'] as $rentalId => $bookings)
        <tr style="background-color: #D9E1F2;">
            @if($rentalId != 0)
            <td class="font-weight-bold">{{ ucwords(strtolower(trim($rentals[$rentalId]))) }}</td>
            @else
            <td class="font-weight-bold">{{ __('accounting_pdfs.not_rental_related') }}</td>
            @endif
            <td></td>
        </tr>
        @foreach($bookings as $booking)
        <tr>
            <td>{{ ucwords(strtolower(trim($booking->name))) }}</td>
            <td class="min" style="text-align: right;">
                <x-format-amount :currency="$payout['currency']" :amount="$booking->value" />
            </td>
        </tr>
        @endforeach
        @endforeach

        <tr class="table-warning font-weight-bold">
            <td><b>{{ __('accounting_pdfs.TOTAL') }}</b></td>
            <td class="min" style="text-align: right;">
                <b>
                    <x-format-amount :currency="$payout['currency']" :amount="$payout['bookings_value']" />
                </b>
            </td>
        </tr>
    </tbody>
</table>