<table>
    @php
    $invoice_taxes_sub_total = 0;
    $invoice_taxes_vat = 0;
    $invoice_taxes_total = 0;
    @endphp
    <thead>
        <tr>
            <th class="service" width="50%">{{ __('accounting_pdfs.TAXES') }}</th>
            <th class="unit">{{ __('accounting_pdfs.invoice.table.heading.price') }}</th>
            <th class="unit">{{ __('accounting_pdfs.invoice.table.heading.vat') }}</th>
            <th class="unit">{{ __('accounting_pdfs.TOTAL') }}</th>
        </tr>
    </thead>
    <tbody>
        @foreach($payout['taxes'] as $rentalId => $taxes)
        <tr>
            @if($rentalId != 0)
            <td class="rental-name">{{ ucwords(strtolower(trim(\App\Models\Rental::withTrashed()->whereId($rentalId)->select('name')->first()->name))) }}</td>
            @else
            <td class="rental-name">{{ __('accounting_pdfs.not_rental_related') }}</td>
            @endif
            <td></td>
            <td></td>
            <td></td>
        </tr>
        @foreach($taxes as $tax)
        <tr>
            <td class="service" width="50%">{{ ucwords(strtolower(trim($tax['name']))) }}</td>
            @php
            list($item, $vat, $total) = \App\Actions\Accounting\Invoices\GetInvoicePriceAction::run($invoice,$tax['value']);
            $invoice_taxes_sub_total += $item;
            $invoice_taxes_vat += $vat;
            $invoice_taxes_total += $total;
            @endphp
            <td class="unit">
                <x-format-amount :currency="$payout['currency']" :amount="$item" />
            </td>
            <td class="unit">
                <x-format-amount :currency="$payout['currency']" :amount="$vat" />
            </td>
            <td class="unit">
                <x-format-amount :currency="$payout['currency']" :amount="$total" />
            </td>
        </tr>
        @endforeach
        @endforeach
        <tr>
            <td width="50%"></td>
            <td class="unit total">
                <x-format-amount :currency="$payout['currency']" :amount="$invoice_taxes_sub_total" />
            </td>
            <td class="unit total">
                <x-format-amount :currency="$payout['currency']" :amount="$invoice_taxes_vat" />
            </td>
            <td class="unit total">
                <x-format-amount :currency="$payout['currency']" :amount="$invoice_taxes_total" />
            </td>
        </tr>
    </tbody>
</table>