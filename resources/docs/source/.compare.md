---
title: API Reference

language_tabs:
- bash
- javascript
- python
- php

includes:

search: true

toc_footers:
- <a href='http://github.com/mpociot/documentarian'>Documentation Powered by <PERSON>umentarian</a>
---
<!-- START_INFO -->
# Info

Welcome to the generated API reference.

<!-- END_INFO -->

#User Management


<!-- START_9d9bd3b1d5366fd9b3cf05167e87e228 -->
## register
Registers a user from lodgify into rental ninja.

> Example request:

```bash
curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/register" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"user_id":16,"api_key":"aut","email":"et","company_name":"non","website_slug":"doloribus","trial_ends_at":"tempora","password":"iste"}'

```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/register"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "user_id": 16,
    "api_key": "aut",
    "email": "et",
    "company_name": "non",
    "website_slug": "doloribus",
    "trial_ends_at": "tempora",
    "password": "iste"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/register'
payload = {
    "user_id": 16,
    "api_key": "aut",
    "email": "et",
    "company_name": "non",
    "website_slug": "doloribus",
    "trial_ends_at": "tempora",
    "password": "iste"
}
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers, json=payload)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->post(
    'https://pm.lodgifyintegration.com/lodgify/register',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
        'json' => [
            'user_id' => 16,
            'api_key' => 'aut',
            'email' => 'et',
            'company_name' => 'non',
            'website_slug' => 'doloribus',
            'trial_ends_at' => 'tempora',
            'password' => 'iste',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "id": 1223,
    "access-token": "$2y$10$gmETmsptePtfEwWw15fwnuW1tiX4YYiSoOjO38f37y4YEdI0Xw.ru"
}
```

### HTTP Request
`POST lodgify/register`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `user_id` | integer |  required  | The user id from Lodgify.
        `api_key` | string |  required  | The API Key to connect to Lodgify user
        `email` | string |  required  | The email of the user. Will be used for login.
        `company_name` | string |  optional  | The company name of the lodgify user if any. If not found we will generate one like: Lodgify-{user_id}.
        `website_slug` | string |  optional  | The company slug. If not found we will generate one with the company name.
        `trial_ends_at` | date |  optional  | The trial ends at.
        `password` | string |  optional  | An optional password for the created user. If not specified we will generate a random password.
    
<!-- END_9d9bd3b1d5366fd9b3cf05167e87e228 -->

<!-- START_5b70df08c8aa8c90a08b81d26b818e81 -->
## account/{id}/refresh-token
Refreshes a token from an existing account. If the user isn&#039;t found, validation will fail.

> Example request:

```bash
curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/account/1/refresh-token" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"user_id":1}'

```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/account/1/refresh-token"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "user_id": 1
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/account/1/refresh-token'
payload = {
    "user_id": 1
}
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers, json=payload)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->post(
    'https://pm.lodgifyintegration.com/lodgify/account/1/refresh-token',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
        'json' => [
            'user_id' => 1,
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "user_id": 1223,
    "access-token": "$2y$10$gmETmsptePtfEwWw15fwnuW1tiX4YYiSoOjO38f37y4YEdI0Xw.ru"
}
```

### HTTP Request
`POST lodgify/account/{id}/refresh-token`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `user_id` | integer |  required  | The user id from Lodgify.
    
<!-- END_5b70df08c8aa8c90a08b81d26b818e81 -->

<!-- START_73dfe13fbac578cb8c07f681b0f6fc52 -->
## account/{id}
Returns current information about a Lodgify user inside rental ninja.

> Example request:

```bash
curl -X GET \
    -G "https://pm.lodgifyintegration.com/lodgify/account/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/account/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/account/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('GET', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->get(
    'https://pm.lodgifyintegration.com/lodgify/account/1',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "data": {
        "id": 1,
        "api_key": "jc\/JGEe1GAirYMkBjxoCsJ5Xb4fzTBBbV7mvQbzvu6qm6IAdS9ONkD3+wY0lkZr4",
        "team_id": 1,
        "business_name": "Pol Test 2",
        "status": "enabled",
        "default_locale": "en",
        "email": "<EMAIL>",
        "default_arrival_time": 15,
        "default_departure_time": 10,
        "default_communication_locale": "en",
        "address1": null,
        "address2": null,
        "city": null,
        "zip": null,
        "state": null,
        "country_code": null,
        "website": null,
        "created_at": "2020-02-07 16:15:02",
        "updated_at": "2020-02-07 16:15:02",
        "canceled_at": null,
        "lodgify_user_id": 132516
    }
}
```

### HTTP Request
`GET lodgify/account/{id}`


<!-- END_73dfe13fbac578cb8c07f681b0f6fc52 -->

<!-- START_1038786d3ad88e0ee1c20a094bb2864a -->
## account/{id}
Removes completely all account related information from our databases

> Example request:

```bash
curl -X DELETE \
    "https://pm.lodgifyintegration.com/lodgify/account/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"user_id":16}'

```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/account/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "user_id": 16
}

fetch(url, {
    method: "DELETE",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/account/1'
payload = {
    "user_id": 16
}
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('DELETE', url, headers=headers, json=payload)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->delete(
    'https://pm.lodgifyintegration.com/lodgify/account/1',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
        'json' => [
            'user_id' => 16,
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`DELETE lodgify/account/{id}`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `user_id` | integer |  required  | The user id from Lodgify
    
<!-- END_1038786d3ad88e0ee1c20a094bb2864a -->

<!-- START_6f2ffaa0713c91a35b06a824320c1aee -->
## account/{id}
Updates a Lodgify Account

> Example request:

```bash
curl -X PUT \
    "https://pm.lodgifyintegration.com/lodgify/account/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"api_key":"ut","status":"itaque","company_name":"velit","email":"voluptates","user_name":"enim","trial_ends_at":"enim","force_sync":false}'

```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/account/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "api_key": "ut",
    "status": "itaque",
    "company_name": "velit",
    "email": "voluptates",
    "user_name": "enim",
    "trial_ends_at": "enim",
    "force_sync": false
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/account/1'
payload = {
    "api_key": "ut",
    "status": "itaque",
    "company_name": "velit",
    "email": "voluptates",
    "user_name": "enim",
    "trial_ends_at": "enim",
    "force_sync": false
}
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('PUT', url, headers=headers, json=payload)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->put(
    'https://pm.lodgifyintegration.com/lodgify/account/1',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
        'json' => [
            'api_key' => 'ut',
            'status' => 'itaque',
            'company_name' => 'velit',
            'email' => 'voluptates',
            'user_name' => 'enim',
            'trial_ends_at' => 'enim',
            'force_sync' => false,
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```



### HTTP Request
`PUT lodgify/account/{id}`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `api_key` | string |  required  | The API Key to connect to the Lodgify User
        `status` | string |  optional  | The status of the account. If enabled, the company will be considered subscribed.
        `company_name` | string |  optional  | The company name of the lodgify user if any. If not found we will not update it.
        `email` | string |  optional  | The email of the account and the user that is owner of it. It must be unique for the users.
        `user_name` | string |  optional  | The name of the user.
        `trial_ends_at` | date |  optional  | The date the trial ends at. If null, the trial will be expired. If not specified, it won't be modified.
        `force_sync` | boolean |  optional  | If set to "true". We will issue a force sync.
    
<!-- END_6f2ffaa0713c91a35b06a824320c1aee -->

<!-- START_e8e9bf85d3a13bd7a294d59d3c905610 -->
## accounts/active
Returns a paginated list of all ACTIVE accounts of lodgify inside rental ninja

> Example request:

```bash
curl -X GET \
    -G "https://pm.lodgifyintegration.com/lodgify/accounts/active" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/accounts/active"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/accounts/active'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('GET', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->get(
    'https://pm.lodgifyintegration.com/lodgify/accounts/active',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "data": [
        {
            "id": 1,
            "api_key": "jc\/JGEe1GAirYMkBjxoCsJ5Xb4fzTBBbV7mvQbzvu6qm6IAdS9ONkD3+wY0lkZr4",
            "team_id": 1,
            "business_name": "Pol Test 2",
            "status": "enabled",
            "default_locale": "en",
            "email": "<EMAIL>",
            "default_arrival_time": 15,
            "default_departure_time": 10,
            "default_communication_locale": "en",
            "address1": null,
            "address2": null,
            "city": null,
            "zip": null,
            "state": null,
            "country_code": null,
            "website": null,
            "created_at": "2020-02-07 16:15:02",
            "updated_at": "2020-02-07 16:15:02",
            "canceled_at": null,
            "lodgify_user_id": 132516
        }
    ],
    "links": {
        "first": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "last": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "prev": null,
        "next": null
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 1,
        "path": "http:\/\/ninja.local\/lodgify\/accounts",
        "per_page": 15,
        "to": 1,
        "total": 1
    }
}
```

### HTTP Request
`GET lodgify/accounts/active`


<!-- END_e8e9bf85d3a13bd7a294d59d3c905610 -->

<!-- START_63a9ecd708e2997a3d977df18fcec0ba -->
## accounts/not-active
Returns a paginated list of all accounts of lodgify inside rental ninja

> Example request:

```bash
curl -X GET \
    -G "https://pm.lodgifyintegration.com/lodgify/accounts/not-active" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/accounts/not-active"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/accounts/not-active'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('GET', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->get(
    'https://pm.lodgifyintegration.com/lodgify/accounts/not-active',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "data": [
        {
            "id": 1,
            "api_key": "jc\/JGEe1GAirYMkBjxoCsJ5Xb4fzTBBbV7mvQbzvu6qm6IAdS9ONkD3+wY0lkZr4",
            "team_id": 1,
            "business_name": "Pol Test 2",
            "status": "enabled",
            "default_locale": "en",
            "email": "<EMAIL>",
            "default_arrival_time": 15,
            "default_departure_time": 10,
            "default_communication_locale": "en",
            "address1": null,
            "address2": null,
            "city": null,
            "zip": null,
            "state": null,
            "country_code": null,
            "website": null,
            "created_at": "2020-02-07 16:15:02",
            "updated_at": "2020-02-07 16:15:02",
            "canceled_at": null,
            "lodgify_user_id": 132516
        }
    ],
    "links": {
        "first": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "last": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "prev": null,
        "next": null
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 1,
        "path": "http:\/\/ninja.local\/lodgify\/accounts",
        "per_page": 15,
        "to": 1,
        "total": 1
    }
}
```

### HTTP Request
`GET lodgify/accounts/not-active`


<!-- END_63a9ecd708e2997a3d977df18fcec0ba -->

<!-- START_80035d8cd6f0eb52e2c7271c72ed7ce1 -->
## accounts
Returns a paginated list of all accounts of lodgify inside rental ninja

> Example request:

```bash
curl -X GET \
    -G "https://pm.lodgifyintegration.com/lodgify/accounts" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/accounts"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/accounts'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('GET', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->get(
    'https://pm.lodgifyintegration.com/lodgify/accounts',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "data": [
        {
            "id": 1,
            "api_key": "jc\/JGEe1GAirYMkBjxoCsJ5Xb4fzTBBbV7mvQbzvu6qm6IAdS9ONkD3+wY0lkZr4",
            "team_id": 1,
            "business_name": "Pol Test 2",
            "status": "enabled",
            "default_locale": "en",
            "email": "<EMAIL>",
            "default_arrival_time": 15,
            "default_departure_time": 10,
            "default_communication_locale": "en",
            "address1": null,
            "address2": null,
            "city": null,
            "zip": null,
            "state": null,
            "country_code": null,
            "website": null,
            "created_at": "2020-02-07 16:15:02",
            "updated_at": "2020-02-07 16:15:02",
            "canceled_at": null,
            "lodgify_user_id": 132516
        }
    ],
    "links": {
        "first": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "last": "http:\/\/ninja.local\/lodgify\/accounts?page=1",
        "prev": null,
        "next": null
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 1,
        "path": "http:\/\/ninja.local\/lodgify\/accounts",
        "per_page": 15,
        "to": 1,
        "total": 1
    }
}
```

### HTTP Request
`GET lodgify/accounts`


<!-- END_80035d8cd6f0eb52e2c7271c72ed7ce1 -->

#Web Hooks


<!-- START_18efb4f1451a53b27836804a158519c7 -->
## Triggers the sync of a new Property

> Example request:

```bash
curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/property" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->post(
    'https://pm.lodgifyintegration.com/lodgify/property',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`POST lodgify/property`


<!-- END_18efb4f1451a53b27836804a158519c7 -->

<!-- START_06e26461be78ba83fc384e8dcf802021 -->
## Triggers the sync of a an updated property

> Example request:

```bash
curl -X PUT \
    "https://pm.lodgifyintegration.com/lodgify/property/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "PUT",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('PUT', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->put(
    'https://pm.lodgifyintegration.com/lodgify/property/1',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`PUT lodgify/property/{id}`


<!-- END_06e26461be78ba83fc384e8dcf802021 -->

<!-- START_9cfd323653d334435576c1482021e654 -->
## Triggers the sync of a deleted Property

> Example request:

```bash
curl -X DELETE \
    "https://pm.lodgifyintegration.com/lodgify/property/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('DELETE', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->delete(
    'https://pm.lodgifyintegration.com/lodgify/property/1',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`DELETE lodgify/property/{id}`


<!-- END_9cfd323653d334435576c1482021e654 -->

<!-- START_f97f1c6cad5e8078f22fe0552de4f606 -->
## Triggers the sync of a new Room

> Example request:

```bash
curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/property/1/room" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1/room"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1/room'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->post(
    'https://pm.lodgifyintegration.com/lodgify/property/1/room',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`POST lodgify/property/{id}/room`


<!-- END_f97f1c6cad5e8078f22fe0552de4f606 -->

<!-- START_52ef579bb00617d962f7eeb1c8c732c9 -->
## Triggers the sync of an updated Room

> Example request:

```bash
curl -X PUT \
    "https://pm.lodgifyintegration.com/lodgify/property/1/room/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1/room/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "PUT",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1/room/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('PUT', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->put(
    'https://pm.lodgifyintegration.com/lodgify/property/1/room/1',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`PUT lodgify/property/{id}/room/{room_id}`


<!-- END_52ef579bb00617d962f7eeb1c8c732c9 -->

<!-- START_ba32f676ac6096763190274472d1744a -->
## Triggers the sync of a deleted Room

> Example request:

```bash
curl -X DELETE \
    "https://pm.lodgifyintegration.com/lodgify/property/1/room/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/property/1/room/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/property/1/room/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('DELETE', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->delete(
    'https://pm.lodgifyintegration.com/lodgify/property/1/room/1',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`DELETE lodgify/property/{id}/room/{room_id}`


<!-- END_ba32f676ac6096763190274472d1744a -->

<!-- START_b2e9ac592cc94d35116d9d760a634511 -->
## Triggers the sync of a new Booking

> Example request:

```bash
curl -X POST \
    "https://pm.lodgifyintegration.com/lodgify/booking" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/booking"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/booking'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('POST', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->post(
    'https://pm.lodgifyintegration.com/lodgify/booking',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`POST lodgify/booking`


<!-- END_b2e9ac592cc94d35116d9d760a634511 -->

<!-- START_2fcd2f8e383b9aca1b25cf7621c072d6 -->
## Triggers the sync of an updated Booking

> Example request:

```bash
curl -X PUT \
    "https://pm.lodgifyintegration.com/lodgify/booking/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/booking/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "PUT",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/booking/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('PUT', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->put(
    'https://pm.lodgifyintegration.com/lodgify/booking/1',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`PUT lodgify/booking/{id}`


<!-- END_2fcd2f8e383b9aca1b25cf7621c072d6 -->

<!-- START_629e38288f12c0c9441b3cd3c16797c6 -->
## Triggers the sync of a Deleted / Cancelled booking

> Example request:

```bash
curl -X DELETE \
    "https://pm.lodgifyintegration.com/lodgify/booking/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "https://pm.lodgifyintegration.com/lodgify/booking/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```

```python
import requests
import json

url = 'https://pm.lodgifyintegration.com/lodgify/booking/1'
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
response = requests.request('DELETE', url, headers=headers)
response.json()
```

```php

$client = new \GuzzleHttp\Client();
$response = $client->delete(
    'https://pm.lodgifyintegration.com/lodgify/booking/1',
    [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]
);
$body = $response->getBody();
print_r(json_decode((string) $body));
```


> Example response (200):

```json
{
    "success": "OK"
}
```

### HTTP Request
`DELETE lodgify/booking/{id}`


<!-- END_629e38288f12c0c9441b3cd3c16797c6 -->


