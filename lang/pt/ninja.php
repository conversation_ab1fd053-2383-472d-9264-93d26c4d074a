<?php

// This file is intended to be used for ninja product strings
return [
    'creating_team' => 'A criar Equipa...',
    'back' => 'Voltar',
    '60_day_money_back' => 'Garantia de reembolso de 60 dias',
    'terms_and_conditions' => 'Termos e condições',
    'failed_payment_owner' => 'O pagamento da sua subscrição falhou. Para reativar a sua subscrição, :linkOpen Confirme o seu pagamento :linkClose. Caso contrário, a conta será removida em breve e perderá o acesso.',
    'failed_payment_member' => 'Ups! O pagamento da sua subscrição falhou. Notifique o seu Proprietário de Equipa para atualizar o pagamento da sua subscrição para manter o acesso à sua Conta.',

    // Coupons
    'coupon_only_valid_in_plans' => 'O cupão atual <strong>É VÁLIDO APENAS</strong> para os seguintes planos: <strong>:plans</strong>.<br><strong>NÃO SERÁ</strong> aplicado a outros planos.',
    'coupon_explanation' => 'O cupão de <strong>:value% de desconto</strong> será aplicado à sua subscrição por um total de <strong>:months meses</strong>.',
    'coupon_explanation_once' => 'O cupão de <strong>:value% de desconto</strong> será aplicado à sua subscrição <strong>no primeiro pagamento</strong>.',
    'coupon_explanation_forever' => 'O cupão de <strong>:value% de desconto</strong> será aplicado à sua subscrição <strong>para sempre</strong>.',
    'coupon_prices_not_shown_bellow' => 'O detalhe de preços abaixo mostra o plano de preços padrão, sem incluir o desconto. No entanto, o seu cupão será aplicado e refletido no seu plano de assinatura e fatura.',
    'coupon_price_explanation' => '- Para o plano <strong>:plan</strong>, o Preço de Aluguer final por mês será de <strong>:monthly€</strong> durante os primeiros <strong>:months meses</strong><br>',
    'coupon_price_explanation_once' => '- Para o plano <strong>:plan</strong>, o Preço de Aluguer final por mês será de <strong>:monthly€</strong> durante o primeiro <strong>período de subscrição</strong><br>',
    'coupon_price_explanation_forever' => '- Para o plano <strong>:plan</strong>, o Preço de Aluguer final por mês será de <strong>:monthly€ para sempre</strong><br>',

    // Providers
    'smoobu.use_your_smoobu_account' => 'Utilize a sua conta Smoobu',
    'smoobu.connect_to_smoobu' => 'Ligue-se à sua conta Smoobu',
    'smoobu.we_need_your_api_key' => 'Precisamos da sua chave de API Smoobu para ligar',
    'smoobu.get_your_api_key_here' => 'Obtenha aqui',
    'smoobu.connect' => 'Ligar com Smoobu 🔌💡',
    'smoobu.account_already_exists' => 'Esta conta Smoobu já está registada na Rental Ninja. Inicie a sessão.',
    'smoobu.try_again' => 'Tente novamente',
    'rentals_united.connect_to_rentals_united' => 'Ligue-se à sua conta Rentals United',
    'rentals_united.try_again' => 'Tente novamente',
    'rentals_united.rentals_united_username' => 'ID de Utilizador da Rentals United',
    'rentals_united.team_name' => 'Nome da Equipa/Agência',
    'rentals_united.user_name' => 'Nome',
    'rentals_united.email' => 'Endereço de e-mail',
    'rentals_united.password' => 'Senha',
    'rentals_united.password_confirmation' => 'Confirmar senha',
    'rentals_united.connect' => 'Verificar a ligação 🔌💡',
    'rentals_united.invalid_username' => 'A ID de Utilizador da Rentals United é necessário para prosseguir.<br>Clique no link a seguir para permitir o acesso da Rental Ninja à sua conta Rentals United e tente novamente.',
    'rentals_united.attempt_failed' => '<strong>ATENÇÃO</strong><br>A tentativa de ligação à Rentals United <b>FALHOU</b>.<br>Clique no link a seguir para permitir o acesso da Rental Ninja à sua conta Rentals United e tente novamente.',
    'rentals_united.team_already_exists' => "A sua Equipa da Rentals United já está registada na Rental Ninja. <a href=':url'>Inicie a sessão</a>.",
    'oauth.reconnect.successful' => 'Reconexão bem-sucedida. Agora você pode fechar esta janela/guia.',

    // New team templates
    'templates.cleaning.checklist.title' => 'Example: Post Check-Out Cleaning and Inspection',
    'templates.cleaning.checklist.description' => 'Clean the property and inspect for any damages or missing items after guest departure.',
    'templates.cleaning.checklist_item.0.title' => 'Full Cleaning',
    'templates.cleaning.checklist_item.0.description' => 'Complete full property cleaning, including all rooms and outdoor spaces.',
    'templates.cleaning.checklist_item.1.title' => 'Damage inspection',
    'templates.cleaning.checklist_item.1.description' => 'Inspect for damages and document with photos if necessary.',
    'templates.cleaning.checklist_item.2.title' => 'Check inventory',
    'templates.cleaning.checklist_item.2.description' => 'Check inventory of supplies (toilet paper, soap, kitchen essentials).',
    'templates.cleaning.checklist_item.3.title' => 'Beds',
    'templates.cleaning.checklist_item.3.description' => 'Verify that linens are washed and beds are remade.',
    'templates.cleaning.checklist_item.4.title' => 'Stock',
    'templates.cleaning.checklist_item.4.description' => 'Restock any used items and replace amenities.',
    'templates.monthly_inspection.checklist.title' => 'Example: Routine Maintenance Inspection',
    'templates.monthly_inspection.checklist.description' => 'Conduct a thorough inspection of property systems and equipment to ensure everything is in proper working order, reducing the likelihood of mid-stay guest complaints.',
    'templates.monthly_inspection.checklist_item.0.title' => 'Electrical',
    'templates.monthly_inspection.checklist_item.0.description' => "- Test all light fixtures and replace any burnt-out bulbs.\n- Ensure outlets are working properly; test power in key appliances.",
    'templates.monthly_inspection.checklist_item.1.title' => 'Plumbing',
    'templates.monthly_inspection.checklist_item.1.description' => "- Check faucets, showers, and toilets for leaks.\n- Run the dishwasher and washing machine briefly to confirm functionality.\n- Inspect drains for clogs and ensure water pressure is adequate.",
    'templates.monthly_inspection.checklist_item.2.title' => 'Heating & Cooling',
    'templates.monthly_inspection.checklist_item.2.description' => "- Test the HVAC system, adjust temperature to a comfortable setting.\n- Clean air filters and check for unusual noises or malfunctions.",
    'templates.monthly_inspection.checklist_item.3.title' => 'Safety Checks',
    'templates.monthly_inspection.checklist_item.3.description' => "- Verify smoke detectors and carbon monoxide detectors are operational.\n- Check the fire extinguisher’s charge and replace if expired.\n- Ensure all windows and doors lock securely.",
    'templates.monthly_inspection.checklist_item.4.title' => 'Outdoor Maintenance',
    'templates.monthly_inspection.checklist_item.4.description' => "- Inspect outdoor lighting and ensure all lights are functional.\n- Check pool equipment (if applicable) and confirm cleanliness of water.\n- Inspect outdoor furniture for damage or wear.",
];
