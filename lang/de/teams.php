<?php

// This file is intended to be used for team management topics. Check also the ninja.php file though.
return [
    'create_team' => 'Team erstellen',
    'team_name' => ' Teamname',
    'team_slug' => 'Team-Slug',
    'team_members' => 'Teammitglieder',
    'team_trial' => 'Team-Probe',
    'member' => ' Mitglied',
    'you_have_x_teams_remaining' => 'Sie haben momentan noch :teamCount Teams übrig.',
    'slug_input_explanation' => 'Dieser Slug wird verwendet um Ihre Teams in URLs zu identifizieren.',
    'plan_allows_no_more_teams' => "Mit Ihrem aktuellen Plan können Sie keine weiteren Teams erstellen",
    'team' => 'Team',
    'update_team_name' => 'Teamname aktualisieren',
    'team_name_was_updated' => ' Ihr Teamname wurde aktualisiert!',
    'current_teams' => ' Aktuelle Teams',
    'leave_team' => ' Team verlassen',
    'are_you_sure_you_want_to_leave_team' => 'Sind <PERSON><PERSON> sic<PERSON>, dass Sie dieses Team verlassen wollen?',
    'delete_team' => ' Team löschen',
    'are_you_sure_you_want_to_delete_team' => 'Sind Sie sicher, dass Sie dieses Team löschen wollen?',
    'if_you_delete_team_all_data_will_be_deleted' => 'Wenn Sie das Team löschen, werden alle Daten unwiderruflich gelöscht.',
    'team_photo' => ' Teambild',
    'edit_team_member' => ' Teammitglied bearbeiten',
    'remove_team_member' => 'Teammitglied entfernen',
    'are_you_sure_you_want_to_delete_member' => ' Sind Sie sicher, dass Sie dieses Teammitglied löschen möchten?',
    'team_settings' => 'Teameinstellungen',
    'team_profile' => ' Teamprofil',
    'view_all_teams' => 'Alle Teams anzeigen',
    'team_billing' => 'Teamrechnung',
    'you_have_x_invitations_remaining' => ' Sie haben momentan noch :count Einladungen übrig.',
    'teams' => ' Teams',
    'teams_currently_trialing' => 'Aktuelle Probe-Teams',
    'we_found_invitation_to_team' => 'Wir haben Ihre Einladung zum Team :teamName gefunden!',
    'wheres_your_team' => "Wo ist Ihr Team?",
    'looks_like_you_are_not_part_of_team' => "Anscheinend sind Sie momentan in keinem Team!",
    'user_invited_to_join_team' => ':userName hat Sie zu einem Team eingeladen!',
    'please_upgrade_to_create_more_teams' => 'Bitte führen Sie ein Abo-Upgrade durch, um weitere Teams zu erstellen.',
    'team_trial_will_expire_on' => "Die Team-Probeversion läuft am :date ab.",
    'you_have_been_invited_to_join_team' => 'Sie wurden in das Team :teamName eingeladen!',
    'please_upgrade_to_add_more_members' => ' Bitte führen Sie ein Abo-Upgrade durch, um weitere Teammitglieder hinzuzufügen.',
    'user_already_on_team' => 'Dieser Nutzer existiert bereits in diesem Team.',
    'user_already_invited_to_team' => 'Dieser Nutzer wurde bereits in dieses Team eingeladen.',
    'user_doesnt_belong_to_team' => 'Dieser Nutzer gehört nicht zum ausgewählten Team.',
    'not_eligible_based_on_current_members_teams' => 'Sie können diesen Plan aufgrund Ihrer momentanen Anzahl an Teams/Teammitgliedern nicht auswählen.',
    'let_us_know_before_cancelling' => "<strong>Kündigung Ihres Abonnements</strong>: Nicht zufrieden mit Rental Ninja? <strong><a onclick=\"Intercom('showNewMessage');\">Bitte lassen Sie es uns zuerst wissen</a></strong>. Wir könnten Ihnen helfen.",
    'role.team_owner' => 'Teambesitzer',
    'role.admin' => 'Teamadministrator',
    'role.rental_manager' => 'Vermietungsmanager',
    'role.rental_owner' => 'Immobilienbesitzer',
    'role.cleaning_staff' => 'Reinigungspersonal',
    'role.maintenance_staff' => 'Maintenance Staff',
    'role.check_in_agent' => 'Check-in-Mitarbeiter',
    'role.basic_member' => 'Mitglied',
    'android' => 'ANDROID',
    'iphone' => 'IPHONE',
    'invitation.intro' => 'Sie wurden eingeladen, einem :appName -Team beizutreten! Das Team :company möchte Ihnen seine Miet- und Buchungsinformationen mitteilen.',
    'invitation.click_to_get_started' => 'Klicken Sie auf die folgende Schaltfläche, um anzufangen! Erstellen Sie Ihr Konto in weniger als einer Minute:',
    'invitation.join_button' => 'Schließen Sie sich :company an',
    'invitation.welcome' => 'Herzlich Willkommen!',
    'invitation.existing_user.how_do_so' => 'Wie nimmt man die neue Einladung an?',
    'email.greeting' => 'Hallo :name,',
    'email.regards' => 'Mit freundlichen Grüßen,',
    'email.fallback_text' => 'Wenn beim Anklicken der Schaltfläche „:actionText“ Probleme auftreten, kopieren Sie bitte die folgende URL und fügen Sie sie in Ihren Webbrowser ein:',
    'rental' => 'Unterkunft',
    'booking' => 'Buchung',
    'view_booking' => 'Buchung ansehen',
    'adults' => 'Erwachsene',
    'children' => 'Kinder',
    'babies' => 'Babys',
    'yes' => 'Yes',
    'no' => 'No',
    'name' => 'Name',
    'country' => 'Country',
    'someone' => 'someone',
    'nights' => 'nights',
    'guests_x_nights' => 'guests x nights',
    'view' => 'View',

    // Reconnect with BS view
    'reconnect.title' => 'Erneute Verbindung mit :provider',
    'reconnect.intro' => 'Wie es aussieht, wollen Sie sich erneut mit :provider verbinden.',
    'reconnect.only_if' => 'Sie sollten dies nur tun, wenn:',
    'reconnect.only_if_1' => 'Jemand von Rental Ninja Ihnen geraten hat, dies zu tun.',
    'reconnect.only_if_2' => 'Sie Probleme mit Ihrer aktuellen Verbindung haben.',
    'reconnect.only_if_3' => 'Sie Zugriff auf das :provider-Konto von :teamName haben',
    'reconnect.button' => 'Jetzt erneut verbinden',

    // New user created first page:
    'new_user.welcome_to_rn' => 'Willkommen bei :appName',
    'new_user.hi' => 'Hallo :user!',
    'new_user.you_were_invited' => 'Sie wurden eingeladen, dem :team-Team als :role beizutreten. Wenn Sie :appName nutzen, können Sie Folgendes tun:',
    'new_user.benefits.admin' => 'Verwalten Sie den Zugriff Ihrer gesamten Teammitglieder auf Unterkünfte und deren Berechtigungen.',
    'new_user.benefits.rental_manager.under_control' => 'Mit allen Informationen an einem Ort haben Sie alles unter Kontrolle, direkt in Ihrer Tasche!',
    'new_user.benefits.rental_manager.tasks' => 'Weisen Sie Ihren Mitarbeitern automatisch die täglichen Arbeiten zu.',
    'new_user.benefits.rental_manager.pictures' => 'Sammeln und teilen Sie ganz einfach Bilder und Informationen mit allen Teammitgliedern.',
    'new_user.benefits.rental_manager.check_ins' => 'Organisieren Sie jeden Check-in reibungslos und begeistern Sie Ihre Gäste.',
    'new_user.benefits.check_in_agent.information' => 'Sie können jederzeit über Ihr Handy Gästeinformationen ganz einfach erfassen, speichern und darauf zugreifen. Rufen Sie den Gast direkt über die App an.',
    'new_user.benefits.check_in_agent.payments' => 'Bearbeiten und verfolgen Sie die Zahlungsnachweise direkt mit Ihren Gästen.',
    'new_user.benefits.check_in_agent.report' => 'Berichten Sie über alles, was beim Check-in festgestellt wurde, über die mobile App.',
    'new_user.benefits.rental_owner.information' => 'Bleiben Sie immer über Ihre Geschäftstätigkeit informiert.',
    'new_user.benefits.rental_owner.calendar' => 'Erhalten Sie Zugriff auf Ihren Kalender und behalten Sie Ihre Vermietungen im Griff.',
    'new_user.benefits.cleaning.get_notified' => 'Lassen Sie sich über jeden neuen Aufenthalt informieren, bei dem Sie eingreifen müssen.',
    'new_user.benefits.cleaning.tasks' => 'Überprüfen und verwalten Sie Ihre Aufgaben.',
    'new_user.benefits.cleaning.pictures' => 'Fügen Sie während oder nach jedem Ihrer Eingriffe Bilder und Kommentare hinzu.',
    'new_user.benefits.cleaning.more' => 'Und vieles mehr!',
    'new_user.benefits.member.information' => 'Nutzen Sie alle Informationen, die Sie brauchen könnten.',
    'new_user.do_not_wait' => 'Warten Sie nicht, sondern nutzen Sie diese neuen leistungsstarken Funktionen. Folgen Sie den nächsten Schritten, um loszulegen:',
    'new_user.admin.set_permissions' => 'Legen Sie Ihre Berechtigungen fest:',
    'new_user.admin.set_your_permissions' => 'Da Sie ein Administrator sind, haben Sie das Recht, Ihre Berechtigungen auszuwählen. Klicken Sie auf die folgende Schaltfläche und finden Sie sich in der Liste. Klicken Sie dann auf „Berechtigungen“ und legen Sie diese in den beiden Registerkarten fest, die Sie in dieser Ansicht finden (Vermietungen und Berechtigungen):',
    'new_user.admin.set_my_permissions' => 'MEINE BERECHTIGUNGEN FESTLEGEN',
    'new_user.share_news' => 'Teilen Sie die gute Nachricht:',
    'new_user.tell_your_manager' => 'Sagen Sie Ihrem/Ihrer Vorgesetzten, dass Sie Ihr :appName-Konto erfolgreich bestätigt haben, dann erhalten Sie Zugriff auf die richtigen Vermietungen, an denen Sie arbeiten müssen.',
    'new_user.download_app' => 'Laden Sie die :appName-App herunter:',
    'new_user.how_download_app' => 'Um mit :team zu arbeiten, müssen Sie die mobile App herunterladen, die Ihnen dies ermöglicht. Suchen Sie im Google Play Store oder im iTunes App Store nach „:appName“ (nicht nach „:appName Guest“). Wenn Sie Ihr Handy bereits nutzen, wählen Sie Ihre Option einfach hier:',
    'new_user.learn' => 'Lernen Sie die Grundlagen über :appName:',
    'new_user.learn.admin' => 'Zuerst sollten Sie alle Ihre Kollegen im Unternehmen zu :appName: hinzufügen',
    'new_user.learn.admin.team_members' => 'TEAMMITGLIEDER',
    'new_user.learn.admin.role_types' => 'ROLLENARTEN',
    'new_user.learn.admin.test_user' => 'TESTNUTZER',
    'new_user.learn.rental_manager.guests' => 'Werden Sie an der Kommunikation mit Gästen arbeiten?',
    'new_user.learn.rental_manager.tasks' => 'Werden Sie an der Organisation von Aufgaben arbeiten?',
    'new_user.learn.rental_manager.accounting' => 'Werden Sie an der Buchhaltung arbeiten?',
    'new_user.learn.check_in_agent.what_is_rn' => 'Was :appName wirklich ist?',
    'new_user.learn.check_in_agent.tasks_phone' => 'Wie werde ich meine Aufgaben in meinem Handy verwalten?',
    'new_user.learn.check_in_agent.bookings_phone' => 'Wie kann ich die Buchungen in meinem Handy sehen?',
    'new_user.learn.rental_owner' => "Ich möchte auf meinem Handy sehen, was in meiner/n Immobilie/n passiert:",
    'new_user.learn.rental_owner.bookings' => 'MEINE BUCHUNGEN ANSEHEN',
    'new_user.learn.rental_owner.rentals' => 'MEINE VERMIETUNG(EN) ANSEHEN',
    'new_user.yes' => 'JA',
    'new_user.see_video' => 'VIDEO ANSEHEN',
    'new_user.see' => 'ANSEHEN',

    // Statuses
    'status.booked' => 'Booked',
    'status.unavailable' => 'Block',
    'status.canceled' => 'Canceled',
    'status.tentative' => 'Tentative',
    'status.lead' => 'Lead',
    'status.request' => 'Request',
    'status.rejected' => 'Rejected',

    // User unauthorized page:
    'unauthorized.access_denied' => 'Zugang verweigert',
    'unauthorized.following_options_will_help' => "<strong>:user</strong>, Sie sind nicht berechtigt, auf diese Website zuzugreifen. Die folgenden Optionen könnten Ihnen helfen:",
    'unauthorized.list.app' => 'Vergewissern Sie sich, dass Sie die App :appName auf Ihrem Handy haben, um auf die benötigten Informationen zuzugreifen. Um sie zu installieren, klicken Sie hier, wenn Sie Ihr Handy nutzen, oder suchen Sie einfach im Google Play Store oder in Ihrem iTunes App Store nach :appName (nicht :appName Guest):',
    'unauthorized.list.want_settings' => 'Wenn Sie zu Ihren Einstellungen gelangen möchten, klicken Sie auf diese Schaltfläche:',
    'unauthorized.list.your_settings' => 'IHRE EINSTELLUNGEN',
    'unauthorized.list.if_email_accept_invitation' => 'Wenn Sie eine Nachricht mit folgendem Text erhalten haben, klicken Sie bitte auf folgende Schaltfläche und akzeptieren Sie die Teameinladung:',
    'unauthorized.list.your_teams' => 'IHRE TEAMS',
    'unauthorized.list.contact_admin_for_permissions' => 'Wenn Sie der Meinung sind, dass Sie auf die Website zugreifen sollten, KONTAKTIEREN SIE BITTE IHREN ADMINISTRATOR und fordern Sie Berechtigungen für die Website an.',
    'unauthorized.questions' => '<strong>Weitere Fragen?</strong> Wenn Sie diesbezüglich weitere Fragen oder Zweifel haben, lesen Sie bitte in unserem <a href=":helpCenter" target="_blank">Online-Handbuch</a>.',

    // Settings
    'settings.rental_copy.owner' => 'Owner',
    'settings.rental_copy.sleeps' => 'Sleeps',
    'settings.rental_copy.surface' => 'Surface',
    'settings.rental_copy.headline' => 'Headline',
    'settings.rental_copy.description' => 'Description',
    'settings.rental_copy.house_rules' => 'House Rules',
    'settings.rental_copy.terms_and_conditions' => 'Terms & Conditions',
    'settings.rental_copy.checkin_time' => 'Check-in Time',
    'settings.rental_copy.max_checkin_time' => 'Max Check-in Time',
    'settings.rental_copy.checkout_time' => 'Check-out Time',
    'settings.rental_copy.checkin_place' => 'Check-in Place',
    'settings.rental_copy.contact_details' => 'Contact Details',
    'settings.rental_copy.checkin_details' => 'Check-in Details',
    'settings.rental_copy.checkout_details' => 'Check-out Details',
    'settings.rental_copy.wifi_details' => 'Wifi Details',
    'settings.rental_copy.door_details' => 'Door Details',
    'settings.rental_copy.maintenance_details' => 'Maintenance Details',
    'settings.rental_copy.general_amenities' => 'General Amenities',
    'settings.rental_copy.room_amenities' => 'Room Amenities',
    'settings.rental_copy.open_window' => 'Availability Window',
    'settings.rental_copy.min_stay' => 'Min Stay',
    'settings.rental_copy.same_day_cut_off' => 'Same Day Cut-off',
    'settings.rental_copy.base_rate' => 'Base Rate',
    'settings.rental_copy.min_rate' => 'Min Rate',
    'settings.rental_copy.max_rate' => 'Max Rate',
    'settings.rental_copy.damage_deposit' => 'Damage Deposit',
    'settings.rental_copy.down_payment' => 'Down Payment',
    'settings.rental_copy.collect_down_payment' => 'Collect Down Payment',
    'settings.rental_copy.seasons' => 'Seasons',
    'settings.rental_copy.long_stay_discounts' => 'Long Stay Discounts',
    'settings.rental_copy.last_minute_discounts' => 'Last Minute Discounts',
    'settings.rental_copy.rental_fees' => 'Rental Fees',
    'settings.rental_copy.rental_upsells' => 'Upsells',
    'settings.rental_copy.cancellation_policy' => 'Cancellation Policy',
    'settings.rental_copy.pictures' => 'Pictures',
    'settings.rental_copy.exterior_pictures' => 'Exterior Pictures',
    'settings.rental_copy.legal_details' => 'Legal Details',
    'settings.rental_copy.pre_check_in_config' => 'Pre Check-in Settings',
    'settings.rental_copy.guest_portal_config' => 'Guest Portal Settings',
    'settings.rental_copy.guide' => 'Guide',

    // Payment Gateway Settings
    'settings.payment_gateway.validation.rental_collision_1' => 'You are selecting all rentals for this gateway while there is another account created, thus rentals collide.',
    'settings.payment_gateway.validation.rental_collision_2' => 'You are selecting at least one rental which is already in another account.',
    'settings.payment_gateway.validation.account_connected_twice' => "This Stripe account is already connected. You can't connect twice the same account. Please disconnect it first and try again.",

    // Clients
    'clients.booking_engine.pending_client' => "This Client was created when a customer tried to make a reservation but yet has not paid. We will collect his/her details when pays or will be deleted in a while if payment is not completed.",
    'clients.booking_engine.new_client' => "This Client was created by the Booking Engine, when confirming a reservation.",
];
