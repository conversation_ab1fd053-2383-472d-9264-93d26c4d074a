<?php

/**
 * Created by IntelliJ IDEA.
 * User: polbatllo
 * Date: 22/12/2017
 * Time: 11:05.
 */

return [
    //General: common in different documents and places. Don't change this section unless you want to change it everywhere:
    'TOTAL' => 'GESAMT',
    'TOTALS' => 'GESAMT',
    'rental_price' => 'Preis der Vermietung',
    'rent_price' => 'Mietpreis',
    'RENTALS' => 'UNTERKUNFT',
    'fees' => 'Gebühren',
    'FEES' => 'GEBÜHREN',
    'fees_and_services' => 'Gebühren und Dienstleistungen',
    'taxes' => 'Steuern',
    'TAXES' => 'STEUERN',
    'commission' => 'Kommission',
    'OTHERS' => 'SONSTIGES',
    'EXPENSES' => 'AUSGABEN',
    'concept' => 'Konzept',
    'description' => 'Beschreibung',
    'income' => 'Einkommen',
    'amount' => 'Betrag',
    'AMOUNT' => 'BETRAG',
    'name' => 'Name',
    'bookings_between' => 'Buchungen zwischen :start und :finish.',
    'bookings_between_checkout' => 'Buchungen, die zwischen :beginn und :ende enden.',
    'income_calculation_type' => 'Einkommensberechnungstyp: :Typ',
    'net_income' => 'Nettoeinkommen',
    'gross_income' => 'Bruttoeinkommen',
    'rentals_included' => 'In der Abrechnung beinhaltete Mietobjekte:',
    'sources_included' => 'In der Erklärung enthaltene Quellen:',
    'not_rental_related' => 'Nicht bezogen auf die Vermietung',

    /// Statement summary
    'statement_summary.title' => 'ALLGEMEINE EINNAHMENÜBERSICHT',
    'statement_summary.details' => 'Details',
    'statement_summary.details.headings.income_rentals' => 'Einkünfte Unterkünfte: ',
    'statement_summary.details.headings.income_fees' => 'Einkünfte Gebühren: ',
    'statement_summary.details.headings.income_taxes' => 'Einkünfte Steuern: ',
    'statement_summary.details.headings.commissions_paid' => 'Bezahlte Kommission: ',
    'statement_summary.details.headings.income' => 'Gesamteinkommen: ',
    'statement_summary.details.table.rental_name' => 'Unterkunftsname',
    'statement_summary.details.table.bookings' => 'Buchungen',
    'statement_summary.details.table.nights' => 'inkl. Nächte',
    'statement_summary.details.table.guests' => 'Gäste',
    'statement_summary.details.table.commissions' => 'Kommissionen',
    'statement_summary.details.table.channels' => 'Kanäle',
    'statement_summary.fees_breakdown' => 'Aufschlüsselung Gebühren',
    'statement_summary.fees_breakdown.times_booked' => 'Anzahl Buchungen',
    'statement_summary.fees_breakdown.average_price' => 'Durchschnittspreis',
    'statement_summary.taxes_breakdown' => 'Aufschlüsselung Steuern',
    'statement_summary.taxes_breakdown.included_in_price' => 'Im Preis inbegrffen',
    'statement_summary.taxes_breakdown.tax_price' => 'Steuerbetrag',
    'statement_summary.commissions_breakdown' => 'Aufschlüsselung Kommission',
    'statement_summary.commissions_breakdown.imposed_by' => 'Erhoben von',
    'statement_summary.commissions_breakdown.commission_price' => 'Kommissionsbetrag',
    'statement_summary.client_payment_methods' => 'Zahlungsmethoden für Kunden',
    'statement_summary.client_payment_methods.total_paid_by_clients' => 'Gesamtbetrag der Zahlungen der Kunden (einschließlich Kautionen)',
    'statement_summary.income_by_channel' => 'Einnahmen nach Kanal',
    'statement_summary.income_by_channel.pending_payments_by_clients' => 'Ausstehende Zahlungen von Kunden',

    //Booking Breakdown
    'booking_breakdown.title' => 'AUFSCHLÜSSELUNG BUCHUNG',
    'booking_breakdown.booking_breakdown' => 'Aufschlüsselung Buchung',
    'booking_breakdown.booking.title' => ':rental – :status von :start bis :end',
    'booking_breakdown.booking.reference' => 'REFERENZ: :reference – QUELLE: :source – KLIENT: :client – GÄSTE: :guests – ÜBERNACHTUNGEN: :nights',
    'booking_breakdown.booking.blocking' => 'Diese Daten sind nur gesperrt. Dies ist keine Buchung.',
    'booking_breakdown.booking.no_money' => 'Diese Buchung hat keine geldbezogenen Werte.',
    'booking_breakdown.booking.initial_price' => 'Basispreis',
    'booking_breakdown.booking.final_rental_price' => 'Endpreis der Vermietung',
    'booking_breakdown.booking.final_price' => 'Endpreis',
    'booking_breakdown.booking.imposed_by' => 'Erhoben von :source',
    'booking_breakdown.booking.discount' => 'Rabatt',
    'booking_breakdown.booking.discount_applied' => 'Angewendeter Rabatt',
    'booking_breakdown.booking.fee' => 'Gebühr',
    'booking_breakdown.booking.price_before_taxes' => 'Preis vor Steuern',
    'booking_breakdown.booking.tax' => 'Steuer',
    'booking_breakdown.booking.tax.excluded' => ':tax von :percentage %',
    'booking_breakdown.booking.tax.included' => '(im Mietpreis enthalten)',

    //Payments summary
    'payments.title' => 'NETTOEINKOMMEN PRO EMPFÄNGER',
    'payments.intro' => 'An die Finanzabrechnung gesendete Zahlungen :statement.',
    'payments.legal_info' => 'Rechtliche Informationen:',
    'payments.extra_info' => 'Zusätzliche Informationen:',
    'payments.table.bookings.concept' => 'Buchungen',
    'payments.table.bookings.description' => 'Einnahmen von Buchungen',
    'payments.table.fees.description' => 'Einnahmen von Gebühren',
    'payments.table.taxes.description' => 'Einnahmen von Steuern',
    'payments.table.others.concept' => 'Sonstiges',
    'payments.table.others.description' => 'Sonstige Einnahmen',
    'payments.table.expenses.concept' => 'Ausgaben',
    'payments.table.expenses.description' => 'Geldabzüge durch Ausgaben',
    'payments.table.already_paid' => 'BEREITS GEZAHLT',
    'payments.table.left_pay' => 'ÜBRIG ZU ZAHLEN',
    'payments.sent_payments.text' => 'Diese Beträge sind bereits teilbeglichen:',
    'payments.sent_payments.amount' => ':amount bezahlt am :date',

    //Invoice
    'invoice.title' => 'RECHNUNG :number',
    'invoice.issuer' => 'Aussteller: :issuer',
    'invoice.client' => 'Klient: :client',
    'invoice.date' => 'Ausstellungsdatum: :date',
    'invoice.table.heading.concept' => 'KONZEPT',
    'invoice.table.heading.price' => 'PREIS',
    'invoice.table.heading.vat' => 'MwSt.',
    'invoice.table.rental_price' => 'Unterkunft – :booking',
    'invoice.table.fee' => 'Gebühr – :name',
    'invoice.table.tax' => 'Steuer – :name',
    'invoice.table.subtotal' => 'ZWISCHENSUMME',
    'invoice.table.tax.invoice' => 'STEUER :percentage %',
    'invoice.table.grand_total' => 'GESAMTSUMME',
    'invoice.others.info' => 'Dieser Abschnitt beinhaltet Beträge, dich nicht Teil der Rechnung sind, aber dennoch beglichen werden müssen. Für weitere Informationen treten Sie bitte mit uns in Kontakt.',
    'invoice.others.table.heading.other_things' => 'SONSTIGES',
    'invoice.others.total' => 'Die Gesamtsumme der Rechnung inklusive der Sonderbeträge beträgt :amount',
    'invoice.thank_you' => 'Vielen Dank.',
    'invoice.email.payee.subject' => 'Rechnungen für :payeeName!',
    'invoice.email.payee.text' => 'Im Anhang finden Sie die Rechnungen, die Sie für :payeeName zum Herunterladen angefordert haben.',
    'invoice.email.payee.download_invoices' => 'Rechnungen herunterladen',

    //Payment
    'payment.title' => 'ZAHLUNG',
    'payment.rentals_included' => 'Enthaltene Unterkünfte:',
    'payment.payee_information' => 'Empfängerinformation',

    //EMAIL
    'email.subject' => 'Zahlung :name',
    'email.button' => 'Zahlung herunterladen',
    'email.statement_subject' => 'Abrechnung :name',
    'email.statement_button' => 'Abrechnung herunterladen',

    // Rectification Invoices
    'invoice.correction' => 'KORREKTUR DER FALSCHEN RECHNUNG (:invoice)',

];
