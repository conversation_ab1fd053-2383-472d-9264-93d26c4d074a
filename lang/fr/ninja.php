<?php

// This file is intended to be used for ninja product strings
return [
    'creating_team' => 'Création de l\'équipe...',
    'back' => 'Retour',
    '60_day_money_back' => 'Remboursement garanti pendant 60 jours',
    'terms_and_conditions' => 'Termes et Conditions',
    'failed_payment_owner' => 'Le paiement de votre abonnement à échoué :linkOpen confirmez votre paiement :linkClose pour réactiver votre abonnement ! Sans quoi l’accès à votre compte sera suspendu prochainement.',
    'failed_payment_member' => 'Le paiement de votre abonnement à échoué. Si le propriétaire de votre équipe ne procède pas au règlement, vous risquez de perdre prochainement l’accès à votre compte.',

    // Coupons
    'coupon_only_valid_in_plans' => 'La remise actuelle <strong>EST UNIQUEMENT VALIDE</strong> pour les forfaits suivants : <strong>:plans</strong>.<br><PERSON><PERSON> <strong>NE SERA PAS</strong> appliqué à d’autres forfaits.',
    'coupon_explanation' => 'La remise de <strong>:value%</strong> sera appliquée à votre abonnement pour un total de <strong>:months mois</strong>.',
    'coupon_explanation_once' => 'La remise de <strong>:value% discount</strong> sera appliquée à votre abonnement sur <strong>: le premier paiement</strong>.',
    'coupon_explanation_forever' => 'La remise de <strong>:value% discount</strong> sera toujours appliquée à votre abonnement.',
    'coupon_prices_not_shown_bellow' => 'La répartition des prix ci-dessous montre le plan tarifaire standard, sans inclure la remise. Votre coupon sera cependant appliqué et reflété dans votre plan d\'abonnement et votre facture.',
    'coupon_price_explanation' => '- Pour les forfaits <strong>:plan</strong>, le prix par logement et par mois final sera de <strong>:monthly€</strong> durant les <strong>:months premiers mois</strong><br>',
    'coupon_price_explanation_once' => '- Pour le plan <strong>:plan</strong>, le prix par logement et par mois sera de <strong>:monthly€</strong> durant les <strong>:months premiers mois</strong><br>',
    'coupon_price_explanation_forever' => '- Pour le plan <strong>:plan</strong>, le prix par logement et par mois sera de <strong>:monthly€</strong> durant toute la période d\'inscription.',

    // Providers
    'smoobu.use_your_smoobu_account' => 'Utilisez votre compte Smoobu',
    'smoobu.connect_to_smoobu' => 'Connectez-vous à votre compte Smoobu',
    'smoobu.we_need_your_api_key' => 'Nous avons besoin de votre clé API Smoobu pour se connecter',
    'smoobu.get_your_api_key_here' => 'Obtenez-le ici',
    'smoobu.connect' => 'Se connecter avec Smoobu 🔌💡',
    'smoobu.account_already_exists' => 'Ce compte Smoobu est déjà enregistré à Rental Ninja. Identifier-vous, s\'il vous plaît.',
    'smoobu.try_again' => 'Essayer à nouveau',
    'rentals_united.connect_to_rentals_united' => 'Connexion à votre compte Rentals United',
    'rentals_united.try_again' => 'Essayer à nouveau',
    'rentals_united.rentals_united_username' => 'Nom d\'utilisateur Rentals United',
    'rentals_united.team_name' => 'Nom d\'équipe/d\'agence',
    'rentals_united.user_name' => 'Nom',
    'rentals_united.email' => 'Adresse email',
    'rentals_united.password' => 'Mot de passe',
    'rentals_united.password_confirmation' => 'Confirmer mot de passe',
    'rentals_united.connect' => 'Vérification de connexion 🔌💡',
    'rentals_united.invalid_username' => 'L\'identifiant utilisateur Rentals United est nécessaire pour poursuivre.<br>Veuillez cliquer sur le lien suivant pour accorder à Rental Ninja l\'accès à votre compte Rentals United et réessayer.',
    'rentals_united.attempt_failed' => ' <strong>ATTENTION</strong><br>La tentative de Rentals United Connection <b>FAILED</b>.<br>Veuillez cliquer sur le lien suivant pour accorder à Rental Ninja l\'accès à votre compte Rentals United et réessayer.',
    'rentals_united.team_already_exists' => "Votre équipe Rentals United est déjà inscrite à Rental Ninja. Veuillez plutôt <a href=':url'>Log in</a>.",
    'oauth.reconnect.successful' => 'Reconnexion Réussie. Vous pouvez maintenant fermer cette fenêtre/onglet.',

    // New team templates
    'templates.cleaning.checklist.title' => 'Exemple : Nettoyage et inspection après le départ',
    'templates.cleaning.checklist.description' => 'Nettoyez la propriété et inspectez-la pour détecter tout dommage ou objet manquant après le départ des clients.',
    'templates.cleaning.checklist_item.0.title' => 'Nettoyage complet',
    'templates.cleaning.checklist_item.0.description' => 'Nettoyage complet de la propriété, y compris toutes les pièces et les espaces extérieurs.',
    'templates.cleaning.checklist_item.1.title' => 'Inspection des dommages',
    'templates.cleaning.checklist_item.1.description' => 'Inspectez les dommages et documentez-les avec des photos si nécessaire.',
    'templates.cleaning.checklist_item.2.title' => 'Vérifier l\'inventaire',
    'templates.cleaning.checklist_item.2.description' => 'Vérifiez l’inventaire des fournitures (papier toilette, savon, ustensiles de cuisine).',
    'templates.cleaning.checklist_item.3.title' => 'Lits',
    'templates.cleaning.checklist_item.3.description' => 'Vérifiez que le linge est lavé et que les lits sont refaits.',
    'templates.cleaning.checklist_item.4.title' => 'Action',
    'templates.cleaning.checklist_item.4.description' => 'Réapprovisionnez tous les articles usagés et remplacez les équipements.',
    'templates.monthly_inspection.checklist.title' => 'Exemple : Inspection de maintenance de routine',
    'templates.monthly_inspection.checklist.description' => 'Procédez à une inspection approfondie des systèmes et équipements de la propriété pour vous assurer que tout est en bon état de fonctionnement, réduisant ainsi le risque de plaintes des clients en milieu de séjour.',
    'templates.monthly_inspection.checklist_item.0.title' => 'Électricité',
    'templates.monthly_inspection.checklist_item.0.description' => "- Testez tous les luminaires et remplacez les ampoules grillées.\n- Assurez-vous que les prises fonctionnent correctement ; testez l'alimentation des appareils clés.",
    'templates.monthly_inspection.checklist_item.1.title' => 'Plomberie',
    'templates.monthly_inspection.checklist_item.1.description' => "- Vérifiez les robinets, les douches et les toilettes pour détecter les fuites.\n- Faites fonctionner brièvement le lave-vaisselle et le lave-linge pour confirmer leur fonctionnalité.\n- Inspectez les drains pour détecter les obstructions et assurez-vous que la pression de l'eau est adéquate.",
    'templates.monthly_inspection.checklist_item.2.title' => 'Chauffage et climatisation',
    'templates.monthly_inspection.checklist_item.2.description' => "- Testez le système CVC, réglez la température à un niveau confortable.\n- Nettoyez les filtres à air et vérifiez les bruits inhabituels ou les dysfonctionnements.",
    'templates.monthly_inspection.checklist_item.3.title' => 'Contrôles de sécurité',
    'templates.monthly_inspection.checklist_item.3.description' => "- Vérifiez que les détecteurs de fumée et les détecteurs de monoxyde de carbone sont opérationnels.\n- Vérifiez la charge de l'extincteur et remplacez-le s'il est périmé.\n- Assurez-vous que toutes les fenêtres et les portes sont bien verrouillées.",
    'templates.monthly_inspection.checklist_item.4.title' => 'Entretien extérieur',
    'templates.monthly_inspection.checklist_item.4.description' => "- Inspectez l'éclairage extérieur et assurez-vous que toutes les lumières fonctionnent.\n- Vérifiez l'équipement de la piscine (le cas échéant) et confirmez la propreté de l'eau.\n- Inspectez les meubles d'extérieur pour détecter tout dommage ou usure.",
];
