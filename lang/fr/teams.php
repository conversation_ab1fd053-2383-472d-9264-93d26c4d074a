<?php

// This file is intended to be used for team management topics. Check also the ninja.php file though.
return [
    'create_team' => 'Créer une équipe',
    'team_name' => 'Nom de l\'équipe',
    'team_slug' => 'Identifiant d\'équipe',
    'team_members' => 'Membres de l\'équipe',
    'team_trial' => 'Equipe d\'essai',
    'member' => ' Membre',
    'you_have_x_teams_remaining' => 'Il vous reste actuellement :teamCount équipes.',
    'slug_input_explanation' => 'Cet identifiant est utilisé pour identifier votre équipe dans l\'URL.',
    'plan_allows_no_more_teams' => "Votre offre actuelle ne vous permet pas de créer plus d'équipes",
    'team' => ' Equipe',
    'update_team_name' => 'Modifier le nom de l\'équipe',
    'team_name_was_updated' => ' Le nom de votre équipe à été mis à jour.',
    'current_teams' => ' Equipes actuelles',
    'leave_team' => ' Quitter l\'équipe',
    'are_you_sure_you_want_to_leave_team' => 'Êtes-vous sûr(e) de vouloir quitter l\'équipe ?',
    'delete_team' => ' Supprimer l\'équipe',
    'are_you_sure_you_want_to_delete_team' => 'Êtes-vous sûr(e) de vouloir supprimer l\'équipe ?',
    'if_you_delete_team_all_data_will_be_deleted' => 'Si vous supprimez cette équipe, toutes les données associées seront perdues.',
    'team_photo' => ' Photo de l\'équipe',
    'edit_team_member' => ' Gérer les membres',
    'remove_team_member' => 'Supprimer le membre',
    'are_you_sure_you_want_to_delete_member' => ' Êtes-vous sûr(e) de vouloir supprimer ce membre ?',
    'team_settings' => 'Paramètres de l\'équipe',
    'team_profile' => ' Profil de l\'équipe',
    'view_all_teams' => 'Voir toutes les équipes',
    'team_billing' => 'Facturation de l\'équipe',
    'you_have_x_invitations_remaining' => ' Il vous reste :count invitation(s).',
    'teams' => ' Equipes',
    'teams_currently_trialing' => 'Equipes actuellement en essai',
    'we_found_invitation_to_team' => 'Nous avons trouvé vote invitation pour l\'équipe :teamName !',
    'wheres_your_team' => "Où est votre équipe ?",
    'looks_like_you_are_not_part_of_team' => "Vous ne faites pas encore partie d'une équipe.",
    'user_invited_to_join_team' => ':userName vous a invité à rejoindre son équipe !',
    'please_upgrade_to_create_more_teams' => 'Merci de mettre à jour votre abonnement pour créer plus d\'équipes.',
    'team_trial_will_expire_on' => "La période d'essai de l'équipe se termine le :date.",
    'you_have_been_invited_to_join_team' => 'Vous avez été invité à rejoindre l\'équipe :teamName !',
    'please_upgrade_to_add_more_members' => ' Merci de mettre à jour votre abonnement pour ajouter plus de membres à l\'équipe.',
    'user_already_on_team' => 'Cet utilisateur fait déjà partie de l\'équipe.',
    'user_already_invited_to_team' => 'Cet utilisateur a déjà été invité à rejoindre l\'équipe.',
    'user_doesnt_belong_to_team' => 'Cet utilisateur n\'appartient pas à cette équipe.',
    'not_eligible_based_on_current_members_teams' => 'Vous n\'êtes pas éligible à cette offre, étant donné le nombre d\'équipes et membres que vous gérez.',
    'let_us_know_before_cancelling' => "<strong>Annuler Votre Abonnement</strong>: pas satisfait de Rental Ninja? <strong><a onclick=\"Intercom('showNewMessage');\">Contactez nous</a></strong>, nous serions ravis d'echanger.",
    'role.team_owner' => 'Propriétaire de l\'équipe',
    'role.admin' => 'Administrateur de l\'équipe',
    'role.rental_manager' => 'Gestionnaire',
    'role.rental_owner' => 'Propriétaire',
    'role.cleaning_staff' => 'Personnel de ménage',
    'role.maintenance_staff' => 'Équipe de Maintenance',
    'role.check_in_agent' => 'Personnel d\'accueil',
    'role.basic_member' => 'Membre',
    'android' => 'ANDROID',
    'iphone' => 'IPHONE',
    'invitation.intro' => 'Vous avez été invité à rejoindre l\'équipe de :company sur :appName! L\'équipe  souhaite partager avec vous les détails des propriétés et logements.',
    'invitation.click_to_get_started' => 'Cliquez sur le bouton ci-dessous pour commencer ! Créez votre compte en moins d\'une minute :',
    'invitation.join_button' => 'Rejoindre :company',
    'invitation.welcome' => 'Bienvenue à vous !',
    'invitation.existing_user.how_do_so' => 'Comment accepter la nouvelle invitation ?',
    'email.greeting' => 'Bonjour :name,',
    'email.regards' => 'Cordialement,',
    'email.fallback_text' => 'Si vous ne pouvez pas cliquer sur le bouton ":actionText", copiez et collez l\'URL ci-dessous dans votre navigateur Web :',
    'rental' => 'Logement',
    'booking' => 'Réservation',
    'view_booking' => 'Voir réservation',
    'adults' => 'Adultes',
    'children' => 'Enfants',
    'babies' => 'Bébés',
    'yes' => 'Oui',
    'no' => 'Non',
    'name' => 'Nom',
    'country' => 'Pays',
    'someone' => 'quelqu\'un',
    'nights' => 'nuits',
    'guests_x_nights' => 'invités x nuits',
    'view' => 'Voir',

    // Reconnect with BS view
    'reconnect.title' => ':provider Reconnexion',
    'reconnect.intro' => 'Il semble que vous vouliez vous reconnecter à :provider.',
    'reconnect.only_if' => 'Vous ne devez le faire que si :',
    'reconnect.only_if_1' => 'Une personne du personnel de Rental Ninja vous a conseillé de le faire.',
    'reconnect.only_if_2' => 'Vous avez des problèmes avec votre connexion actuelle.',
    'reconnect.only_if_3' => 'Vous avez accès au compte :provider de :teamName',
    'reconnect.button' => 'Reconnectez-vous maintenant',

    // New user created first page:
    'new_user.welcome_to_rn' => 'Bienvenue dans :appName',
    'new_user.hi' => 'Bonjour :user!',
    'new_user.you_were_invited' => 'Vous avez été invité(e) à rejoindre l\'équipe :team en tant que :role. Dès que vous commencerez à utiliser :appName au quotidien vous pourrez :',
    'new_user.benefits.admin' => 'Gérer les membres de votre équipe, leurs accès aux propriétés et leurs permissions.',
    'new_user.benefits.rental_manager.under_control' => 'Avoir toutes les informations indispensables à un seul endroit, directement dans votre poche !',
    'new_user.benefits.rental_manager.tasks' => 'Assigner automatiquement les tâches répétitives aux différents membres de votre équipe.',
    'new_user.benefits.rental_manager.pictures' => 'Collecter très simplement les informations et photos partagées par votre équipes durant leurs missions.',
    'new_user.benefits.rental_manager.check_ins' => 'Organiser très simplement les arrivées pour le plus grand plaisir de vos voyageurs.',
    'new_user.benefits.check_in_agent.information' => 'Collecter, stocker et consulter toutes les informations concernant les séjours et les voyageurs depuis votre mobile. Et les appeler directement depuis :appName !',
    'new_user.benefits.check_in_agent.payments' => 'Gérer et collecter les paiements des voyageurs depuis l\'application.',
    'new_user.benefits.check_in_agent.report' => 'Signaler toute chose constatée durant l\'arrivée des voyageurs.',
    'new_user.benefits.rental_owner.information' => 'Rester informé(e) de toute nouvelle activité grâce aux notifications.',
    'new_user.benefits.rental_owner.calendar' => 'Accéder à votre calendrier et garder le contrôle sur votre logement.',
    'new_user.benefits.cleaning.get_notified' => 'Être notifié de chaque nouvelle réservation pour laquelle vous devrez intervenir.',
    'new_user.benefits.cleaning.tasks' => 'Consulter et gérer vos missions et tâches.',
    'new_user.benefits.cleaning.pictures' => 'Ajouter des photos et commentaires durant ou après chaque intervention.',
    'new_user.benefits.cleaning.more' => 'Et bien plus encore !',
    'new_user.benefits.member.information' => 'Consulter toutes les informations dont vous pourriez avoir besoin.',
    'new_user.do_not_wait' => 'N\'attendez pas pour utiliser vos nouveaux super pouvoirs! Suivez les étapes suivantes pour démarrer :',
    'new_user.admin.set_permissions' => 'Réglez vos autorisations :',
    'new_user.admin.set_your_permissions' => 'En tant qu\'Administrateur, vous pouvez choisir vos propres autorisations. Cliquez ci-dessous et accédez à vos permissions. Cliquez sur "Autorisations" et choisissez les Logements et Autorisations à vous attribuer :',
    'new_user.admin.set_my_permissions' => 'CONFIGURER MES AUTORISATIONS',
    'new_user.share_news' => 'Partagez la bonne nouvelle :',
    'new_user.tell_your_manager' => 'Informez vite la personne qui vous a invité que votre compte est désormais créé, il vous donnera accès aux logements et autorisations qui vous seront nécessaires.',
    'new_user.download_app' => 'Télécharger l\'application :appName:',
    'new_user.how_download_app' => 'Pour commencer à travailler au sein de l\'équipe :team et utiliser :appName vous devez télécharger l\'application mobile. Cherchez ":appName" (Attention ne pas confondre avec ":appName Guest") dans le Google Play Store ou l\'Apple App Store. Si vous êtes actuellement sur votre téléphone, il vous suffit de cliquer ci-dessous :',
    'new_user.learn' => 'Découvrez les bases de :appName :',
    'new_user.learn.admin' => 'Le meilleur moyen de profiter pleinement de :appName est d\'inviter vos collaborateurs :',
    'new_user.learn.admin.team_members' => 'MEMBRES DE L\'EQUIPE',
    'new_user.learn.admin.role_types' => 'ROLES TYPES',
    'new_user.learn.admin.test_user' => 'UTILISATEUR TEST',
    'new_user.learn.rental_manager.guests' => 'Serez-vous en charge de la communication avec les voyageurs ?',
    'new_user.learn.rental_manager.tasks' => 'Serez-vous en charge de la gestion des tâches ?',
    'new_user.learn.rental_manager.accounting' => 'Serez-vous en charge de la facturation propriétaires ?',
    'new_user.learn.check_in_agent.what_is_rn' => 'Qu\'est-ce que :appName ?',
    'new_user.learn.check_in_agent.tasks_phone' => 'Comment vais-je gérer mes tâches depuis mon téléphone ?',
    'new_user.learn.check_in_agent.bookings_phone' => 'Comment puis-je consulter mes réservations depuis mon téléphone ?',
    'new_user.learn.rental_owner' => "Je souhaite consulter mon activité via mon téléphone :",
    'new_user.learn.rental_owner.bookings' => 'VOIR MES RESERVATIONS',
    'new_user.learn.rental_owner.rentals' => 'VOIR MES LOGEMENTS',
    'new_user.yes' => 'OUI',
    'new_user.see_video' => 'VOIR LA VIDEO',
    'new_user.see' => 'VOIR',

    // Statuses
    'status.booked' => 'Booked',
    'status.unavailable' => 'Block',
    'status.canceled' => 'Canceled',
    'status.tentative' => 'Tentative',
    'status.lead' => 'Lead',
    'status.request' => 'Request',
    'status.rejected' => 'Rejected',

    // User unauthorized page:
    'unauthorized.access_denied' => 'Accès interdit',
    'unauthorized.following_options_will_help' => "<strong>:user</strong>, vous n'avez pas les autorisations nécessaires pour consulter le site. Les informations ci-dessous peuvent vous aider :",
    'unauthorized.list.app' => 'Assurez-vous d\'avoir téléchargé et installé l\'application :appName sur votre téléphone. Pour l\'installer, cliquez-ici si vous êtes sur votre téléphone ou cherchez :appName (pas :appName Guest) dans le Google Play Store ou l\'Apple App Store :',
    'unauthorized.list.want_settings' => 'Pour accéder facilement à vos réglages, cliquez ci-dessous :',
    'unauthorized.list.your_settings' => 'MES REGLAGES',
    'unauthorized.list.if_email_accept_invitation' => 'Si vous avez été invité(e) à réjoindre une équipe, cliquez ci-dessous pour accéder à l\'invitation :',
    'unauthorized.list.your_teams' => 'MES EQUIPES',
    'unauthorized.list.contact_admin_for_permissions' => 'Si vous pensez que vous devriez pouvoir accéder au site, merci de contacter l\'Administrateur de votre équipe afin de lui demander cette autorisation.',
    'unauthorized.questions' => '<strong>D\'autres questions ?</strong> Si vous avez la moindre interrogation concernant :appName, n\'hésitez pas à consulter notre <a href=":helpCenter" target="_blank">manuel en ligne</a>.',

    // Settings
    'settings.rental_copy.owner' => 'Owner',
    'settings.rental_copy.sleeps' => 'Sleeps',
    'settings.rental_copy.surface' => 'Surface',
    'settings.rental_copy.headline' => 'Headline',
    'settings.rental_copy.description' => 'Description',
    'settings.rental_copy.house_rules' => 'House Rules',
    'settings.rental_copy.terms_and_conditions' => 'Terms & Conditions',
    'settings.rental_copy.checkin_time' => 'Check-in Time',
    'settings.rental_copy.max_checkin_time' => 'Max Check-in Time',
    'settings.rental_copy.checkout_time' => 'Check-out Time',
    'settings.rental_copy.checkin_place' => 'Check-in Place',
    'settings.rental_copy.contact_details' => 'Contact Details',
    'settings.rental_copy.checkin_details' => 'Check-in Details',
    'settings.rental_copy.checkout_details' => 'Check-out Details',
    'settings.rental_copy.wifi_details' => 'Wifi Details',
    'settings.rental_copy.door_details' => 'Door Details',
    'settings.rental_copy.maintenance_details' => 'Maintenance Details',
    'settings.rental_copy.general_amenities' => 'General Amenities',
    'settings.rental_copy.room_amenities' => 'Room Amenities',
    'settings.rental_copy.open_window' => 'Availability Window',
    'settings.rental_copy.min_stay' => 'Min Stay',
    'settings.rental_copy.same_day_cut_off' => 'Same Day Cut-off',
    'settings.rental_copy.base_rate' => 'Base Rate',
    'settings.rental_copy.min_rate' => 'Min Rate',
    'settings.rental_copy.max_rate' => 'Max Rate',
    'settings.rental_copy.damage_deposit' => 'Damage Deposit',
    'settings.rental_copy.down_payment' => 'Down Payment',
    'settings.rental_copy.collect_down_payment' => 'Collect Down Payment',
    'settings.rental_copy.seasons' => 'Seasons',
    'settings.rental_copy.long_stay_discounts' => 'Long Stay Discounts',
    'settings.rental_copy.last_minute_discounts' => 'Last Minute Discounts',
    'settings.rental_copy.rental_fees' => 'Rental Fees',
    'settings.rental_copy.rental_upsells' => 'Upsells',
    'settings.rental_copy.cancellation_policy' => 'Cancellation Policy',
    'settings.rental_copy.pictures' => 'Pictures',
    'settings.rental_copy.exterior_pictures' => 'Exterior Pictures',
    'settings.rental_copy.legal_details' => 'Legal Details',
    'settings.rental_copy.pre_check_in_config' => 'Pre Check-in Settings',
    'settings.rental_copy.guest_portal_config' => 'Guest Portal Settings',
    'settings.rental_copy.guide' => 'Guide',

    // Payment Gateway Settings
    'settings.payment_gateway.validation.rental_collision_1' => 'You are selecting all rentals for this gateway while there is another account created, thus rentals collide.',
    'settings.payment_gateway.validation.rental_collision_2' => 'You are selecting at least one rental which is already in another account.',
    'settings.payment_gateway.validation.account_connected_twice' => "This Stripe account is already connected. You can't connect twice the same account. Please disconnect it first and try again.",

    // Clients
    'clients.booking_engine.pending_client' => "This Client was created when a customer tried to make a reservation but yet has not paid. We will collect his/her details when pays or will be deleted in a while if payment is not completed.",
    'clients.booking_engine.new_client' => "This Client was created by the Booking Engine, when confirming a reservation.",
];
