<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => 'L\'attribut ":attribute" doit être accepté.',
    'accepted_if' => 'Le :attribute doit être accepté lorsque :other est :value.',
    'active_url' => 'L\'attribut ":attribute" n\'est pas une URL valide.',
    'after' => 'L\'attribut ":attribute" doit être une date après le :date.',
    'after_or_equal' => 'L\'attribut ":attribute" doit être une date après ou égale à :date.',
    'alpha' => 'L\'attribut ":attribute" ne doit contenir que des lettres.',
    'alpha_dash' => 'L\'attribut ":attribute" ne doit contenir que lettres, nombres, et tirets (-).',
    'alpha_num' => 'L\'attribut ":attribute" ne doit contenir que lettres et nombres.',
    'array' => 'L\'attribut ":attribute" doit être un tableau.',
    'before' => 'L\'attribut ":attribute" doit être une date avant le :date.',
    'before_or_equal' => 'L\'attribut ":attribute" doit être une date avant ou égale à :date.',
    'between' => [
        'array' => 'L\'attribut ":attribute" doit avoir entre :min et :max éléments.',
        'file' => 'L\'attribut ":attribute" doit peser entre :min et :max kilo-octets.',
        'numeric' => 'L\'attribut ":attribute" doit être entre :min et :max.',
        'string' => 'L\'attribut ":attribute" doit avoir entre :min et :max caractères.',
    ],
    'boolean' => 'L\'attribut ":attribute" doit valoir "true" ou "false".',
    'confirmed' => 'La confirmation de l\'attribut ":attribute" ne correspond pas.',
    'current_password' => 'Le mot de passe est incorrect.',
    'country' => 'L\'attribut ":attribute" n\'est pas un pays valide.',
    'date' => 'L\'attribut ":attribute" n\'est pas une date valide.',
    'date_equals' => 'L\'attribut ":attribute" doit être une date égale à :date.',
    'date_format' => 'L\'attribut ":attribute" ne correspond pas au format :format.',
    'declined' => 'L\'attribut : doit être refusé.',
    'declined_if' => 'Le :attribute doit être refusé lorsque :other est :value.',
    'different' => 'Les attributs ":attribute" et ":other" doivent être differents.',
    'digits' => 'L\'attribut ":attribute" doit avoir :digits chiffre(s).',
    'digits_between' => 'L\'attribut ":attribute" doit avoir entre :min et :max chiffres.',
    'dimensions' => 'L\'attribut : attribut a des dimensions d\'image non valides.',
    'distinct' => 'L\'attribut ":attribute" existe déjà.',
    'email' => 'L\'attribut ":attribute" doit être une adresse e-mail valide.',
    'ends_with' => 'L\'attribut : attribut doit se terminer par l\'un des éléments suivants : : values.',
    'enum' => 'L\'attribut : sélectionné n\'est pas valide.',
    'exists' => 'L\'attribut ":attribute" n\'est pas valide.',
    'file' => 'Le :attribute doit être un fichier.',
    'filled' => 'L\'attribut ":attribute" est requis.',
    'gt' => [
        'array' => 'L\'attribut ":attribute" doit être supérieur à :value éléments.',
        'file' => 'L\'attribut ":attribute" doit être supérieur à :value kilo-octets.',
        'numeric' => 'L\'attribut ":attribute" doit être supérieur à :value.',
        'string' => 'L\'attribut ":attribute" doit être supérieur à :value caractères.',
    ],
    'gte' => [
        'array' => 'L\'attribut ":attribute" doit être supérieur ou égal à :value éléments.',
        'file' => 'L\'attribut ":attribute" doit être supérieur ou égal à :value kilo-octets.',
        'numeric' => 'L\'attribut ":attribute" doit être supérieur ou égal à :value.',
        'string' => 'L\'attribut ":attribute" doit être supérieur ou égal à :value caractères.',
    ],
    'image' => 'L\'attribut ":attribute" doit être une image.',
    'in' => 'L\'attribut :attribute n\'est pas valide.',
    'in_array' => 'L\'attribut ":attribute" n\'existe pas dans ":other".',
    'integer' => 'L\'attribut ":attribute" doit être un entier.',
    'ip' => 'L\'attribut ":attribute" doit être une adresse IP valide.',
    'ipv4' => 'L\'attribut ":attribute" doit être une adresse IPv4 valide.',
    'ipv6' => 'L\'attribut ":attribute" doit être une adresse IPv6 valide.',
    'json' => 'L\'attribut ":attribute" doit être une chaine JSON valide.',
    'lt' => [
        'array' => 'L\'attribut ":attribute" doit être inférieur à :value éléments.',
        'file' => 'L\'attribut ":attribute" doit être inférieur à :value kilo-octets.',
        'numeric' => 'L\'attribut ":attribute" doit être inférieur à :value.',
        'string' => 'L\'attribut ":attribute" doit être inférieur à :value caractères.',
    ],
    'lte' => [
        'array' => 'L\'attribut ":attribute" doit être inférieur ou égal à :value éléments.',
        'file' => 'L\'attribut ":attribute" doit être inférieur ou égal à :value kilo-octets.',
        'numeric' => 'L\'attribut ":attribute" doit être inférieur ou égal à :value.',
        'string' => 'L\'attribut ":attribute" doit être inférieur ou égal à :value caractères.',
    ],
    'mac_address' => 'L\'attribut : doit être une adresse MAC valide.',
    'max' => [
        'array' => 'L\'attribut ":attribute" ne doit pas contenir plus de :max éléments.',
        'file' => 'L\'attribut ":attribute" ne doit pas peser plus que :max kilo-octets.',
        'numeric' => 'L\'attribut ":attribute" ne doit pas être plus grand que :max.',
        'string' => 'L\'attribut ":attribute" ne doit pas dépasser :max caractères.',
    ],
    'mimes' => 'L\'attribut ":attribute" doit être un fichier de type: :values.',
    'mimetypes' => 'L\'attribut ":attribute" doit être un fichier de type: :values.',
    'min' => [
        'array' => 'L\'attribut ":attribute" doit avoir au moins :min éléments.',
        'file' => 'L\'attribut ":attribute" doit peser au moins :min kilo-octets.',
        'numeric' => 'L\'attribut ":attribute" doit être plus petit que :min.',
        'string' => 'L\'attribut ":attribute" doit faire au moins :min caractères.',
    ],
    'multiple_of' => 'Le :attribute doit être un multiple de :value.',
    'not_in' => 'L\'attribut ":attribute" n\'est pas valide.',
    'not_regex' => 'Le format de l\'attribut ":attribute" n\'est pas valide.',
    'numeric' => 'L\'attribut ":attribute" doit être un nombre.',
    'password' => 'Le Mot de passe est incorrect.',
    'present' => 'L\'attribut ":attribute" doit être present.',
    'prohibited' => 'Le champ :attribute est interdit.',
    'prohibited_if' => 'Le champ :attribute est interdit lorsque :other vaut :value.',
    'prohibited_unless' => 'Le champ :attribute est interdit sauf si :other est dans :values.',
    'prohibits' => 'Le champ :attribute interdit à :other d\'être présent.',
    'regex' => 'Le format de l\'attribut ":attribute" n\'est pas valide.',
    'required' => 'L\'attribut ":attribute" est requis.',
    'required_array_keys' => 'Le champ :attribute doit contenir des entrées pour : valeurs.',
    'required_if' => 'L\'attribut ":attribute" est requis si ":other" vaut ":value".',
    'required_unless' => 'L\'attribut ":attribute" est requis sauf si ":other" vaut :values.',
    'required_with' => 'L\'attribut ":attribute" est requis si ":values" est présent.',
    'required_with_all' => 'L\'attribut ":attribute" est requis si ":values" est présent.',
    'required_without' => 'L\'attribut ":attribute" est requis si ":values" n\'est pas présent.',
    'required_without_all' => 'L\'attribut ":attribute" est requis si aucun de ":values" n\'est présent.',
    'same' => 'Les attributs ":attribute" et ":other" doivent correspondre.',
    'size' => [
        'array' => 'L\'attribut ":attribute" doit contenir :size éléments.',
        'file' => 'L\'attribut ":attribute" doit peser :size kilo-octets.',
        'numeric' => 'L\'attribut ":attribute" doit être :size.',
        'string' => 'L\'attribut ":attribute" doit faire :size caractères.',
    ],
    'starts_with' => 'L\'attribut ":attribute" doit commencer par un des éléments suivants: :values',
    'string' => 'L\'attribut ":attribute" doit être une chaine de caractères.',
    'timezone' => 'L\'attribut ":attribute" doit être un fuseau horaire valide.',
    'unique' => 'L\'attribut ":attribute" existe déjà.',
    'uploaded' => 'L\'attribut ":attribute" n\'a pas été correctement téléversé.',
    'url' => 'Le format de l\'attribut ":attribute" n\'est pas valide.',
    'uuid' => 'Le :attribute doit être un UUID valide.',
    'vat_id' => 'Ce N° de TVA n\'est pas valide.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'message-personnalisé',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [
        'team' => 'équipe',
    ],

];
