<?php

// This file is intended to be used for ninja product strings
return [
    'creating_team' => 'Creando Squadra...',
    'back' => 'Indietro',
    '60_day_money_back' => '60 Giorni di garanzia di rimborso',
    'terms_and_conditions' => 'Termini e Condizioni',
    'failed_payment_owner' => 'Il pagamento relativo al tuo abbonamento non è andato a buon fine. Per riattivare il tuo abbonamento :linkOpen Confirm your Payment :linkClose. In caso contrario, il tuo Account sarà rimosso e tu perderai l\'accesso.',
    'failed_payment_member' => 'Il pagamento relativo al tuo abbonamento non è andato a buon fine. Se il proprietario della tua squadra non completa il pagamento, presto potresti perdere l\'accesso.',

    // Coupons
    'coupon_only_valid_in_plans' => 'L\'attuale coupon <strong>È VALIDO SOLO</strong> per i seguenti piani: <strong>:plans</strong>.<br> <strong>NON VERRÀ</strong> applicato ad altri piani.',
    'coupon_explanation' => 'Il coupon <strong>:valore% sconto</strong> sarà applicato al tuo abbonamento per un totale di <strong>:mesi months</strong>.',
    'coupon_explanation_once' => 'Il coupon <strong>:value% discount</strong> verrà applicato al tuo abbonamento al <strong>primo pagamento</strong>.',
    'coupon_explanation_forever' => 'Il coupon <strong>:value% discount</strong> verrà applicato al tuo abbonamento <strong>per sempre</strong>.',
    'coupon_prices_not_shown_bellow' => 'La ripartizione dei prezzi di seguito mostra il piano tariffario standard, senza includere lo sconto. Il tuo coupon verrà comunque applicato e riportato nel tuo piano di abbonamento e nella fattura.',
    'coupon_price_explanation' => '- Per il piano <strong>:plan</strong>, il prezzo finale di affitto mensile sarà di <strong>:monthly€</strong> durante i primi <strong>:months mesi</strong><br>',
    'coupon_price_explanation_once' => '- Per l\'abbonamento <strong>:plan</strong>, il prezzo mensile finale sarà di <strong>:€/mese</strong> durante il primo <strong>periodo di abbonamento</strong><br>',
    'coupon_price_explanation_forever' => '- Per l\'abbonamento <strong>:plan</strong>, il prezzo mensile finale sarà di <strong>:€/mese per sempre</strong><br>',

    // Providers
    'smoobu.use_your_smoobu_account' => 'Usa il tuo account Smoobu',
    'smoobu.connect_to_smoobu' => 'Collegati al tuo account Smoobu',
    'smoobu.we_need_your_api_key' => 'Abbiamo bisogno del tuo Codice API Smoobu per fare il collegamento',
    'smoobu.get_your_api_key_here' => 'Ottienilo qui',
    'smoobu.connect' => 'Connettiti con Smoobu 🔌💡',
    'smoobu.account_already_exists' => 'Questo Account Smoobu è gia Registrato in Rental Ninja. Inizia la sessione, per favore.',
    'smoobu.try_again' => 'Riprova',
    'rentals_united.connect_to_rentals_united' => 'Connettare il tuo Account Rentals United',
    'rentals_united.try_again' => 'Riprova',
    'rentals_united.rentals_united_username' => 'User ID di Rentals United',
    'rentals_united.team_name' => 'Squadra / Nome Agenzia',
    'rentals_united.user_name' => 'Nome',
    'rentals_united.email' => 'Indirizzo Email',
    'rentals_united.password' => 'Password',
    'rentals_united.password_confirmation' => 'Conferma Password',
    'rentals_united.connect' => 'Controllare Connessione 🔌💡',
    'rentals_united.invalid_username' => 'Per procedere è necessario il User ID di Rentals United.<br>Fare clic sul seguente link per concedere a Rental Ninja l\'accesso al tuo account Rentals United e riprovare.',
    'rentals_united.attempt_failed' => '<strong>ATTENZIONE</strong><br>Tentativo di connessione di Rentals United <b>FALLITO</b>.<br>Fare clic sul seguente collegamento per concedere a Rental Ninja l\'accesso al tuo account di Rentals United e riprovare.',
    'rentals_united.team_already_exists' => "La tua Squadra di Rentals United è già registrata con Rental Nina. <a href=':url'>Accedi</a> all'account esistente invece di crearne uno nuovo.",
    'oauth.reconnect.successful' => 'Riconnessione riuscita! Ora può chiudere questa finestra / scheda.',

    // New team templates
    'templates.cleaning.checklist.title' => 'Example: Post Check-Out Cleaning and Inspection',
    'templates.cleaning.checklist.description' => 'Clean the property and inspect for any damages or missing items after guest departure.',
    'templates.cleaning.checklist_item.0.title' => 'Full Cleaning',
    'templates.cleaning.checklist_item.0.description' => 'Complete full property cleaning, including all rooms and outdoor spaces.',
    'templates.cleaning.checklist_item.1.title' => 'Damage inspection',
    'templates.cleaning.checklist_item.1.description' => 'Inspect for damages and document with photos if necessary.',
    'templates.cleaning.checklist_item.2.title' => 'Check inventory',
    'templates.cleaning.checklist_item.2.description' => 'Check inventory of supplies (toilet paper, soap, kitchen essentials).',
    'templates.cleaning.checklist_item.3.title' => 'Beds',
    'templates.cleaning.checklist_item.3.description' => 'Verify that linens are washed and beds are remade.',
    'templates.cleaning.checklist_item.4.title' => 'Stock',
    'templates.cleaning.checklist_item.4.description' => 'Restock any used items and replace amenities.',
    'templates.monthly_inspection.checklist.title' => 'Example: Routine Maintenance Inspection',
    'templates.monthly_inspection.checklist.description' => 'Conduct a thorough inspection of property systems and equipment to ensure everything is in proper working order, reducing the likelihood of mid-stay guest complaints.',
    'templates.monthly_inspection.checklist_item.0.title' => 'Electrical',
    'templates.monthly_inspection.checklist_item.0.description' => "- Test all light fixtures and replace any burnt-out bulbs.\n- Ensure outlets are working properly; test power in key appliances.",
    'templates.monthly_inspection.checklist_item.1.title' => 'Plumbing',
    'templates.monthly_inspection.checklist_item.1.description' => "- Check faucets, showers, and toilets for leaks.\n- Run the dishwasher and washing machine briefly to confirm functionality.\n- Inspect drains for clogs and ensure water pressure is adequate.",
    'templates.monthly_inspection.checklist_item.2.title' => 'Heating & Cooling',
    'templates.monthly_inspection.checklist_item.2.description' => "- Test the HVAC system, adjust temperature to a comfortable setting.\n- Clean air filters and check for unusual noises or malfunctions.",
    'templates.monthly_inspection.checklist_item.3.title' => 'Safety Checks',
    'templates.monthly_inspection.checklist_item.3.description' => "- Verify smoke detectors and carbon monoxide detectors are operational.\n- Check the fire extinguisher’s charge and replace if expired.\n- Ensure all windows and doors lock securely.",
    'templates.monthly_inspection.checklist_item.4.title' => 'Outdoor Maintenance',
    'templates.monthly_inspection.checklist_item.4.description' => "- Inspect outdoor lighting and ensure all lights are functional.\n- Check pool equipment (if applicable) and confirm cleanliness of water.\n- Inspect outdoor furniture for damage or wear.",
];
