<?php

/**
 * Created by IntelliJ IDEA.
 * User: polbatllo
 * Date: 22/12/2017
 * Time: 11:05.
 */

return [
    //General: common in different documents and places. Don't change this section unless you want to change it everywhere:
    'TOTAL' => 'TOTALE',
    'TOTALS' => 'TOTALI',
    'rental_price' => 'Prezzo della Struttura',
    'rent_price' => 'Prezzo della Struttura',
    'RENTALS' => 'STRUTTURE',
    'fees' => 'Servizi',
    'FEES' => 'SERVIZI',
    'fees_and_services' => 'Servizi e Tariffe',
    'taxes' => 'Tasse',
    'TAXES' => 'TASSE',
    'commission' => 'Comissione',
    'OTHERS' => 'ALTRI',
    'EXPENSES' => 'SPESE',
    'concept' => 'Oggetto',
    'description' => 'Descrizione',
    'income' => 'Reddito',
    'amount' => 'Importo',
    'AMOUNT' => 'IMPORTO',
    'name' => 'Nome',
    'bookings_between' => 'Prenotazioni con inizio tra il :start ed il :finish.',
    'bookings_between_checkout' => 'Prenotazioni finendo tra :start e :finish.',
    'income_calculation_type' => 'Tipo di Calcolo del Reddito: :type',
    'net_income' => 'Reddito netto',
    'gross_income' => 'Reddito lordo',
    'rentals_included' => 'Strutture incluse nel Rendiconto:',
    'sources_included' => 'Fonti incluse nella dichiarazione:',
    'not_rental_related' => 'Non correlato ad alcun Alloggio',

    /// Statement summary
    'statement_summary.title' => 'RIEPILOGO GENERALE ENTRATE',
    'statement_summary.details' => 'Dettagli',
    'statement_summary.details.headings.income_rentals' => 'Reddito Strutture: ',
    'statement_summary.details.headings.income_fees' => 'Reddito Servizi: ',
    'statement_summary.details.headings.income_taxes' => 'Tassa sul reddito: ',
    'statement_summary.details.headings.commissions_paid' => 'Comissioni Pagate: ',
    'statement_summary.details.headings.income' => 'Reddito totale: ',
    'statement_summary.details.table.rental_name' => 'Nome Struttura',
    'statement_summary.details.table.bookings' => 'Prenotazioni',
    'statement_summary.details.table.nights' => 'Num. Notti',
    'statement_summary.details.table.guests' => 'Ospiti',
    'statement_summary.details.table.commissions' => 'Comissioni',
    'statement_summary.details.table.channels' => 'Canali',
    'statement_summary.fees_breakdown' => 'Disaggregazione Servizi',
    'statement_summary.fees_breakdown.times_booked' => 'Numero di prenotazioni',
    'statement_summary.fees_breakdown.average_price' => 'Prezzo Medio',
    'statement_summary.taxes_breakdown' => 'Disaggregazione Tasse',
    'statement_summary.taxes_breakdown.included_in_price' => 'Incluso nel Prezzo',
    'statement_summary.taxes_breakdown.tax_price' => 'Prezzo Tasse',
    'statement_summary.commissions_breakdown' => 'Disaggregazione Comissioni',
    'statement_summary.commissions_breakdown.imposed_by' => 'Imposta Da',
    'statement_summary.commissions_breakdown.commission_price' => 'Prezzo Comissione',
    'statement_summary.client_payment_methods' => 'Metodi di pagamento del cliente',
    'statement_summary.client_payment_methods.total_paid_by_clients' => 'Totale pagato dai clienti (inclusi i depositi cauzionali)',
    'statement_summary.income_by_channel' => 'Reddito per canale',
    'statement_summary.income_by_channel.pending_payments_by_clients' => 'Pagamenti in sospeso da parte dei clienti',

    //Booking Breakdown
    'booking_breakdown.title' => 'DISAGGREGAZIONE PRENOTAZIONI',
    'booking_breakdown.booking_breakdown' => 'Disaggregazione prenotazioni',
    'booking_breakdown.booking.title' => ':struttura - :status Dal :start Al :end',
    'booking_breakdown.booking.reference' => 'N. RIFERIMENTO: :reference - PROVENIENZA: :source - CLIENTE: :client - OSPITI: :guests - NOTTI: :nights',
    'booking_breakdown.booking.blocking' => 'Queste date sono bloccate. Non è una prenotazione.',
    'booking_breakdown.booking.no_money' => 'Questa prenotazione non ha un valore economico assegnato.',
    'booking_breakdown.booking.initial_price' => 'Prezzo Iniziale',
    'booking_breakdown.booking.final_rental_price' => 'Prezzo Finale del Soggiorno',
    'booking_breakdown.booking.final_price' => 'Prezzo Finale',
    'booking_breakdown.booking.imposed_by' => 'Imposto da :source',
    'booking_breakdown.booking.discount' => 'Sconto',
    'booking_breakdown.booking.discount_applied' => 'Sconto Applicato',
    'booking_breakdown.booking.fee' => 'Servizio',
    'booking_breakdown.booking.price_before_taxes' => 'Prezzo prima delle Tasse',
    'booking_breakdown.booking.tax' => 'Tasse',
    'booking_breakdown.booking.tax.excluded' => ':tassa del :percentage %',
    'booking_breakdown.booking.tax.included' => '(incluso nel prezzo del soggiorno)',

    //Payments summary
    'payments.title' => 'REDDITO NETTO PER DESTINATARIO',
    'payments.intro' => 'Pagamenti realizzati per il resoconto contabile :statement.',
    'payments.legal_info' => 'Informazione legale:',
    'payments.extra_info' => 'Informazione extra:',
    'payments.table.bookings.concept' => 'Prenotazioni',
    'payments.table.bookings.description' => 'Importo incassato dalle prenotazioni',
    'payments.table.fees.description' => 'Importo incassato dai Servizi',
    'payments.table.taxes.description' => 'Importo incassato dalle dalle Tasse',
    'payments.table.others.concept' => 'Altri',
    'payments.table.others.description' => 'Importo incassato di altra natura',
    'payments.table.expenses.concept' => 'Spese',
    'payments.table.expenses.description' => 'Importo dedotto in quanto Spesa',
    'payments.table.already_paid' => 'GIÀ PAGATO',
    'payments.table.left_pay' => 'DA PAGARE',
    'payments.sent_payments.text' => 'Pagamenti  parziali già effettuati',
    'payments.sent_payments.amount' => ':amount pagato il :date',

    //Invoice
    'invoice.title' => 'FATTURA :number',
    'invoice.issuer' => 'Emittente: :issuer',
    'invoice.client' => 'Cliente: :client',
    'invoice.date' => 'Data emissione: :date',
    'invoice.table.heading.concept' => 'OGGETTO',
    'invoice.table.heading.price' => 'PREZZO',
    'invoice.table.heading.vat' => 'IVA',
    'invoice.table.rental_price' => 'Prenotazione - :booking',
    'invoice.table.fee' => 'Servizio - :name',
    'invoice.table.tax' => 'Tassa - :name',
    'invoice.table.subtotal' => 'SUBTOTALE',
    'invoice.table.tax.invoice' => 'IVA :percentage %',
    'invoice.table.grand_total' => 'IMPORTO COMPLESSIVO',
    'invoice.others.info' => 'La prossima sezione include voci che non fanno parte della fattura ma che devono essere pagati ugualmente. Per maggiori informazioni, contattaci.',
    'invoice.others.table.heading.other_things' => 'ALTRE VOCI',
    'invoice.others.total' => 'L`importo totale della fattura incluse le voci aggiuntive è pari a :amount',
    'invoice.thank_you' => 'Grazie.',
    'invoice.email.payee.subject' => 'Fatture per :payeeName!',
    'invoice.email.payee.text' => 'In allegato troverai le fatture che hai richiesto di scaricare per :payeeName.',
    'invoice.email.payee.download_invoices' => 'Scarica Fatture',

    //Payment
    'payment.title' => 'PAGAMENTO',
    'payment.rentals_included' => 'Strutture incluse:',
    'payment.payee_information' => 'Informazioni sul Beneficiario',

    //EMAIL
    'email.subject' => 'Pagamento di :name',
    'email.button' => 'Scaricare il pagamento',
    'email.statement_subject' => 'Rendiconto :name',
    'email.statement_button' => 'Scaricare il Rendiconto',

    // Rectification Invoices
    'invoice.correction' => 'CORREZIONE DELLA FATTURA ERRATA (:invoice)',

];
