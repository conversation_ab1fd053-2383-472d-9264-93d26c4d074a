<?php

return [
    // Event translations
    'events' => [
        'booking-created' => 'Booking created',
        'booking-dates-updated' => 'Booking dates updated',
        'booking-cancelled' => 'Booking cancelled',
        'booking-tentative' => 'Booking tentative',
        'block-created' => 'Block created',
        'lead-created' => 'Lead created',
        'booking-requested' => 'Booking requested',
        'booking-rejected' => 'Booking rejected',
        'payment-created' => 'Payment created',
        'payment-canceled' => 'Payment canceled',
        'payment-refunded' => 'Payment refunded',
        'check-in-out-time-modified' => 'Check-in/out time modified',
        'upsale-purchased' => 'Upsale purchased',
    ],

    // Filter types translations
    'filter_types' => [
        'any' => 'Any',
        'include' => 'Include',
        'exclude' => 'Exclude',
    ],

    // Offset types translations
    'offset_types' => [
        'check_in_offset' => 'Check-in day',
        'check_out_offset' => 'Check-out day',
        'created_offset' => 'Creation day',
    ],

    // Trigger types translations
    'trigger_types' => [
        'event' => 'Event',
        'offset' => 'Offset',
    ],

    // Paid status options translations
    'paid_status_options' => [
        'fully_paid' => 'Fully paid',
        'booking_paid' => 'Booking paid',
        'rental_price_paid' => 'Rental price paid',
        'downpayment_paid' => 'Downpayment paid',
    ],

    // Placeholders translations
    'placeholders' => [
        'booking_reference' => 'Booking reference',
        'client_name' => 'Client name',
        'client_fullname' => 'Client full name',
        'check_in_date' => 'Check-in date',
        'check_out_date' => 'Check-out date',
        'check_in_time' => 'Check-in time',
        'check_out_time' => 'Check-out time',
        'booking_final_price' => 'Booking final price',
        'booking_deposit_price' => 'Booking deposit price',
        'booking_paid_amount' => 'Booking paid amount',
        'booking_missing_payment' => 'Booking missing payment',
        'booking_guest_portal_url' => 'Booking guest portal URL',
        'rental_public_name' => 'Rental public name',
        'rental_name' => 'Rental name',
        'rental_city' => 'Rental city',
        'rental_country' => 'Rental country',
        'rental_address' => 'Rental address',
        'rental_zip' => 'Rental ZIP',
        'team_name' => 'Team name',
        'rental_contact_name' => 'Rental contact name',
        'rental_contact_email' => 'Rental contact email',
        'rental_contact_phone' => 'Rental contact phone',
    ],

    // Placeholders descriptions
    'placeholders_descriptions' => [
        'booking_reference' => 'The unique identifier for the booking',
        'client_name' => 'The first name of the client who made the booking',
        'client_fullname' => 'The full name (first and last name) of the client',
        'check_in_date' => 'The date when the guest will check in',
        'check_out_date' => 'The date when the guest will check out',
        'check_in_time' => 'The time when the guest can check in',
        'check_out_time' => 'The time when the guest must check out',
        'booking_final_price' => 'The total final price of the booking',
        'booking_deposit_price' => 'The deposit amount required for the booking',
        'booking_paid_amount' => 'The amount already paid by the client',
        'booking_missing_payment' => 'The amount still to be paid by the client',
        'booking_guest_portal_url' => 'The URL to the guest portal for this booking',
        'rental_public_name' => 'The public name of the rental property',
        'rental_name' => 'The internal name of the rental property',
        'rental_city' => 'The city where the rental property is located',
        'rental_country' => 'The country where the rental property is located',
        'rental_address' => 'The street address of the rental property',
        'rental_zip' => 'The ZIP/postal code of the rental property',
        'team_name' => 'The name of your team/company',
        'rental_contact_name' => 'The name of the contact person for the rental',
        'rental_contact_email' => 'The email address of the contact person',
        'rental_contact_phone' => 'The phone number of the contact person',
    ],

    // Trigger action types translations
    'trigger_action_types' => [
        'guest_communication' => 'Guest communication',
        'sms_communication' => 'SMS communication',
        'email_communication' => 'Email communication',
        'team_notification' => 'Team notification',
        'push_notification' => 'Push notification',
        'slack_notification' => 'Slack notification',
        'telegram_notification' => 'Telegram notification',
        'create_alert' => 'Create alert',
        'add_tag' => 'Add tag',
        'clear_data' => 'Clear data',
        'cancel_booking' => 'Cancel booking',
    ],
];
