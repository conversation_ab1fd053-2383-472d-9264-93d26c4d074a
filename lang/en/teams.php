<?php

// This file is intended to be used for team management topics. Check also the ninja.php file though.
return [
    'create_team' => 'Create Team',
    'team_name' => ' Team Name',
    'team_slug' => 'Team Slug',
    'team_members' => 'Team Members',
    'team_trial' => 'Team Trial',
    'member' => ' Member',
    'you_have_x_teams_remaining' => 'You currently have :teamCount teams remaining.',
    'slug_input_explanation' => 'This slug is used to identify your team in URLs.',
    'plan_allows_no_more_teams' => "Your current plan doesn't allow you to create more teams",
    'team' => ' Team',
    'update_team_name' => 'Update Team Name',
    'team_name_was_updated' => ' Your team name has been updated!',
    'current_teams' => ' Current Teams',
    'leave_team' => ' Leave Team',
    'are_you_sure_you_want_to_leave_team' => 'Are you sure you want to leave this team?',
    'delete_team' => ' Delete Team',
    'are_you_sure_you_want_to_delete_team' => 'Are you sure you want to delete this team?',
    'if_you_delete_team_all_data_will_be_deleted' => 'If you choose to delete the team all data will be permanently deleted.',
    'team_photo' => ' Team Photo',
    'edit_team_member' => ' Edit Team Member',
    'remove_team_member' => 'Remove Team Member',
    'are_you_sure_you_want_to_delete_member' => ' Are you sure you want to remove this team member?',
    'team_settings' => 'Team Settings',
    'team_profile' => ' Team Profile',
    'view_all_teams' => 'View All Teams',
    'team_billing' => 'Team Billing',
    'you_have_x_invitations_remaining' => ' You currently have :count invitation(s) remaining.',
    'teams' => ' Teams',
    'teams_currently_trialing' => 'Teams Currently Trialing',
    'we_found_invitation_to_team' => 'We found your invitation to the :teamName team!',
    'wheres_your_team' => "Where's Your Team?",
    'looks_like_you_are_not_part_of_team' => "It looks like you're not part of any team!",
    'user_invited_to_join_team' => ':userName has invited you to join their team!',
    'please_upgrade_to_create_more_teams' => 'Please upgrade your subscription to create more teams.',
    'team_trial_will_expire_on' => "The team's trial period will expire on :date.",
    'you_have_been_invited_to_join_team' => 'You have been invited to join the :teamName team!',
    'please_upgrade_to_add_more_members' => ' Please upgrade your subscription to add more team members.',
    'user_already_on_team' => 'That user is already on the team.',
    'user_already_invited_to_team' => 'That user is already invited to the team.',
    'user_doesnt_belong_to_team' => 'The user does not belong to the given team.',
    'not_eligible_based_on_current_members_teams' => 'You are not eligible for this plan based on your current number of teams / team members.',
    'let_us_know_before_cancelling' => "<strong>Cancel Your Subscription</strong>: Not happy with Rental Ninja? Please <strong><a onclick=\"Intercom('showNewMessage');\">let us know first</a></strong>. We could help you.",
    'role.team_owner' => 'Team Owner',
    'role.admin' => 'Team Administrator',
    'role.rental_manager' => 'Rental Manager',
    'role.rental_owner' => 'Property Owner',
    'role.cleaning_staff' => 'Cleaning Staff',
    'role.maintenance_staff' => 'Maintenance Staff',
    'role.check_in_agent' => 'Check-in Agent',
    'role.basic_member' => 'Member',
    'android' => 'ANDROID',
    'iphone' => 'IPHONE',
    'invitation.intro' => 'You have been invited to join a :appName Team! The team :company wants to share with you their rentals and bookings information.',
    'invitation.click_to_get_started' => 'Click on the button below to get started! Create your account in less than a minute:',
    'invitation.join_button' => 'Join :company',
    'invitation.welcome' => 'Welcome!',
    'invitation.existing_user.how_do_so' => 'How to accept the new invitation?',
    'email.greeting' => 'Hello :name,',
    'email.regards' => 'Regards,',
    'email.fallback_text' => 'If you’re having trouble clicking the ":actionText" button, copy and paste the URL below into your web browser:',
    'rental' => 'Rental',
    'booking' => 'Booking',
    'view_booking' => 'View Booking',
    'adults' => 'Adults',
    'children' => 'Children',
    'babies' => 'Babies',
    'yes' => 'Yes',
    'no' => 'No',
    'name' => 'Name',
    'country' => 'Country',
    'someone' => 'someone',
    'nights' => 'nights',
    'guests_x_nights' => 'guests x nights',
    'view' => 'View',

    // Reconnect with BS view
    'reconnect.title' => ':provider Reconnection',
    'reconnect.intro' => 'It looks like you want to reconnect to :provider.',
    'reconnect.only_if' => 'You should only do this if:',
    'reconnect.only_if_1' => 'Someone from the Rental Ninja staff has advised you to do so.',
    'reconnect.only_if_2' => 'You are having issues with your current connection.',
    'reconnect.only_if_3' => 'You have access to the :provider account of :teamName',
    'reconnect.button' => 'Reconnect Now',

    // New user created first page:
    'new_user.welcome_to_rn' => 'Welcome to :appName',
    'new_user.hi' => 'Hi :user!',
    'new_user.you_were_invited' => 'You were invited to join the :team team as :role. By using :appName daily you will be able to:',
    'new_user.benefits.admin' => 'Manage all your team members access to Rentals and their permissions.',
    'new_user.benefits.rental_manager.under_control' => 'Have everything under control with all the information in one place, right in your pocket!',
    'new_user.benefits.rental_manager.tasks' => 'Assign automatically day to day work for your staff.',
    'new_user.benefits.rental_manager.pictures' => 'Easily collect and share pictures and information through all the team members.',
    'new_user.benefits.rental_manager.check_ins' => 'Smoothly organize each check-in and delight your guests.',
    'new_user.benefits.check_in_agent.information' => 'Collect, store and access easily to guest information anytime from your mobile. Direct call the guest from the App.',
    'new_user.benefits.check_in_agent.payments' => 'Handle and track payment records directly with your guests.',
    'new_user.benefits.check_in_agent.report' => 'Report anything discovered during the check-in through the mobile app.',
    'new_user.benefits.rental_owner.information' => 'Stay up to date about what’s going on with your business.',
    'new_user.benefits.rental_owner.calendar' => 'Get access to your calendar and keep control on your rental.',
    'new_user.benefits.cleaning.get_notified' => 'Get notified of each new stay for which you will have to intervene.',
    'new_user.benefits.cleaning.tasks' => 'Check and manage your tasks.',
    'new_user.benefits.cleaning.pictures' => 'Add pictures and comments during or after each of your interventions.',
    'new_user.benefits.cleaning.more' => 'And much more!',
    'new_user.benefits.member.information' => 'Consult all the information you may need.',
    'new_user.do_not_wait' => 'Don’t wait to use these new super powers and follow the next steps to get started:',
    'new_user.admin.set_permissions' => 'Set your Permissions:',
    'new_user.admin.set_your_permissions' => 'As you are an Administrator, you have the right of choosing your permissions. Click on the following button and find yourself in the list. Then click on "Permissions" and set them in the two tabs you will find in that view (Rentals & Permissions):',
    'new_user.admin.set_my_permissions' => 'SET MY PERMISSIONS',
    'new_user.share_news' => 'Share the good news:',
    'new_user.tell_your_manager' => 'Tell your manager that you successfully confirmed your :appName account, then they’ll give you access to the right rentals you need to work on.',
    'new_user.download_app' => 'Download :appName App:',
    'new_user.how_download_app' => 'To start working with :team you must download the Mobile App that will allow you to do so. Search for ":appName" (Not ":appName Guest") in Google Play Store or the Itunes App Store. If you are already using your mobile, just choose your option here:',
    'new_user.learn' => 'Learn the basics about :appName:',
    'new_user.learn.admin' => 'First thing you should do is adding all your company mates to :appName:',
    'new_user.learn.admin.team_members' => 'TEAM MEMBERS',
    'new_user.learn.admin.role_types' => 'ROLE TYPES',
    'new_user.learn.admin.test_user' => 'TEST USER',
    'new_user.learn.rental_manager.guests' => 'Will you be working on Guests Communication?',
    'new_user.learn.rental_manager.tasks' => 'Will you be working on organizing Tasks?',
    'new_user.learn.rental_manager.accounting' => 'Will you be working on Accounting?',
    'new_user.learn.check_in_agent.what_is_rn' => 'What :appName really is?',
    'new_user.learn.check_in_agent.tasks_phone' => 'How I will be managing my Tasks in my mobile phone?',
    'new_user.learn.check_in_agent.bookings_phone' => 'How can I see the bookings in my mobile phone?',
    'new_user.learn.rental_owner' => "I want to see what's going on in my property/ies from my mobile phone:",
    'new_user.learn.rental_owner.bookings' => 'SEE MY BOOKINGS',
    'new_user.learn.rental_owner.rentals' => 'SEE MY RENTAL/S',
    'new_user.yes' => 'YES',
    'new_user.see_video' => 'WATCH VIDEO',
    'new_user.see' => 'SEE',

    // Statuses
    'status.booked' => 'Booked',
    'status.unavailable' => 'Block',
    'status.canceled' => 'Canceled',
    'status.tentative' => 'Tentative',
    'status.lead' => 'Lead',
    'status.request' => 'Request',
    'status.rejected' => 'Rejected',
    'status.archived' => 'Archived',

    // User unauthorized page:
    'unauthorized.access_denied' => 'Access Denied',
    'unauthorized.following_options_will_help' => "<strong>:user</strong>, you don't have the permissions to access this website. The following options might help you:",
    'unauthorized.list.app' => 'Make sure you have the :appName App in your phone to access the information you need. To install it, click here if you are in your phone or just look for :appName (not :appName Guest) in Google Play Store or in your Itunes App Store:',
    'unauthorized.list.want_settings' => 'If you are aiming to reach your settings click this button:',
    'unauthorized.list.your_settings' => 'YOUR SETTINGS',
    'unauthorized.list.if_email_accept_invitation' => 'If you have received a message with the following text, click on the following button and accept the team invitation:',
    'unauthorized.list.your_teams' => 'YOUR TEAMS',
    'unauthorized.list.contact_admin_for_permissions' => 'If you feel you should be able to access the website, please CONTACT YOUR ADMINISTRATOR and ask for permissions on the website.',
    'unauthorized.questions' => '<strong>More Questions?</strong> If you have more questions or doubts about, please check our <a href=":helpCenter" target="_blank">online manual</a>.',

    // Settings
    'settings.rental_copy.owner' => 'Owner',
    'settings.rental_copy.sleeps' => 'Sleeps',
    'settings.rental_copy.surface' => 'Surface',
    'settings.rental_copy.headline' => 'Headline',
    'settings.rental_copy.description' => 'Description',
    'settings.rental_copy.house_rules' => 'House Rules',
    'settings.rental_copy.terms_and_conditions' => 'Terms & Conditions',
    'settings.rental_copy.checkin_time' => 'Check-in Time',
    'settings.rental_copy.max_checkin_time' => 'Max Check-in Time',
    'settings.rental_copy.checkout_time' => 'Check-out Time',
    'settings.rental_copy.checkin_place' => 'Check-in Place',
    'settings.rental_copy.contact_details' => 'Contact Details',
    'settings.rental_copy.checkin_details' => 'Check-in Details',
    'settings.rental_copy.checkout_details' => 'Check-out Details',
    'settings.rental_copy.wifi_details' => 'Wifi Details',
    'settings.rental_copy.door_details' => 'Door Details',
    'settings.rental_copy.maintenance_details' => 'Maintenance Details',
    'settings.rental_copy.general_amenities' => 'General Amenities',
    'settings.rental_copy.room_amenities' => 'Room Amenities',
    'settings.rental_copy.open_window' => 'Availability Window',
    'settings.rental_copy.min_stay' => 'Min Stay',
    'settings.rental_copy.same_day_cut_off' => 'Same Day Cut-off',
    'settings.rental_copy.base_rate' => 'Base Rate',
    'settings.rental_copy.min_rate' => 'Min Rate',
    'settings.rental_copy.max_rate' => 'Max Rate',
    'settings.rental_copy.damage_deposit' => 'Damage Deposit',
    'settings.rental_copy.down_payment' => 'Down Payment',
    'settings.rental_copy.collect_down_payment' => 'Collect Down Payment',
    'settings.rental_copy.seasons' => 'Seasons',
    'settings.rental_copy.long_stay_discounts' => 'Long Stay Discounts',
    'settings.rental_copy.last_minute_discounts' => 'Last Minute Discounts',
    'settings.rental_copy.rental_fees' => 'Rental Fees',
    'settings.rental_copy.rental_upsells' => 'Upsells',
    'settings.rental_copy.cancellation_policy' => 'Cancellation Policy',
    'settings.rental_copy.pictures' => 'Pictures',
    'settings.rental_copy.exterior_pictures' => 'Exterior Pictures',
    'settings.rental_copy.legal_details' => 'Legal Details',
    'settings.rental_copy.pre_check_in_config' => 'Pre Check-in Settings',
    'settings.rental_copy.guest_portal_config' => 'Guest Portal Settings',
    'settings.rental_copy.guide' => 'Guide',

    // Payment Gateway Settings
    'settings.payment_gateway.validation.rental_collision_1' => 'You are selecting all rentals for this gateway while there is another account created, thus rentals collide.',
    'settings.payment_gateway.validation.rental_collision_2' => 'You are selecting at least one rental which is already in another account.',
    'settings.payment_gateway.validation.account_connected_twice' => "This Stripe account is already connected. You can't connect twice the same account. Please disconnect it first and try again.",

    // Clients
    'clients.booking_engine.pending_client' => "This Client was created when a customer tried to make a reservation but yet has not paid. We will collect his/her details when pays or will be deleted in a while if payment is not completed.",
    'clients.booking_engine.new_client' => "This Client was created by the Booking Engine, when confirming a reservation.",
];
