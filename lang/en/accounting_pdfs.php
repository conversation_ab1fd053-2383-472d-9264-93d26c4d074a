<?php

/**
 * Created by IntelliJ IDEA.
 * User: polbatllo
 * Date: 22/12/2017
 * Time: 11:05.
 */

return [
    //General: common in different documents and places. Don't change this section unless you want to change it everywhere:
    'TOTAL' => 'TOTAL',
    'TOTALS' => 'TOTALS',
    'rental_price' => 'Rental Price',
    'rent_price' => 'Rent Price',
    'RENTALS' => 'RENTALS',
    'fees' => 'Fees',
    'FEES' => 'FEES',
    'fees_and_services' => 'Fees & Services',
    'taxes' => 'Tax',
    'TAXES' => 'TAXES',
    'commission' => 'Commission',
    'OTHERS' => 'OTHERS',
    'EXPENSES' => 'EXPENSES',
    'concept' => 'Concept',
    'description' => 'Description',
    'income' => 'Income',
    'amount' => 'Amount',
    'AMOUNT' => 'AMOUNT',
    'name' => 'Name',
    'bookings_between' => 'Bookings Starting Between :start and :finish.',
    'bookings_between_checkout' => 'Bookings Ending Between :start and :finish.',
    'income_calculation_type' => 'Income Calculation Type: :type',
    'net_income' => 'Net Income',
    'gross_income' => 'Gross Income',
    'rentals_included' => 'Rentals Included in the Statement:',
    'sources_included' => 'Sources Included in the Statement:',
    'not_rental_related' => 'Not Rental Related',

    /// Statement summary
    'statement_summary.title' => 'GENERAL INCOME SUMMARY',
    'statement_summary.details' => 'Details',
    'statement_summary.details.headings.income_rentals' => 'Income Rentals: ',
    'statement_summary.details.headings.income_fees' => 'Income Fees: ',
    'statement_summary.details.headings.income_taxes' => 'Income Taxes: ',
    'statement_summary.details.headings.commissions_paid' => 'Commissions Paid: ',
    'statement_summary.details.headings.income' => 'Total Income: ',
    'statement_summary.details.table.rental_name' => 'Rental Name',
    'statement_summary.details.table.bookings' => 'Bookings',
    'statement_summary.details.table.nights' => 'Acc/ Nights',
    'statement_summary.details.table.guests' => 'Guests',
    'statement_summary.details.table.commissions' => 'Commissions',
    'statement_summary.details.table.channels' => 'OTA',
    'statement_summary.fees_breakdown' => 'Fees Breakdown',
    'statement_summary.fees_breakdown.times_booked' => 'Times Booked',
    'statement_summary.fees_breakdown.average_price' => 'Average Price',
    'statement_summary.taxes_breakdown' => 'Taxes Breakdown',
    'statement_summary.taxes_breakdown.included_in_price' => 'Included in Price',
    'statement_summary.taxes_breakdown.tax_price' => 'Tax Price',
    'statement_summary.commissions_breakdown' => 'Commissions Breakdown',
    'statement_summary.commissions_breakdown.imposed_by' => 'Imposed By',
    'statement_summary.commissions_breakdown.commission_price' => 'Commission Price',
    'statement_summary.client_payment_methods' => 'Client Payment Methods',
    'statement_summary.client_payment_methods.total_paid_by_clients' => 'Total paid by clients (including damage deposits)',
    'statement_summary.income_by_channel' => 'Income by Channel',
    'statement_summary.income_by_channel.pending_payments_by_clients' => 'Pending Payments by Clients',

    //Booking Breakdown
    'booking_breakdown.title' => 'BOOKING BREAKDOWN',
    'booking_breakdown.booking_breakdown' => 'Booking Breakdown',
    'booking_breakdown.booking.title' => ':rental - :status From :start To :end',
    'booking_breakdown.booking.reference' => 'REFERENCE: :reference - SOURCE: :source - CLIENT: :client - GUESTS: :guests - NIGHTS: :nights',
    'booking_breakdown.booking.blocking' => 'These dates were not a Booking but just an unavailable period',
    'booking_breakdown.booking.no_money' => 'This Booking Has No Money Related Values.',
    'booking_breakdown.booking.initial_price' => 'Initial Price',
    'booking_breakdown.booking.final_rental_price' => 'Final Rental Price',
    'booking_breakdown.booking.final_price' => 'Final Price',
    'booking_breakdown.booking.imposed_by' => 'Imposed By :source',
    'booking_breakdown.booking.discount' => 'Discount',
    'booking_breakdown.booking.discount_applied' => 'Discount Applied',
    'booking_breakdown.booking.fee' => 'Fee',
    'booking_breakdown.booking.price_before_taxes' => 'Price Before Taxes',
    'booking_breakdown.booking.tax' => 'Tax',
    'booking_breakdown.booking.tax.excluded' => ':tax of :percentage %',
    'booking_breakdown.booking.tax.included' => '(included in rental price)',

    //Payments summary
    'payments.title' => 'NET INCOME PER RECIPIENT',
    'payments.intro' => 'Payments Sent out for the accounting statement :statement.',
    'payments.legal_info' => 'Legal Information:',
    'payments.extra_info' => 'Extra Information:',
    'payments.table.bookings.concept' => 'Bookings',
    'payments.table.bookings.description' => 'Money Received From Bookings',
    'payments.table.fees.description' => 'Money Received From Fees',
    'payments.table.taxes.description' => 'Money Received From Taxes',
    'payments.table.others.concept' => 'Others',
    'payments.table.others.description' => 'Money Received From Other Things',
    'payments.table.expenses.concept' => 'Expenses',
    'payments.table.expenses.description' => 'Money Deducted due to Expenses',
    'payments.table.already_paid' => 'ALREADY PAID',
    'payments.table.left_pay' => 'LEFT TO PAY',
    'payments.sent_payments.text' => 'The following are partial payments already sent out:',
    'payments.sent_payments.amount' => ':amount paid on :date',

    //Invoice
    'invoice.title' => 'INVOICE :number',
    'invoice.issuer' => 'Issuer: :issuer',
    'invoice.client' => 'Client: :client',
    'invoice.date' => 'Issue Date: :date',
    'invoice.table.heading.concept' => 'CONCEPT',
    'invoice.table.heading.price' => 'PRICE',
    'invoice.table.heading.vat' => 'VAT',
    'invoice.table.rental_price' => 'Rental - :booking',
    'invoice.table.fee' => 'Fee - :name',
    'invoice.table.tax' => 'Tax - :name',
    'invoice.table.subtotal' => 'SUBTOTAL',
    'invoice.table.tax.invoice' => 'TAX :percentage %',
    'invoice.table.grand_total' => 'GRAND TOTAL',
    'invoice.others.info' => 'The following section includes things that are not part of the invoice but need to be paid never the less. For more information please contact us.',
    'invoice.others.table.heading.other_things' => 'OTHER THINGS',
    'invoice.others.total' => 'The total amount of the invoice plus other things is :amount',
    'invoice.thank_you' => 'Thank you for trusting in our services',
    'invoice.email.payee.subject' => 'Invoices for :payeeName!',
    'invoice.email.payee.text' => 'Attached you will find the invoices you requested to download for :payeeName.',
    'invoice.email.payee.download_invoices' => 'Download Invoices',

    //Payment
    'payment.title' => 'PAYMENT',
    'payment.rentals_included' => 'Rentals Included:',
    'payment.payee_information' => 'Recipient Information',

    //EMAIL
    'email.subject' => 'Payment :name',
    'email.button' => 'Download the Payment',
    'email.statement_subject' => 'Statement :name',
    'email.statement_button' => 'Download the Statement',

    // Rectification Invoices
    'invoice.correction' => 'CORRECTION OF WRONG INVOICE (:invoice)',

];
