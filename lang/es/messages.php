<?php

// This file is intended to be used for rental ninja features
return [
    // General for emails:
    'email.note' => 'Nota: <PERSON> has recibido este email en tu bandeja de Spam, márcalo como "No es Spam"! De este modo recibirás tus próximas comunicaciones importantes directamente en la bandeja de entrada.',
    'email.intro.dear_name' => 'Apreciado/a :name,',
    'email.intro.there' => ' ',

    // Payment Received Push Notification
    'alert.payment_received.title' => 'Nuevo pago recibido',
    'alert.payment_received.author' => ':author ha añadido un nuevo pago de :amount en :rental',
    'alert.payment_received.no_author' => 'Pago de :amount recibido en :rental',

    // Alerts
    'alert.already_left' => 'Ya se han marchado!',
    'alert.already_left.description' => 'Los clientes parecen haberse marchado sin pagar el importe total del alquiler',
    'alert.damage_deposit' => 'Fianza',
    'alert.damage_deposit.description' => 'Falta Fianza',
    'alert.missing_payment' => 'Faltan pagos',
    'alert.missing_payment.description' => 'Parece que falta algo de dinero',
    'alert.missing_authorisations' => 'Missing Authorizations',
    'alert.missing_authorisations.description' => 'The booking misses Smart Lock Authorizations',
    'alert.device_low_battery' => 'Device Low Battery',
    'alert.device_low_battery.description' => 'The Device is in low battery alarm state',
    'alert.cleaning_needed' => 'Limpieza Necesaria',
    'alert.other_alert' => 'Alerta de :appName',
    'alert.created' => 'Nueva alerta: :alert_name :alert_emoji',
    'alert.modified' => 'Alerta modificada: :alert_name :alert_emoji',
    'alert.already_left.value' => 'Los clientes se han ido y aun deben un total de :value',
    'alert.damage_deposit.value' => 'Un total de :value tiene que ser pagado como parte de la fianza',
    'alert.missing_payment.value' => 'Los clientes deben un total de :value',
    'alert.missing_authorisations.value' => 'The Booking misses an Smart Lock Authorization for the device :name',
    'alert.device_low_battery.value' => 'The Device :name is about to run out of battery.',
    'alert.cleaning_needed.value' => 'El :value necesita una limpieza urgentemente.',
    'alert.other_alert.value' => 'Por favor, comprueba tus alertas, ya que hay alguna cosa que necesita tu atención urgentemente.',

    // New Lead
    'new_lead.title' => '¡Nueva consulta de interesado!',
    'new_lead.body' => 'Preguntando sobre :rental de :from a :to',

    // Booking Created
    'booking_created.title' => 'Nueva Reserva: :rental',
    'booking_created.body' => ':source. :pax pax. De :from a :to',

    // Booking Canceled.
    'booking_canceled.title' => 'Reserva cancelada: :rental',
    'booking_canceled.body' => ':source. :pax pax. De :from a :to',

    // booking_tentative
    'booking_tentative.title' => 'Tentativa de Reserva: :rental',
    'booking_tentative.body' => ':source. :pax Huéspedes desde :from hasta :to',

    // booking_block
    'booking_block.title' => 'Nuevo bloqueo para: :rental',
    'booking_block.body' => ':source desde :from hasta :to',

    // booking_request
    'booking_request.title' => 'Solicitud de reserva: :rental',
    'booking_request.body' => ':source. :pax Huéspedes desde :from hasta :to',

    // booking_rejected
    'booking_rejected.title' => 'Reserva Rechazada: :rental',
    'booking_rejected.body' => ':source. :pax Huéspedes desde :from hasta :to',

    // Check-in/out time Modified
    'check_date_modified.title' => 'Fecha de Check in/out: modificada en :rental',
    'check_date_modified.body' => ':from a :to',

    // Check-in/out time Modified
    'check_time_modified.title' => 'Hora de Check in/out: modificada en :rental',
    'check_time_modified.body' => ':from a :to a: :time',

    // Note for Block Created
    'block_created.created_by_note' => 'Bloqueo creado por :user',

    // Comment created
    'new_comment_created.title' => 'Nuevo comentario en :rental (:from a :to)',
    'author_in_comment.title' => ':author comentó en :rental (:from a :to)',
    'mentioned_in_comment.title' => ':author te mencionó en :rental (:from to :to)',

    // Picture added
    'picture_created.title' => 'Nueva Foto',
    'picture_created.body' => ':author ha añadido una nueva foto',

    // New Message
    'new_message.title' => 'Nuevo Mensaje en la Bandeja de Entrada',
    'new_message.body' => ':person envió un mensaje para una reserva en :rental. Referencia de reserva: :reference.',
    'new_message.email.title' => 'Nuevo mensaje para tu reserva en :city el :on',

    // Home
    'home' => 'Inicio',
    'tasks' => 'Tareas',
    'accounting' => 'Contabilidad',
    'guest_app' => 'Huéspedes',
    'settings' => 'Configuración',
    'your_settings' => 'Tu Configuración',
    'team_settings' => 'Config. Equipo',
    'locale' => 'Idioma',
    'locale.updated' => 'Idioma Actualizado',

    // JOB ASSIGNATION
    'task.assigned' => 'Trabajo Assignado 👨‍🌾',
    'task.assigned.role' => 'El trabajo :title en el :rental puede ser completado por cualquier persona con el rol de :role',
    'task.assigned.you' => 'El trabajo :title en el :rental se te ha asignado! Debe ser completado antes del :date',
    'task.assigned.name' => 'El trabajo :title en el :rental se ha asignado a :name! Debe ser completado antes del :date',

    // JOB COMPLETED
    'task.completed' => 'Trabajo Completado ✅',
    'task.completed.body' => 'El trabajo :title en el :rental ha sido marcado como completado!',
    'task.completed.body.by' => 'El trabajo :title en el :rental ha sido marcado como completado por :requester!',

    // JOB CREATED
    'task.created' => 'Trabajo Creado ✔️',
    'task.created.body' => 'Nuevo Trabajo: :title en el :rental!',
    'task.created.body.by' => ':requester ha creado el trabajo :title en el :rental!',

    // JOB CHANGE OF DATES
    'task.dates' => 'Fechas cambiadas 📅',
    'task.dates.body' => 'Las fechas del trabajo :title en el :rental han sido actualizadas!',
    'task.dates.body.by' => 'Las fechas del trabajo :title en el :rental han sido actualizadas por :requester!',

    // JOB INSPECTED
    'task.inspected' => 'Trabajo Aprobado 👏!',
    'task.inspected.body' => 'El trabajo :title en el :rental ha sido aprobado!',
    'task.inspected.body.by' => 'El trabajo :title en el :rental ha sido aprobado por :requester!',

    // JOB REJECTED
    'task.rejected' => 'Trabajo Rechazado 💩!',
    'task.rejected.body' => 'El trabajo :title en el :rental ha sido rechazado!',
    'task.rejected.body.by' => 'El trabajo :title en el :rental ha sido rechazado por :requester!',

    // JOB UNCOMPLETED
    'task.uncompleted' => 'Trabajo Marcado como no completado ❌!',
    'task.uncompleted.body' => 'El trabajo :title en el :rental ha sido marcado como no completado!',
    'task.uncompleted.body.by' => 'El trabajo :title en el :rental ha sido marcado como no completado por :requester!',

    // Pre-check-in email:
    'guest' => 'Huésped',
    'pre_check_in_form.email.title.no_rental' => 'Tu próxima estancia el :on',
    'pre_check_in_form.email.title.name' => ':name, tu estancia en :city el :on',
    'pre_check_in_form.email.title.no_name' => 'Tu estancia en :city el :on',
    'pre_check-in_email.thank_you_for_booking' => '¡Gracias de nuevo por reservar con nosotros!',
    'pre_check-in_email.we_need_your_details_1' => 'Para poder entrar en el alojamiento y por razones legales,',
    'pre_check-in_email.we_need_your_details_2' => 'es importante rellenar el Formulario de PreCheck-in antes de su llegada.',
    'pre_check-in_email.why_need_your_details_1' => 'Una vez rellenado, recibirá un e-mail de recordatorio con el acceso a los detalles para entrar.',
    'pre_check-in_email.confirm_information' => 'Confirmar los detalles',
    'pre_check-in_email.confirm_information.plain' => 'Para confirmar tu Información, por favor, rellena nuestro formulario de Precheck-in en:',
    'pre_check-in_email.if_cant_click_button' => "Si no puedes clicar en el botón, copia este enlace:",
    'pre_check-in_email.why_you_receive_this_email' => 'Has recibido este email porque hiciste una reserva en un alojamiento gestionado por :team.',
    'pre_check-in_email.how_RN_helps_guests' => ':team utiliza :appName, un software diseñado para ayudar al gestor de esta propiedad a guiarte a través de un proceso fácil de check-in y asegurar que accedes al alojamiento cómodamente. Contacta con el gestor en :email si tienes cualquier duda.',
    'pre_check-in_email.see_you_soon' => 'Hasta pronto!',
    'pre_check-in_email.powered' => 'Enviado mediante <a href=":url" style="color: #999999; font-size: 12px; text-align: center; text-decoration: none;">:name</a>',
    'pre_check-in_email.powered_by' => 'Desarrollado por',
    'pre_check_in_form.sms.text' => "Hola :name, gracias por reservar con nosotros en :rentalName.\nPor favor, rellena el siguiente formulario para escoger tu hora de llegada y acceder a los detalles del check-in: :url",
    'pre_check_in_form.sms.comment.manual' => 'SMS de recordatorio de Check-in Online enviado manualmente: :now UTC',
    'pre_check_in_form.sms.comment.automatic' => 'SMS de recordatorio de Check-in Online enviado automáticamente: :now UTC',

    // Pre-check-in form completed email:
    'thank_you_for_confirming_details' => 'Gracias por confirmar los detalles de llegada!',
    'pre_check-in_completed_email.received_info' => 'hemos recibido tu información!',
    'pre_check-in_completed_email.your_check_in_info' => 'Accede a la Información de tu Check-in',
    'pre_check-in_completed_email.here' => 'aquí',
    'pre_check-in_completed_email.link_active_until' => 'El enlace permanecerá activo hasta el día de salida por si alguna vez necesitas la información de tu estancia.',
    'pre_check-in_completed_email.reference_code' => 'Este es tu código de reserva: :code. Puedes utilizar este código para iniciar sesión en nuestro Portal de Huéspedes (enlace arriba).',
    'pre_check-in_completed_email.check_in_details' => 'Detalles del Check-in',
    'pre_check-in_completed_email.upsales.title' => 'Servicios Extra',
    'pre_check-in_completed_email.upsales.offer' => 'Personaliza tu estancia con nuestros Servicios Extra. Te ofrecemos algunos servicios para mejorar tu estancia con nosotros. ¡Échales un vistazo!',
    'pre_check-in_completed_email.upsales.button' => 'Ver Servicios Extra',

    // Pre-check-in form completed agency email:
    'pre_check-in_completed_agency_email.subject' => 'Formulario de PreCheck-In Completado para la Reserva :reference',
    'pre_check-in_completed_agency_email.title' => 'Formulario de PreCheck-In Completado',
    'pre_check-in_completed_agency_email.intro' => 'Hola! Vuestro cliente :clientName ha completado el formulario de PreCheck-In.',
    'pre_check-in_completed_agency_email.table.title' => 'Información Proporcionada',
    'pre_check-in_completed_agency_email.table.confirmed_email' => 'Email Confirmado',
    'pre_check-in_completed_agency_email.table.confirmed_phone' => 'Teléfono Confirmado',
    'pre_check-in_completed_agency_email.table.arrival_type' => 'Medio de Transporte',
    'pre_check-in_completed_agency_email.table.eta' => 'Hora de Llegada',
    'pre_check-in_completed_agency_email.arrival_details' => 'Detalles Llegada',
    'pre_check-in_completed_agency_email.passport_received' => 'Documentos Recibidos',
    'pre_check-in_completed_agency_email.passport_verified' => 'Verificado con DocScan:',
    'pre_check-in_completed_agency_email.passport_number' => 'Número Pasaporte/Documento Identidad:',
    'pre_check-in_completed_agency_email.view_passport' => 'Ver Documento',

    // Booking confirmation & payment gateway
    'payment_gateway.charge_description' => 'Reserva del alquiler :rentalName, check-in el :date para :guests huésped(es). Alojamiento gestionado por :team.',
    'payment_gateway.charge_description.downpayment' => "Pago inicial de la reserva para el Alojamiento :rentalName, check-in el :date para :guests huésped(es) y administrado por :team.",
    'payment_gateway.charge_description.hold' => 'Fianza retenida para:',
    'payment_gateway.charge_description.hold-return' => 'Tus fondos se liberarán algunos días después de tu salida, una vez que nuestro equipo haya validado su idoneidad. Gracias por tu comprensión.',
    'payment_gateway.charge_description.charged-deposit' => 'Este pago incluye la fianza a pagar. El importe de la fianza te será devuelto unos días después de tu salida; una vez que nuestro equipo haya revisado el Alojamiento. Gracias por tu comprensión.',
    'payment_gateway.charge_description.upsales' => 'Compra de los siguientes Servicios Extra: :buyingList',
    'payment_gateway.booking_already_paid' => 'Tu reserva ya está pagada. Gracias por tu pago.',
    'payment_gateway.outside_damage_deposit_window' => "La única cantidad que queda por pagar es la fianza. Sin embargo, las fianzas sólo pueden abonarse el día del check-in. Podrá hacerlo directamente en el Portal de Huéspedes Online para su comodidad. Revise su email. Busque el email de Confirmación de su Formulario, a través del cual tendrá acceso directo!",
    'payment_gateway.wrong_amount' => "El Importe proporcionado es incorrecto. No debería ser mayor que lo que queda por pagar. Ponte en contacto con tu anfitrión.",
    'payment_gateway.wrong_amount_deposit_charge' => "El importe proporcionado es incorrecto. Contiene parte de la Fianza y no podemos retener el importe (ni en estancias cortas ni fuera del plazo de recogida) ni cobrarlo (la configuración de los cargos de la Fianza en franjas está desactivada). Contacta a tu anfitrión y pasa este mensaje.",
    'payment_gateway.thank_your_for_payment' => 'Gracias por tu pago. ¡Te esperamos!',
    'payment_gateway.refund.error_with_stripe' => "No pudimos reembolsar su pago con Stripe. Esto es lo que dice Stripe: :error",
    'payment_gateway.refund.partial.note' => 'Reembolso parcial de Stripe.',
    'payment_gateway.damage_deposit.note' => 'Fianza de Stripe (Pre-autorización)',
    'payment_gateway.damage_deposit.returned' => 'Caducada Fianza Pre-autorizada via Stripe.',
    'payment_gateway.damage_deposit.partially_captured' => 'Fianza de Stripe parcialmente retenida. Este es el importe devuelto.',
    'payment_gateway.damage_deposit.task.title' => 'Devolución de la Fianza para la Reserva :bookingReference y check-out en :checkOut',
    'payment_gateway.damage_deposit.task.description' => 'El depósito de daños de la reserva :bookingReference fue debidamente cobrado junto con el pago de la reserva. Por lo tanto, es obligatorio revisarlo y reembolsarlo MANUALMENTE al huésped. Los huéspedes salieron el :checkOut',
    'payment_gateway.damage_deposit.report_email.subject' => ':gatewayName: tus próximas Fianzas a ser devueltas',
    'payment_gateway.damage_deposit.report_email.title' => 'Informe de Fianza a Devolver próximamente',
    'payment_gateway.damage_deposit.report_email.intro' => "¡Hola! Aquí están las Fianzas que se devolverán automáticamente dentro de entre :start y :end horas.
Nosotros nos encargaremos de todo, no necesitas hacer nada a menos que debas retener alguna fianza (de forma parcial o total) antes de que las liberemos. En ese caso, accede al pago de la reserva y entra dentre del mismo pago de la fianza para retenerla.",
    'payment_gateway.damage_deposit.report_email.table_header' => 'Detalles de la Fianza',
    'payment_gateway.damage_deposit.report_email.deposit_amount' => 'Importe de la Fianza',
    'payment_gateway.damage_deposit.report_email.return_date' => 'Fecha de devolución (aproximada)',
    'payment_gateway.damage_deposit.report_email.payment_intent' => 'N de Intención de Pago de Stripe',
    'payment_gateway.damage_deposit.report_email.view_in_stripe' => 'Ver pago en Stripe',
    'payment_gateway.settings.validation.sources_disabled' => "Estás deshabilitando un Origen de Reservas que se está usando actualmente en un sitio web de distribución. No se puede eliminar, de lo contrario no se podrían crear reservas desde ese sitio web.",

    // Down payment
    'payment_gateway.down_payment_already_paid' => 'Tu paga y señal está pagada. Gracias por tu pago.',

    // Upsales
    'upsales.push_notification.title' => 'Nuevo/s Servicio/s Extra adquiridos',
    'upsales.push_notification.body' => 'Servicios adquiridos: :services. Reserva en :rentalName a partir del :day. Referencia de reserva: :reference.',
    'upsales.slack_notification.body' => 'Servicios reservados: :services.',

    // Exports
    'exports.mail.subject' => 'La exportación que has solicitado está lista!',
    'exports.mail.text' => 'Aquí encontrarás la exportación que has solicitado descargar.',
    'exports.mail.download' => 'Descargar Exportación',
    'exports.mail.link_expiry_note' => 'Puedes descargar esto durante los próximos :days días. Después el acceso caducará.',

    // Messages for message_to_user view
    'message_to_user.promotion_trial.error.title' => 'No es Posible',
    'message_to_user.promotion_trial.error.promotion_expired' => 'Lamentamos informarte que esta promoción ha caducado. Ya no se puede acceder a ella.',
    'message_to_user.promotion_trial.error.not_team_member' => 'Parece que no eres parte de ningún Equipo ahora mismo. Esta promoción es solamente para usuarios que pertenezcan a un Equipo.',
    'message_to_user.promotion_trial.error.not_admin' => 'Lo sentimos, esta promoción es solo accesible para Usuarios con el rol de Administrador o Propietario del Equipo. Avisa a tus Administradores acerca de esta promoción!',
    'message_to_user.promotion_trial.error.not_website_access' => 'Lo sentimos, necesitas tener acceso a la web para poder acceder a esta promoción. Cambia a otro usuario o avisa a alguien de tu equipo con dicho acceso para aplicar esta promoción.',
    'message_to_user.promotion_trial.error.promotion_not_applicable' => 'Esta promoción está disponible solamente una vez para equipos que no estén Suscritos o actualmente en Periodo de Prueba. Parece que alguien en tu equipo ya la ha usado, has refrescado esta página o bien tu equipo está actualmente Suscrito o en Periodo de Prueba.',
    'message_to_user.promotion_trial.success.title' => 'Promoción aplicada!',
    'message_to_user.promotion_trial.success.message' => "La promoción se ha aplicado correctamente! Ahora debes Reconectar Rental Ninja con Smily para que podamos sincronizar tu información otra vez. Necesitarás acceso a la cuenta de Smily de tu equipo. Si no tienes acceso, contacta a alguien en tu equipo para hacerlo. Contactanos si tienes algún problema.",
    'message_to_user.promotion_trial.success.button' => 'Reconectar con Smily',

    // Qr Code Login
    'qrcode.title' => 'Código QR App',
    'qrcode.info' => 'Puedes usar este Código QR para entrar en la Aplicación Móvil :app',

    // General
    'sync_in_progress' => 'Sincronización en progreso. Danos unos minutos...',
    'date_to_date' => 'Del :start hasta :end',
    'reservation_details_guest' => 'Reserva en :City el :on',
    'dear_guest' => 'Querido Húesped',
    'go_to_guests_portal' => 'Ir al Portal de Huéspedes',
    'booking_not_found.guests' => 'No encontramos esta Reserva. Por favor, contacta con el Administrador.',

    // Custom Error Messages
    'errors.task.access_denied' => 'Este Trabajo ya no está disponible.',
    'errors.accounting.settlements.already_exists' => 'Una Liquidación ya existente entra en conflicto con los parametros aquí elegidos',
    'errors.accounting.settlements.already_exists.modify_filters' => 'Por favor, modifica los parametros y vuelve a intentarlo',
    'errors.accounting.scheduled_settlements.already_exists' => 'Al menos uno de los Alojamientos seleccionados ya está incluido en otra Liquidación Programada existente y activa, con la misma moneda. Éste es el ID(s) del Alojamiento/s repetido/s: :rental_ids',

    // New Lead Messages
    'new_ru_lead.subject' => '¿Tienes problemas de registro con Rental Ninja? Estamos aquí para ayudar.',
    'new_ru_lead.content_1' => '¡Hola :name!',
    'new_ru_lead.content_2' => "Hola. Somos el equipo de Rental Ninja. ¿Ha surgido algún problema con su suscripción? No se preocupe, haciendo clic en el siguiente botón podrá volver al formulario de registro para continuar.",
    'new_ru_lead.button' => 'Página de suscripción',
    'new_ru_lead.content_3' => 'Si el problema persiste no dude en responder mensaje. Está a un paso de hacer la gestión de sus alojamientos mucho más fácil y agradable.',
    'new_ru_lead.salutation' => 'El equipo de Rental Ninja,',

    // Errors when trying to push information to providers
    'push_error.see_booking' => 'Ver Reserva',
    'push_error.contact_us' => 'Si el problema persiste, no dudes en ponerte en contacto con Rental Ninja.',

    'push_error.create_payment.subject' => 'Error al sincronizar un nuevo pago',
    'push_error.create_payment.body' => 'El pago que creaste recientemente en Rental Ninja no se pudo sincronizar con :provider. Revisa los campos de pago y la configuración del proveedor y vuelve a intentarlo en unos minutos.',

    'push_error.create_payment_validation.subject' => 'Error al sincronizar un nuevo pago',
    'push_error.create_payment_validation.body' => 'El pago que acaba de crear en Rental Ninja no se pudo sincronizar con :provider. Si estás intentando agregar un pago de Fianza, asegúrate de haber configurado :provider para aceptar pagos de Fianzas antes de que comience la Reserva.',

    'push_error.cancel_payment.subject' => 'Error al sincronizar una cancelación de pago',
    'push_error.cancel_payment.body' => 'El pago que creaste recientemente en Rental Ninja no se pudo sincronizar con :provider. Revisa los campos de pago y la configuración del proveedor y vuelve a intentarlo en unos minutos.',

    'push_error.add_comment.subject' => 'Error al sincronizar un nuevo comentario de Reserva',
    'push_error.add_comment.body' => '¡Un nuevo comentario en Rental Ninja no se pudo sincronizar con :provider! Intente agregarlo manualmente en :provider.',

    'push_error.delete_comment.subject' => 'Error al sincronizar un comentario de reserva eliminado',
    'push_error.delete_comment.body' => 'Un comentario eliminado en Rental Ninja no se pudo sincronizar con :provider! Intenta agregarlo manualmente en :provider.',

    // Down Payment Email (in order to confirm the booking).
    'downpayment_email.subject' => 'Completa tu Reserva: se requiere paga y señal',
    'downpayment_email.intro' => 'Gracias por elegir :property_name para tu estadía en :on, reservada a través de :booking_source. Estamos encantados de recibirte y esperamos que tu estancia sea magnífica.',
    'downpayment_email.instruction_intro' => 'Para confirmar tu reserva se requiere una paga y señal.',
    'downpayment_email.button' => 'Confirmar Reserva',
    'downpayment_email.button.if_cant_click_button' => 'Si no puedes hacer clic en el botón de arriba, copia y pega el siguiente enlace en tu navegador:',
    'downpayment_email.important_note' => '**Importante:** Tu reserva con referencia de reserva :booking_reference se retiene temporalmente a la espera de la paga y señal. Para evitar cualquier inconveniente, recomendamos completar este paso lo antes posible. El saldo restante de tu reserva deberá pagarse antes del :final_payment_date, y se proporcionarán más detalles a medida que se acerque la fecha de llegada.',
    'downpayment_email.assistance_offer' => 'Si tienes alguna pregunta o necesitas ayuda con tu pago, nuestro equipo de soporte está listo para ayudarte. Comunícate con nosotros directamente a :contact_email o a :contact_phone para cualquier consulta o asistencia adicional.',
    'downpayment_email.closing' => 'Agradecemos tu elección de :property_name para vuestra visita. Esperamos que sea placentera e inolvidable!',

    // Booking confirmation email
    'booking_confirmation_email.subject' => 'Reserva confirmada: :city el :on',
    'booking_confirmation_email.from' => 'Desde',
    'booking_confirmation_email.to' => 'Al',
    'booking_confirmation_email.days_before_arrival' => 'días antes de la llegada',
    'booking_confirmation_email.penalty' => 'multa',
    'booking_confirmation_email.cancellation_policy' => 'Política de Cancelación',
    'booking_confirmation_email.payments' => 'Pagos',
    'booking_confirmation_email.amount_paid' => 'Cantidad pagada',
    'booking_confirmation_email.left_to_pay' => 'Queda por pagar',
    'booking_confirmation_email.left_to_pay_deposit' => 'Fianza Pendiente de Pago',
    'booking_confirmation_email.how_to_arrive' => 'Cómo llegar',
    'booking_confirmation_email.location' => 'Ubicación',
    'booking_confirmation_email.checkin' => 'Check In',
    'booking_confirmation_email.checkout' => 'Check Out',
    'booking_confirmation_email.booking_reference' => 'Referencia de la Reserva:',
    'booking_rejected_email.email' => 'Lamentamos informarle que tu reserva para :property_name el :on a través de :booking_source ha sido rechazada.',
    'booking_rejected_email.reason' => ' El motivo del rechazo es probablemente porque el alojamiento ya no está disponible. Estaba disponible pero al momento de finalizar tu pago se había reservado. Todos sus fondos han sido liberados y no se te ha hecho ningún cargo.',
    'booking_rejected_email.alternative_suggestions' => 'Te animamos a explorar otras propiedades disponibles en nuestra plataforma. Puedes intentarlo',
    'booking_rejected_email.assistant_offer' => 'Si necesitas ayuda, comunícate con nosotros a :contact_email o llámanos al :contact_phone.',
    'booking_rejected_email.closing' => 'Nos disculpamos por cualquier inconveniente causado. Gracias por tu comprensión.',
    'booking_rejected_email.pre_check-in_email' => '¡Esperamos verte pronto!',
    'booking_rejected_email.dear_name' => 'Apreciado/a :name,',
    'booking_rejected_email.subject' => 'Reserva rechazada: :city el :on',
    'booking_rejected_email.status_rejected' => 'ESTADO DE LA RESERVA: RECHAZADA',

    // Create new booking errors
    'store_booking.error.min_stay' => 'La reserva no cumple con los requisitos de estancia mínima de :num_nights noches. ¿Quieres crear la reserva de todos modos?',
    'store_booking.error.max_stay' => 'La reserva no cumple con los requisitos de estadía máxima de :num_nights noches. ¿Deseas realizar la reserva de todos modos?',
    'store_booking.error.changeover' => 'La reserva no cumple con los requisitos establecidos. ¿Deseas crear la reserva de todos modos?',
    'store_booking.error.not_available' => 'El alquiler ya no está disponible.',

    // booking upsells email
    'upsell_purchased_email.subject' => 'Confirmación de compra de Servicio Extra',
    'upsell_purchased_email.thank_you' => '¡Gracias por tu compra!',
    'upsell_purchased_email.intro' => 'Nos complace confirmar tu reciente compra de Servicios Adicionales para tu estancia en :property_name.',
    'upsell_purchased_email.purchased_items' => 'Ítems comprados',
    'upsell_purchased_email.total_paid' => 'Total pagado',
    'upsell_purchased_email.assistance_offer' => 'Si tienes alguna pregunta sobre tu compra o necesitas ayuda, no dudes en contactarnos a: contact_email o :contact_phone.',
    'upsell_purchased_email.closing' => 'Esperamos mejorar tu estancia en :property_name con estos servicios adicionales.',

    // Inbox
    'inbox.attachments.unsupported_extension.guest' => 'ADVERTENCIA: El huésped adjuntó un archivo con una extensión no compatible. Pídele que utilice una de las siguientes extensiones: :extensions.',
    'inbox.attachments.unsupported_extension.user' => 'Extensión no compatible en el archivo: :file. Utiliza una de las siguientes extensiones: :extensions.',
    'inbox.attachments.file_too_big' => 'El archivo :file es demasiado grande. El tamaño máximo del archivo es :max_size MB.',
    'inbox.attachments.total_files_too_big' => 'El tamaño máximo de todos los archivos adjuntos es: max_size MB.',
    'inbox.errors.api' => 'Error al enviar por API. Inténtalo de nuevo y si el problema persiste, ponte en contacto con el servicio de asistencia.',

    // Home Automation
    'home_automation.booking_authorisations.created_but_not_sent' => "Todas las autorizaciones se crearon correctamente, pero aún no se enviaron al huésped porque la reserva no está suficientemente pagada según la configuración del Portal del Huésped de tu alojamiento. Las autorizaciones se enviarán una vez alcanzado el pago configurado.",
    'home_automation.account.errors.unsuccessful_connection' => "La Conexión NO se ha logrado",
    'home_automation.account.errors.duplicated_account' => "Estás intentando conectar una cuenta que ya está conectada. Por favor, selecciona otra.",
    'home_automation.devices.errors.open_error' => "Tenemos un problema con el dispositivo. Por alguna razón, no pudimos abrir la puerta. Verifique el estado del dispositivo.",
    'home_automation.devices.errors.lock_error' => "Tenemos un problema con el dispositivo. Por alguna razón, no pudimos cerrar la puerta. Verifica el estado del dispositivo.",
    'home_automation.devices.errors.existing_code' => "El código que proporcionas ya existe en la lista de códigos del Dispositivo.",
    'home_automation.devices.errors.booking_without_smartlocks' => "Esta reserva no dispone de ningún dispositivo Smart Lock.",
    'home_automation.devices.errors.some_devices_offline' => "Al menos un dispositivo vinculado al Alquiler de la Reserva está fuera de línea, por lo que no podemos crear ninguna Autorización para esta Reserva.",
    'home_automation.devices.errors.device_offline' => "Hemos detectado que el dispositivo está desconectado. No pudimos realizar la acción.",
    'home_automation.devices.errors.motor_blocked' => "Dispositivo con el motor bloqueado.",
    'home_automation.devices.errors.keypad_battery_critical' => "La batería del teclado está en estado crítico.",
    'home_automation.devices.errors.door_sensor_battery_critical' => "La batería del sensor de puerta está en estado crítico.",
    'home_automation.devices.errors.nuki.invalid_code' => "El código no es válido. Debe ser numérico, de 6 dígitos y no puede contener 0 ni empezar por 12.",
    'home_automation.authorisations.errors.offline' => "Este dispositivo está desconectado. No se pudo sincronizar la autorización. Conéctalo y vuelve a intentarlo.",

    'home_automation.smartlock_authorisation_email.subject' => 'Tus instrucciones de check-in para tu estancia en :city el :on',
    'home_automation.smartlock_authorisation_email.introduction' => 'Nos comunicamos contigo para brindarte la información necesaria para acceder a la propiedad.',
    'home_automation.smartlock_authorisation_email.download_app_instructions' => 'Para acceder a la propiedad, descarga la aplicación y sigue las instrucciones:',
    'home_automation.smartlock_authorisation_email.download_app' => 'Descargar Aplicación',
    'home_automation.smartlock_authorisation_email.code_instructions' => 'Para acceder a la propiedad, utiliza el siguiente código:',
    'home_automation.smartlock_authorisation_email.valid_from' => 'Válido desde',
    'home_automation.smartlock_authorisation_email.valid_until' => 'Válido hasta',
    'home_automation.smartlock_authorisation_email.see_you_soon' => '¡Hasta pronto!',
    
    // Additional smartlock instructions for email
    'home_automation.smartlock_authorisation_email.door_uses_smartlock' => 'Esta puerta utiliza un sistema de Cerradura Inteligente',
    'home_automation.smartlock_authorisation_email.download_app_using_button' => 'Utiliza el botón de arriba para descargar la Aplicación de Cerraduras Inteligentes',
    'home_automation.smartlock_authorisation_email.open_app_and_follow_instructions' => 'Abre la aplicación y sigue las instrucciones de configuración',
    'home_automation.smartlock_authorisation_email.opening_the_door' => 'Abriendo la Puerta',
    'home_automation.smartlock_authorisation_email.need_to_be_close_to_device' => 'Debes estar cerca de la Puerta para usar la aplicación',
    'home_automation.smartlock_authorisation_email.follow_app_instructions' => 'Siga las instrucciones de la aplicación para desbloquear la puerta',
    'home_automation.smartlock_authorisation_email.contact_host_for_issues' => 'Si tiene algún problema, por favor, comunícate con tu anfitrión',
    'home_automation.smartlock_authorisation_email.door_uses_keypad' => 'Esta puerta utiliza un sistema de teclado',
    'home_automation.smartlock_authorisation_email.locating_keypad' => 'Localiza el teclado cerca de la Puerta de entrada',
    'home_automation.smartlock_authorisation_email.enter_code_as_displayed' => 'Introduce el código que se muestra arriba exactamente como se muestra',
    'home_automation.smartlock_authorisation_email.door_instructions' => 'Instrucciones de la Puerta',
    
    // Additional door access messages
    'home_automation.door_instructions' => 'Instrucciones de la Puerta',
    'home_automation.door_key' => 'Código de la Puerta',
    'home_automation.smart_lock' => 'Cerradura Inteligente',
    'home_automation.smart_lock_keypad' => 'Teclado de Cerradura Inteligente',
    'home_automation.rental_door_key' => 'Llave de la puerta del Alojamiento',
    'home_automation.no_data_found' => 'No se encontraron datos',
    'home_automation.door_uses_keypad' => 'Esta puerta utiliza un sistema de teclado',
    'home_automation.door_uses_smart_lock' => 'Esta puerta utiliza un sistema de Cerradura Inteligente',
    'home_automation.download_and_open_app' => 'Descargue y abra la App :name',
    'home_automation.text_copied_to_clipboard' => 'Texto copiado al portapapeles',
    'home_automation.link_copied' => 'Enlace copiado',
    'home_automation.use_door_key_as_instructions' => 'Utilice la llave de la puerta según las instrucciones proporcionadas',
    'home_automation.try_again' => 'Volver a Intentar',
    'home_automation.error' => 'Se produjo un error',
];

