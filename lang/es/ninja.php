<?php

// This file is intended to be used for ninja product strings
return [
    'creating_team' => 'Creando equipo...',
    'back' => 'Atrás',
    '60_day_money_back' => '60 Días de garantía de devolución de dinero',
    'terms_and_conditions' => 'Términos y Condiciones',
    'failed_payment_owner' => 'El pago de tu subscripción ha fallado, :linkOpen confirma tu pago :linkClose para activar tu subscripción! Si no, podrías perder el acceso a tu cuenta pronto.',
    'failed_payment_member' => 'El pago de tu subscripción ha fallado. Si el propietario de tu equipo no completa el pago pendiente, podrías perder el acceso a tu cuenta pronto.',

    // Coupons
    'coupon_only_valid_in_plans' => 'El cupón actual <strong>SOLO ES VÁLIDO</strong> para los siguientes planes: <strong>:plans</strong>.<br><strong> NO SERÁ</strong> aplicado a otros planes.',
    'coupon_explanation' => 'El cupón <strong>:value% de descuento</strong> se aplicará a tu subscripción por un total de <strong>:months meses</strong>.',
    'coupon_explanation_once' => 'El cupón <strong>:value% discount</strong> se aplicará a su suscripción en <strong>el primer pago</strong>.',
    'coupon_explanation_forever' => 'El cupón <strong>:value% discount</strong> se aplicará a su suscripción <strong>para siempre</strong>.',
    'coupon_prices_not_shown_bellow' => 'El desglose de precios a continuación muestra el plan de precios estándar, sin incluir el descuento. Sin embargo, su Cupón se aplicará y se reflejará en su Plan de Suscripción y Factura.',
    'coupon_price_explanation' => '- Para el plan <strong>:plan</strong>, el Precio Final Mensual por Alojamiento será de <strong>:monthly€</strong> durante los primeros <strong>:months meses</strong><br>',
    'coupon_price_explanation_once' => '- Para el plan <strong>:plan</strong>, el Precio final mensual por Alojamiento será de <strong>:monthly€</strong> durante el primer <strong>período de suscripción</strong><br>',
    'coupon_price_explanation_forever' => '- Para el plan <strong>:plan</strong>, el Precio final mensual por Alojamiento será de <strong>:monthly€ para siempre</strong><br>',

    // Providers
    'smoobu.use_your_smoobu_account' => 'Usar tu cuenta de Smoobu',
    'smoobu.connect_to_smoobu' => 'Conectar a tu Cuenta de Smoobu',
    'smoobu.we_need_your_api_key' => 'Necesitamos tu Clave API de Smoobu para conectar',
    'smoobu.get_your_api_key_here' => 'Consígela Aquí',
    'smoobu.connect' => 'Conectar con Smoobu 🔌💡',
    'smoobu.account_already_exists' => 'Esta Cuenta de Smoobu ya está Registrada en Rental Ninja. Por favor, inicia sesión.',
    'smoobu.try_again' => 'Volver a Intentar',
    'rentals_united.connect_to_rentals_united' => 'Conéctese a su cuenta de Rentals United',
    'rentals_united.try_again' => 'Volver a intentar',
    'rentals_united.rentals_united_username' => 'ID de usuario de Rentals United',
    'rentals_united.team_name' => 'Nombre Equipo/Agencia',
    'rentals_united.user_name' => 'Nombre',
    'rentals_united.email' => 'Dirección de email',
    'rentals_united.password' => 'Contraseña',
    'rentals_united.password_confirmation' => 'Confirmar contraseña',
    'rentals_united.connect' => 'Comprueba la conexión 🔌💡',
    'rentals_united.invalid_username' => 'Se requiere el ID de usuario de Rentals United para continuar.<br>Haga clic en el siguiente enlace para otorgar a Rental Ninja acceso a su cuenta de Rentals United y vuelva a intentarlo.',
    'rentals_united.attempt_failed' => '<strong>ATENCIÓN</strong><br>El intento de conexión a Rentals United <b>FALLÓ</b>.<br>Haga clic en el siguiente enlace para conceder a Rental Ninja acceso a su cuenta de Rentals United y vuelva a intentarlo.',
    'rentals_united.team_already_exists' => "Su equipo de Rentals United ya está registrado en Rental Ninja. Por favor, <a href=':url'>Inicie la sesión</a>.",
    'oauth.reconnect.successful' => 'Reconectado con éxito. Ya puedes cerrar esta ventana/pestaña.',

    // New team templates
    'templates.cleaning.checklist.title' => 'Ejemplo: Limpieza e inspección posterior al check-out',
    'templates.cleaning.checklist.description' => 'Limpiar la propiedad e inspeccionar si hay daños o artículos faltantes después de la salida del huésped.',
    'templates.cleaning.checklist_item.0.title' => 'Limpieza completa',
    'templates.cleaning.checklist_item.0.description' => 'Limpieza completa de toda la propiedad, incluidas todas las habitaciones y espacios exteriores.',
    'templates.cleaning.checklist_item.1.title' => 'Inspección de daños',
    'templates.cleaning.checklist_item.1.description' => 'Inspeccione si hay daños y documente con fotografías si es necesario.',
    'templates.cleaning.checklist_item.2.title' => 'Verificar inventario',
    'templates.cleaning.checklist_item.2.description' => 'Revisar inventario de suministros (papel higiénico, jabón, artículos esenciales de cocina).',
    'templates.cleaning.checklist_item.3.title' => 'Camas',
    'templates.cleaning.checklist_item.3.description' => 'Verificar que la ropa de cama esté lavada y las camas hechas nuevamente.',
    'templates.cleaning.checklist_item.4.title' => 'Existencias',
    'templates.cleaning.checklist_item.4.description' => 'Reponga los artículos usados y reemplace los artículos de tocador.',
    'templates.monthly_inspection.checklist.title' => 'Ejemplo: Inspección de mantenimiento de rutina',
    'templates.monthly_inspection.checklist.description' => 'Realice una inspección exhaustiva de los sistemas y equipos de la propiedad para garantizar que todo esté funcionando correctamente, reduciendo la probabilidad de quejas de los huéspedes a mitad de la estadía.',
    'templates.monthly_inspection.checklist_item.0.title' => 'Eléctrico',
    'templates.monthly_inspection.checklist_item.0.description' => "- Pruebe todas las luminarias y reemplace las bombillas quemadas.\n- Asegúrese de que los tomacorrientes funcionen correctamente; pruebe la energía en los electrodomésticos clave.",
    'templates.monthly_inspection.checklist_item.1.title' => 'Plomería',
    'templates.monthly_inspection.checklist_item.1.description' => "- Revise los grifos, las duchas y los inodoros para detectar fugas.\n- Haga funcionar el lavavajillas y la lavadora brevemente para confirmar su funcionamiento.\n- Inspeccione los desagües para detectar obstrucciones y asegúrese de que la presión del agua sea adecuada.",
    'templates.monthly_inspection.checklist_item.2.title' => 'Calefacción y refrigeración',
    'templates.monthly_inspection.checklist_item.2.description' => "- Pruebe el sistema HVAC, ajuste la temperatura a un nivel cómodo.\n- Limpie los filtros de aire y verifique si hay ruidos inusuales o fallas de funcionamiento.",
    'templates.monthly_inspection.checklist_item.3.title' => 'Controles de seguridad',
    'templates.monthly_inspection.checklist_item.3.description' => "- Verifique que los detectores de humo y de monóxido de carbono estén operativos.\n- Verifique la carga del extintor de incendios y reemplácelo si está vencido.\n- Asegúrese de que todas las ventanas y puertas cierren bien.",
    'templates.monthly_inspection.checklist_item.4.title' => 'Mantenimiento exterior',
    'templates.monthly_inspection.checklist_item.4.description' => "- Inspeccione la iluminación exterior y asegúrese de que todas las luces funcionen.\n- Revise el equipo de la piscina (si corresponde) y confirme la limpieza del agua.\n- Inspeccione los muebles de exterior para detectar daños o desgaste.",
];
