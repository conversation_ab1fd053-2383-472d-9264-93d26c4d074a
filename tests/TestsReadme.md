# Readme file of the Testing suite

We use PEST as our testing framework. Check the [documentation](https://pestphp.com/docs/installation).

## 1- How are tests organized?

For now, we organize tests in 3 folders: Feature, Unit and OnBackup.

### Feature VS Unit tests
There is a bit of a debate in the community about how unit tests should be. If they should test only functions, or classes or even more.
Also, if they should touch the DB or not. We are going to be pragmatic here and also follow the recommendations given in the
[Spatie Laravel Testing with Pest course chapter](https://spatie.be/courses/testing-laravel-with-pest/discovering-unit-tests)
which talks about this. We will keep the separation between Feature and Unit tests for a while, to see if it has benefits, as the majority
of the community follows this pattern of separation. But under my point of view, they could be merged. So in some time, 
if we don't find any special benefit for us as both tests touch the DB, we may merge them.

#### Unit tests
Our unit tests will be used to test single methods or classes. In particular, they will be super useful to test our business
logic, which is the most important logic to test, which is all migrated to our Actions! So, unit tests are perfect to test
our Actions, which contain the core logic of our application.

We should also test here things such as Helper Functions or logic placed in Models or Query Builders.

#### Feature tests
In feature tests, we will locate all tests which are not unit tests and that touch multiple classes. The line between the two
is somehow thin. Because yeah, when you test an action, it may be calling other actions, helper functions, logic in models,...
But, let's just be pragmatic, if your entry point of the test is an action, put it in Unit tests and let's put in feature tests the
rest of tests. A good example of a feature test could be testing a route: you will be testing middleware, a Request (validation),
actions, resources, maybe views,...

### OnBackup Tests
This folder will contain tests which we need to run using a backup database. Sometimes, to check if a code works well, the best
approach is to test it against lots of already existing data, instead of creating custom data for the test. A good example of this
is the calculation of expanded settlements, where we do lots of things there. Real data could have null values, empty values 
or other weird combination of data which can make the code to break, therefore catching edge cases given by customer data.

These kind of tests require a different configuration. This is why they need to be in a different folder.

## 2- Adopted testing strategy & PEST configuration

As you can see from the previous section, we have some tests which need to run on an empty test database, while some in a real
backup database. For tests done in an empty testing database, we will use laravel model factories to populate the DB as desired.
We need two different configurations for each kind of test.

Now, Pest configuration is done in the Pest.php file. There you have the "Test Case" section, where you can define which Test classes
and traits are used to define the behaviour of the tests. You can define different behaviours per each folder. As you can see,
we use the LazilyRefreshDatabase trait. What this does is to use the Laravel trait RefreshDatabase only in the case that in that test, 
we are using the DB. Therefore, it doesn't permanently persist the records of the test in DB, removing them after the test,
as it uses a transaction for all DB connections (check laravel docs). More info about LazilyRefreshDatabase in 
[this laracast episode](https://laracasts.com/series/jeffreys-larabits/episodes/10). What this means is that you need to be aware that 
RefreshDatabase will run migrate:fresh for the first test only. Therefore, any data stored in the DB used when you run one of these tests, 
will be removed. More info about laravel traits in this [laracast episode](https://laracasts.com/series/whats-new-in-laravel-5-5/episodes/14).

**Note:** after some time of investigation, looks like when migrating the DB before each test (using LazilyRefreshDatabase), laravel will 
look for DB dumps (in database/schema) which have the same db connection name (ex: testing-schema.dump). That's why if you check
the pest configuration, we make sure that picks the same dump as we use for our normal DB. This is not documented in Laravel
documentation, therefore it's important to keep it in mind. An alternative solution could be that, whenever we run 
``php artisan schema:dump --prune``, we make sure we make another copy of the file with the DB_CONNECTION name it is used for testing.
You can do that modifying the script we use to create the dump schema (bin/php/dump-schema.sh).

The rest of the configuration is done in the phpunit.xml file, where you can define the ENV variables for the tests. The values defined there,
will substitute the values of the local env file.

What all this means is that we should follow one of the two configurations depending on the test we want to run:
1- **Normal tests:** we run them on top of an empty DB and we populate data for each test. They must be run using ```DB_CONNECTION=testing```,
which can be set in phpunit.xml.
2- **On Backup tests:** tests meant to use a backup database. You must change your DB_CONNECTION to use the connection to your backup DB. We use
"mysql" connection normally, so you need to go to phpunit.xml and change DB_CONNECTION to "mysql", or simply comment the DB_CONNECTION
line and it will take your local env value, which normally will be set to "mysql". We will try to improve this in the future.

#### Warning!
If you run a normal test, which uses the LazilyRefreshDatabase trait, when your DB_CONNECTION it set to a DB which contains data, it will be lost.

### Deployment strategy

Right now tests are not integrated to deployment CI processes. However, they will.
When doing so, it will be interesting to add the following command to ensure that a minimum % of the codebase is covered by tests:
``vendor/bin/pest --coverage --min=desiredMinimumPercentage``. You must substitute ``desiredMinimumPercentage`` by the minimum
percentage the codebase should be covered by tests. The command returns a non-zero in the cli when the minimum desired % of code
is not covered, so it should fail the workflow. Be aware though this command has some requirements, like having xDebug installed
or other alternatives: check it in [the documentation](https://pestphp.com/docs/coverage).

When implementing tests in CI, please use PEST parallel feature where you can run tests in parallel, therefore dramatically
reducing the amount of time spent on tests. Documentation is [here](https://pestphp.com/docs/plugins/parallel).
To run the tests in parallel, you will just need to add the ``--parallel`` flag to the artisan test command.

If you ever encounter a situation where a test is failing (therefore not allowing you to deploy), but you want to deploy anyway
(for instance because the test fails simply because it's not updated to new changes), you can add ``->skip()``.
Check out [PEST documentation](https://pestphp.com/docs/skipping-tests) of skipping.

## 3- Setup you have to do to start running tests

This section will guide you through the steps you have to follow to start running tests. But first, we recommend you to read
the previous sections so you know the type of tests we have so you can choose the appropriate options according to the type 
of test you want to run.

PEST is built on top of Laravel, so there is only just a bit of configuration to run tests. Tests are run in your local environment,
not inside your docker container. This means that you will need the following tools installed outside yor container, in your machine:
1. PHP (as per tests done, you shouldn't need xDebug installed to be able to run tests).
2. Mysql (when accessing the DB, laravel uses mysql to connect to our mysql DBs).
3. Make sure you run bin/php/composer.sh install to ensure you have all the correct dependencies in your vendor folder.

We strongly recommend you to get a [PEST plugin](https://pestphp.com/docs/ide-plugins) installed in your IDE. It is really helpful.
It provides autocompletion for your IDE as well as the possibility to run tests from the IDE. Make sure you have your IDE interpreter
(php) set up in the configuration: in phpStorm you need to go to Configuration->Languages & Frameworks->Php and use the 3 dots if you don't
have any. You will need to indicate the location of your php installation which you can find by running "which php" in your terminal.
Now, the PEST plugin default configuration is to pick the project's interpreter. So you shouldn't need to do anything else. If for any
reason, the pest plugin is unable to find which interpreter to use, go to Configuration->Languages & Frameworks->PHP dropdown->Test Frameworks. 
More info about Pest plugin in this [episode](https://spatie.be/courses/testing-laravel-with-pest/installing-the-pest-plugin) of the Spatie Laravel
Test with Pest course for more details.

### Running On Backup tests
You will need to have a real backup DB in your env, and set the DB_CONNECTION env variable to use the correct connection.
Refer to the configuration section for more info.

### Running normal tests
These tests require a testing DB where data can be removed. You will need to create a DB that will use the ```testing```
connection. We need a "mysql" DB named ```tests``` in the database container. Create the ``tests`` database by running 
this command: ``docker exec ninja_db mysql -uroot -psecret -e "CREATE DATABASE tests;"`` when your containers are running.
Then, when you run your first test which accesses to this DB, it will run all migrations and create the schema for you.
But before you run your first normal test, you should ensure that DB_CONNECTION is set to use the ```testing``` connection
by checking the value in the phpunit.xml file.

### Other configuration
Tests use the environment variables of your .env file to run the tests. Their values can be altered in the phpunit.xml file.
However, you can create a ```.env.testing``` env file and Laravel will pick it as base env file to run tests. You can check
[laravel documentation](https://laravel.com/docs/9.x/testing#environment) about the topic.

## 4- Running tests

Check setup setup and configuration sections before running tests. This section is only about how to run tests.

### Through the cli

``vendor/bin/pest`` will run all your tests. But you will provably want to run only some tests. You can also run a single test or the tests 
within a directory by using ``vendor/bin/pest --filter=testNameOrFolder``. There are multiple other options, check laravel and PEST documentation.
It is recommended to make alias for the pest commands if you want to run tests on the cli, like pf for pest filter.

### With Pest's plugin: play buttons & key shortcuts

However, if you have the PEST plugin in your IDE, as stated in the setup section of this file, you can easily run one single test by clicking
on the "play" button on the left of your test, or the "double play" button at the top of your file, which will run all your tests.
Check this [episode](https://spatie.be/courses/testing-laravel-with-pest/running-tests-via-phpstorm) of the Laravel Pest course for more info.

You can also configure your IDE to create some key shortcuts to run tests. For instance, you can do "command + T" to run the test where your cursor is,
or, if you are outside of a test, it will run all the tests in the file. But it also works with the files tree. If you select a folder,
you can run all tests within that folder. "Command + shift + T" could be interesting to set up to repeat last test.
Check this [episode](https://spatie.be/courses/testing-laravel-with-pest/running-tests-via-phpstorm) of the Laravel Pest course for more info.

### Running tests inside docker

I suspect that by running tests as shown above, you are using your local php version, which may differ from the production one. The same
could happen with other dependencies. We could run tests locally in docker, but I don't recommend it for the following reasons:

- They are super slow.
- I tried to run them in docker via PhpStorm functionality and I wasn't able to do it. I had to run them all from the cli.
- Tests using application routes fail for some reason, although they are ok: if you run them outside of docker they pass.
- Most documentation I have seen doesn't run tests in docker. I asume that the idea is to be able tu run tests fast in your local 
env and, if by any chance, any of these tests could faild because of a different version of a dependency, that will be catched by
your CI/CD tests.

Let me save here the configuration steps I have tried to do in my PhpStorm to be able to run tests in docker with the PEST plugin.
I save this here because there is no much information about it online. Be aware that I was not able to make it work + 
you should not be running test in docker:

Configuring your PhpStorm to run tests in docker:
1- Make sure the Docker and PHP Docker plugins are installed and enabled.
2- Connect PhpStorm to docker by going to Preferences->Build, execution & Deployment->Docker and adding docker there (for mac, is auto configured).
3- Creating a new interpreter in Preferences->Languages & Frameworks->PHP->CLI intepreters->click on the 3 dots->+ icon->From Docker,... option
->choose docker and put the correct image name->it should auto recognize php version->I would also toggle the option for this project only.
4- Create a PEST configuration using that docker interpreter: Preferences->Languages & Frameworks->PHP dropdown->Test Frameworks->+ icon
->Pest by remote interpreter->chose the CLI interpreter you previously created->it should auto recognize the mapping, pest 
and the .xml config file in the container.

I found that info between the following sources:
a- [Jetbrains help](https://www.jetbrains.com/help/phpstorm/configuring-remote-interpreters.html#487450a9)
b- [This youtube video](https://www.youtube.com/watch?v=I7aGWO6K3Ho)

### Slow tests

Looks like Pest V2 is shipping with a new feature to identify slow tests. You just need to add the flag '--profile' to the command to identify them.

## 5- Creating tests

You can create tests by using ``php artisan pest:test --unit TestName`` for unit tests, and ``php artisan pest:test TestName``
for feature tests. That will create a simple PEST file. If you use ``php artisan make:test``, it will create a PHP unit styled
test instead of a PEST test. We don't want that.

**Important:** when creating test files, is super important that each test file ends with "Test". Otherwise, PEST won't recognize it.

You can also create global datasets (check pest documentation to know about them) by doing ```php artisan pest:dataset DatasetName```.

## 6- Using factories to populate DB

For the tests we don't run on a backup db, we will use factories to populate the db. We have two ways to create data using factories:

1- Factories are thought to be totally independent and create the data they need. 
You can directly call a factory to create a model and it will create all the models with a higher order.
For instance, if you create a rental, it will create the team and the team owner (the higher order models).
This approach makes it super easy to create data. But if you want to create multiple records for the same higher order model (like a team),
you need to use the 2nd way to use factories.
2-Re-use (or recycle) the same higher order models: you can create the higher order models first and pass their ids to the factories. For this,
it will be super useful tu use the factory's relationships methods + creating some helper methods widely re-used in the pest.php file.

## 7- Debugging tests

You can use xdebug to debug tests. You just need to click on the "play" button on the side of the test (I think it only appears if you have the plugin installed),
and click on debug the test (after placing the breakpoints). The test should run and stop there. If no, check one of the following:

1- When running tests outside docker, you need to have the correct php version and xdebug installed in your local. Make "php -v" to check if xdebug is installed.
2- If you get a warning from your IDE saying that the port 9000 is busy, go and check Preferences->Languages & Frameworks->PHP->Debug and change the Xdebug - Debug port.
I am using 9003 and works well.