<?php

use App\Actions\Rentals\GetTeamRentalsAction;
use App\Enum\TeamRolesEnum;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

use function Pest\Laravel\actingAs;

/*
 * Testing GetTeamRentalsAction
 */
beforeEach(function () {
    $this->team = createRentals(5);
});

test('user can get a list of slim rentals', function () {
    actingAs($this->team->owner)
        ->getJson(
            '/slim-rentals/'.$this->team->id.'/for-user'
        )->assertSuccessful()
        ->assertJsonCount(5, 'data');
});

it('returns a list of slim rentals of the team', function () {
    actingAs($this->team->owner)
        ->getJson('/slim-rentals/'.$this->team->id)
        ->assertSuccessful()
        ->assertJsonCount(5, 'data');
});

it('caches user slim rentals', function () {
    $user = $this->team->owner;
    actingAs($user);

    expect(GetTeamRentalsAction::make()->asCachedUserSlimRentals())->toBeInstanceOf(AnonymousResourceCollection::class);

    expect(Cache::has("$user->id.slim_rentals"));
});

/* TESTING THE GetUsersWithAccessToRentalsAction::class */

it('gets users with access to the given rentals list', function () {
    $rentals = $this->team->teamRentals;
    // Create one user with access to the first rental of the list:
    createUserOnTeamAndAssignRentals($this->team->id, TeamRolesEnum::RENTAL_MANAGER, $rentals->first()->id);
    // Create one user with access to the last rental of the list:
    createUserOnTeamAndAssignRentals($this->team->id, TeamRolesEnum::RENTAL_MANAGER, $rentals->last()->id);

    // Create another team and a rental having the same id as one of the others. Give a user access to that rental.
    // That user should not be returned as it belongs to another team (we had this bug once)
    $team2 = Team::factory()->create();
    $rentalTeam2 = Rental::factory()
        ->recycle($team2)
        ->create(['id' => $rentals->last()->id]);
    createUserOnTeamAndAssignRentals($team2->id, TeamRolesEnum::RENTAL_MANAGER, $rentalTeam2->id);

    actingAs($this->team->owner)
        ->json('GET',
            '/slim-rentals/'.$this->team->id.'/for-rentals',
            ['rentals' => $rentals->slice(2)->pluck('id')->toStringArray()]) //Asking for users having access for a section of rentals of the team
        ->assertSuccessful()
        ->assertJsonCount(1, 'data'); // Only owner has access to these rentals

    actingAs($this->team->owner)
        ->json('GET',
            '/slim-rentals/'.$this->team->id.'/for-rentals',
            ['rentals' => "[$rentalTeam2->id]"]) //Asking for users having access for the last rental of the team
        ->assertSuccessful()
        ->assertJsonCount(2, 'data'); // Owner and user with access to last rental
});

it('fails when no rentals list is provided', function () {
    actingAs($this->team->owner)
        ->getJson('/slim-rentals/'.$this->team->id.'/for-rentals')
        ->assertStatus(400);
});
