<?php

use App\Actions\Alerts\UpdateBookingPaymentAlertsAction;
use App\Actions\Providers\BookingSync\HandleBookingSyncWebhookAction;
use App\Actions\Tasks\ScheduledTask\GenerateTasksSurroundingBookingAction;
use App\DataProviders\ApiConnectors\BookingSyncConnector;
use App\DataProviders\Providers\BookingSync;
use App\DTO\Providers\BookingSync\BookingSyncWebhookDto;
use App\Events\Booking\BookingConfirmedEvent;
use App\Jobs\HandleBookingSyncWebhook;
use App\Models\BookingComment;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Mockery\MockInterface;

use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;
use function Pest\Laravel\postJson;

beforeEach(function () {
    $this->team = createTeam(BookingSync::ID);
    $this->payload = new BookingSyncWebhookDto(account_id: $this->team->providerAccountId());
});

uses()->group('BookingSync');

/// Job dispatcher ///

/**
 * We will test here the HandleBookingSyncWebhookAction only and then, we will test each event type below by directly calling the HandleBookingSyncWebhook Job.
 * This way, we don't have to test each time the HandleBookingSyncWebhookAction.
 */
test('the HandleBookingSyncWebhookAction dispatches the HandleBookingSyncWebhook Job', function () {
    Bus::fake();
    $this->payload->event = 'booking_created';

    postJson(action(HandleBookingSyncWebhookAction::class), $this->payload->toArray(), BookingSyncWebhookDto::HEADERS)
        ->assertOk();

    Bus::assertDispatched(HandleBookingSyncWebhook::class);
});

/// Events in HandleBookingSyncWebhook Job ///

it('ensures that a booking created BookingSync webhook works well', function () {
    Queue::fake()->except([HandleBookingSyncWebhook::class]); // We must use Queue and not Bus so we can assertPushed() on an action (check docs)
    Event::fake();
    UpdateBookingPaymentAlertsAction::allowToRun(); // This is mocking the class (it will have it's own test separately) and allowing us to make assertions later.
    $booking = fakeBookingHttpRequest($this->team);

    $this->payload->event = 'booking_created';
    $this->payload->resource = ['booking' => ['id' => $booking->id]];

    // Dispatch sync what we are testing in this test:
    dispatch_sync(new HandleBookingSyncWebhook($this->team, $this->payload->toArray()));

    assertDatabaseHas('booking', ['id' => $booking->id, 'team_id' => $this->team->id]); // Resolver will have his own test too
    assertDatabaseHas('booking_fee', ['price' => $booking->bookingFees->first()->price]);
    assertDatabaseHas('booking_tax', ['amount' => $booking->bookingTaxes->first()->amount]);

    Event::assertDispatched(BookingConfirmedEvent::class); // Will have his own test
    GenerateTasksSurroundingBookingAction::assertPushed(); // Will have his own test
    UpdateBookingPaymentAlertsAction::spy()->shouldHaveReceived('handle'); // This fails in case the mocked Action is not called
});

// TODO: Booking payment created or updated test

it('ensures that comment created or updated BookingSync webhooks work well', function () {
    // This webhook basically updates the entire booking. So, just ensure that we are making a call to fetch the booking

    $fetcher = $this->mock(BookingSyncConnector::class, function (MockInterface $mock) {
        $mock->shouldReceive('updateBooking')->once();
    });
    // This is not going through an action. Study how to mock the Fetcher
    //    GetProviderFetcherAction::mock()->shouldReceive('handle')->andReturn($fetcher);

    $this->payload->event = 'booking_comment_created';
    $this->payload->resource = ['booking_comment' => ['links' => ['booking' => rand(1000, 10000)]]];

    dispatch_sync(new HandleBookingSyncWebhook($this->team, $this->payload->toArray()));
});

it('destroys a booking comment after receiving a BS comment destroyed webhook', function () {
    $comment = BookingComment::factory()
        ->recycle($this->team)
        ->create();
    assertDatabaseHas('booking_comment', ['id' => $comment->id, 'team_id' => $comment->team_id]);

    $this->payload->event = 'booking_comment_destroyed';
    $this->payload->resource = ['booking_comment' => ['id' => $comment->id]];

    dispatch_sync(new HandleBookingSyncWebhook($this->team, $this->payload->toArray()));

    assertDatabaseMissing('booking_comment', ['id' => $comment->id, 'team_id' => $comment->team_id]);
});
