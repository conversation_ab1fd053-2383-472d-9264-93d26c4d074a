<?php

use App\Actions\Alerts\UpdateBookingPaymentAlertsAction;
use App\Actions\Providers\Generic\IncreaseWebhookInvocationCountAction;
use App\Actions\Providers\RentalsUnited\HandleRentalsUnitedWebhookAction;
use App\Actions\Tasks\ScheduledTask\GenerateTasksSurroundingBookingAction;
use App\DataProviders\Providers\RentalsUnited;
use App\DTO\Providers\RentalsUnited\RentalsUnitedWebhookDto;
use App\Events\Booking\BookingCancelledEvent;
use App\Events\Booking\BookingConfirmedEvent;
use App\Events\Booking\CheckInOutTimeModifiedEvent;
use App\Models\Booking;
use App\Models\Rental;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;

use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

beforeEach(function () {
    $this->team = createTeam(RentalsUnited::ID);
});

uses()->group('Rentals United');

/**
 * We will only test the handle method, to avoid having to mock the entire request as xml. However, this could be done in the future.
 * It shouldn't be difficult as we are already managing data as DTOs, which could be converted to arrays and then, to xml.
 * Also, for now, there is not much interest on testing the asController method.
 */
it('ensures that a confirmed new reservation rentals united webhook works well', function () {
    // Prepare test
    $rental = Rental::factory()
        ->recycle($this->team)
        ->create(); // The Booking Resolver needs a saved rental to work.
    $booking = Booking::factory()
        ->recycle([$this->team, $rental])
        ->make();

    $webhook = RentalsUnitedWebhookDto::confirmedReservationWebhookFromModel($booking);

    Queue::fake(); // We must use Queue and not Bus so we can assertPushed() on an action (check docs)
    UpdateBookingPaymentAlertsAction::allowToRun(); // This is mocking the class (it will have it's own test separately) and allowing us to make assertions later.
    IncreaseWebhookInvocationCountAction::allowToRun();
    Event::fake();

    // Run
    HandleRentalsUnitedWebhookAction::run(
        HandleRentalsUnitedWebhookAction::CONFIRM,
        collect($webhook->toArray())->first()
    );

    // Assert
    GenerateTasksSurroundingBookingAction::assertPushed(); // Will have his own test
    UpdateBookingPaymentAlertsAction::spy()->shouldHaveReceived('handle'); // This fails in case the mocked Action is not called
    IncreaseWebhookInvocationCountAction::spy()->shouldHaveReceived('handle');
    Event::assertDispatched(BookingConfirmedEvent::class); // Will have his own test
    assertDatabaseHas('booking', ['id' => $booking->id, 'team_id' => $this->team->id]);
});

it('ensures that a confirmed updated reservation rentals united webhook works well', function () {
    /** @var Booking $booking */
    $booking = Booking::factory()
        ->recycle($this->team)
        ->create();

    $booking->start_at += 86400; // Add one day
    $booking->final_rental_price += 100;
    $booking->final_price += 100;
    $webhook = RentalsUnitedWebhookDto::confirmedReservationWebhookFromModel($booking, true);

    Queue::fake();
    Event::fake();
    UpdateBookingPaymentAlertsAction::allowToRun();
    IncreaseWebhookInvocationCountAction::allowToRun();

    HandleRentalsUnitedWebhookAction::run(
        HandleRentalsUnitedWebhookAction::CONFIRM,
        collect($webhook->toArray())->first()
    );

    GenerateTasksSurroundingBookingAction::assertPushed();
    Event::assertDispatched(CheckInOutTimeModifiedEvent::class);
    UpdateBookingPaymentAlertsAction::spy()->shouldHaveReceived('handle');
    IncreaseWebhookInvocationCountAction::spy()->shouldHaveReceived('handle');

    assertDatabaseHas('booking', [
        'id' => $booking->id,
        'team_id' => $this->team->id,
        'final_rental_price' => $booking->final_rental_price,
        'final_price' => $booking->final_price,
    ]);
});

it('ensures that a canceled reservation rentals united webhook works well', function () {
    $booking = Booking::factory()
        ->recycle($this->team)
        ->create();

    $webhook = RentalsUnitedWebhookDto::canceledReservationWebhook($booking->id);

    IncreaseWebhookInvocationCountAction::allowToRun();
    Event::fake();

    HandleRentalsUnitedWebhookAction::run(
        HandleRentalsUnitedWebhookAction::CANCEL,
        collect($webhook->toArray())->first()
    );

    IncreaseWebhookInvocationCountAction::spy()->shouldHaveReceived('handle');
    Event::assertDispatched(BookingCancelledEvent::class);
    assertDatabaseMissing('booking', ['id' => $booking->id, 'team_id' => $this->team->id, 'canceled_at' => null]);
});
