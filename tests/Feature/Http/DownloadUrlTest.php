<?php

use App\Models\HashDownloadableRoute;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\get;

it('checks the ninja download route', function () {
    // Create a hash pointing to a route created by the factory
    /** @var HashDownloadableRoute $hashModel */
    $hashModel = HashDownloadableRoute::factory()->create();

    // Create and save a fake file using the route of the factory
    Storage::fake('s3')->put($hashModel->route, 'dummy file contents');

    // Retrieve it and test ok
    get('/download-resource?hash='.$hashModel->hash)
        ->assertRedirectContains($hashModel->route);
    //Note: the redirection contains a timestamp in a query parameter
});
