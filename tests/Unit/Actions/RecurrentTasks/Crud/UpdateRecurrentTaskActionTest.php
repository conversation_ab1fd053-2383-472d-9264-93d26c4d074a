<?php

use App\Actions\Tasks\RecurrentTask\CreateFutureTasksForRecurrentTaskAction;
use App\Actions\Tasks\RecurrentTask\Crud\UpdateRecurrentTaskAction;
use App\DTO\Tasks\RecurrentTaskDto;
use App\Enum\TeamRolesEnum;
use App\Models\RecurrentTask;
use App\Models\Task;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Carbon;

it('gets updates a recurrent tasks and the depending created tasks for a team', function () {
    Event::fake();
    Queue::fake();

    $team = createRentals(2);
    $teamUser = createUserOnTeamAndAssignRentals($team->id, TeamRolesEnum::RENTAL_MANAGER, $team->teamRentals->first()->id);

    /** @var RecurrentTask $rTask */
    $rTask = RecurrentTask::factory()->onTeam($team->id)->withRole(TeamRolesEnum::OWNER)->create();
    $original = $rTask->replicate();

    Task::factory()
        ->onTeam($team->id)
        ->forRecurrentTask($rTask->id)
        ->withRole($rTask->role)
        ->withAssignee($rTask->assignee_id)
        ->withSupervisor($rTask->supervisor_id)
        ->count(6)
        ->state(new Sequence(
            ['start_at' => now()->addDays(-3), 'start_from' => now()->addDays(-1), 'finish_before' => now()->addDays(-1)->addHour(), 'end_at' => now()->addDays(-1)->addHour()],
            ['start_at' => now()->addDays(-1), 'start_from' => now()->addDays(1), 'finish_before' => now()->addDays(1)->addHour(), 'end_at' => now()->addDays(1)->addHour()],
            ['start_at' => now()->addDays(1), 'start_from' => now()->addDays(2), 'finish_before' => now()->addDays(2)->addHour(), 'end_at' => now()->addDays(2)->addHour()],
            ['start_at' => now()->addDays(3), 'start_from' => now()->addDays(3), 'finish_before' => now()->addDays(3)->addHour(), 'end_at' => now()->addDays(3)->addHour()],
        ))
        ->create();

    $dto = RecurrentTaskDto::from([
        'team_id' => $team->id,
        'checklist_id' => $rTask->checklist_id,
        'active' => true,
        'title' => 'Limpeza',
        'description' => null,
        'priority' => 3,
        'is_team_task' => false,
        'rentals' => $team->teamRentals->pluck('id')->toArray(),
        'role' => TeamRolesEnum::RENTAL_MANAGER,
        'assignee_id' => null,
        'supervisor_id' => $teamUser->id,
        'recurrent_start_date' => '2022-01-01',
        'recurrent_end_date' => null,
        'periodicity_type' => 'monthly_on_day_num',
        'periodicity_value' => 2,
        'periodicity_on' => 3,
        'time_start' => '10:00',
        'duration' => '8:00',
        'needs_supervision' => true,
    ]);

    CreateFutureTasksForRecurrentTaskAction::shouldRun();

    UpdateRecurrentTaskAction::run($rTask, $dto);

    $tasks = Task::query()->get();
    $future = $tasks->where('start_at', '>', now());
    $past = $tasks->where('start_at', '<', now());
    $nextRecurrent = Carbon::today()->setDate(2022, 01, 3);

    expect($tasks)->toHaveCount(2)
        ->and($future)->toHaveCount(0)
        ->and($past->pluck('role')->toArray())->toMatchArray([$original->role, $original->role])
        ->and($past->pluck('assignee_id'))->toMatchArray([$rTask->assignee_id, $rTask->assignee_id])
        ->and($past->pluck('supervisor_id'))->toMatchArray([$original->supervisor_id, $original->supervisor_id])
        ->and($rTask->next_recurrent)->toEqual($nextRecurrent);
});
