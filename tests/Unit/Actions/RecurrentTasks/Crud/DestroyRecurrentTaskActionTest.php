<?php

use App\Actions\Tasks\RecurrentTask\Crud\DestroyRecurrentTaskAction;
use App\Enum\TeamRolesEnum;
use App\Models\RecurrentTask;
use App\Models\Task;
use Illuminate\Database\Eloquent\Factories\Sequence;

it('gets updates a recurrent tasks and the depending created tasks for a team', function () {
    Event::fake();
    Queue::fake();

    $team = createRentals(2);
    $teamUser = createUserOnTeamAndAssignRentals($team->id, TeamRolesEnum::RENTAL_MANAGER, $team->teamRentals->first()->id);

    /** @var RecurrentTask $rTask */
    $rTask = RecurrentTask::factory()->onTeam($team->id)->withRole(TeamRolesEnum::OWNER)->create();

    Task::factory()
        ->onTeam($team->id)
        ->forRecurrentTask($rTask->id)
        ->withRole($rTask->role)
        ->withAssignee($rTask->assignee_id)
        ->withSupervisor($rTask->supervisor_id)
        ->count(6)
        ->state(new Sequence(
            ['start_at' => now()->addDays(-3), 'start_from' => now()->addDays(-1), 'finish_before' => now()->addDays(-1)->addHour(), 'end_at' => now()->addDays(-1)->addHour()],
            ['start_at' => now()->addDays(-1), 'start_from' => now()->addDays(1), 'finish_before' => now()->addDays(1)->addHour(), 'end_at' => now()->addDays(1)->addHour()],
            ['start_at' => now()->addDays(1), 'start_from' => now()->addDays(2), 'finish_before' => now()->addDays(2)->addHour(), 'end_at' => now()->addDays(2)->addHour(), 'completed_at' => now()],
            ['start_at' => now()->addDays(3), 'start_from' => now()->addDays(3), 'finish_before' => now()->addDays(3)->addHour(), 'end_at' => now()->addDays(3)->addHour()],
        ))
        ->create();

    DestroyRecurrentTaskAction::run($rTask);

    $tasks = Task::query()->get();
    $future = $tasks->where('start_at', '>', now());
    $past = $tasks->where('start_at', '<', now());

    expect($tasks)->toHaveCount(3)
        ->and($past)->toHaveCount(2)
        ->and($future)->toHaveCount(1)
        ->and($rTask->deleted_at)->toBeTruthy();
});
