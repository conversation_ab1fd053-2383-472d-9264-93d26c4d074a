<?php

use App\Actions\Tasks\GetTaskWithRelationsAction;
use App\Enum\TeamRolesEnum;
use App\Models\Booking;
use App\Models\Task;
use Illuminate\Support\Carbon;

it('returns a task with its relations', function () {
    $team = createBookings(1);
    $teamUser = createUserOnTeamAndAssignRentals($team->id, TeamRolesEnum::RENTAL_MANAGER, $team->teamRentals->first()->id);

    $firstBooking = $team->bookings->first();

    $task = Task::factory()
        ->onTeam($team->id)
        ->onRental($team->teamRentals->first()->id)
        ->onBooking($firstBooking->id)
        ->withAssignee($teamUser->id)
        ->withSupervisor($team->owner_id)
        ->create();

    // Create a second booking after the first booking
    Booking::factory()
        ->onTeam($team->id)
        ->onRental($firstBooking->rental_id)
        ->setStartAt(Carbon::createFromTimestamp($firstBooking->end_at)->addDays(2))
        ->setDurationInDays(2)
        ->create();

    // This should load also next booking
    $task = GetTaskWithRelationsAction::run($task);

    expect($task)->toHaveKeys([
        'booking',
        'supervisor',
        'assignee',
        'items',
        'scheduled',
        'next_booking',
    ])->and($task->next_booking)
        ->not()
        ->toBe(null);
});
