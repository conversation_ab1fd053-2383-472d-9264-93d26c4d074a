<?php

use App\Actions\Tasks\Cleanup\CleanupCancelledBookingTasksAction;
use App\Models\Booking;
use App\Models\Task;
use App\Models\Team;

test('CleanupCancelledBookingTasksAction works well', function () {
    $team = Team::factory()->create();
    Booking::factory()
        ->statusCancelled()
        ->recycle($team)
        ->count(5)
        ->create()
        ->each(function (Booking $booking) use ($team) { // TODO: this could be refactored when we can use ->with() for Compoships relations
            Task::factory()
                ->recycle([$team, $booking, $booking->rental])
                ->with<PERSON>signee($team->owner->id)
                ->create();
        });

    CleanupCancelledBookingTasksAction::run($team);
    expect(Task::all())->toBeEmpty();
});
