<?php

use App\Actions\Tasks\TaskFromNonScheduledAction;
use App\Models\Booking;
use App\Models\Task;
use App\Models\Team;

use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

test('that task is deleted when booking is canceled and task should be removed on cancellation', function () {
    /** @var Team $team */
    $team = Team::factory()->create();
    /** @var Booking $booking */
    $booking = Booking::factory()
        ->statusCancelled()
        ->recycle($team)
        ->create();

    // This could be refactored when we can use ->with() for Compoships relations:
    $task = Task::factory()
        ->recycle([$team, $booking, $booking->rental])
        ->with<PERSON>signee($team->owner->id)
        ->create(['remove_on_cancellation' => true]);
    assertDatabaseHas('tasks', ['id' => $task->id, 'deleted_at' => null, 'deleted_by' => null]);

    TaskFromNonScheduledAction::run($task, $booking);
    assertDatabaseMissing('tasks', ['id' => $task->id, 'deleted_at' => null, 'deleted_by' => null]);
});
