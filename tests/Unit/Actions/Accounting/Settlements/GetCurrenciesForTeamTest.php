<?php

use App\Actions\Accounting\Settlements\GetCurrenciesForTeamAction;
use App\Models\Booking;

it('gets all team currencies', function () {
    //Create 4 bookings:
    // 2 should be active and have the same currency
    // 1 should be active and have a different currency than the previous
    // 1 should be cancelled and with a different currency than the others.
    // We should receive 2 different currencies.

    $team = createBookings(3);
    $bookings = $team->bookings;

    $bookings->first()->currency = 'EUR';
    $bookings->get(1)->currency = 'EUR';
    $bookings->last()->currency = 'GBP';

    $bookings->each(fn (Booking $booking) => $booking->save());

    Booking::factory()
        ->onTeam($team->id)
        ->statusCancelled()
        ->create(['currency' => 'USD']);

    $currencies = GetCurrenciesForTeamAction::run($team);
    expect($currencies)
        ->toHaveCount(2)
        ->toContain('EUR', 'GBP');
});
