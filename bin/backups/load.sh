#!/bin/bash
set -e
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR=$(
	cd ${SCRIPT_DIR}/../..
	pwd
)
source ${ROOT_DIR}/bin/common.sh

# Set variable date from input
DATE=$1

# Check if "go" is installed
if command -v go &> /dev/null
then
    echo "Go is already installed."
else
    # Print instructions to install Go
    echo "Go is not installed. Please follow the instructions below to install it:"
    echo "1. Visit the official Go website: https://golang.org/dl/"
    echo "2. Download the latest stable version of Go for your operating system."
    echo "3. Follow the installation instructions provided on the Go website. WARNING: you may need to manually add the PATH variable in order to make go work."
    exit
fi

# Check if myloader is installed
loader_path="../go-mydumper/bin/myloader"
# Check if the file exists
if [ -e "$loader_path" ]; then
    echo "myloader is properly installed"
else
    echo "myloader is not properly installed"
    echo "Please install it on ../go-mydumper "
    echo "Please follow the build instructions from https://github.com/aquarapid/go-mydumper. Place the cloned repo to the upstream folder of this ninja project."
    exit
fi

backup_folder="./backups/planetscale"
mkdir -p "$backup_folder"
file_path="$backup_folder/ninja.tar.gz"


# Check if any file is found and ask if re-use it.
DOWNLOAD=true
if [ -f "$file_path" ]; then
    echo "Backup found:"
    ask "There seems to be a backup already downloaded, want to use this one?" && DOWNLOAD=false

    if [[ ${DOWNLOAD} == "true" ]]; then
    	rm -rf $backup_folder*
    fi

fi

# Download the file if required
if [[ ${DOWNLOAD} == "true" ]]; then
	if [ -n "$DATE" ]; then
		CALL="php artisan ninja:import-planetscale-dump --date $DATE"
		echo "Downloading backup from $DATE: $CALL"
		docker exec ninja_xdebug $CALL
	else
		echo "Downloading latest backup"
		docker exec ninja_backend_app php artisan ninja:import-planetscale-dump
	fi
	sleep 1
fi


backupUncompressed=$backup_folder/ninja
mkdir -p "$backupUncompressed"
echo "Uncompressed backup in: $backupUncompressed"
tar -xzf $file_path -C $backupUncompressed

# Loop through SQL files in the directory
for file in "$backupUncompressed"/*.sql; do
    # Skip files containing 'schema-create' in their names
    if [[ "$file" == *schema-create* ]]; then
        continue
    fi
	echo "Add USE vapor"
    # Add 'USE vapor;' at the beginning of the file
    sed '1s/^/USE vapor;\n/' "$file" > "$file.tmp" && mv "$file.tmp" "$file"
done

docker exec ninja_db mysql -uroot -psecret -e "DROP DATABASE vapor; CREATE DATABASE vapor;"

$loader_path -h localhost -u root -p secret -d "$backupUncompressed" -t 1 -o
rm -rf $backupUncompressed

ask "Want to delete the back-up?" && DELETE_BU=true
if [[ ${DELETE_BU} == "true" ]]; then
	rm -rf $backup_folder*
fi

echo "Database import done!"
