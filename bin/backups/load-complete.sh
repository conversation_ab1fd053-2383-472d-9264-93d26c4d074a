#!/bin/bash
set -e
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR=$(
	cd ${SCRIPT_DIR}/../..
	pwd
)
source ${ROOT_DIR}/bin/common.sh

DIR=${ROOT_DIR}/backups
DOWNLOAD=true

if [ -d "$DIR" ]; then
	cd ${DIR}
	count=$(ls -1 *.tar.gz 2>/dev/null | wc -l)
	if [ $count != 0 ]; then
		ask "There seems to be a backup already downloaded, want to use this one?" && DOWNLOAD=false
	fi
fi
cd ${ROOT_DIR}

if [[ ${DOWNLOAD} == "true" ]]; then
	rm -rf ${ROOT_DIR}/backups/*
	OBJECT="$(aws s3 ls s3://rn-db-backups/backups-mysqlsh/backups-production-complete --recursive | sort | tail -n 1 | awk '{print $4}')"
	echo "Downloading latest backup: ${OBJECT}..."
	aws s3 cp s3://rn-db-backups/${OBJECT} ${ROOT_DIR}/backups/ninja.tar.gz
	chmod 0777 ${ROOT_DIR}/backups/ninja.tar.gz
	sleep 10
fi

echo "Removing old backup if any, and importing the latest one into the database container..."
docker exec ninja_db rm -rf /tmp/ninja
docker cp backups/ninja.tar.gz ninja_db:/tmp

echo "Un-compressing RN Dump"
docker exec -w /tmp ninja_db tar -C /tmp -xf ninja.tar.gz

echo "Load the backup image locally"
docker exec ninja_db mysql -uroot -psecret -e "DROP DATABASE vapor; CREATE DATABASE vapor;"
docker exec ninja_db mysql -uroot -psecret -e "set global local_infile='ON';"
docker exec ninja_db mysqlsh root@ninja_db --password=secret -e "util.loadDump('/tmp/ninja/ddl', {includeSchemas: ['vapor'], loadDdl: true, showProgress: true, skipBinlog: true, ignoreExistingObjects: true, resetProgress: true, ignoreVersion: true, threads: 100})"
docker exec ninja_db mysqlsh root@ninja_db --password=secret -e "util.loadDump('/tmp/ninja/data', {includeSchemas: ['vapor'], loadDdl: false, showProgress: true, skipBinlog: true, ignoreExistingObjects: true, resetProgress: true, ignoreVersion: true, threads: 100, excludeTables: ['vapor.failed_jobs', 'vapor.telescope_entries', 'vapor.telescope_monitoring', 'vapor.telescope_entries_tags']})"
docker exec ninja_db mysql -uroot -psecret -e "set global local_infile='OFF';"
docker exec ninja_db rm -rf /tmp/ninja
docker exec ninja_db rm -f /tmp/ninja.tar.gz
echo "Import finished for RN"

docker exec ninja_backend_app php ./artisan migrate

rm -rf ${ROOT_DIR}/backups/*
echo "Database import done!"
