[mysql]
# Number of dumping threads
threads = 30

# The host to connect to
host = aws.connect.psdb.cloud

# TCP/IP port to conect to
port = 3306

# Username and password with privileges to run the dump
user = __DB_USERNAME__
password = __DB_PASSWORD__

# Database to dump (NOTE @vapor changes the database name in the import)
database = vapor

# Table(s) to dump ;  comment out to dump all tables in database
# table = accounting_advanced_settings,action_events,admin_users,announcements,api_routes,billable_addons,booking,booking_alerts,booking_alerts_snooze_time,booking_comment,booking_fee,booking_payments,booking_tag_pivot,booking_tags,booking_tax,cache,chartmogul_data,chartmogul_invoices,checklist_items,checklists,client,companies,company_company_tag,company_feedback,company_notes,company_tags,custom_user_tokens,excel_imports,excel_uploads,factures_local_carbonera,features,fee,fee_taxes,fees,guests_application_rental_settings,guests_application_settings,hash_downloadable_routes,i_cal_inputs,i_cal_outputs,invitations,invoices,job_batches,lodgify_property_rooms,migrations,monitored_scheduled_task_log_items,monitored_scheduled_tasks,ninja_picture,ninja_queue_jobs,nova_field_attachments,nova_notifications,nova_pending_field_attachments,oauth_auth_codes,oauth_clients,oauth_personal_access_clients,partial_payouts,payee_invoices,payees,payout_attachments,payout_details,payouts,performance_indicators,possible_leads,pre_check_in_form,pre_check_in_form_passport,professional_plan_demos,provider_accounts,recurrent_task_rentals,recurrent_tasks,rental,rental_availability,rental_fee,rental_legal_details,rental_ninja_team,rental_owners,rental_pictures,scheduled_settlements,scheduled_tasks,scheduled_tasks_rentals,sessions,settlements,source,source_commission_strategies,task_items,task_pictures,tasks,tax,tax_rates,team_monthly_performance_indicators,team_performance_indicators,team_registration_data,team_related_batches,team_settings,team_subscription_items,team_subscriptions,team_user_rentals,team_users,team_users_permissions,teams,updated_since_request_synced_at,user_notification_tokens,user_settings,users

# Directory to dump files to
outdir = ./backup_folder

# Split tables into chunks of this output file size. This value is in MB
chunksize = 512

# Session variables, split by &
# initvars= "xx=xx&xx=xx"
# vars= "xx=xx&xx=xx"
# initvars are for the initial metadata-only retrieval phase, does not need or want streaming SELECTs
# Adjust the charset as required
initvars=tls=skip-verify&charset=utf8mb4

# The workload variable here is required for Vitess to use streaming SELECTs if we don't use streaming selects, we'll run into row limits.
vars=tls=skip-verify&charset=utf8mb4&workload=olap

# Format to dump:
#  mysql  - MySQL inserts (default)
#  tsv    - TSV format
#  tsv.gz - TSV format (gzipped)
#  csv    - CSV format
#  csv.gz - CSV format (gzipped)
format = mysql

# Use this to use regexp to control what databases to export. These are optional
[database]
# regexp = ^(mysql|sys|information_schema|performance_schema)$
# As the used regexp lib does not allow for lookarounds, you may use this to invert the whole regexp
# This option should be refactored as soon as a GPLv3 compliant go-pcre lib is found
# invert_regexp = on

# Use this to restrict exported data. These are optional
[where]
# sample_table1 = created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
# sample_table2 = created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

# We are performing a complete backups
telescope_entries = sequence < 1
telescope_entries_tags = id < 1
telescope_monitoring = id < 1
pulse_entries = id < 1
pulse_aggregates = id < 1
pulse_values = id < 1

# Use this to override value returned from tables. These are optional
[select]
# customer.first_name = CONCAT('Bohu', id)
# customer.last_name = 'Last'


# Use this to ignore the column to dump.
[filter]
# table1.column1 = ignore
rental_daily_details.available = ignore
rental_daily_details.closed = ignore
rental_daily_details.price_in_cents = ignore
rental_daily_details.extra_guest_in_cents = ignore
rental_daily_details.min_stay = ignore
rental_daily_details.changeover = ignore
# Remember to include virtualAs columns here