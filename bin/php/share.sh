#!/bin/bash

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ROOT_DIR=$(cd ${SCRIPT_DIR}/../..; pwd)

source ${ROOT_DIR}/bin/common.sh
trap "Exiting" SIGUSR1

cd ${ROOT_DIR}

docker exec ninja_backend_app php artisan optimize
docker exec -it ninja_redis redis-cli FLUSHALL

if [[ ${NGROK_TOKEN} != "" ]]; then
  	ngrok authtoken ${NGROK_TOKEN}
    ngrok http --hostname=${RN_DOMAIN} 80
else
  echo "ERROR: There is no NGROK_TOKEN in your ~/.ninja.conf file"
fi
