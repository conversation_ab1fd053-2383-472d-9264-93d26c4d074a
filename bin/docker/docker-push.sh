#!/bin/bash
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ROOT_DIR=$(cd ${SCRIPT_DIR}/../..; pwd)
source ${ROOT_DIR}/bin/common.sh
echo "Starting Up"

cd ${ROOT_DIR}
export BUILD_NUMBER=`git log --pretty="%h" -n1 HEAD`
echo ${BUILD_NUMBER}
# BUILD PHP
docker buildx create --use --name ninja-backend
docker buildx build --push --platform linux/arm64/v8,linux/amd64 ./docker -t polbatllo/ninja:backend-${BUILD_NUMBER} -f ./docker/local/Dockerfile