#!/usr/bin/env bash

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ROOT_DIR=$(cd ${SCRIPT_DIR}/../..; pwd)
source ${ROOT_DIR}/bin/common.sh
echo "Starting Up"

trap - SIGINT

echo "== Stopping running docker containers..."
while ! docker ps > /dev/null 2>&1 ; do sleep 2; done

cd ${ROOT_DIR} \
    && docker compose down --remove-orphans \
    && docker compose rm -f -s -v

docker image prune -a --force
docker volume prune --force