#!/bin/bash

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ROOT_DIR=$(cd ${SCRIPT_DIR}/..; pwd)

source ${SCRIPT_DIR}/common.sh
trap "Exiting" SIGUSR1

# The issue number is the input
ISSUE_NUMBER=$1

if [ -z "$ISSUE_NUMBER" ]; then
    echo "Error: Issue number is empty. Please provide a valid issue number."
    exit 1
fi

if [ -z "$LINEAR_USER_NAME" ]
then
    echo "Linear user name is not defined. Please, enter your linear user name:"
    read LINEAR_USER_NAME
    echo "LINEAR_USER_NAME=$LINEAR_USER_NAME" >> ~/.ninja.conf
else
    echo "Hello $LINEAR_USER_NAME"
fi

# Get the correct branch name
NAME=$LINEAR_USER_NAME/rn-$ISSUE_NUMBER
echo "Creating branch:  $NAME"

git checkout -b ${NAME}