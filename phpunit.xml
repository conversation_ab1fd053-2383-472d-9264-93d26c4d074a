<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         cacheDirectory=".phpunit.cache"
>
  <testsuites>
    <testsuite name="Unit">
      <directory suffix="Test.php">./tests/Unit</directory>
    </testsuite>
    <testsuite name="Feature">
      <directory suffix="Test.php">./tests/Feature</directory>
    </testsuite>
    <testsuite name="OnBackup">
      <directory suffix="Test.php">./tests/OnBackup</directory>
    </testsuite>
  </testsuites>
  <php>
    <!--The following env values will substitute the current env values
        of the env file where tests are run. The rest will keep the same.-->
    <env name="APP_ENV" value="testing"/>
    <!--If you don't add the following line, tests are done through the tunnel in local envs. Check env file-->
    <env name="APP_URL" value="http://localhost"/>
    <env name="BCRYPT_ROUNDS" value="4"/>
    <env name="CACHE_DRIVER" value="array"/>
    <env name="MAIL_MAILER" value="array"/>
    <env name="QUEUE_CONNECTION" value="sync"/>
    <env name="SESSION_DRIVER" value="array"/>
    <env name="TELESCOPE_ENABLED" value="false"/>
    <!--DB CONFIGURATION-->
    <env name="DB_HOST" value="127.0.0.1"/>
    <!--"ninja_db" alias (in env file) only works from inside docker-->
    <!-- On Backup tests: comment the following line or change value to the connection used for a backup DB
             WARNING! If you do so, and you run any test using the trait LazilyRefreshDatabase or RefreshDatabase,
             your database will be refreshed.-->
    <env name="DB_CONNECTION" value="testing"/>
    <!--REDIS CONFIGURATION-->
    <env name="REDIS_HOST" value="127.0.0.1"/>
    <env name="LODGIFY_ENABLED" value="false"/>
  </php>
  <source>
    <include>
      <directory suffix=".php">./app</directory>
    </include>
  </source>
</phpunit>
