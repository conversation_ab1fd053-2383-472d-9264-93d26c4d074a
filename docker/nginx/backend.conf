# default Docker DNS server
resolver 127.0.0.11;
client_max_body_size 50M;

map $cookie_XDEBUG_SESSION $my_fastcgi_pass {
    # default ninja_backend_app;
    # To debug:
    default ninja_backend_app;
    PHPSTORM ninja_xdebug;
}

map $request_uri $allow_embed {
	default 0;
	~*embed 1;
}


# APP
server {
    listen 80 default_server;
    index index.php index.html;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    server_name _;
    root /var/www/public;
    server_name _;

    location ~ \.php$ {
		try_files $uri =404;
		fastcgi_split_path_info ^(.+\.php)(/.+)$;
		fastcgi_pass $my_fastcgi_pass:9000;
		fastcgi_index index.php;
		include fastcgi_params;
		fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
		fastcgi_param PATH_INFO $fastcgi_path_info;
  		if ($allow_embed) {
			add_header Map-Result $allow_embed;
        }
		if ($allow_embed = 0) {
			add_header Map-Result $allow_embed;
            add_header X-Frame-Options "SAMEORIGIN";
		}
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }

    location ~ ^/flux/flux(\.min)?\.(js|css)$ {
        expires off;
        try_files $uri $uri/ /index.php?$query_string;
    }

	location ^~ /website/livewire/livewire.js {
		add_header expires off;
		add_header Access-Control-Allow-Origin *;
		rewrite ^/website/(.*)$  /$1;
		try_files $uri $uri/ /index.php?$1;
	}

	location ^~ /website/livewire/livewire.min.js {
		add_header expires off;
		add_header Access-Control-Allow-Origin *;
		rewrite ^/website/(.*)$  /$1;
		try_files $uri $uri/ /index.php?$1;
	}

    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        add_header Access-Control-Allow-Origin *;
		add_header Cache-Control "public,max-age=86400, stale-while-revalidate=604800";
    }

	## add compression
	gzip on;
	gzip_static on;
	gzip_comp_level 4;
	gzip_proxied any;
	gzip_types text/plain text/css application/javascript text/xml application/xml application/json;
	gzip_min_length 256;
	gzip_vary on;

}
