# Project Development Guidelines

This document provides essential information for developers working on this project. It includes build/configuration instructions, testing information, and additional development details.

## Build/Configuration Instructions

### Docker Setup

The project uses Docker for local development. The main services defined in `docker-compose.yml` are:

1. **ninja_backend_app**: Main application container
2. **ninja_scheduler_app**: Handles scheduled tasks
3. **ninja_xdebug**: Container with Xdebug for debugging
4. **ninja_horizon**: Laravel Horizon for queue management
5. **ninja_webserver**: Nginx web server
6. **ninja_db**: MySQL database
7. **ninja_redis**: Redis for caching and queues

### Getting Started

1. Clone the repository
2. Copy `.env.example` to `.env` and configure environment variables
3. Run `docker-compose up -d` to start the containers
4. Install dependencies:
   ```bash
   docker exec ninja_backend_app composer install
   docker exec ninja_backend_app npm install
   ```
5. Run migrations:
   ```bash
   docker exec ninja_backend_app php artisan migrate
   ```
6. Generate application key:
   ```bash
   docker exec ninja_backend_app php artisan key:generate
   ```

### Database Setup

The MySQL database is configured with:
- Database name: `vapor`
- Root password: `secret`
- Port: `3306`

For testing, a separate database named `tests` is used.

## Testing Information

You don't need to create tests nor run any test at all except if it is explicitly mentioned in the instructions. The below information is only in case you
are requested to do so.

### Testing Framework

The project uses [Pest PHP](https://pestphp.com/) testing framework, which is built on top of PHPUnit but provides a more expressive syntax.

### Test Organization

Tests are organized in three main directories:

1. **Unit Tests** (`tests/Unit`): For testing single methods or classes, particularly Actions which contain core business logic.
2. **Feature Tests** (`tests/Feature`): For testing multiple classes together, like routes.
3. **OnBackup Tests** (`tests/OnBackup`): For testing against real data in a backup database.

### Running Tests

Tests are run locally (not in Docker) and require PHP and MySQL installed on your machine.

#### Setup for Testing

1. Create a testing database:
   ```bash
   docker exec ninja_db mysql -uroot -psecret -e "CREATE DATABASE tests;"
   ```

2. Ensure your `phpunit.xml` has the correct configuration:
   - For normal tests: `DB_CONNECTION=testing`
   - For OnBackup tests: `DB_CONNECTION=mysql` or comment out the line to use your local env value

#### Running Tests via CLI

```bash
# Run all tests
vendor/bin/pest

# Run a specific test or directory
vendor/bin/pest --filter=TestNameOrFolder

# Run tests in parallel
vendor/bin/pest --parallel
```

#### Running Tests via IDE

If you have the Pest plugin installed in your IDE, you can:
- Click the "play" button next to a test to run it
- Click the "double play" button at the top of a file to run all tests in that file
- Configure keyboard shortcuts (e.g., Command+T) to run tests

### Creating New Tests

```bash
# Create a unit test
php artisan pest:test --unit TestName

# Create a feature test
php artisan pest:test TestName
```

Important: Test files must end with "Test" (e.g., `NinjaMathHelperTest.php`).

### Example Test

Here's a simple test for the `NinjaMathHelper` class:

```php
<?php

use App\Support\NinjaMathHelper;

it('rounds to the nearest half', function () {
    expect(NinjaMathHelper::nearestHalf(1.2))->toBe(1.0);
    expect(NinjaMathHelper::nearestHalf(1.5))->toBe(1.5);
    expect(NinjaMathHelper::nearestHalf(1.8))->toBe(1.5);
});

it('checks if two values are equal to the nearest half', function () {
    expect(NinjaMathHelper::equalsToTheNearestHalf(1.2, 1.4))->toBeTrue();
    expect(NinjaMathHelper::equalsToTheNearestHalf(1.2, 1.5))->toBeFalse();
});
```

## Additional Development Information

### Code Organization

The project follows a domain-driven structure with the following key directories:

- **Actions**: Contains business logic in single-purpose action classes
- **Models**: Database models with relationships and business logic
- **DTO**: Data Transfer Objects for structured data passing
- **Enum**: Enumeration classes for type-safe constants
- **Support**: Helper classes and utilities
- **Traits**: Reusable trait classes
- **Query**: Query builders and database-related functionality

### Laravel Actions

The project uses the [Laravel Actions](https://laravelactions.com/) package (lorisleiva/laravel-actions) to organize business logic into single-purpose classes. This approach helps maintain a clean codebase by separating business logic from controllers and other components.

#### Key Features

1. **Single-Purpose Classes**: Each action class handles one specific task or operation.
2. **Multiple Execution Contexts**: Actions can be used in different contexts:
   - As a regular class (using `->handle()` method)
   - As a controller (using `AsController` trait)
   - As a listener (using `AsListener` trait)
   - As a job (using `AsJob` trait)

#### Implementation Pattern

Actions are implemented using the following pattern:

```php
<?php

namespace App\Actions\XXXX;

class XXXXAction
{
    use AsAction;

    /** Main method that contains the business logic **/
    public function handle(/* parameters... */) {
        // Business logic implementation
        return $result;
    }
}
```

#### Best Practices

1. **Naming Convention**: Action class names should be descriptive and end with "Action" (e.g., `StoreBookingAction`).
2. **Organization**: Actions are organized by domain and functionality in the `app/Actions` and `app/Domains/*/Actions` directories.
3. **Single Responsibility**: Each action should have a single responsibility and be focused on a specific task.
4. **Type Hinting**: Use type hints for parameters and return values to improve code clarity and IDE support.
5. **Documentation**: Document the purpose of the action and its parameters with PHPDoc comments.

### Laravel Data Package

The project uses the [Laravel Data](https://spatie.be/docs/laravel-data/v3/introduction) package (spatie/laravel-data) to create strongly-typed data transfer objects (DTOs). These DTOs help ensure data consistency and provide a structured way to pass data between different parts of the application.

#### Key Features

1. **Type Safety**: Properties are strongly typed, providing better IDE support and catching errors early.
2. **Data Validation**: DTOs can include validation rules to ensure data integrity.
3. **Transformations**: Data can be easily transformed between different formats (array, JSON, etc.).
4. **Optional Properties**: The `Optional` type allows for properties that may or may not be present.

#### Implementation Pattern

DTOs are implemented using the following pattern:

```php
<?php

namespace App\DTO\Tasks;

use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Optional;
use Illuminate\Support\Carbon;

class TaskDto extends Data
{
    public function __construct(
        public Optional|int $team_id,
        public Optional|int|null $booking_id,
        public Optional|string|null $title,
        public Optional|string|null $description,
        public Optional|Carbon|null $due_date,

        #[DataCollectionOf(TaskItemDto::class)]
        public Optional|DataCollection|null $task_items,
    ) {
    }

    // Helper methods for transforming the DTO

}
```

#### Best Practices

1. **Naming Convention**: DTO class names should be descriptive and end with "Dto" or "Data" (e.g., `BookingDto`, `TaskData`).
2. **Organization**: DTOs are organized by domain in the `app/DTO` directory.
3. **Type Hinting**: Use type hints for all properties to improve code clarity and IDE support.
4. **Immutability**: Treat DTOs as immutable objects when possible, creating new instances instead of modifying existing ones.
5. **Helper Methods**: Include helper methods for logical functions.
6. **Nested DTOs**: Use nested DTOs for complex data structures, using the `DataCollectionOf` attribute for collections.

### Helper Functions for Testing

The project provides several helper functions in `tests/Pest.php` to simplify test setup:

- `createTeam()`: Creates a team with optional provider
- `createUserOnTeam()`: Creates a user with a specific role in a team
- `createRentals()`: Creates rentals for a team
- `createBookings()`: Creates bookings with optional team, rental, client, source

### Debugging Tests

You can use Xdebug to debug tests by:
1. Setting breakpoints in your code
2. Using the "debug" option in your IDE's Pest plugin
3. Configuring the Xdebug port (default: 9003) in your IDE settings
