import puppeteer from "@cloudflare/puppeteer";

const DEFAULT_MAX_RETRIES = 3;
const DEFAULT_BROWSER_RETRY_BASE_DELAY = 1000; // milliseconds
const DEFAULT_CONTENT_LOAD_TIMEOUT = 30000; // milliseconds
const DEFAULT_PDF_GENERATION_TIMEOUT = 55000; // milliseconds
const DEFAULT_FONT_LOAD_TIMEOUT = 10000; // milliseconds

async function acquireBrowserWithLock(env) {
    const maxRetries = parseInt(env.MAX_BROWSER_RETRIES || String(DEFAULT_MAX_RETRIES), 10);
    const baseDelay = parseInt(env.BROWSER_RETRY_BASE_DELAY || String(DEFAULT_BROWSER_RETRY_BASE_DELAY), 10);
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const limits = await puppeteer.limits(env.MYBROWSER);

            if (limits.allowedBrowserAcquisitions > 0) {
                console.log(`Attempt ${attempt}: Trying to launch a new browser.`);
                try {
                    const browser = await puppeteer.launch(env.MYBROWSER, {
                        keep_alive: 55000,
                    });
                    console.log(`Attempt ${attempt}: Successfully launched a new browser.`);
                    return { browser, sessionId: browser.sessionId() };
                } catch (launchError) {
                    console.warn(`Attempt ${attempt}: Failed to launch browser: ${launchError.message}`);
                    // Fallback to existing sessions
                }
            }

            const sessions = await puppeteer.sessions(env.MYBROWSER);
            const availableSessions = sessions.filter(s => !s.connectionId);

            if (availableSessions.length > 0) {
                for (const session of availableSessions) {
                    try {
                        console.log(`Attempt ${attempt}: Trying to connect to session ${session.sessionId}.`);
                        const browser = await puppeteer.connect(
                            env.MYBROWSER,
                            session.sessionId,
                            { timeout: 10000 } // Add connection timeout
                        );
                        console.log(`Attempt ${attempt}: Successfully connected to session ${session.sessionId}.`);
                        return { browser, sessionId: session.sessionId };
                    } catch (e) {
                        console.error(`Attempt ${attempt}: Failed to connect to session ${session.sessionId}: ${e.message}`);
                        continue;
                    }
                }
            }

            if (attempt === maxRetries) {
                throw new Error("No available browser sessions after multiple retries.");
            }

            const delay = baseDelay * Math.pow(2, attempt - 1) * (0.5 + Math.random());
            console.log(`Attempt ${attempt}: Waiting for ${delay.toFixed(0)}ms before retrying.`);
            await new Promise(resolve => setTimeout(resolve, delay));

        } catch (error) {
            lastError = error;
            if (attempt === maxRetries) {
                throw new Error(`Failed to acquire browser after ${maxRetries} attempts: ${error.message}`);
            }
        }
    }
    throw lastError || new Error("Failed to acquire browser.");
}

async function generatePDF(browser, data, env) {
    let page = null;
    try {
        page = await browser.newPage();

        await page.setViewport({
            width: 800,
            height: 1132,
            deviceScaleFactor: 1,
        });

        const contentLoadTimeout = parseInt(env.CONTENT_LOAD_TIMEOUT || String(DEFAULT_CONTENT_LOAD_TIMEOUT), 10);
        if (data.content) {
            await Promise.race([
                page.setContent(data.content, { waitUntil: "networkidle0" }),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error("Content load timeout")), contentLoadTimeout)
                )
            ]);
        }

        const fontLoadTimeout = parseInt(env.FONT_LOAD_TIMEOUT || String(DEFAULT_FONT_LOAD_TIMEOUT), 10);
        await Promise.race([
            page.evaluate(() => document.fonts.ready),
            new Promise((_, reject) => setTimeout(() => reject(new Error("Font load timeout")), fontLoadTimeout))
        ]);

        await page.evaluate(() => {
            const pages = document.querySelectorAll(".page-container");
            pages.forEach((page) => {
                if (!page.textContent.trim() && !page.querySelector("img")) {
                    page.remove();
                }
            });

            const containers = document.querySelectorAll(".page-container");
            if (containers.length > 0) {
                containers[containers.length - 1].style.pageBreakAfter = "avoid";
                containers[containers.length - 1].style.minHeight = "auto";
            }
        });

        await page.addStyleTag({
            content: `
                @page {
                    margin: 100px 20px 100px 20px;
                    size: A4;
                }
                .header-template, .footer-template {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    display: block !important;
                    visibility: visible !important;
                }
            `,
        });

        return await page.pdf({
            format: "A4",
            printBackground: true,
            displayHeaderFooter: true,
            headerTemplate: data.headerTemplate,
            footerTemplate: data.footerTemplate,
            margin: {
                top: "100px",
                bottom: "100px",
                right: "20px",
                left: "20px",
            },
            preferCSSPageSize: true,
        });
    } finally {
        if (page) await page.close().catch(console.error);
    }
}

export default {
    async fetch(request, env, ctx) {
        const url = new URL(request.url);
        if (url.searchParams.get("api_key") !== env.API_KEY) {
            return new Response("Unauthorized", { status: 401 });
        }

        if (request.method !== "POST") {
            return new Response("Method not allowed", { status: 405 });
        }

        let browser = null;
        let sessionId = null;

        try {
            const data = await request.json();

            const browserData = await acquireBrowserWithLock(env);
            browser = browserData.browser;
            sessionId = browserData.sessionId;

            const pdfGenerationTimeout = parseInt(env.PDF_GENERATION_TIMEOUT || String(DEFAULT_PDF_GENERATION_TIMEOUT), 10);
            const pdf = await Promise.race([
                generatePDF(browser, data, env),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error("PDF generation timeout")), pdfGenerationTimeout)
                )
            ]);

            return new Response(pdf, {
                headers: {
                    "Content-Type": "application/pdf",
                    "Content-Disposition": `inline; filename="${
                        data.filename || "document.pdf"
                    }"`,
                },
            });
        } catch (error) {
            console.error(`PDF Generation Error (Session ID: ${sessionId || 'N/A'}):`, {
                message: error.message,
                stack: error.stack,
            });
            return new Response(
                JSON.stringify({
                    error: `PDF generation failed: ${error.message}`,
                    stack: error.stack,
                    sessionId,
                }),
                {
                    status: 500,
                    headers: { "Content-Type": "application/json" },
                }
            );
        } finally {
            if (browser) {
                try {
                    await browser.close();
                } catch (e) {
                    console.error("Error closing browser:", e);
                }
            }
        }
    },
};
