<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('booking', function (Blueprint $table) {
            $table->boolean('archived_conversation')
                ->after('nights')
                ->default(false);
        });
        Schema::create('booking_user', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('team_id');
            $table->unsignedBigInteger('booking_id');
            $table->unsignedInteger('user_id');
            $table->dateTime('conversation_read_at')
                  ->nullable();
            $table->unique(['team_id', 'booking_id', 'user_id']);
        });
        Schema::table('threads', function (Blueprint $table) {
            $table->index('last_message_date');
        });
        Schema::table('messages', function (Blueprint $table) {
            $table->index('sent_at');
        });
        Schema::table('booking', function (Blueprint $table) {
            $table->index(['team_id', 'provider_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('booking', function (Blueprint $table) {
            $table->dropColumn('archived_conversation');
        });
        Schema::dropIfExists('booking_user');
        Schema::table('threads', function (Blueprint $table) {
            $table->dropIndex('threads_last_message_date_index');
        });
        Schema::table('messages', function (Blueprint $table) {
            $table->dropIndex('messages_sent_at_index');
        });
        Schema::table('booking', function (Blueprint $table) {
            $table->dropIndex('booking_team_id_provider_id_index');
        });
    }
};
