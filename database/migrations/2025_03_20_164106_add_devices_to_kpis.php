<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('team_performance_indicators', function (Blueprint $table) {
            $table->unsignedSmallInteger('home_automation_devices')
                ->default(0)
                ->after('smart_pricing_rentals');
        });

        Schema::table('team_monthly_performance_indicators', function (Blueprint $table) {
            $table->unsignedSmallInteger('home_automation_devices')
                ->default(0)
                ->after('smart_pricing_rentals');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('team_performance_indicators', function (Blueprint $table) {
            $table->dropColumn('home_automation_devices');
        });

        Schema::table('team_monthly_performance_indicators', function (Blueprint $table) {
            $table->dropColumn('home_automation_devices');
        });
    }
};
