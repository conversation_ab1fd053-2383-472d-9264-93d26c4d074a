<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $columns = DB::select('SHOW COLUMNS FROM team_performance_indicators');

        Schema::create('lodgify_performance_indicators', function (Blueprint $table) use ($columns) {
            $table->id();
            foreach ($columns as $column) {
                if ($column->Field == 'id') {
                    continue;
                }

                // Adjust the data types and options as needed
                $type = $column->Type;
                $field = $column->Field;

                // Parse type and length
                preg_match('/([a-z]+)(?:\(([\d,]+)\))?(?:\s(unsigned))?/', $type, $matches);
                $type = $matches[1];
                $length = $matches[2] ?? null;
                $unsigned = ($matches[3] ?? null) === 'unsigned';

                // Determine the appropriate column type and options
                switch ($type) {
                    case 'int':
                        $col = $table->integer($field, false, $unsigned);
                        break;
                    case 'bigint':
                        $col = $table->bigInteger($field, false, $unsigned);
                        break;
                    case 'mediumint':
                        $col = $table->mediumInteger($field, false, $unsigned);
                        break;
                    case 'smallint':
                        $col = $table->smallInteger($field, false, $unsigned);
                        break;
                    case 'tinyint':
                        $col = $table->tinyInteger($field, false, $unsigned);
                        break;
                    case 'varchar':
                        $col = $table->string($field, $length);
                        break;
                    case 'char':
                        $col = $table->char($field, $length);
                        break;
                    case 'text':
                        $col = $table->text($field);
                        break;
                    case 'mediumtext':
                        $col = $table->mediumText($field);
                        break;
                    case 'longtext':
                        $col = $table->longText($field);
                        break;
                    case 'date':
                        $col = $table->date($field);
                        break;
                    case 'datetime':
                        $col = $table->dateTime($field);
                        break;
                    case 'timestamp':
                        $col = $table->timestamp($field);
                        break;
                    case 'time':
                        $col = $table->time($field);
                        break;
                    case 'decimal':
                        if (is_null($length)) {
                            $precision = null;
                            $scale = null;
                        } else {
                            [$precision, $scale] = explode(',', $length);
                        }
                        $col = $table->decimal($field, $precision, $scale);
                        break;
                    case 'float':
                        if (is_null($length)) {
                            $precision = null;
                            $scale = null;
                        } else {
                            [$precision, $scale] = explode(',', $length);
                        }
                        $col = $table->float($field, $precision, $scale);
                        break;
                    case 'double':
                        if (is_null($length)) {
                            $precision = null;
                            $scale = null;
                        } else {
                            [$precision, $scale] = explode(',', $length);
                        }
                        $col = $table->double($field, $precision, $scale);
                        break;
                    case 'boolean':
                        $col = $table->boolean($field);
                        break;
                    case 'enum':
                        $values = str_getcsv($length);
                        $col = $table->enum($field, $values);
                        break;
                    case 'json':
                        $col = $table->json($field);
                        break;
                    case 'binary':
                        $col = $table->binary($field);
                        break;
                    default:
                        $col = $table->string($field);
                        break;
                }

                // Handle nullable columns
                if ($column->Null === 'YES') {
                    $col->nullable();
                }

                // Handle default values
                if (! is_null($column->Default)) {
                    $col->default($column->Default);
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('lodgify_performance_indicators');
    }
};
