<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('slug');
            $table->dropColumn('oauth_access_token');
            $table->dropColumn('oauth_refresh_token');
            $table->dropColumn('oauth_expires_at');
            $table->string('currency')->after('departure_time')->nullable();
        });

        Schema::table('provider_accounts', function (Blueprint $table) {
            $table->dropColumn('business_name');
            $table->dropColumn('name');
            $table->dropColumn('extra_info');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
