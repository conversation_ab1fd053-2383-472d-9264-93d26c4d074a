<?php

use App\DataProviders\Providers\ChannelManagerProvider;
use App\DataProviders\Providers\RentalsUnited;
use App\Models\Booking;
use App\Models\Source;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $ruName = RentalsUnited::get()->fullName();
        Source::query()
            ->whereIn('provider_id', [RentalsUnited::ID, ChannelManagerProvider::ID])
            ->where('name', $ruName)
            ->each(function (Source $s) {
                $s->name = 'Internal Booking';
                $s->save();
            });

        Booking::query()
            ->whereProviderId(ChannelManagerProvider::ID)
            ->each(function (Booking $b) {
                nLog("Booking $b->id, payment {$b->bookingPayments->first()->provider_id}, source {$b->source->provider_id}");
                $b->bookingPayments()->where('provider_id', 4)->update(['provider_id' => 6]);
                if ($b->source->provider_id == 4) {
                    $b->source->provider_id = ChannelManagerProvider::ID;
                    $b->source->save();
                }
                nLog("Booking $b->id, payment {$b->bookingPayments->first()->provider_id}, source {$b->source->provider_id}");
                nLog('');
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
