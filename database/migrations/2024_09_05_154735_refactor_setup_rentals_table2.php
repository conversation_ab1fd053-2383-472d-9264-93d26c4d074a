<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('setup_rentals', function (Blueprint $table) {
            $table->string('airbnb_name')->nullable()->after('airbnb_id');
            $table->string('booking_name')->nullable()->after('booking_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('setup_rentals', function (Blueprint $table) {
            $table->dropColumn('airbnb_name');
            $table->dropColumn('booking_name');
        });
    }
};
