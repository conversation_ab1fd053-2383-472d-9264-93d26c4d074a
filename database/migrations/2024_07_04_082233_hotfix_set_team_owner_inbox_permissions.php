<?php

use App\Enum\TeamRolesEnum;
use App\Models\TeamUsersPermission;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // We want to make sure team owners, admins and PM's can see the inbox when we launch
        TeamUsersPermission::query()
            ->whereIn('user_id', User::query()
                 ->whereIn('ninja_role', TeamRolesEnum::managementRoles())
                 ->pluck('id'))
            ->update([
                'can_access_inbox' => 2,
                'can_send_inbox_messages' => 2,
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
