<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('team_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('team_id');

            // Contact information
            $table->string('contact_first_name')->nullable();
            $table->string('contact_last_name')->nullable();
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->string('contact_city')->nullable();
            $table->string('contact_country')->nullable();
            $table->string('contact_address')->nullable();
            $table->string('contact_zip')->nullable();
            $table->date('contact_birthday')->nullable();
            $table->string('contact_language')->nullable();

            // Company information
            $table->string('company_name')->nullable();
            $table->string('company_website')->nullable();
            $table->string('company_city')->nullable();
            $table->json('company_locations')->nullable();
            // Not required by RU
            $table->string('company_country')->nullable();
            $table->string('company_zip')->nullable();
            $table->string('company_timezone')->nullable();
            $table->string('company_region')->nullable();
            $table->string('company_address')->nullable();
            $table->string('company_phone')->nullable();
            $table->string('company_id')->nullable();
            $table->string('company_vat')->nullable();
            $table->string('company_merchant_name')->nullable();
            $table->integer('company_num_rentals')->nullable();
            $table->integer('company_num_employees')->nullable();
            $table->integer('company_creation_year')->nullable();
            $table->text('company_description')->nullable();

            // Legal representative
            $table->string('representative_first_name')->nullable();
            $table->string('representative_last_name')->nullable();
            $table->string('representative_address')->nullable();
            $table->string('representative_birthday')->nullable();
            $table->string('representative_nationality')->nullable();
            $table->string('representative_email')->nullable();
            $table->string('representative_residency_country')->nullable();
            $table->string('representative_city')->nullable();
            $table->string('representative_zip')->nullable();
            $table->string('representative_region')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('team_details');
    }
};
