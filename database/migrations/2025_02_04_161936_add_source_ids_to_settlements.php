<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settlements', function (Blueprint $table) {
            $table->json('sources')
                ->after('rentals')
                ->nullable();
        });
        Schema::table('scheduled_settlements', function (Blueprint $table) {
            $table->json('sources')
                  ->after('rentals')
                  ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settlements', function (Blueprint $table) {
            $table->dropColumn('sources');
        });
        Schema::table('scheduled_settlements', function (Blueprint $table) {
            $table->dropColumn('sources');
        });
    }
};
