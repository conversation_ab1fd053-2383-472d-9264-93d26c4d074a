<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('booking_alerts', function (Blueprint $table) {
            $table->unsignedInteger('rental_id')->nullable()->change();
            $table->dropColumn('rental_name');
            $table->unsignedInteger('booking_id')->nullable()->change();
            $table->dropColumn('start_at');
            $table->dropColumn('end_at');
            $table->unsignedSmallInteger('alert_type')->change();
            $table->dropColumn('emoji');
            $table->dropColumn('alert_name');
            $table->string('real_value')->nullable()->change();
            $table->dropColumn('alert_description');
            $table->dropColumn('alert_value');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
