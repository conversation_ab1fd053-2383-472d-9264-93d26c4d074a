<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('setup_rentals', function (Blueprint $table) {
            $table->string('airbnb_host')->nullable()->after('airbnb_name');
            $table->json('tmp_availability')->nullable()->after('last_fetched_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('setup_rentals', function (Blueprint $table) {
            $table->dropColumn('airbnb_host');
            $table->dropColumn('tmp_availability');
        });
    }
};
