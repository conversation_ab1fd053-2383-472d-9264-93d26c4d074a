<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('team_performance_indicators', function (Blueprint $table) {
            $table->double('ai_revenue')->default(0)->after('upscale_revenue');
            $table->double('addons_revenue')->default(0)->after('booking_engine_commission');
        });

        Schema::table('team_monthly_performance_indicators', function (Blueprint $table) {
            $table->double('ai_revenue')->default(0)->after('upscale_revenue');
            $table->double('addons_revenue')->default(0)->after('booking_engine_commission');
        });

        Schema::create('billable_addon_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('team_id');
            $table->string('type');
            $table->integer('quantity');
            $table->unsignedBigInteger('rental_id')->nullable();
            $table->unsignedBigInteger('booking_id')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('extra')->nullable();
            $table->timestamps();
        });

        Schema::table('team_settings', function (Blueprint $table) {
            $table->integer('ai_price')->after('upscale_price')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('team_performance_indicators', function (Blueprint $table) {
            $table->dropColumn('ai_revenue');
            $table->dropColumn('addons_revenue');
        });

        Schema::table('team_monthly_performance_indicators', function (Blueprint $table) {
            $table->dropColumn('ai_revenue');
            $table->dropColumn('addons_revenue');
        });

        Schema::dropIfExists('billable_addon_logs');

        Schema::table('team_settings', function (Blueprint $table) {
            $table->dropColumn('ai_price');
        });
    }
};
