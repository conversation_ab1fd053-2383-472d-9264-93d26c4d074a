<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update rates_sync_paused_until column from date to timestamp
        Schema::table('rental', function (Blueprint $table) {
            $table->dropColumn('rates_sync_paused_until');
        });

        Schema::table('rental', function (Blueprint $table) {
            $table->timestamp('rates_sync_paused_until')->nullable()->after('api_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rental', function (Blueprint $table) {
            // Revert rates_sync_paused_until column from datetime to date
            $table->date('rates_sync_paused_until')->nullable()->change();
        });
    }
};
