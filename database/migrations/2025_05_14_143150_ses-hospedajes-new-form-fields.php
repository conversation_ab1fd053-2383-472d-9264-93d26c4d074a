<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client', function (Blueprint $table) {
            $table->dropColumn('passport'); // TODO: should we drop this column? If so, remove from the rest of the places
        });

        Schema::table('pre_check_in_form_passport', function (Blueprint $table) {
            $table->dropColumn('last_name_2'); // TODO: we will study if we drop this column
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
