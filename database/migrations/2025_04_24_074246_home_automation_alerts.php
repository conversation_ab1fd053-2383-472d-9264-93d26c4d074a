<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('team_users_permissions', function (Blueprint $table) {
            $table->dropColumn('share_alert');
            $table->dropColumn('alert_cleaning_needed');
            $table->dropColumn('alert_others');
        });

        Schema::table('booking_alerts', function (Blueprint $table) {
            $table->string('alertable_type')
                ->nullable()
                ->after('booking_id');
            $table->unsignedBigInteger('alertable_id')
                ->nullable()
                ->after('alertable_type');
            $table->index(['alertable_type', 'alertable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('booking_alerts', function (Blueprint $table) {
            $table->dropColumn('alertable_type');
            $table->dropColumn('alertable_id');
        });
    }
};
