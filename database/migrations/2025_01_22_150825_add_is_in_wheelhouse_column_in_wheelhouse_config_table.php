<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wheelhouse_configs', function (Blueprint $table) {
            $table->boolean('wh_successful')->default(false)->after('wh_conservative_base_rate');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wheelhouse_configs', function (Blueprint $table) {
            $table->dropColumn('wh_successful');
        });
    }
};
