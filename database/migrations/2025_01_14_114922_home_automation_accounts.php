<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('home_automation_accounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('team_id')
                ->index();
            $table->string('provider')
                ->comment('nuki, igloohome,...');
            $table->unsignedBigInteger('external_id')
                ->nullable();
            $table->string('name') // Given by the user
                  ->nullable();
            $table->string('access_token')
                ->nullable();
            $table->string('refresh_token')
                  ->nullable();
            $table->timestamp('token_expires_at')
                  ->nullable();
            $table->timestamps();

            $table->unique(['external_id', 'provider']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('home_automation_accounts');
    }
};
