<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('distribution_websites', function (Blueprint $table) {
            $table->string('fathom_id')->nullable()->after('domain_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('distribution_websites', function (Blueprint $table) {
            $table->dropColumn('fathom_id');
        });
    }
};
