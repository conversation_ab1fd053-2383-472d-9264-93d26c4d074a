<?php

use App\DataProviders\Providers\BookingSync;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('fees', function (Blueprint $table) {
            $table->smallInteger('provider_id')->after('external_id')->nullable();
        });
        // At migration time, all external fees are from BookingSync
        DB::table('fees')
            ->whereNotNull('external_id')
            ->select('id')
            ->chunkById(1000, function (Collection $rows) {
                DB::table('fees')
                    ->whereIn('id', $rows->pluck('id'))
                    ->update(['provider_id' => BookingSync::ID]);
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('fees', function (Blueprint $table) {
            $table->dropColumn('provider_id');
        });
    }
};
