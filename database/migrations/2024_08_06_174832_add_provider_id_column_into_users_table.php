<?php

use App\DataProviders\Providers\RentalsUnitedPms;
use App\DataProviders\Providers\Tokeet;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedSmallInteger('provider_id')->nullable()->after('external_id');
        });
        User::query()->whereRaw('LENGTH(external_id) > 35')->update(['provider_id' => Tokeet::ID]);
        User::query()->whereRaw('LENGTH(external_id) < 8')->update(['provider_id' => RentalsUnitedPms::ID]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('provider_id');
        });
    }
};
