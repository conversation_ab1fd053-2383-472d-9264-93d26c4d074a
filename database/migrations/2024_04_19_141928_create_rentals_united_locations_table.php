<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rentals_united_locations', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('external_id')->index('external_index');
            $table->string('name');
            $table->unsignedSmallInteger('type');
            $table->unsignedInteger('external_parent_id');
            $table->char('country', 2)->nullable();
            $table->char('currency', 3)->nullable();
            $table->char('timezone', 32)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rentals_united_locations');
    }
};
