<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('distribution_websites', function (Blueprint $table) {
            $table->string('contact_booking_phone')->after('contact_phone')->nullable();
            $table->renameColumn('contact_phone', 'contact_support_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('distribution_websites', function (Blueprint $table) {
            //
        });
    }
};
