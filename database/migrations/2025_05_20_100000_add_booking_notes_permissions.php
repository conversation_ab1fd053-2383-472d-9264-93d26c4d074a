<?php

use App\Enum\TeamRolesEnum;
use App\Models\TeamUsersPermission;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('team_users_permissions', function (Blueprint $table) {
            $table->tinyInteger('edit_booking_notes')
                ->after('view_booking_notes')
                ->default(0);
            $table->tinyInteger('view_booking_notice')
                ->after('edit_booking_notes')
                ->default(0);
        });

        // Give Access to All current rental managers or above.
        TeamUsersPermission::query()
            ->whereIn('user_id', function ($query) {
                $query->select('id')
                    ->from('users')
                    ->whereIn('users.ninja_role', [TeamRolesEnum::ADMIN, TeamRolesEnum::OWNER]);
            })
            ->update([
                'edit_booking_notes' => DB::raw('view_booking_notes'),
                'view_booking_notice' => DB::raw('view_booking_notes'),
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('team_users_permissions', function (Blueprint $table) {
            $table->dropColumn('edit_booking_notes');
            $table->dropColumn('view_booking_notice');
        });
    }
};
