<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('updated_since_request_synced_at', function (Blueprint $table) {
            $table->smallInteger('provider_id')->after('team_id');
        });

        DB::statement('UPDATE updated_since_request_synced_at
                        JOIN teams t ON updated_since_request_synced_at.team_id = t.id
                        SET updated_since_request_synced_at.provider_id = t.provider_id');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('update_since_request_synced_at', function (Blueprint $table) {
            //
        });
    }
};
