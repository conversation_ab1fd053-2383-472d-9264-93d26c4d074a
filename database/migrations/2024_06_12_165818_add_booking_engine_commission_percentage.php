<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('team_settings', function (Blueprint $table) {
            $table->decimal('booking_engine_commission_percentage', 5, 3, true)
                ->comment('Decimal of 5 digits with 3 decimal places')
                ->nullable()
                ->after('upscale_price');
        });
        Schema::table('team_performance_indicators', function (Blueprint $table) {
            $table->double('booking_engine_commission')
                ->after('cm_commission_revenue')
                ->default(0);
        });
        Schema::table('team_monthly_performance_indicators', function (Blueprint $table) {
            $table->double('booking_engine_commission')
                  ->after('cm_commission_revenue')
                  ->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('team_settings', function (Blueprint $table) {
            $table->dropColumn('booking_engine_commission_percentage');
        });
        Schema::table('team_performance_indicators', function (Blueprint $table) {
            $table->dropColumn('booking_engine_commission');
        });
        Schema::table('team_monthly_performance_indicators', function (Blueprint $table) {
            $table->dropColumn('booking_engine_commission');
        });
    }
};
