<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wheelhouse_configs', function (Blueprint $table) {
            $table->boolean('active')->nullable()->after('rental_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wheelhouse_configs', function (Blueprint $table) {
            $table->dropColumn('active');
        });
    }
};
