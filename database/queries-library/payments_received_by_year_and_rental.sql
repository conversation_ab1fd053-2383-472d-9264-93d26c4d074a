SELECT name,
       SUM(CASE WHEN G.`paid_at`='2006' THEN G.`amount` ELSE 0.0 END) AS '2006',
       SUM(CASE WHEN G.`paid_at`='2007' THEN G.`amount` ELSE 0.0 END) AS '2007',
       SUM(CASE WHEN G.`paid_at`='2008' THEN G.`amount` ELSE 0.0 END) AS '2008',
       SUM(CASE WHEN G.`paid_at`='2009' THEN G.`amount` ELSE 0.0 END) AS '2009',
       SUM(CASE WHEN G.`paid_at`='2010' THEN G.`amount` ELSE 0.0 END) AS '2010',
       SUM(CASE WHEN G.`paid_at`='2011' THEN G.`amount` ELSE 0.0 END) AS '2011',
       SUM(CASE WHEN G.`paid_at`='2012' THEN G.`amount` ELSE 0.0 END) AS '2012',
       SUM(CASE WHEN G.`paid_at`='2013' THEN G.`amount` ELSE 0.0 END) AS '2013',
       SUM(CASE WHEN G.`paid_at`='2014' THEN G.`amount` ELSE 0.0 END) AS '2014',
       SUM(CASE WHEN G.`paid_at`='2015' THEN G.`amount` ELSE 0.0 END) AS '2015',
       SUM(CASE WHEN G.`paid_at`='2016' THEN G.`amount` ELSE 0.0 END) AS '2016',
       SUM(CASE WHEN G.`paid_at`='2017' THEN G.`amount` ELSE 0.0 END) AS '2017',
       SUM(CASE WHEN G.`paid_at`='2018' THEN G.`amount` ELSE 0.0 END) AS '2018',
       SUM(CASE WHEN G.`paid_at`='2019' THEN G.`amount` ELSE 0.0 END) AS '2019',
       SUM(CASE WHEN G.`paid_at`='2020' THEN G.`amount` ELSE 0.0 END) AS '2020',
       SUM(CASE WHEN G.`paid_at`='2021' THEN G.`amount` ELSE 0.0 END) AS '2021',
       SUM(CASE WHEN G.`paid_at`='2022' THEN G.`amount` ELSE 0.0 END) AS '2022',
       SUM(CASE WHEN G.`paid_at`='2023' THEN G.`amount` ELSE 0.0 END) AS '2023',
       SUM(CASE WHEN G.`paid_at`='2024' THEN G.`amount` ELSE 0.0 END) AS '2024',
       SUM(CASE WHEN G.`paid_at`='2025' THEN G.`amount` ELSE 0.0 END) AS '2025'
FROM
  (SELECT name,
          paid_at,
          SUM(amount) AS amount FROM
     (SELECT amount, rental.name AS name, paid_at
      FROM
        (SELECT base.id, base.amount, base.paid_at, base.booking_id, booking.rental_id
         FROM
           (SELECT payment.id, Round(payment.amount_in_cents / 100, 2) AS `amount`, Date_format(From_unixtime(`paid_at`), "%Y") AS 'paid_at', booking_payment.booking_id AS booking_id
            FROM payment
            RIGHT JOIN booking_payment ON payment.id = booking_payment.payment_id
            WHERE payment.team_id = 1
              AND payment.currency = 'EUR'
              AND payment.canceled_at = '0'
              AND payment.rejected_at = '0') AS base
         RIGHT JOIN booking ON base.booking_id = booking.id
         WHERE base.id IS NOT NULL) AS r
      RIGHT JOIN rental ON r.rental_id = rental.id
      WHERE r.id IS NOT NULL ) AS P
   GROUP BY P.name,
            P.paid_at
   ORDER BY name DESC) AS G
GROUP BY name
