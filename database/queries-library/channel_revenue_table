-- CHANNEL REVENUE BY YEAR
SELECT year, name, ROUND( SUM(amount), 2) AS amount
FROM (
  SELECT booking.final_price AS amount,
  IFNULL(source.name, 'NONE') AS name,
  Date_format(From_unixtime(booking.start_at), "%Y") AS year
  FROM booking
  RIGHT JOIN source ON booking.source_id = source.id
  WHERE booking.team_id = 1
  AND booking.currency = 'EUR'
  AND booking.canceled_at = '0'
  AND booking.final_price IS NOT NULL
  ) AS baseview
GROUP BY name, year
ORDER BY year ASC

-- CHANNEL REVENUE BY RENTAL
SELECT rental, name, ROUND( SUM(amount), 2) AS amount
FROM (
  SELECT booking.final_price AS amount,
  IFNULL(source.name, 'No Channel Found') AS name,
  rental.name as rental
  FROM booking
  LEFT JOIN source ON booking.source_id = source.id
  RIGHT JOIN rental ON booking.rental_id = rental.id
  WHERE booking.team_id = 1
  AND booking.currency = 'EUR'
  AND booking.canceled_at = '0'
  AND booking.final_price IS NOT NULL
  ) AS baseview
GROUP BY name, rental
ORDER BY rental ASC