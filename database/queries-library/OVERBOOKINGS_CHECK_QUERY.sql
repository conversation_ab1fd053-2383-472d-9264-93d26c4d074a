SELECT
  b1.id AS booking_id_1,
  b2.id AS booking_id_2,
  b1.team_id,
  b1.rental_id,
  date(from_unixtime(b1.start_at)) AS booking_1_start,
  date(from_unixtime(b1.end_at)) AS booking_1_end,
  date(from_unixtime(b2.start_at)) AS booking_2_start,
  date(from_unixtime(b2.end_at)) AS booking_2_end,
  b1.provider_id,
  b2.provider_id
FROM
  booking b1
  JOIN booking b2 ON b1.team_id = b2.team_id
  AND b1.rental_id = b2.rental_id
  AND b1.id <> b2.id
  AND date(from_unixtime(b1.start_at)) < date(from_unixtime(b2.end_at))
  AND date(from_unixtime(b1.end_at)) > date(from_unixtime(b2.start_at))
WHERE
  b1.rental_id IS NOT NULL
  AND b1.status = 'Booked'
  AND b2.status = 'Booked'
  AND b1.rental_ical_id IS NULL
  AND b2.rental_ical_id IS NULL
  AND b1.provider_id = 6
  AND b1.start_at > ********** #WARNING transform to current timestamp to see future bookings only
  AND b2.start_at > ********** #WARNING transform to current timestamp to see future bookings only
ORDER BY
  b1.team_id,
  b1.rental_id,
  b1.start_at
  LIMIT 1000;