SELECT name,
       (P.`2006` + P.`N2006`) AS '2006',
       (P.`2007` + P.`N2007`) AS '2007',
       (P.`2008` + P.`N2008`) AS '2008',
       (P.`2009` + P.`N2009`) AS '2009',
       (P.`2010` + P.`N2010`) AS '2010',
       (P.`2011` + P.`N2011`) AS '2011',
       (P.`2012` + P.`N2012`) AS '2012',
       (P.`2013` + P.`N2013`) AS '2013',
       (P.`2014` + P.`N2014`) AS '2014',
       (P.`2015` + P.`N2015`) AS '2015',
       (P.`2016` + P.`N2016`) AS '2016',
       (P.`2017` + P.`N2017`) AS '2017',
       (P.`2018` + P.`N2018`) AS '2018',
       (P.`2019` + P.`N2019`) AS '2019',
       (P.`2020` + P.`N2020`) AS '2020',
       (P.`2021` + P.`N2021`) AS '2021',
       (P.`2022` + P.`N2022`) AS '2022',
       (P.`2023` + P.`N2023`) AS '2023',
       (P.`2024` + P.`N2024`) AS '2024',
       (P.`2025` + P.`N2025`) AS '2025'
FROM
  ( SELECT name,
           SUM(CASE
                   WHEN G.`start_year`='2006' THEN G.`nights`
                   ELSE 0
               END) AS '2006',
           SUM(CASE
                   WHEN G.`start_year`='2007' THEN G.`nights`
                   ELSE 0
               END) AS '2007',
           SUM(CASE
                   WHEN G.`start_year`='2008' THEN G.`nights`
                   ELSE 0
               END) AS '2008',
           SUM(CASE
                   WHEN G.`start_year`='2009' THEN G.`nights`
                   ELSE 0
               END) AS '2009',
           SUM(CASE
                   WHEN G.`start_year`='2010' THEN G.`nights`
                   ELSE 0
               END) AS '2010',
           SUM(CASE
                   WHEN G.`start_year`='2011' THEN G.`nights`
                   ELSE 0
               END) AS '2011',
           SUM(CASE
                   WHEN G.`start_year`='2012' THEN G.`nights`
                   ELSE 0
               END) AS '2012',
           SUM(CASE
                   WHEN G.`start_year`='2013' THEN G.`nights`
                   ELSE 0
               END) AS '2013',
           SUM(CASE
                   WHEN G.`start_year`='2014' THEN G.`nights`
                   ELSE 0
               END) AS '2014',
           SUM(CASE
                   WHEN G.`start_year`='2015' THEN G.`nights`
                   ELSE 0
               END) AS '2015',
           SUM(CASE
                   WHEN G.`start_year`='2016' THEN G.`nights`
                   ELSE 0
               END) AS '2016',
           SUM(CASE
                   WHEN G.`start_year`='2017' THEN G.`nights`
                   ELSE 0
               END) AS '2017',
           SUM(CASE
                   WHEN G.`start_year`='2018' THEN G.`nights`
                   ELSE 0
               END) AS '2018',
           SUM(CASE
                   WHEN G.`start_year`='2019' THEN G.`nights`
                   ELSE 0
               END) AS '2019',
           SUM(CASE
                   WHEN G.`start_year`='2020' THEN G.`nights`
                   ELSE 0
               END) AS '2020',
           SUM(CASE
                   WHEN G.`start_year`='2021' THEN G.`nights`
                   ELSE 0
               END) AS '2021',
           SUM(CASE
                   WHEN G.`start_year`='2022' THEN G.`nights`
                   ELSE 0
               END) AS '2022',
           SUM(CASE
                   WHEN G.`start_year`='2023' THEN G.`nights`
                   ELSE 0
               END) AS '2023',
           SUM(CASE
                   WHEN G.`start_year`='2024' THEN G.`nights`
                   ELSE 0
               END) AS '2024',
           SUM(CASE
                   WHEN G.`start_year`='2025' THEN G.`nights`
                   ELSE 0
               END) AS '2025',
           SUM(CASE
                   WHEN G.`end_year`='2006' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2006',
           SUM(CASE
                   WHEN G.`end_year`='2007' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2007',
           SUM(CASE
                   WHEN G.`end_year`='2008' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2008',
           SUM(CASE
                   WHEN G.`end_year`='2009' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2009',
           SUM(CASE
                   WHEN G.`end_year`='2010' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2010',
           SUM(CASE
                   WHEN G.`end_year`='2011' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2011',
           SUM(CASE
                   WHEN G.`end_year`='2012' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2012',
           SUM(CASE
                   WHEN G.`end_year`='2013' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2013',
           SUM(CASE
                   WHEN G.`end_year`='2014' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2014',
           SUM(CASE
                   WHEN G.`end_year`='2015' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2015',
           SUM(CASE
                   WHEN G.`end_year`='2016' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2016',
           SUM(CASE
                   WHEN G.`end_year`='2017' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2017',
           SUM(CASE
                   WHEN G.`end_year`='2018' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2018',
           SUM(CASE
                   WHEN G.`end_year`='2019' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2019',
           SUM(CASE
                   WHEN G.`end_year`='2020' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2020',
           SUM(CASE
                   WHEN G.`end_year`='2021' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2021',
           SUM(CASE
                   WHEN G.`end_year`='2022' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2022',
           SUM(CASE
                   WHEN G.`end_year`='2023' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2023',
           SUM(CASE
                   WHEN G.`end_year`='2024' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2024',
           SUM(CASE
                   WHEN G.`end_year`='2025' THEN G.`next_year`
                   ELSE 0
               END) AS 'N2025'
   FROM
     ( SELECT rental.name AS name,
              start_at AS start_year,
              SUM(nights) AS nights,
              end_at AS end_year,
              SUM(next_year) AS next_year
      FROM
        ( SELECT rental_id,
                 start_at,
                 (CASE
                      WHEN base.nights_until_year_end < base.nights THEN base.nights_until_year_end
                      ELSE base.nights
                  END) AS nights,
                 (CASE
                      WHEN base.nights_until_year_end < base.nights THEN base.nights - base.nights_until_year_end
                      ELSE 0
                  END) AS next_year,
                 end_at
         FROM
           ( SELECT rental_id,
                    date_format(from_unixtime(start_at), '%Y') AS start_at,
                    datediff(from_unixtime(end_at), from_unixtime(start_at)) AS nights,
                    to_days(concat(YEAR(from_unixtime(start_at)), '-12-31')) - to_days(from_unixtime(start_at)) AS nights_until_year_end,
                    date_format(from_unixtime(end_at), '%Y') AS end_at
            FROM booking
            WHERE team_id = ?
              AND canceled_at = '0'
              AND unavailable = '0' ) AS base
         ORDER BY next_year DESC ) AS r
      RIGHT JOIN rental ON r.rental_id = rental.id
      WHERE r.start_at IS NOT NULL
      GROUP BY name,
               start_year,
               end_year ) AS G
   GROUP BY name ) AS P
GROUP BY name