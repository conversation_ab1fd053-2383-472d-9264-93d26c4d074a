<?php

namespace Database\Factories;

use App\Models\Checklist;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class ChecklistItemFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'checklist_id' => Checklist::factory(),
            'order' => rand(1, 10),
            'title' => $this->faker->title(),
            'description' => $this->faker->text(),
            'picture_required' => false,
            'allow_multiple_pictures' => true,
        ];
    }
}
