<?php

namespace Database\Factories;

use App\Models\Currency;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class RentalFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'id' => rand(1, 100000), //The id comes from the provider, is not auto-increment
            'team_id' => Team::factory(),
            'name' => $this->faker->streetName(),
            'rental_type' => $this->faker->word(),
            'city' => $this->faker->city(),
            'country_code' => $this->faker->countryCode(),
            'checkin_time' => 16,
            'checkout_time' => 10,
            'currency' => strtoupper(Currency::query()->inRandomOrder()->limit(1)->first()->code_alphabetic),
        ];
    }

    public function onTeam(int $team_id)
    {
        return $this->state(fn (array $attributes) => ['team_id' => $team_id]);
    }

    public function checkInTime(string $checkin_time)
    {
        return $this->state(fn (array $attributes) => ['checkin_time' => $checkin_time]);
    }

    public function checkOutTime(string $checkout_time)
    {
        return $this->state(fn (array $attributes) => ['checkout_time' => $checkout_time]);
    }

    public function withDamageDeposit()
    {
        return $this->state(fn (array $attributes) => ['damage_deposit' => $this->faker->randomFloat(2, 0, 200)]);
    }
}
