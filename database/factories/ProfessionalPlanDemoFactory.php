<?php

namespace Database\Factories;

use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProfessionalPlanDemo>
 */
class ProfessionalPlanDemoFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'trial_ends_at' => now()->addWeek(), // Let's assume you always want a valid pro plan
        ];
    }

    public function withExpiredTrial()
    {
        return $this->state(fn (array $attributes) => ['trial_ends_at' => now()->subWeek()]);
    }

    public function withTrialEndsAt(Carbon $trial_ends_at)
    {
        return $this->state(fn (array $attributes) => ['trial_ends_at' => $trial_ends_at]);
    }
}
