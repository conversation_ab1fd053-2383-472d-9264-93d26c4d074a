<?php

namespace Database\Factories;

use App\Enum\TeamRolesEnum;
use App\Models\Booking;
use App\Models\Rental;
use App\Models\Task;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class TaskFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'booking_id' => Booking::factory(),
            'rental_id' => Rental::factory(),
            'assignee_id' => User::factory(),
            //'supervisor_id' => User::factory(), We don't want to force assigning a supervisor. This implies making the test create more users, and may not be needed.
            'title' => $this->faker->title(),
            'description' => $this->faker->text(),
            'can_start' => $this->faker->randomElement(Task::START_OPTIONS),
            'due_date' => $this->faker->randomElement(Task::END_OPTIONS),
            'priority' => rand(1, 5),
            'start_from' => now(),
            'finish_before' => now()->addDay(),
            'start_at' => now(),
            'end_at' => now()->addDay(),
            'has_timer' => false,
            'max_time' => $this->faker->randomNumber(),
            'remove_on_cancellation' => true,
            'manually_modified' => false,
        ];
    }

    public function onTeam(int $team_id)
    {
        return $this->state(fn (array $attributes) => ['team_id' => $team_id]);
    }

    public function onRental(int $rental_id)
    {
        return $this->state(fn (array $attributes) => ['rental_id' => $rental_id]);
    }

    public function onBooking(int $booking_id)
    {
        return $this->state(fn (array $attributes) => ['booking_id' => $booking_id]);
    }

    public function forScheduledTask(int $schTaskId): TaskFactory
    {
        return $this->state(fn (array $attributes) => ['scheduled_task_id' => $schTaskId]);
    }

    public function forRecurrentTask(int $rTaskId): TaskFactory
    {
        return $this->state(fn (array $attributes) => ['recurrent_task_id' => $rTaskId]);
    }

    public function setDate(Carbon $date): TaskFactory
    {
        return $this
            ->state(fn (array $attributes) => ['start_from' => $date])
            ->state(fn (array $attributes) => ['start_at' => $date])
            ->state(fn (array $attributes) => ['finish_before' => $date->addHours(2)])
            ->state(fn (array $attributes) => ['end_at' => $date->addHours(2)]);
    }

    public function withAssignee(?int $assignee_id)
    {
        return $this->state(fn (array $attributes) => ['assignee_id' => $assignee_id]);
    }

    public function withRole(?TeamRolesEnum $role)
    {
        return $this->state(fn (array $attributes) => ['role' => $role]);
    }

    public function withSupervisor(?int $supervisor_id)
    {
        return $this->state(fn (array $attributes) => ['supervisor_id' => $supervisor_id]);
    }

    public function completed()
    {
        return $this->state(fn (array $attributes) => ['completed_at' => now()->subDay()]);
    }

    public function supervised()
    {
        return $this->state(fn (array $attributes) => ['supervised_at' => now()->subDay()]);
    }
}
