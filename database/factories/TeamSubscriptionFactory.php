<?php

namespace Database\Factories;

use App\DTO\Team\PriceData;
use App\Models\Team;
use App\Support\NinjaCashier;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TeamSubscription>
 */
class TeamSubscriptionFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'type' => 'default',
            'stripe_id' => 'sub_'.$this->faker->bothify('???????##?#?##'),
            // TODO 'stripe_price' => , We should pick a random plan and then get the default prices for it
            'quantity' => 1, // For now, not needed to be updated with the team's rentals. Can be changed if needed.
            'ends_at' => null, // By default, we give it an endless subscription
            'stripe_status' => NinjaCashier::initialize()->getSubscriptionActiveStatus(),
        ];
    }

    // PLANS
    public function onProfessionalMonthly(bool $withFutureDueDate = false)
    {
        return $this->state(fn (array $attributes) => [
            'stripe_price' => PriceData::allPrices()
                ->filter(fn (PriceData $price) => $price->id === 'professional')
                ->first()
                ->getStripePrice(),
        ])->when($withFutureDueDate, fn (self $factory) => $factory->withFutureExpiringSubscription());
        // NOTE: we don't have to do anything else thanks to the defaults of the definition
    }

    public function expiredProfessionalSubscription(bool $isYearly = false)
    {
        return $this->when($isYearly, fn (self $factory) => $factory->onProfessionalYearly(), fn (self $factory) => $factory->onProfessionalMonthly())
            ->withExpiredSubscription()
            ->canceled();
    }

    public function onProfessionalYearly(bool $withFutureDueDate = false)
    {
        return $this->state(fn (array $attributes) => [
            'stripe_price' => PriceData::allPrices()
               ->filter(fn (PriceData $price) => $price->id === 'professional-yearly')
               ->first()
               ->getStripePrice(),
        ])->when($withFutureDueDate, fn (self $factory) => $factory->withFutureExpiringSubscription());
        // NOTE: we don't have to do anything else thanks to the defaults of the definition
    }

    public function onPlan(string $plan)
    {
        return $this->state(fn (array $attributes) => ['stripe_price' => $plan]);
    }

    // PLAN DUE DATE
    public function subscriptionEndingAt(Carbon $ends_at)
    {
        return $this->state(fn (array $attributes) => ['ends_at' => $ends_at]);
    }

    public function withFutureExpiringSubscription()
    {
        return $this->state(fn (array $attributes) => ['ends_at' => now()->addWeek()]);
    }

    public function withExpiredSubscription()
    {
        return $this->state(fn (array $attributes) => ['ends_at' => now()->subWeek()]);
    }

    // STATUS
    public function status(string $status)
    {
        return $this->state(fn (array $attributes) => ['stripe_status' => $status]);
    }

    public function active()
    {
        return $this->state(fn (array $attributes) => ['stripe_status' => NinjaCashier::initialize()->getSubscriptionActiveStatus()]);
    }

    public function canceled()
    {
        return $this->state(fn (array $attributes) => ['stripe_status' => NinjaCashier::initialize()->getSubscriptionCanceledStatus()]);
    }
}
