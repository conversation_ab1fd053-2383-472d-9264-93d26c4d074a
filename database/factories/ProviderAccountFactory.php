<?php

namespace Database\Factories;

use App\Enum\TeamStatusEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProviderAccountFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'account_id' => $this->faker->uuid(),
            'business_name' => $this->faker->name(),
            'name' => $this->faker->email(),
            'status' => TeamStatusEnum::enabled,
            'api_key' => $this->faker->randomKey(),

            'default_communication_locale' => array_rand(config('ninja.locales')),
        ];
    }

    public function setProviderId(int $providerId): self
    {
        return $this->state(fn (array $attributes) => ['provider_id' => $providerId]);
    }
}
