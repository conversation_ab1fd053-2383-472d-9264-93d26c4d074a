<?php

namespace Database\Factories;

use App\Models\Booking;
use App\Models\Team;
use Awobaz\Compoships\Database\Eloquent\Factories\ComposhipsFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BookingTax>
 */
class BookingTaxFactory extends Factory
{
    use ComposhipsFactory;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'booking_id' => Booking::factory(),
            'tax_id' => null, // Only BS bookingTaxes have a tax_id
            'amount' => $this->faker->randomFloat(2, 0, 50),
            'name' => $this->faker->words(2, true),
            'percentage' => $this->faker->randomFloat(2, 0, 100),
            'tax_included_in_price' => $this->faker->boolean(),
            'taxable_id' => $this->faker->randomNumber(5),
            'taxable_type' => 'Rental',
            'created_at' => now(),
            'updated_at' => now(),
            'canceled_at' => null,
        ];
    }
}
