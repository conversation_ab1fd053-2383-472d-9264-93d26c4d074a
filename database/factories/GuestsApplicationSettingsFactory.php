<?php

namespace Database\Factories;

use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class GuestsApplicationSettingsFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'sms' => 0,
        ];
    }

    public function withActiveSms()
    {
        return $this->state(fn (array $attributes) => ['sms' => 1]);
    }
}
