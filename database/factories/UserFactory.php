<?php

namespace Database\Factories;

use App\Enum\TeamRolesEnum;
use App\Models\TeamUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        // NOTE: Do not add here current_team_id, otherwise, when creating a team, you will create an infinite loop
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'locale' => array_rand(config('ninja.locales')),
            'password' => bcrypt('secret'),
            'remember_token' => Str::random(10),
            'ninja_role' => TeamRolesEnum::OWNER, //so default users created by the team factory are already owners
        ];
    }

    /** Set Role for non-owner users */
    public function setRole(TeamRolesEnum $role)
    {
        return $this->state(fn (array $attributes) => ['ninja_role' => $role]);
    }

    public function onTeam(int $team_id)
    {
        return $this->state(fn (array $attributes) => ['current_team_id' => $team_id]);
    }

    public function configure()
    {
        return $this->afterCreating(function (User $user) {
            // Only in the event that we are not creating a team owner and a team, where we still don't have team_id, do:
            if ($user->current_team_id) {
                // Create TeamUser record
                TeamUser::create(['team_id' => $user->current_team_id, 'user_id' => $user->id, 'role' => $user->ninja_role]);
                // Set TeamUsersPermission
                $user->setDefaultPermissionsForRole();
            }
        });
    }
}
