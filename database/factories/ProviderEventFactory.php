<?php

namespace Database\Factories;

use App\Models\Booking;
use App\Models\ProviderEvent;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProviderEvent>
 */
class ProviderEventFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'rental_id' => Rental::factory(),
            'type' => collect(ProviderEvent::TYPES)->random(1)->first(),
            'booking_id' => Booking::factory(),
        ];
    }

    public function onTeam(int $team_id)
    {
        return $this->state(fn (array $attributes) => ['team_id' => $team_id]);
    }

    public function onRental(int $rental_id)
    {
        return $this->state(fn (array $attributes) => ['rental_id' => $rental_id]);
    }

    public function onBooking(int $booking_id)
    {
        return $this->state(fn (array $attributes) => ['booking_id' => $booking_id]);
    }
}
