<?php

namespace App\Casts;

use App\DTO\ChannelManager\TranslationArray;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class TranslationArrayCaster implements CastsAttributes
{
    public function get(Model $model, string $key, mixed $value, array $attributes): ?TranslationArray
    {
        return is_null($value) ? null : new TranslationArray(json_decode($value, true));
    }

    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (is_null($value)) {
            return null;
        } elseif (is_array($value)) {
            return json_encode($value);
        } elseif (is_string($value)) {
            return $value;
        } elseif ($value instanceof TranslationArray) {
            return json_encode($value->array);
        }

        return null;
    }
}
