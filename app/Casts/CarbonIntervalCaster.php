<?php

namespace App\Casts;

use Carbon\CarbonInterval;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Spatie\LaravelData\Casts\Cast;
use Spatie\LaravelData\Support\DataProperty;

class CarbonIntervalCaster implements CastsAttributes, Cast
{
    // Return a Carbon interval.
    public function get(Model $model, string $key, mixed $value, array $attributes): CarbonInterval
    {
        return CarbonInterval::createFromFormat('H:i:s', $value);
    }

    // Database stores the value as 'H:i:s'. We can received either as a Carbon interval or H:i.
    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if ($value instanceof CarbonInterval) {
            return $value->format('%H:%I:%S');
        }
        if (is_string($value)) {
            if (Str::of($value)->explode(':')->count() == 2) {
                $value = $value.':00';
            }

            return $value;
        }

        return $value;
    }

    /** I need to duplicate this from the get method as this must be used by Laravel Data to cast */
    public function cast(DataProperty $property, mixed $value, array $context): mixed
    {
        if (Str::of($value)->explode(':')->count() == 2) {
            $value = $value.':00';
        }

        return CarbonInterval::createFromFormat('H:i:s', $value);
    }
}
