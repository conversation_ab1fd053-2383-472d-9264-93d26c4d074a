<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\Boolean;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Email;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class AdminUser extends Resource
{
    public static $model = \App\Models\AdminUser::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('Name')->required(),
            Email::make('Email')->required()->sortable(),
            Boolean::make('Has Nova Access')->default(false)->filterable(),
            Boolean::make('Is Support Team')->default(false)->filterable(),
            Boolean::make('Is Developer')->default(false),
            DateTime::make('updated_at')->readonly()->hideFromIndex(),
            DateTime::make('created_at')->readonly()->hideFromIndex(),
            DateTime::make('deleted_at')->readonly()->hideFromIndex(),

            HasMany::make('Companies Managed', 'companiesManaged', Company::class),
            HasMany::make('Companies Demoed', 'companiesDemoed', Company::class),
            HasMany::make('Companies Onboarded', 'companiesOnboarded', Company::class),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
