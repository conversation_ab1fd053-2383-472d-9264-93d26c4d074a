<?php

namespace App\Nova;

use App\Models\TeamSettings;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class TeamSetting extends Resource
{
    public static $displayInNavigation = false;

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = TeamSettings::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'team_id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'team_id',
    ];

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        $options = collect(config('white_labels'))->mapWithKeys(function ($value, $key) {
            $key = $key === 'null' ? null : $key;

            return [$key => $value['app_name']];
        });

        return [
            Number::make('SMS price')->help('Price in cents. Leave empty for default: '.TeamSettings::DEFAULT_SMS_PRICE.' cents'),
            Number::make('Scan price')->help('Price in cents. Leave empty for default: '.TeamSettings::DEFAULT_SCAN_PRICE.' cents'),
            Number::make('Upscale price')->help('Price in cents. Leave empty for default: '.TeamSettings::DEFAULT_UPSCALE_PRICE.' cents'),
            // We only have one AI stripe product, the content price is a factor of the AI price. If, for any reason, we need an AI price of 0, we do need to rethink this.
            Number::make('AI price')->help('Price in cents. Leave empty for default: '.TeamSettings::DEFAULT_AI_PRICE.' cents')->min(1),
            Number::make('Content AI price')->help('Price in cents. Leave empty for default: '.TeamSettings::DEFAULT_CONTENT_AI_PRICE.' cents')->min(1),
            Number::make('Booking Engine Commission Percentage')
                ->max(99.999)
                ->min(0)
                ->step(0.001)
                ->help('% to be applied to direct bookings. We will store it with 3 decimals. Leave empty for default: '.TeamSettings::DEFAULT_BOOKING_ENGINE_COMMISSION_PERCENTAGE.'%'),
            Number::make('Upsales Commission Percentage')
                  ->max(99.999)
                  ->min(0)
                  ->step(0.001)
                  ->help('% to be applied to upsells. We will store it with 3 decimals. Leave empty for default: '.TeamSettings::DEFAULT_UPSALE_COMMISSION_PERCENTAGE.'%'),
            Select::make('White label', 'white_label_id')->options($options),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
