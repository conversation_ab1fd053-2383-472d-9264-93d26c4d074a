<?php

namespace App\Nova;

use Exception;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Date;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

/**
 * @mixin \App\Models\Booking
 */
class NovaRental extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Rental::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = ['id', 'name', 'public_name'];

    public static function label()
    {
        return 'Rentals';
    }

    public static function perPageOptions()
    {
        return [100, 200, 400];
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @throws Exception
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Text::make('Name'),

            Text::make('Public Name'),

            ID::make()
                ->sortable()
                ->readonly(),

            BelongsTo::make('Team')
                ->searchable()
                ->sortable()
                ->hideWhenUpdating()
                ->hideWhenCreating(),

            Text::make('External id', 'external_id')
                ->sortable()
                ->readonly(),

            Number::make('Provider', 'provider_id'),

            Boolean::make('Channel Manager', 'cm_active'),

            Text::make('Currency', fn () => "$this->currency ($this->ru_currency)"),

            Date::make('Delete date', 'deleted_at'),
        ];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new Filters\RentalFilter(),
        ];
    }
}
