<?php

namespace App\Nova\Actions;

use App\Actions\Segment\SyncUserToSegmentAction;
use Illuminate\Support\Collection;
use Lara<PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class SyncUserToSegmentNAction extends Action
{
    public $name = 'Sync user to Segment';

    /**
     * Perform the action on the given models.
     */
    public function handle(ActionFields $fields, Collection $models): void
    {
        foreach ($models as $model) {
            SyncUserToSegmentAction::dispatch($model);
        }
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        return [];
    }
}
