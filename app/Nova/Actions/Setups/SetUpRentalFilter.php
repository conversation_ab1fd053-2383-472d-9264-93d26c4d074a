<?php

/** @noinspection ALL */

namespace App\Nova\Actions\Setups;

use Illuminate\Support\Facades\Cache;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Boolean;

class SetUpRentalFilter extends Action
{
    public function __construct(
        public bool $basicInfo = false,
        public bool $content = false,
        public bool $amenities = false,
        public bool $availability = false,
        public bool $rates = false,
        public bool $fees = false,
        public bool $pictures = false,
        public bool $location = false,
        public bool $houseRules = false,
        public bool $cmEnabled = false,
    ) {
    }

    public static function fromFields(ActionFields $fields): self
    {
        Cache::set('setup_general.basic', $fields->basic, 3600);
        Cache::set('setup_general.content', $fields->content, 3600);
        Cache::set('setup_general.amenities', $fields->amenities, 3600);
        Cache::set('setup_general.availability', $fields->availability, 3600);
        Cache::set('setup_general.rates', $fields->rates, 3600);
        Cache::set('setup_general.fees', $fields->fees, 3600);
        Cache::set('setup_general.pictures', $fields->pictures, 3600);
        Cache::set('setup_general.location', $fields->location, 3600);
        Cache::set('setup_general.houseRules', $fields->houseRules, 3600);

        return new self(
            basicInfo: $fields->basic,
            content: $fields->content,
            amenities: $fields->amenities,
            availability: $fields->availability,
            rates: $fields->rates,
            fees: $fields->fees,
            pictures: $fields->pictures,
            location: $fields->location,
            houseRules: $fields->houseRules,
        );
    }

    public static function fromFieldsForRU(ActionFields $fields): self
    {
        Cache::set('setup_ru.basic', $fields->basic, 3600);
        Cache::set('setup_ru.content', $fields->content, 3600);
        Cache::set('setup_ru.rates', $fields->rates, 3600);
        Cache::set('setup_ru.fees', $fields->fees, 3600);
        Cache::set('setup_ru.location', $fields->location, 3600);
        Cache::set('setup_ru.houseRules', $fields->houseRules, 3600);
        Cache::set('setup_ru.cmEnabled', $fields->cmEnabled, 3600);

        return new self(
            basicInfo: $fields->basic,
            content: $fields->content,
            rates: $fields->rates,
            fees: $fields->fees,
            location: $fields->location,
            houseRules: $fields->houseRules,
            cmEnabled: $fields->cmEnabled,
        );
    }

    public static function all(): self
    {
        return new self(true, true, true, true, true, true, true, true, true);
    }

    public static function getNovaFields(): array
    {
        return [
            Boolean::make('Basic info', 'basic')
                ->default(Cache::get('setup_general.basic') ?? false),
            Boolean::make('Content', 'content')
                ->default(Cache::get('setup_general.content') ?? false),
            Boolean::make('Amenities and rooms', 'amenities')
                ->default(Cache::get('setup_general.amenities') ?? false),
            Boolean::make('Availability', 'availability')
                ->default(Cache::get('setup_general.availability') ?? false),
            Boolean::make('Rates', 'rates')
                ->default(Cache::get('setup_general.rates') ?? false),
            Boolean::make('Fees', 'fees')
                ->default(Cache::get('setup_general.fees') ?? false),
            Boolean::make('Pictures', 'pictures')
                ->default(Cache::get('setup_general.pictures') ?? false),
            Boolean::make('Location', 'location')
                ->default(Cache::get('setup_general.location') ?? false),
            Boolean::make('House rules', 'houseRules')
                ->default(Cache::get('setup_general.houseRules') ?? false),
        ];
    }

    public static function getNovaFieldsForRU(): array
    {
        return [
            Boolean::make('Basic info', 'basic')
                ->default(Cache::get('setup_ru.basic') ?? false),
            Boolean::make('Content', 'content')
                ->default(Cache::get('setup_ru.basic') ?? false),
            Boolean::make('Rates', 'rates')
                ->default(Cache::get('setup_ru.basic') ?? false),
            Boolean::make('Fees', 'fees')
                ->default(Cache::get('setup_ru.basic') ?? false),
            Boolean::make('Location', 'location')
                ->default(Cache::get('setup_ru.basic') ?? false),
            Boolean::make('House rules', 'houseRules')
                ->default(Cache::get('setup_ru.basic') ?? false),

            Boolean::make('Set CM enabled', 'cmEnabled')
                ->default(Cache::get('setup_ru.cmEnabled') ?? false),
        ];
    }
}
