<?php

namespace App\Nova\Actions;

use App\Actions\ChannelManager\Fetchers\FetchAllBookingsFromRentalsUnitedAction;
use App\Actions\Sync\SyncTeamAction;
use App\Actions\Sync\SyncTeamForceAction;
use App\Actions\Sync\SyncTeamFullAction;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\DataProviders\Providers\NinjaProvider;
use App\Models\Team;
use Exception;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Psr\SimpleCache\InvalidArgumentException;

class SyncTeamNAction extends Action
{
    public $name = 'Synchronize team';

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     *
     * @throws Exception
     * @throws InvalidArgumentException
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        if ($models->count() != 1) {
            abort(403, 'Cannot refresh oauth tokens to more than one team');
        }
        /** @var Team $team */
        $team = $models->first();

        /** @var string $type */
        /** @noinspection PhpUndefinedFieldInspection */
        $type = $fields->type;

        /** @noinspection PhpUndefinedFieldInspection */
        $providerId = $fields->provider;

        $this->launchSync($team, $type, $providerId);
        $this->markAsFinished($team);

        return Action::message("A Sync of type $type has been issued for team $team->name ($team->id)");
    }

    /**
     * @param  Team  $model
     */
    private function launchSync(Team $team, string $type, int $providerId): void
    {
        switch ($type) {
            case 'normal':
                SyncTeamAction::dispatch($team, $providerId)->onQueue('priority-sync');
                break;
            case 'full':
                SyncTeamFullAction::dispatch($team, $providerId)->onQueue('priority-sync');
                break;
            case 'force':
                SyncTeamForceAction::dispatch($team, $providerId)->onQueue('priority-sync');
                if ($providerId == ChannelManagerProvider::ID) {
                    FetchAllBookingsFromRentalsUnitedAction::dispatch($team);
                }
                break;
        }
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        /** @var Team $team */
        $team = $request->findModel();
        $providers = $team->getAllProviders()->filter(fn (NinjaProvider $p) => $p->id() > 0);
        $options = $providers->mapWithKeys(fn (NinjaProvider $p) => [$p->id() => $p->fullName()]);

        return [
            Select::make('Type')
                ->options([
                    'normal' => 'Normal: only most important endpoints, since last sync of each endpoint',
                    'full' => 'Full: all endpoints, since last sync of each endpoint',
                    'force' => 'Force: all endpoints, since beginning',
                ])
                ->displayUsingLabels()
                ->rules(['required']),

            Select::make('Provider')
                ->options($options)
                ->displayUsingLabels()
                ->default($options->keys()->first())
                ->rules(['required']),
        ];
    }
}
