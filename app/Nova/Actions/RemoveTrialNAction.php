<?php

namespace App\Nova\Actions;

use App\Enum\TeamStatusEnum;
use App\Models\Team;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class RemoveTrialNAction extends Action
{
    public $name = 'Remove trial';

    /**
     * Perform the action on the given models.
     */
    public function handle(ActionFields $fields, Collection $models): mixed
    {
        if ($fields->sure == 'no') {
            return Action::danger('Not updated');
        }

        $notify_stripe = $fields->stripe;
        foreach ($models as $team) {
            /* @var Team $team */
            $team->trial_ends_at = null;
            if (! app()->environment('local') && $team->subscribed() && $team->config()->billedThroughRentalNinja() && $notify_stripe) {
                $team->subscription()->skipTrial();
            } else {
                if ($team->config()->billedThroughRentalNinja()) {
                    $team->status = $team->current_rn_plan === null ? TeamStatusEnum::disabled : TeamStatusEnum::enabled;
                } else {
                    $team->status = TeamStatusEnum::disabled; // The provider must then say via the API which should be the status
                }
            }
            $team->save();
        }

        return Action::message('Trial Ends At set on NULL');
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Select::make('Sure')
                ->options([
                    'yes' => 'Yes, make NULL',
                    'no' => 'No',
                ])
                ->rules(['required']),
            Boolean::make("Remove trial from Stripe's subscription?", 'stripe')
                ->rules(['required']),
        ];
    }
}
