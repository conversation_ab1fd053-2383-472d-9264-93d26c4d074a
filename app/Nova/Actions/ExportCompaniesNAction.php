<?php

namespace App\Nova\Actions;

use App\Actions\Crm\GetCompaniesExcelExportUrl;
use App\Notifications\ExcelExportNotification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\NovaRequest;

class ExportCompaniesNAction extends Action
{
    public $name = 'Export Companies';

    public static $chunkCount = 2000;

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $user = Auth::user();
        $url = GetCompaniesExcelExportUrl::run($user->team, $models);
        $user->notify(new ExcelExportNotification($url));

        return Action::message('Sending export via email. Check your inbox.');
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        return [];
    }
}
