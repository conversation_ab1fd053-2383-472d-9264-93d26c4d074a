<?php

namespace App\Nova\Actions;

use App\Models\ProviderAccount;
use Illuminate\Http\UploadedFile;
use Illuminate\Mail\Message;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\File;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class WriteTicketToRentalsUnitedNAction extends Action
{
    public $name = 'Write ticket to Rentals United';

    /** @noinspection PhpUndefinedFieldInspection */
    public function handle(ActionFields $fields, Collection $pAccount): ActionResponse
    {
        if ($pAccount->count() != 1) {
            return Action::danger('Please select only one account');
        }

        $providerAccount = $pAccount->first();
        $from = $fields->from;
        $subject = $fields->subject;
        $body = $fields->body;
        /** @var UploadedFile $attachment */
        $attachment = $fields->attachment;

        if (! str_contains($subject, $providerAccount->account_id)) {
            return Action::danger('Subject must contain the account id');
        }

        Mail::send([], [], function (Message $message) use ($from, $subject, $body, $attachment) {
            $message
                ->from($from, Str::of($from)->before('@')->camel()) // Set the "from" address
                ->to('<EMAIL> ')          // Set the "to" address
                ->subject($subject)               // Set the subject
                ->text($body); // Set the body (plain text)
            if ($attachment) {
                $message->attachFromPath($attachment->getRealPath(), $attachment->getClientOriginalName());
            }
        });

        return Action::message('Ticket sent');
    }

    public function fields(NovaRequest $request): array
    {
        /** @var ProviderAccount $providerAccount */
        $providerAccount = $request->findModel();
        $user = auth()->user();
        $subject = "WL - $providerAccount->account_id";

        return [
            Text::make('From')->default($user->email),
            Text::make('Subject')->default($subject),
            Textarea::make('Body')->default("Hey, \nThis user\n\nBest regards,\n$user->name"),
            File::make('Attachment'),
        ];
    }
}
