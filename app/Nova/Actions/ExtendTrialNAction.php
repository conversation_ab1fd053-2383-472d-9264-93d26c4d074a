<?php

namespace App\Nova\Actions;

use App\Enum\TeamStatusEnum;
use App\Models\Team;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\DateTime;
use Laravel\Nova\Http\Requests\NovaRequest;

class ExtendTrialNAction extends Action
{
    public $name = 'Extend trial';

    /**
     * Perform the action on the given models.
     */
    public function handle(ActionFields $fields, Collection $models): mixed
    {
        $trial = Carbon::parse($fields->trial);
        $notify_stripe = $fields->stripe;
        foreach ($models as $team) {
            /* @var Team $team */
            $team->trial_ends_at = $trial;
            if (! app()->environment('local') && $team->subscribed() && $team->config()->billedThroughRentalNinja() && $notify_stripe) {
                $team->subscription()->extendTrial($trial);
            } elseif (! $team->config()->billedThroughRentalNinja() && $trial->isFuture()) {
                $team->status = TeamStatusEnum::trialing;
                $team->save();
            }

            $team->save();
        }

        return Action::message('Trial Updated');
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            DateTime::make('Trial')
                ->nullable(false)
                ->rules(['required']),
            Boolean::make("Add trial to Stripe's subscription?", 'stripe')
                ->rules(['required']),
        ];
    }
}
