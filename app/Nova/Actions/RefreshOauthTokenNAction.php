<?php

namespace App\Nova\Actions;

use App\DataProviders\ProviderApi\ProviderApi;
use App\DataProviders\Providers\Contracts\NinjaProviderHasOauth;
use App\DataProviders\Providers\NinjaProvider;
use App\Models\Team;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class RefreshOauthTokenNAction extends Action
{
    public $name = 'Refresh oauth tokens';

    /**
     * Perform the action on the given models.
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        if ($models->count() != 1) {
            abort(403, 'Cannot refresh oauth tokens to more than one team');
        }
        /** @var Team $team */
        $team = $models->first();
        /** @noinspection PhpUndefinedFieldInspection */
        $providerId = $fields->provider;
        $api = ProviderApi::from($providerId);
        $account = $team->getProviderAccount($providerId);
        if ($api instanceof NinjaProviderHasOauth) {
            $status = $api->refreshOauthToken($account);

            if ($status) {
                return Action::message('Token refreshed');
            } else {
                return Action::danger('Token could not be refreshed');
            }
        }

        return Action::danger('This provider does not implement oauth');
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        /** @var Team $team */
        $team = $request->findModel();
        $providers = $team->getAllProviders()->filter(fn (NinjaProvider $p) => $p->getProviderApi() instanceof NinjaProviderHasOauth);
        $options = $providers->mapWithKeys(fn (NinjaProvider $p) => [$p->id() => $p->fullName()]);

        return [
            Select::make('Provider')
                ->options($options)
                ->displayUsingLabels()
                ->rules(['required'])
                ->default($options->keys()->first()),
        ];
    }
}
