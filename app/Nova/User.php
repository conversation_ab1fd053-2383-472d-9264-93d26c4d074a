<?php

namespace App\Nova;

use App\Models\Country;
use App\Nova\Actions\RemoveTeamMemberNAction;
use App\Nova\Fields\NinjaAvatar;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Email;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Password;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

/**
 * @mixin \App\Models\User
 */
class User extends Resource
{
    public static $perPageViaRelationship = 10;

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\User::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    public static $with = ['team', 'team.owner'];

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'email',
    ];

    public static $globallySearchable = true; // Only the Resources with this line will be searchable

    public static function label()
    {
        return 'Users';
    }

    public static function perPageOptions()
    {
        return [100, 200, 300];
    }

    /**
     * Get the fields displayed by the resource.
     *
     *
     * @throws Exception
     */
    public function fields(NovaRequest $request): array
    {
        $team = $this->team;

        return [
            ID::make()
                ->sortable(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            NinjaAvatar::make('Photo Url')
                ->readonly()
                ->hideWhenUpdating()
                ->maxWidth(50)
                ->squared()
                ->disableDownload(),

            Text::make('Email')
                ->hideFromIndex()
                ->hideFromDetail()
                ->rules('required', 'email', 'max:254')
                ->creationRules('unique:users,email')
                ->updateRules('unique:users,email,{{resourceId}}'),

            Text::make('Phone', function () {
                if ($this->phone) {
                    if ($this->country_code) {
                        $countryCode = Country::query()
                            ->where('id', $this->country_code)
                            ->first()
                            ->calling_code;
                    }

                    return isset($countryCode) ? '+'.$countryCode.' '.$this->phone : $this->phone;
                } else {
                    return null;
                }
            })->readonly()
                ->hideFromIndex()
                ->hideWhenUpdating()
                ->hideWhenCreating(),

            Select::make('Phone Country Code', 'country_code')
                ->options(Country::query()->select(['id', 'name'])->orderBy('name')->pluck('name', 'id'))
                ->displayUsingLabels()
                ->hideFromIndex()
                ->hideFromDetail()
                ->rules('max:10')
                ->nullable(),

            Text::make('Phone')
                ->hideFromIndex()
                ->hideFromDetail()
                ->rules('max:25')
                ->nullable(),

            Email::make('Email')
                ->sortable()
                ->hideWhenUpdating()
                ->hideWhenCreating(),

            Password::make('Password')
                ->onlyOnForms()
                ->creationRules('required', 'string', 'min:6')
                ->updateRules('nullable', 'string', 'min:6'),

            BelongsTo::make('Team')
                ->searchable()
                ->sortable()
                ->hideWhenUpdating()
                ->hideWhenCreating(),

            Text::make('Role', function () use ($team) {
                if (! isset($team)) {
                    return '—';
                }

                return $this->ninja_role;
            })->sortable()
                ->readonly(),

            Text::make('Team Owner', function () use ($team) {
                if (! isset($team)) {
                    return '—';
                }
                $owner = $team->owner;

                return "<a class='link-default' href='/nova/resources/users/$owner->id'>$owner->email</a>";
            })
                ->asHtml()
                ->readonly()
                ->hideFromIndex(),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [
            (new Actions\ImpersonateUserInFlutterNAction())->onlyOnDetail(),
            new Actions\SyncUserToSegmentNAction(),
            (new RemoveTeamMemberNAction())->onlyOnDetail()->showInline(),
            Action::using('Touch User', function (ActionFields $fields, Collection $models) {
                $models->each(fn (\App\Models\User $user) => $user->touch());
            }),
        ];
    }
}
