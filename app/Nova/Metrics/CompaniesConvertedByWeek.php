<?php

namespace App\Nova\Metrics;

use App\Models\Company;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Trend;

class CompaniesConvertedByWeek extends Trend
{
    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->countByDays($request, Company::class, 'converted_at')
            ->showLatestValue();
    }

    /**
     * Get the ranges available for the metric.
     */
    public function ranges(): array
    {
        return [
            48 => __('1 Year'),
            8 => __('2 Months'),
            4 => __('1 Month'),
        ];
    }

    /**
     * Get the URI key for the metric.
     */
    public function uriKey(): string
    {
        return 'companies-converted-by-week';
    }
}
