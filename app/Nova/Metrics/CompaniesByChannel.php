<?php

namespace App\Nova\Metrics;

use App\Models\ChannelManager;
use App\Models\Company;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Partition;

class CompaniesByChannel extends Partition
{
    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->count($request, Company::class, 'channel_manager_id')
            ->label(function ($value) {
                return ChannelManager::find($value)?->name ?? 'No Channel Manager';
            });
    }

    /**
     * Get the URI key for the metric.
     */
    public function uriKey(): string
    {
        return 'companies-by-channel';
    }
}
