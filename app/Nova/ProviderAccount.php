<?php

namespace App\Nova;

use App\DataProviders\Providers\NinjaProvider;
use App\Models\RentalNinjaTeam;
use App\Nova\Actions\LoginToRentalsUnitedNAction;
use App\Nova\Actions\WriteTicketToRentalsUnitedNAction;
use Illuminate\Support\Carbon;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

/**
 * @mixin \App\Models\ProviderAccount
 */
class ProviderAccount extends Resource
{
    public static $displayInNavigation = true;

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\ProviderAccount::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'account_id',
    ];

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Text::make('Provider',
                fn () => ! is_null($this->provider_id) ? NinjaProvider::provider($this->provider_id)->fullName() : 'EMPTY'
            )->readonly(),
            Text::make('Account Id')->readonly(),
            BelongsTo::make('team')->readonly(),
            Number::make('Rentals')->readonly(),
            DateTime::make('Last sync', 'synced_at')
                ->readonly()
                ->hideWhenUpdating(),
            DateTime::make('Last force sync', 'force_synced_at')
                ->readonly()
                ->hideWhenUpdating(),

            Text::make(
                'Oauth status',
                function () {
                    if (is_null($this->oauth_access_token)) {
                        return 'Not using oauth';
                    } elseif (is_null($this->oauth_refresh_token)) {
                        return 'Invalid refresh';
                    } elseif (Carbon::createFromTimestamp($this->oauth_expires_at)->addHours(3)->isPast()) {
                        return 'Unable to refresh';
                    } elseif (Carbon::createFromTimestamp($this->oauth_expires_at)->isPast()) {
                        return 'Expired';
                    } else {
                        return 'Valid';
                    }
                }
            )
                ->readonly(),

            Text::make('Secondary Id')->hideFromIndex()->readonly(),

            Text::make('API Key')->hideFromIndex()->readonly(),
            Text::make('Encoded API Key', fn () => htmlspecialchars($this->api_key))->hideFromIndex()
                ->readonly()->help('This is useful to use the key in postman'),

            Text::make('Oauth Token', 'oauth_access_token')
                ->onlyOnDetail()
                ->canSee(fn ($request) => $this->hasNovaAccess($request)),

            Text::make('Refresh Token', 'oauth_refresh_token')
                ->onlyOnDetail()
                ->canSee(fn ($request) => $this->hasNovaAccess($request)),

            Text::make('Token Expiration', 'oauth_expires_at')
                ->canSee(fn ($request) => $this->hasNovaAccess($request))
                ->onlyOnDetail()
                ->displayUsing(fn ($value) => empty($value) ? '' : Carbon::createFromTimestamp($value)->toDateTimeLocalString().' - '.Carbon::createFromTimestamp($value)->diffForHumans()),

            Date::make('Created at')->onlyOnDetail(),

        ];
    }

    private function hasNovaAccess($request): bool
    {
        return RentalNinjaTeam::userHasNovaAccess($request->user());
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [
            new Filters\TeamProvider,
        ];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [
            LoginToRentalsUnitedNAction::make()->onlyOnDetail()->showInline(),
            WriteTicketToRentalsUnitedNAction::make()->onlyOnDetail(),
        ];
    }
}
