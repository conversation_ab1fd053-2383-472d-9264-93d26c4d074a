<?php

namespace App\Nova;

use App\Nova\Filters\ApiRouteFilter;
use <PERSON><PERSON>\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Markdown;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

/**
 * @mixin \App\Models\ApiRoute
 */
class ApiRoute extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\ApiRoute::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'url',
    ];

    public static function label()
    {
        return 'API Routes';
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Select::make('Method', 'method')->options([
                'GET' => 'GET',
                'POST' => 'POST',
                'PUT' => 'PUT',
                'PATCH' => 'PATCH',
                'DELETE' => 'DELETE',
            ])->readonly(),
            Text::make('URL', 'url')->readonly(),
            Text::make('Path', 'path')->readonly()
                ->hideFromIndex(),
            Markdown::make('Description', 'description'),
            Code::make('Headers', 'headers')->json()->readonly(),
            Code::make('Query', 'query')->json()->readonly(),
            Code::make('Query Keys', 'query_keys')->json()->readonly(),
            Code::make('Body', 'body')->json()->readonly(),
            Code::make('Body Keys', 'body_keys')->json()->readonly(),
            Code::make('Tests', 'tests')->help('Info here: https://learning.postman.com/docs/writing-scripts/test-scripts/')->language('javascript'),
            DateTime::make('Created At', 'created_at')->readonly(),
            DateTime::make('Updated At', 'updated_at')->readonly()->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [
            new ApiRouteFilter,
        ];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [

        ];
    }
}
