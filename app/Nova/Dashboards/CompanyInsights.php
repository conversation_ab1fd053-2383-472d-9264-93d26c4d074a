<?php

namespace App\Nova\Dashboards;

use App\Nova\Metrics\CompaniesByChannel;
use App\Nova\Metrics\CompaniesConvertedByWeek;
use App\Nova\Metrics\CompaniesPerWeek;
use App\Nova\Metrics\CompanyDemos;
use App\Nova\Metrics\ConvertedCompanies;
use App\Nova\Metrics\NewCompanies;
use Laravel\Nova\Dashboard;

class CompanyInsights extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     */
    public function cards(): array
    {
        return [
            new NewCompanies,
            new CompaniesPerWeek,
            new ConvertedCompanies,
            new CompanyDemos,
            new CompaniesByChannel,
            new CompaniesConvertedByWeek,
            //new GeneratedRevenue,
        ];
    }

    /**
     * Get the URI key for the dashboard.
     */
    public function uriKey(): string
    {
        return 'company-insights';
    }
}
