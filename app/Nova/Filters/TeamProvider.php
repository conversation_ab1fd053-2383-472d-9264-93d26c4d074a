<?php

namespace App\Nova\Filters;

use App\DataProviders\Providers\ChannelManagerProvider;
use App\DataProviders\Providers\NinjaProvider;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class TeamProvider extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  mixed  $value
     */
    public function apply(NovaRequest $request, $query, $value): Builder
    {
        return $query->where('provider_id', $value);
    }

    /**
     * Get the filter's available options.
     */
    public function options(NovaRequest $request): array
    {
        return NinjaProvider::activeProviders()
            ->map(fn ($val) => $val->fullName())
            ->filter(fn ($value, $key) => $key !== ChannelManagerProvider::ID) // We are puting all teams in provider_id = 0
            ->flip()
            ->toArray();
    }
}
