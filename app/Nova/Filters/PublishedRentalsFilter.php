<?php

namespace App\Nova\Filters;

use App\Query\TeamQuery;
use Laravel\Nova\Filters\BooleanFilter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class PublishedRentalsFilter extends BooleanFilter
{
    /**
     * Apply the filter to the given query.
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        if ($value['published_only']) {
            /** @var TeamQuery $query */
            return $query->whereHasPublishedRentals();
        } else {
            return $query;
        }
    }

    /**
     * Get the filter's available options.
     */
    public function options(NovaRequest $request): array
    {
        return [
            'With Published Rentals' => 'published_only',
        ];
    }
}
