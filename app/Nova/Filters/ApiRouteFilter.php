<?php

namespace App\Nova\Filters;

use Illuminate\Database\Eloquent\Builder;
use Lara<PERSON>\Nova\Filters\BooleanFilter;
use Laravel\Nova\Http\Requests\NovaRequest;

class ApiRouteFilter extends BooleanFilter
{
    /**
     * Apply the filter to the given query.
     *
     * @param  mixed  $value
     */
    public function apply(NovaRequest $request, $query, $value): Builder
    {
        return $query
            ->when($value['not-described'], fn (Builder $query) => $query
                ->whereNull('description')
                ->orWhere('description', '=', ''))
            ->when($value['not-tested'], fn (Builder $query) => $query
                ->whereNull('tests')
                ->orWhere('tests', '=', ''));
    }

    /**
     * Get the filter's available options.
     */
    public function options(NovaRequest $request): array
    {
        return [
            'Without Description' => 'not-described',
            'Without Tests' => 'not-tested',
        ];
    }
}
