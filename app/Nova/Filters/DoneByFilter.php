<?php

namespace App\Nova\Filters;

use App\Models\AdminUser;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class DoneByFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public $name = 'Done By';

    /**
     * Apply the filter to the given query.
     *
     * @param  mixed  $value
     */
    public function apply(NovaRequest $request, $query, $value): Builder
    {
        return $query->where('done_by', $value);
    }

    /**
     * Get the filter's available options.
     */
    public function options(NovaRequest $request): array
    {
        return AdminUser::withTrashed()
            ->select(['id', 'name'])
            ->whereIn('id', function ($query) {
                $query->select('done_by')
                    ->from('sales_support_actions')
                    ->whereNotNull('done_by')
                    ->groupBy('done_by');
            })
            ->get()
            ->pluck('id', 'name')
            ->toArray();
    }
}
