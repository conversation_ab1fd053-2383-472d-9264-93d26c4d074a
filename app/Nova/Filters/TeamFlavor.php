<?php

namespace App\Nova\Filters;

use App\Flavors\NinjaFlavor;
use Illuminate\Database\Eloquent\Builder;
use Laravel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class TeamFlavor extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  mixed  $value
     */
    public function apply(NovaRequest $request, $query, $value): Builder
    {
        return $query->where('flavor_id', $value);
    }

    /**
     * Get the filter's available options.
     */
    public function options(NovaRequest $request): array
    {
        return NinjaFlavor::all()
            ->mapWithKeys(fn (NinjaFlavor $val) => [$val->name() => $val::FLAVOR_ID])
            ->toArray();
    }
}
