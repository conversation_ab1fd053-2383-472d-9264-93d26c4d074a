<?php

namespace App\Nova\Filters;

use App\Models\AdminUser;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class NextStepDoneByFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public $name = 'Next Step Done By';

    /**
     * Apply the filter to the given query.
     *
     * @param  mixed  $value
     */
    public function apply(NovaRequest $request, $query, $value): Builder
    {
        return $query->where('next_step_done_by', $value);
    }

    /**
     * Get the filter's available options.
     */
    public function options(NovaRequest $request): array
    {
        return AdminUser::withTrashed()
            ->select(['id', 'name'])
            ->whereIn('id', function ($query) {
                $query->select('next_step_done_by')
                    ->from('sales_support_actions')
                    ->whereNotNull('next_step_done_by')
                    ->groupBy('next_step_done_by');
            })
            ->get()
            ->pluck('id', 'name')
            ->toArray();
    }
}
