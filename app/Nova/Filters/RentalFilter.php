<?php

namespace App\Nova\Filters;

use App\Models\Rental;
use App\Query\RentalQuery;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class RentalFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  mixed  $value
     */
    public function apply(NovaRequest $request, $query, $value): Builder
    {
        /** @var RentalQuery|Rental $query */
        return match ($value) {
            'In channels' => $query->where('cm_active', true)->whereRelation('channelRentals', 'active', true),
            'In Rentals United' => $query->where('cm_active', true)->whereNotNull('external_id')->whereProviderId(6),
            'CM active, not in RU' => $query->where('cm_active', true)->whereNull('external_id'),
            'No provider (no cm)' => $query->whereProviderId(0)->where('cm_active', false),
            'Other providers' => $query->whereNotIn('provider_id', [0, 6]),
        };
    }

    /**
     * Get the filter's available options.
     */
    public function options(NovaRequest $request): array
    {
        return ['In channels', 'In Rentals United', 'CM active, not in RU', 'No provider (no cm)', 'Other providers'];
    }
}
