<?php

namespace App\Mail\GuestNotifications;

use App\Actions\Clients\GetClientLocaleAction;
use App\DTO\Bookings\BookingFeeData;
use App\Models\Booking;
use App\Models\RentalFee;
use App\Models\Team;
use Exception;
use Illuminate\Support\Collection;

class UpsalePurchasedEmail extends NinjaGuestNotificationEmail
{
    /**
     * Create a new message instance.
     */
    public function __construct(
    public Team $team,
    public int $bookingId,
    /** Collection<BookingFeeData> */
    public Collection $bookingFeesData,
    public bool $forcePlain = false,
  ) {
        parent::__construct($forcePlain);
    }

    /**
     * Build the message.
     *
     * @throws Exception
     */
    public function build(): static
    {
        $team = $this->team;
        $booking = Booking::getBookingModel($team, $this->bookingId, ['rental', 'client']);
        $locale = GetClientLocaleAction::run($booking->client);

        $upsalesRentalFees = RentalFee::query()
          ->where('team_id', $team->id)
          ->where('rental_id', $booking->rental_id)
          ->whereIn('fee_id', $this->bookingFeesData->pluck('feeId'))
          ->get();

        $upsales = $this->bookingFeesData->map(function (BookingFeeData $feeData) use ($upsalesRentalFees, $locale) {
            $rentalFee = $upsalesRentalFees->firstWhere('fee_id', $feeData->feeId);
            $quantityUnit = $feeData->rateKind->getQuantityUnit();
            $quantity = empty($quantityUnit) ? $feeData->timesBooked : $feeData->timesBooked.' '.'('.$quantityUnit.')';

            return [
                'name' => $rentalFee->getName($locale),
                'description' => $rentalFee->getDescription($locale),
                'unitPrice' => $feeData->price,
                'quantity' => $quantity, // String with the quantity and unit
                'totalPrice' => $feeData->feeTotalPrice(),
                'rateKind' => $feeData->rateKind,
            ];
        });

        $rental = $booking->rental;
        $propertyName = ! empty($rental->city) ? $rental->city : $rental->name;
        $sub = __('messages.upsell_purchased_email.subject');
        $client = $booking->client;
        $photo = ninja_str_contains($team->photo_url, 'avatar') ? null : $team->imgixPhoto();

        $totalPaid = $upsales->sum('totalPrice');
        $currency = $booking->currency;
        $contactEmail = $this->getReplyToAddress($team, $rental, $booking);
        $contactPhone = $rental->contact_phone ?: $team->owner->phone;
        $teamName = ucwords(strtolower($team->name));

        return $this
          ->subject($sub)
          ->from($this->getMailFromAddress($team, $rental, $booking), $teamName)
          ->replyTo($contactEmail, $teamName)
          ->view(! $this->forcePlain ? 'mail.upsales.upsale_purchased_confirmed' : null)
          ->text('mail.upsales.upsale_purchased_confirmed_plain')
          ->with([
              'sub' => $sub,
              'team' => $team,
              'booking' => $booking,
              'clientName' => $client->getClientNameForEmail(),
              'photo' => $photo,
              'propertyName' => $propertyName,
              'bookingReference' => $booking->reference,
              'upsales' => $upsales,
              'totalPaid' => $totalPaid,
              'contactEmail' => $contactEmail,
              'contactPhone' => $contactPhone,
              'teamName' => $teamName,
              'currency' => $currency,
          ]);
    }
}
