<?php

namespace App\Mail\GuestNotifications;

use App\Models\Booking;
use App\Models\PreCheckInForm;
use App\Models\Team;

class PreCheckInFormCompletedEmail extends NinjaGuestNotificationEmail
{
    public function __construct(
        public PreCheckInForm $form,
        public int $bookingId,
        public int $clientId,
        public Team $team,
        public bool $forcePlain = false
    ) {
        parent::__construct($forcePlain);
    }

    /**
     * Build the message.
     */
    public function build(): static
    {
        $booking = Booking::getBookingModel($this->team, $this->bookingId, ['rental', 'client', 'guestApplicationRentalSettings']);
        $client = $booking->client;
        $teamName = ucwords(strtolower($this->team->name));
        $rental = $booking->rental;

        $gas = $this->team->guestApplicationSettings;
        $details = null;
        if ($gas != null && $gas->include_checkin_details_email) {
            $details = $rental->checkin_details?->getLocaledText($this->locale);
        }
        $replyToEmail = $this->getReplyToAddress($this->team, $rental, $booking);

        return $this->subject(__('messages.thank_you_for_confirming_details'))
            ->from($this->getMailFromAddress($this->team, $rental, $booking), $teamName)
            ->replyTo($replyToEmail, $teamName)
            ->view(! $this->forcePlain ? 'mail.precheckin.precheckin_completed' : null)
            // We need to add the plain text format to ensure airbnb guests receive the message
            ->text('mail.precheckin.precheckin_completed_plain') // Note: the view must use the {!! !!} notation to ensure things like < do not transform into &lt; which is not later on re-encoded
            ->with([
                'booking' => $booking,
                'clientName' => $client->getClientNameForEmail(),
                'guestAppActivated' => $booking->guestApplicationRentalSettings->guest_app,
                'details' => $details,
                'contactEmail' => $replyToEmail,
                'hasUpsales' => $rental->guestsApplicationRentalSettings->upsales_enabled && $this->team->gatewayActivated($rental->id),
                'photo' => ninja_str_contains($this->team->photo_url, 'avatar') ? null : $this->team->imgixPhoto(),
            ]);
    }
}
