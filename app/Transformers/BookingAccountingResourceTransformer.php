<?php

namespace App\Transformers;

use App\Http\Resources\BookingAccountingResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Spatie\LaravelData\Support\DataProperty;
use Spatie\LaravelData\Transformers\Transformer;

class BookingAccountingResourceTransformer implements Transformer
{
    public function transform(DataProperty $property, mixed $value): AnonymousResourceCollection|array
    {
        return BookingAccountingResource::collection($value);
    }
}
