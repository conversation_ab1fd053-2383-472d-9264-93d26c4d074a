<?php

namespace App\Models;

use App\Events\SubscriptionCancelledEvent;
use App\Events\SubscriptionUpdatedEvent;
use App\Events\TeamCreatedEvent;
use App\Events\TeamMemberAddedEvent;
use App\Events\TeamMemberRemovedEvent;
use App\Events\TeamSubscribedEvent;
use App\Events\UserInvitedToTeam;
use App\Query\ProviderEventQuery;
use Eloquent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\ProviderEvent.
 *
 * @property int $id
 * @property int $team_id
 * @property int|null $rental_id
 * @property string $type
 * @property int|null $booking_id
 * @property int|null $initiator_id
 * @property array|null $data
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $related_model_type
 * @property int|null $related_model_id
 * @property-read Model|\Eloquent $relatedModel
 * @property-read \App\Models\Team $team
 *
 * @method static \Database\Factories\ProviderEventFactory factory($count = null, $state = [])
 * @method static ProviderEventQuery|ProviderEvent filterByTasksAccess(\App\Models\User $user)
 * @method static ProviderEventQuery|ProviderEvent filterOnType(\Illuminate\Support\Collection|array|string $type)
 * @method static ProviderEventQuery|ProviderEvent newModelQuery()
 * @method static ProviderEventQuery|ProviderEvent newQuery()
 * @method static ProviderEventQuery|ProviderEvent onBookings(\App\Models\Booking|\Illuminate\Support\Collection|array|int $booking)
 * @method static ProviderEventQuery|ProviderEvent onRentals(\App\Models\Rental|\Illuminate\Support\Collection|array|int $rental)
 * @method static ProviderEventQuery|ProviderEvent onTeam(\App\Models\Team|int $team)
 * @method static ProviderEventQuery|ProviderEvent onTypes(\Traversable|array|string $types)
 * @method static ProviderEventQuery|ProviderEvent query()
 * @method static ProviderEventQuery|ProviderEvent typeIsNotTask()
 * @method static ProviderEventQuery|ProviderEvent typeIsTaskButUserHasAccessToIt(\App\Models\User $user)
 * @method static ProviderEventQuery|ProviderEvent whereBookingId($value)
 * @method static ProviderEventQuery|ProviderEvent whereCreatedAt($value)
 * @method static ProviderEventQuery|ProviderEvent whereData($value)
 * @method static ProviderEventQuery|ProviderEvent whereId($value)
 * @method static ProviderEventQuery|ProviderEvent whereInitiatorId($value)
 * @method static ProviderEventQuery|ProviderEvent whereRelatedModelId($value)
 * @method static ProviderEventQuery|ProviderEvent whereRelatedModelType($value)
 * @method static ProviderEventQuery|ProviderEvent whereRentalId($value)
 * @method static ProviderEventQuery|ProviderEvent whereTeamId($value)
 * @method static ProviderEventQuery|ProviderEvent whereType($value)
 * @method static ProviderEventQuery|ProviderEvent whereUpdatedAt($value)
 *
 * @mixin Eloquent
 */
class ProviderEvent extends Model
{
    use HasFactory;

    const ALERT_CREATED = 'alert-created';
    const BOOKING_CANCELLED = 'booking-cancelled';
    const BOOKING_CREATED = 'booking-created';
    const BOOKING_UPDATED = 'booking-updated';
    const BOOKING_DATES_UPDATED = 'booking-dates-updated';
    const BOOKING_REQUESTED = 'booking-requested';
    const BOOKING_TENTATIVE = 'booking-tentative';
    const BOOKING_ARCHIVED = 'booking-archived';
    const BOOKING_REJECTED = 'booking-rejected';
    const BOOKING_BLOCK = 'block-created';
    const LEAD_CREATED = 'lead-created';
    const GUEST_NOTIFICATION_SENT = 'guest-notification-sent';
    const COMMENT_CREATED = 'comment-created';
    const PAYMENT_CREATED = 'payment-created';
    const PAYMENT_CANCELED = 'payment-canceled';
    const PAYMENT_REFUNDED = 'payment-refunded';
    const DAMAGE_DEPOSIT_RETURNED = 'damage-deposit-returned';
    const CHECK_IN_OUT_TIME_MODIFIED = 'check-in-out-time-modified';
    const PICTURE_CREATED = 'picture-created';
    const TASK_CREATED = 'task-created';
    const TASK_DELETED = 'task-deleted';
    const TASK_COMPLETED = 'task-completed';
    const TASK_UN_COMPLETED = 'task-un-completed';
    const RENTAL_CREATED = 'rental-created';
    const RENTAL_UPDATED = 'rental-updated';
    const RENTAL_RESTORED = 'rental-restored';
    const RENTAL_DELETED = 'rental-deleted';
    const PRE_CHECK_IN_FORM_FILLED = 'pre-check-in-form-filled';
    const PRE_CHECK_IN_FORM_RESET = 'pre-check-in-form-reset';
    const PCIF_SMS_NOTIFICATION_SENT = 'pre-check-in-form-sms-sent';
    const PCIF_EMAIL_NOTIFICATION_SENT = 'pre-check-in-form-email-sent';
    const STATEMENT_CREATED = 'statement-created';
    const STATEMENT_DELETED = 'statement-deleted';
    const STATEMENT_DOWNLOADED = 'statement-downloaded';
    const STATEMENT_EMAILED = 'statement-emailed';
    const PAYOUT_CREATED = 'payout-created';
    const PAYOUT_DELETED = 'payout-deleted';
    const USER_INVITED = 'user-invited';
    const TEAM_SUBSCRIBED = 'team-subscribed';
    const TEAM_MEMBER_REMOVED = 'team-member-removed';
    const TEAM_CREATED = 'team-created';
    const SUBSCRIPTION_CANCELLED = 'subscription-cancelled';
    const SUBSCRIPTION_UPDATED = 'subscription-updated';
    const USER_REGISTERED = 'user-registered';
    const PRE_CHECK_IN_ENABLED = 'pre-check-in-enabled';
    const PRE_CHECK_IN_DISABLED = 'pre-check-in-disabled';
    const GUEST_APP_DEACTIVATED = 'guest-app-deactivated';
    const GUEST_APP_ACTIVATED = 'guest-app-activated';
    const UPSALES_DEACTIVATED = 'upsales-deactivated';
    const UPSALES_ACTIVATED = 'upsales-activated';
    const PAYEE_INVOICES_DOWNLOADED = 'payee-invoices-downloaded';
    const PAYEE_DELETED = 'payee-deleted';
    const PAYEE_CREATED = 'payee-created';
    const PARTIAL_PAYOUT_DELETED = 'partial-payout-deleted';
    const PARTIAL_PAYOUT_CREATED = 'partial-payout-created';
    const PAYOUT_EXPORTED = 'payout-exported';
    const PAYOUT_INVOICE_CREATED = 'payout-invoice-created';
    const PAYOUT_MARKED_PAID = 'payout-marked-paid';
    const GUEST_APP_INSTALLED = 'guest-app-installed'; // Old name, refers to the fact that the guest accesses the guests portal
    const RENTAL_COUNT_CHANGED = 'rental-count-changed';
    const RENTAL_PUBLISHED_IN_CM = 'rental-published-in-cm';
    const RENTAL_UNPUBLISHED_FROM_CM = 'rental-unpublished-from-cm';
    const NEW_MESSAGE = 'new-message';
    const UPSALE_PURCHASED = 'upsale-purchased';
    const HOME_AUTOMATION_ACCOUNT_CREATED = 'home-automation-account-created';
    const HOME_AUTOMATION_ACCOUNT_DELETED = 'home-automation-account-deleted';
    const DEVICE_CREATED = 'device-created';
    const DEVICE_DELETED = 'device-deleted';
    const DEVICE_ONLINE = 'device-online';
    const DEVICE_OFFLINE = 'device-offline';
    const DEVICE_MAPPED = 'device-mapped';
    const DEVICE_UNMAPPED = 'device-unmapped';
    const SMARTLOCK_AUTHORISATION_CREATED = 'smartlock-authorisation-created';
    const SMARTLOCK_AUTHORISATION_DELETED = 'smartlock-authorisation-deleted';
    const AUTHORITY_COMMUNICATION_SUBMITTED = 'authority-report-submitted';
    const AUTHORITY_COMMUNICATION_CANCELED = 'authority-report-canceled';

    const TYPES = [
        self::ALERT_CREATED,
        self::BOOKING_CANCELLED,
        self::BOOKING_CREATED,
        self::BOOKING_UPDATED,
        self::LEAD_CREATED,
        self::GUEST_NOTIFICATION_SENT,
        self::COMMENT_CREATED,
        self::PAYMENT_CREATED,
        self::PAYMENT_CANCELED,
        self::PAYMENT_REFUNDED,
        self::DAMAGE_DEPOSIT_RETURNED,
        self::CHECK_IN_OUT_TIME_MODIFIED,
        self::PICTURE_CREATED,
        self::TASK_CREATED,
        self::TASK_COMPLETED,
        self::TASK_UN_COMPLETED,
        self::RENTAL_CREATED,
        self::RENTAL_RESTORED,
        self::RENTAL_DELETED,
        self::PRE_CHECK_IN_FORM_FILLED,
        self::PCIF_SMS_NOTIFICATION_SENT,
        self::PCIF_EMAIL_NOTIFICATION_SENT,
        self::STATEMENT_CREATED,
        self::STATEMENT_DELETED,
        self::STATEMENT_DOWNLOADED,
        self::STATEMENT_EMAILED,
        self::PAYOUT_CREATED,
        self::PAYOUT_DELETED,
        self::USER_INVITED,
        self::TEAM_SUBSCRIBED,
        self::TEAM_MEMBER_REMOVED,
        self::TEAM_CREATED,
        self::SUBSCRIPTION_CANCELLED,
        self::SUBSCRIPTION_UPDATED,
        self::USER_REGISTERED,
        self::PRE_CHECK_IN_ENABLED,
        self::PRE_CHECK_IN_DISABLED,
        self::GUEST_APP_DEACTIVATED,
        self::GUEST_APP_ACTIVATED,
        self::UPSALES_DEACTIVATED,
        self::UPSALES_ACTIVATED,
        self::PAYEE_INVOICES_DOWNLOADED,
        self::PAYEE_DELETED,
        self::PAYEE_CREATED,
        self::PARTIAL_PAYOUT_DELETED,
        self::PARTIAL_PAYOUT_CREATED,
        self::PAYOUT_EXPORTED,
        self::PAYOUT_INVOICE_CREATED,
        self::PAYOUT_MARKED_PAID,
        self::GUEST_APP_INSTALLED,
        self::RENTAL_COUNT_CHANGED,
        self::RENTAL_PUBLISHED_IN_CM,
        self::RENTAL_UNPUBLISHED_FROM_CM,
        self::NEW_MESSAGE,
        self::UPSALE_PURCHASED,
        self::HOME_AUTOMATION_ACCOUNT_CREATED,
        self::HOME_AUTOMATION_ACCOUNT_DELETED,
        self::DEVICE_CREATED,
        self::DEVICE_DELETED,
        self::DEVICE_MAPPED,
        self::DEVICE_UNMAPPED,
        self::DEVICE_ONLINE,
        self::DEVICE_OFFLINE,
        self::SMARTLOCK_AUTHORISATION_CREATED,
        self::SMARTLOCK_AUTHORISATION_DELETED,
    ];
    const TASK_EVENTS = [
        self::TASK_CREATED,
        self::TASK_COMPLETED,
        self::TASK_UN_COMPLETED,
    ];

    protected $table = 'bookingsync_events';

    protected $casts = ['data' => 'array'];

    protected $fillable = [
        'team_id',
        'type',
        'data',
        'rental_id',
        'booking_id',
        'initiator_id',
        'related_model_id',
        'related_model_type',
    ];

    public static function bookingData(Booking $booking): array
    {
        return [
            'id' => $booking->id,
            'rental_id' => $booking->rental_id,
            'team_id' => $booking->team_id,
            'start_at' => apiDateFromTimestamp($booking->start_at),
            'end_at' => apiDateFromTimestamp($booking->end_at),
            'guests' => ($booking->adults ?? 0) + ($booking->children ?? 0),
            'canceled_at' => apiDateFromTimestamp($booking->canceled_at),
            'final_price' => floatval($booking->final_price),
            'initial_price' => floatval($booking->initial_price),
            'paid_amount' => floatval($booking->paid_amount),
            'damage_deposit' => intval($booking->damage_deposit),
            'currency' => $booking->currency,
            'source' => $booking->source_public_name,
            'status' => $booking->status,
            'reference' => $booking->reference,
            'expected_checkin_time' => $booking->getCheckInTime(),
            'expected_checkout_time' => $booking->getCheckOutTime(),
        ];
    }

    public static function handleTeamMemberAdded(TeamMemberAddedEvent $event)
    {
        $user = $event->member;
        self::create([
            'team_id' => $user->current_team_id,
            'type' => self::USER_REGISTERED, // This event was triggered only when user was registered, now when added to the team.
            'rental_id' => 0,
            'related_model_id' => $user->id,
            'related_model_type' => User::class,
            'data' => [
                'id' => $user->id,
                'team_id' => $user->current_team_id,
                'email' => $user->email,
            ],
        ]);
    }

    public static function handleSubscriptionUpdated(SubscriptionUpdatedEvent $event)
    {
        $team = $event->team;
        self::create([
            'team_id' => $team->id,
            'type' => self::SUBSCRIPTION_UPDATED,
            'rental_id' => 0,
            'related_model_id' => $event->team->id,
            'related_model_type' => Team::class,
            'data' => [
                'team_id' => $team->id,
                'rentals' => $team->rentals,
                'users' => $team->users->count(),
                'provider_id' => $team->provider_id,
                'account_id' => $team->external_id,
            ],
        ]);
    }

    public static function handleSubscriptionCancelled(SubscriptionCancelledEvent $event)
    {
        $team = $event->team;
        self::create([
            'team_id' => $team->id,
            'type' => self::SUBSCRIPTION_CANCELLED,
            'rental_id' => 0,
            'related_model_id' => $event->team->id,
            'related_model_type' => Team::class,
            'data' => [
                'team_id' => $team->id,
                'rentals' => $team->rentals,
                'users' => $team->users->count(),
                'provider_id' => $team->provider_id,
                'account_id' => $team->external_id,
            ],
        ]);
    }

    public static function handleTeamCreated(TeamCreatedEvent $event)
    {
        $team = $event->team;
        self::create([
            'team_id' => $team->id,
            'type' => self::TEAM_CREATED,
            'rental_id' => 0,
            'related_model_id' => $event->team->id,
            'related_model_type' => Team::class,
            'data' => [
                'team_id' => $team->id,
                'rentals' => $team->rentals,
                'users' => $team->users->count(),
                'provider_id' => $team->provider_id,
                'account_id' => $team->external_id,
            ],
        ]);
    }

    public static function handleTeamMemberRemoved(TeamMemberRemovedEvent $event)
    {
        $team = $event->team;
        self::create([
            'team_id' => $team->id,
            'type' => self::TEAM_MEMBER_REMOVED,
            'rental_id' => 0,
            'related_model_id' => $event->member?->id,
            'related_model_type' => User::class,
            'initiator_id' => $event->initiator?->id,
            'data' => [
                'team_id' => $team->id,
                'rentals' => $team->rentals,
                'users' => $team->users->count(),
                'provider_id' => $team->provider_id,
                'account_id' => $team->external_id,
            ],
        ]);
    }

    public static function handleTeamSubscribed(TeamSubscribedEvent $event)
    {
        $team = $event->team;
        self::create([
            'team_id' => $team->id,
            'type' => self::TEAM_SUBSCRIBED,
            'rental_id' => 0,
            'related_model_id' => $team->id,
            'related_model_type' => Team::class,
            'data' => [
                'team_id' => $team->id,
                'rentals' => $team->rentals,
                'users' => $team->users->count(),
                'provider_id' => $team->provider_id,
                'account_id' => $team->external_id,
            ],
        ]);
    }

    public static function handleUserInvitedToTeam(UserInvitedToTeam $event)
    {
        $team = $event->team;
        self::create([
            'team_id' => $team->id,
            'type' => self::USER_INVITED,
            'rental_id' => 0,
            'related_model_id' => $event->user->id,
            'related_model_type' => User::class,
            'data' => [
                'team_id' => $team->id,
                'rentals' => $team->rentals,
                'users' => $team->users->count(),
                'provider_id' => $team->provider_id,
                'account_id' => $team->external_id,
            ],
        ]);
    }

    public static function handleGuestAppInstalled(Team $team, PreCheckInForm $form)
    {
        self::create([
            'team_id' => $team->id,
            'type' => self::GUEST_APP_INSTALLED,
            'rental_id' => $form->booking->rental_id,
            'booking_id' => $form->booking->id,
            'related_model_id' => $form->id,
            'related_model_type' => PreCheckInForm::class,
            'data' => [
            ],
        ]);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function relatedModel(): MorphTo
    {
        return $this->morphTo()->where('team_id', $this->team_id);
    }

    /**
     * @return ProviderEventQuery<ProviderEvent>
     */
    public function newEloquentBuilder($query): ProviderEventQuery
    {
        return new ProviderEventQuery($query);
    }
}
