<?php

namespace App\Models;

use App\Query\HashDownloadableRouteQuery;
use Eloquent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;

/**
 * App\Models\HashDownloadableRoute.
 *
 * @property int $id
 * @property string|null $downloadable_type
 * @property int|null $downloadable_id
 * @property string $hash
 * @property string $route
 * @property int $downloads
 * @property int|null $provider_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Model|\Eloquent $downloadable
 *
 * @method static \Database\Factories\HashDownloadableRouteFactory factory($count = null, $state = [])
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute forModel(?\Illuminate\Database\Eloquent\Model $model = null)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute newModelQuery()
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute newQuery()
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute onHash(string $hash)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute onRoute(string $route)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute query()
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute whereCreatedAt($value)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute whereDownloadableId($value)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute whereDownloadableType($value)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute whereDownloads($value)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute whereHash($value)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute whereId($value)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute whereProviderId($value)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute whereRoute($value)
 * @method static HashDownloadableRouteQuery|HashDownloadableRoute whereUpdatedAt($value)
 *
 * @mixin Eloquent
 */
class HashDownloadableRoute extends Model
{
    use HasFactory;

    protected $table = 'hash_downloadable_routes';

    /**
     * @return HashDownloadableRouteQuery<HashDownloadableRoute>
     */
    public function newEloquentBuilder($query): HashDownloadableRouteQuery
    {
        return new HashDownloadableRouteQuery($query);
    }

    public function downloadable(): MorphTo
    {
        return $this->morphTo();
    }

    public function amazonPath(): string
    {
        return Storage::disk('s3')->url($this->route);
    }
}
