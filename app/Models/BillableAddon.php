<?php

namespace App\Models;

use App\Enum\BillableAddonTypeEnum;
use Awo<PERSON>z\Compoships\Compoships;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\BillableAddon.
 *
 * @property int $id
 * @property int $team_id
 * @property BillableAddonTypeEnum $type
 * @property int $quantity
 * @property int|null $rental_id
 * @property int|null $booking_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Booking|null $booking
 * @property-read \App\Models\Rental|null $rental
 * @property-read \App\Models\Team $team
 *
 * @method static Builder|BillableAddon newModelQuery()
 * @method static Builder|BillableAddon newQuery()
 * @method static Builder|BillableAddon query()
 * @method static Builder|BillableAddon whereBookingId($value)
 * @method static Builder|BillableAddon whereCreatedAt($value)
 * @method static Builder|BillableAddon whereId($value)
 * @method static Builder|BillableAddon whereQuantity($value)
 * @method static Builder|BillableAddon whereRentalId($value)
 * @method static Builder|BillableAddon whereTeamId($value)
 * @method static Builder|BillableAddon whereType($value)
 * @method static Builder|BillableAddon whereUpdatedAt($value)
 *
 * @mixin Eloquent
 */
class BillableAddon extends Model
{
    use Compoships;

    protected $fillable = [
        'team_id',
        'type',
        'quantity',
        'rental_id',
        'booking_id',
    ];

    protected $casts = [
        'type' => BillableAddonTypeEnum::class,
    ];

    protected $attributes = [
        'quantity' => 0,
    ];

    /** Revenue in EUR */
    public function getTotalRevenue(?TeamSettings $settings = null): float
    {
        $settings ??= $this->team->teamSettings;
        $cents = match ($this->type) {
            BillableAddonTypeEnum::SMS => $this->quantity * $settings->getSmsPriceInCents(),
            BillableAddonTypeEnum::SCAN => $this->quantity * $settings->getScanPriceInCents(),
            BillableAddonTypeEnum::UPSCALE => $this->quantity * $settings->getUpscalePriceInCents(),
            BillableAddonTypeEnum::BOOKING_ENGINE_COMMISSION, BillableAddonTypeEnum::UPSALE_COMMISSION => $this->quantity, // Quantity is already commission in euro cents
            BillableAddonTypeEnum::AI => $this->quantity * $settings->getAiPriceInCents(),
        };

        return round($cents / 100, 2); // Transform to EUR
    }

    public function getDataToCreateStripePrice(Team $team, ?TeamSettings $settings = null, ?string $lookupKey = null): array
    {
        $settings ??= $team->teamSettings;
        $lookupKey ??= $this->type->stripePriceLookupKey($team, $settings);
        $unitCentsAmount = $this->type->getStripePriceUnitAmount($team, $settings); // In cents (check note in method description)

        $options = [
            'currency' => 'eur',
            'product' => $this->type->getStripeProduct(),
            'billing_scheme' => 'per_unit',
            'unit_amount_decimal' => $unitCentsAmount,
            'lookup_key' => $lookupKey,
            //'tax_behavior' => 'unspecified', If not specified, will take the default of the account
        ];

        if ($team->isYearlySubscription()) {
            $options['nickname'] = $unitCentsAmount.' for Yearly Subs';
        } else {
            $options['recurring'] = [
                'interval' => 'month',
                'usage_type' => 'metered',
                'aggregate_usage' => 'sum',
            ];
        }

        return $options;
    }

    /**
     * For yearly subscriptions, we can't report the original rental price (for booking engine commissions) or the original fee price (for upsale commision)
     * as the commission % may have been changed by us during the month. This is why we report to stripe directly the quantity.
     */
    public function getUsageToReportToStripe(Team $team, ?TeamSettings $settings = null): int
    {
        $settings ??= $this->team->teamSettings;
        $isYearly = $team->isYearlySubscription();

        return match ($this->type) {
            BillableAddonTypeEnum::SMS, BillableAddonTypeEnum::SCAN, BillableAddonTypeEnum::UPSCALE, BillableAddonTypeEnum::AI => $this->quantity,
            // For monthly plans, we need to get back from commission in cents to final rental price / fee price in cents:
            // For yearly plans, we will report directly the final amount to be charged, thus, the quantity:
            BillableAddonTypeEnum::BOOKING_ENGINE_COMMISSION => $isYearly ? $this->quantity : ninjaIntval($this->quantity * 100 / $settings->getBookingEngineCommissionPercentage()),
            BillableAddonTypeEnum::UPSALE_COMMISSION => $isYearly ? $this->quantity : ninjaIntval($this->quantity * 100 / $settings->getUpsalesCommissionPercentage()),
        };
    }

    public function reportAddonToKpi(TeamPerformanceIndicator $kpi, ?TeamSettings $settings = null): void
    {
        $settings ??= $this->team->teamSettings;

        switch ($this->type) {
            case BillableAddonTypeEnum::SMS:
                $kpi->sms_revenue += $this->getTotalRevenue($settings);
                break;
            case BillableAddonTypeEnum::SCAN:
                $kpi->document_scans += $this->quantity;
                $kpi->scan_revenue += $this->getTotalRevenue($settings);
                break;
            case BillableAddonTypeEnum::UPSCALE:
                $kpi->upscale_revenue += $this->getTotalRevenue($settings);
                break;
            case BillableAddonTypeEnum::BOOKING_ENGINE_COMMISSION:
                $kpi->booking_engine_commission += $this->getTotalRevenue($settings);
                break;
            case BillableAddonTypeEnum::UPSALE_COMMISSION:
                $kpi->upsales_commission += $this->getTotalRevenue($settings);
                break;
            case BillableAddonTypeEnum::AI:
                $kpi->ai_revenue += $this->getTotalRevenue($settings);
                break;
        }
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class, ['booking_id', 'team_id'], ['id', 'team_id'])->withoutGlobalScopes();
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id']);
    }
}
