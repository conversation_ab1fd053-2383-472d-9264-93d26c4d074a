<?php

namespace App\Models;

use App\Domains\Hostboost\Actions\PostNewScrapeAction;
use Awo<PERSON>z\Compoships\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\SetupRental.
 *
 * @property int $id
 * @property int $setup_company_id
 * @property int|null $team_id
 * @property int|null $rental_id
 * @property string|null $external_id
 * @property string|null $name
 * @property float|null $lat
 * @property float|null $lng
 * @property string|null $airbnb_id
 * @property string|null $airbnb_name
 * @property array|null $airbnb_hosts
 * @property int|null $airbnb_owner
 * @property string|null $airbnb_cancellation_policy
 * @property int|null $booking_legal_entity_id
 * @property int|null $booking_hotel_id
 * @property int|null $booking_room_id
 * @property string|null $booking_url
 * @property string|null $booking_name
 * @property string|null $currency
 * @property bool $locked
 * @property int|null $scrape_id
 * @property string|null $scrape_status
 * @property bool $fetched
 * @property \Illuminate\Support\Carbon|null $fetched_at
 * @property \Illuminate\Support\Carbon|null $last_fetched_at
 * @property array|null $tmp_availability
 * @property bool $reload_availability
 * @property \Illuminate\Support\Carbon|null $reload_availability_requested_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SetupAirbnbHost $airbnbHost
 * @property-read \App\Models\Rental|null $rental
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SetupBlock> $setupBlocks
 * @property-read int|null $setup_blocks_count
 * @property-read \App\Models\SetupCompany $setupCompany
 * @property-read \App\Models\Team|null $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental query()
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereAirbnbCancellationPolicy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereAirbnbHosts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereAirbnbId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereAirbnbName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereAirbnbOwner($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereBookingHotelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereBookingLegalEntityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereBookingName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereBookingRoomId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereBookingUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereFetched($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereFetchedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereLastFetchedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereLng($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereLocked($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereReloadAvailability($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereReloadAvailabilityRequestedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereRentalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereScrapeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereScrapeStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereSetupCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereTmpAvailability($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupRental whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class SetupRental extends NinjaProviderModel
{
    protected $fillable = [
        'setup_company_id',
        'external_id',
        'team_id',
        'rental_id',
        'name',
        'airbnb_id',
        'booking_legal_entity_id_id',
        'booking_hotel_id',
        'booking_room_id',
        'booking_url',
    ];

    protected $casts = [
        'airbnb_hosts' => 'array',
        'tmp_availability' => 'array',
        'airbnb_id' => 'string',
        'fetched' => 'boolean',
        'locked' => 'boolean',
        'reload_availability' => 'boolean',
        'fetched_at' => 'datetime',
        'last_fetched_at' => 'datetime',
        'reload_availability_requested_at' => 'datetime',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function setupCompany(): BelongsTo
    {
        return $this->belongsTo(SetupCompany::class);
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id']);
    }

    public function airbnbHost(): BelongsTo
    {
        return $this->belongsTo(SetupAirbnbHost::class);
    }

    public function setupBlocks(): HasMany
    {
        return $this->hasMany(SetupBlock::class);
    }

    public function getScrappingPath(): string
    {
        return '/setups/'.$this->setup_company_id.'/'.$this->id.'/';
    }

    public function getScrappingFile(): string
    {
        return $this->getScrappingPath().'data.json';
    }

    // Create an observer to update certain attributes when a model is created or updated
    protected static function booted(): void
    {
        static::created(function ($setupRental) {
            $setupRental->team_id = $setupRental->team_id ?? $setupRental->setupCompany?->team_id;
            $setupRental->save();
        });

        static::updating(function ($setupRental) {
            if (is_null($setupRental->team_id)) {
                $setupRental->team_id = $setupRental->setupCompany->team_id;
            }
        });
    }

    public function lockRental(): void
    {
        $this->locked = true;
        $this->save();
        PostNewScrapeAction::run($this);
    }
}
