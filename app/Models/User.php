<?php

namespace App\Models;

use App\Actions\Auth\DeleteLoginQrCodeAction;
use App\Actions\Cache\GetRentalsIdForUserAction;
use App\Actions\Pictures\HandleTeamOrUserProfilePictureAction;
use App\Actions\Support\Locales\GetLongLocaleFromShortLocaleAction;
use App\Actions\Teams\TeamMembers\SetDefaultPermissionsForRoleAction;
use App\DataProviders\Providers\NinjaProvider;
use App\Domains\HomeAutomation\Models\SmartlockAuthorisation;
use App\Enum\AlertTypeEnum;
use App\Enum\TeamRolesEnum;
use App\Flavors\DefaultFlavor;
use App\Flavors\NinjaFlavor;
use App\Notifications\NinjaResetPasswordNotification;
use App\Query\UserQuery;
use App\Traits\NinjaNotifiable;
use Awobaz\Compoships\Compoships;
use Eloquent;
use Exception;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection as BaseCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Laravel\Passport\HasApiTokens;
use Laravel\Passport\Token;

/**
 * App\Models\User.
 *
 * @property int $id
 * @property string|null $external_id
 * @property int|null $provider_id
 * @property string $name
 * @property string $email
 * @property string|null $locale
 * @property string $password
 * @property string|null $qr_password
 * @property string|null $remember_token
 * @property string $photo_url
 * @property string|null $country_code
 * @property string|null $phone
 * @property int|null $current_team_id
 * @property TeamRolesEnum $ninja_role
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Collection<int, \App\Models\AlertsSnoozeTime> $alertsSnoozeTimes
 * @property-read int|null $alerts_snooze_times_count
 * @property-read Collection<int, \Laravel\Passport\Client> $clients
 * @property-read int|null $clients_count
 * @property-read \App\Models\CustomUserToken|null $customUserToken
 * @property-read Collection<int, \App\Models\Invitation> $invitations
 * @property-read int|null $invitations_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \App\Models\TeamUsersPermission|null $permissions
 * @property-read string|null $short_locale
 * @property-read Collection<int, SmartlockAuthorisation> $smartlockAuthorisations
 * @property-read int|null $smartlock_authorisations_count
 * @property-read \App\Models\Team|null $team
 * @property-read Collection<int, Token> $tokens
 * @property-read int|null $tokens_count
 * @property-read Collection<int, \App\Models\TeamUserRental> $userRentals
 * @property-read int|null $user_rentals_count
 *
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static UserQuery|User newModelQuery()
 * @method static UserQuery|User newQuery()
 * @method static UserQuery|User onTeam(\App\Models\Team $team)
 * @method static UserQuery|User onTeamId(int $id)
 * @method static UserQuery|User query()
 * @method static UserQuery|User whereCountryCode($value)
 * @method static UserQuery|User whereCreatedAt($value)
 * @method static UserQuery|User whereCurrentTeamId($value)
 * @method static UserQuery|User whereEmail($value)
 * @method static UserQuery|User whereExternalId($value)
 * @method static UserQuery|User whereId($value)
 * @method static UserQuery|User whereLocale($value)
 * @method static UserQuery|User whereName($value)
 * @method static UserQuery|User whereNinjaRole($value)
 * @method static UserQuery|User wherePassword($value)
 * @method static UserQuery|User wherePhone($value)
 * @method static UserQuery|User wherePhotoUrl($value)
 * @method static UserQuery|User whereProviderId($value)
 * @method static UserQuery|User whereQrPassword($value)
 * @method static UserQuery|User whereRememberToken($value)
 * @method static UserQuery|User whereRentalManagerOrAbove()
 * @method static UserQuery|User whereUpdatedAt($value)
 * @method static UserQuery|User withAccessToRentals(array|int $rentals)
 *
 * @mixin Eloquent
 */
class User extends Authenticatable implements HasLocalePreference
{
    use Compoships;
    use HasApiTokens;
    use HasFactory;
    use NinjaNotifiable;

    protected $fillable = [
        'external_id',
        'name',
        'email',
        'ninja_role',
    ];

    protected $casts = [
        'ninja_role' => TeamRolesEnum::class,
    ];

    protected $with = ['team'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'qr_password',
        'remember_token',
    ];

    /** This returns the currently used passport oauth client by our frontend application.
     * It shouldn't be changed, but in case it is, here we can change it.
     * You should only use it for very particular cases, where you auth a user with passport manually.
     */
    public function getCurrentPassportOauthClient()
    {
        return DB::table('oauth_clients')
                 ->where('id', '=', 2)
                 ->first(); // This is the oauth client used by the frontend
    }

    /**
     * Validate the password of the user for the Passport password grant.
     */
    public function validateForPassportPasswordGrant(string $password): bool
    {
        if (request()->boolean('qr_password')) {
            $valid = Hash::check($password, $this->qr_password);
            DeleteLoginQrCodeAction::run($this);

            return $valid;
        }

        return Hash::check($password, $this->password);
    }

    public function locale(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value): ?string => is_null($value) ? null : (strlen($value) == 2 ? GetLongLocaleFromShortLocaleAction::run($value) : $value),
            set: fn (?string $value): ?string => $value,
        );
    }

    public function shortLocale(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value, array $attributes): ?string => is_null($attributes['locale']) ? null : Str::of($attributes['locale'])->before('_'),
        );
    }

    protected function photoUrl(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value): string => HandleTeamOrUserProfilePictureAction::run($this, $value),
            set: fn (?string $value): ?string => $value,
        );
    }

    public function permissionFor(string $permission): bool
    {
        try {
            return boolval($this->getPermissions()->{$permission});
        } catch (Exception) {
            return false;
        }
    }

    public function getPermissions(): ?TeamUsersPermission
    {
        if (! $this->current_team_id) {
            return null;
        }

        return once(fn () => $this->permissions ? $this->permissions : $this->setDefaultPermissionsForRole());
    }

    public function setDefaultPermissionsForRole(?TeamRolesEnum $role = null): ?TeamUsersPermission
    {
        $role ??= $this->ninja_role;

        return SetDefaultPermissionsForRoleAction::run($this, $role);
    }

    /**
     * Returns true if at least one of the permissions of the array is true.
     */
    public function hasPermissionFor(array $permissions): bool
    {
        $valid = Arr::where($permissions, function ($value) {
            return $this->permissionFor($value) === true;
        });

        return ! empty($valid);
    }

    public function canSeeAlerts(): bool
    {
        return AlertTypeEnum::getAlertTypesAsPerPermissions($this->getPermissions())->isNotEmpty();
    }

    public function canSeePictures(): bool
    {
        return $this->hasPermissionFor([
            'view_booking_pictures',
            'view_rental_pictures',
            'see_album',
        ]);
    }

    // Notes permissions
    public function canSeeInternalNote(): bool
    {
        return $this->permissionFor('view_booking_notes');
    }

    public function canEditInternalNote(): bool
    {
        return $this->permissionFor('edit_booking_notes');
    }

    public function canSeeBookingNotice(): bool
    {
        return $this->permissionFor('view_booking_notice');
    }

    public function canSeePayments(): bool
    {
        return $this->permissionFor('see_payments_tab');
    }

    public function canSeeComments(): bool
    {
        return $this->permissionFor('view_booking_comments');
    }

    public function canAccessInbox(): bool
    {
        return $this->permissionFor('can_access_inbox');
    }

    /**
     * FIXME: This needs to be checked based on pricing.
     */
    public function canSeeAccounting(): bool
    {
        if ($this->ninja_role == TeamRolesEnum::OWNER) {
            return true;
        }

        return $this->permissionFor('accounting');
    }

    /**
     * FIXME: This needs to be checked based on pricing.
     */
    public function canSeeGuestApp(): bool
    {
        if ($this->ninja_role == TeamRolesEnum::OWNER) {
            return true;
        }

        return $this->permissionFor('guest_app');
    }

    public function canManageGuestApp(): bool
    {
        if ($this->ninja_role == TeamRolesEnum::OWNER) {
            return true;
        }

        return $this->permissionFor('guest_app_general_settings');
    }

    public function canManagePreCheckIn(): bool
    {
        if ($this->ninja_role == TeamRolesEnum::OWNER) {
            return true;
        }

        return $this->permissionFor('guest_app_pre_check_in_form_settings');
    }

    public function canSeeWebsite(): bool
    {
        if ($this->ninja_role == TeamRolesEnum::OWNER || $this->ninja_role == TeamRolesEnum::ADMIN) {
            return true;
        }

        return $this->permissionFor('website_access');
    }

    public function canSeeCostBreakdown(): bool
    {
        return $this->permissionFor('see_cost_break_down_tab');
    }

    public function canSeeNetCostBreakdown(): bool
    {
        return $this->permissionFor('see_net_cost_break_down');
    }

    public function canSeeLimitedCostBreakdown(): bool
    {
        return $this->permissionFor('see_limited_cost_break_down');
    }

    public function canSeeClientDetails(): bool
    {
        return $this->permissionFor('see_client_details');
    }

    public function isRentalManagerOrAbove(): bool
    {
        return $this->ninja_role->isRentalManagerOrAbove();
    }

    public function isAdminOrAbove(): bool
    {
        return $this->ninja_role->isAdminOrAbove();
    }

    public function isTeamOwner(?Team $team = null): bool
    {
        if (! $team) {
            $team = $this->team;
        }

        if (! $team) {
            return false;
        }

        return $team->owner_id == $this->id && $this->ninja_role->isTeamOwner();
    }

    public function updateGivenPermissions(array $permissions)
    {
        $current = (new TeamUsersPermission())
            ->firstOrNew([
                'user_id' => $this->id,
                'team_id' => $this->current_team_id,
            ]);

        $permissions = Arr::except($permissions, ['team_id', 'user_id']);

        // Ensure we only store current permissions. Useful when updating models and need old and new name.
        $permissions = Arr::only($permissions, Schema::getColumnListing('team_users_permissions'));

        foreach ($permissions as $key => $value) {
            $current->{$key} = intval($value);
        }

        if ($current->isDirty()) {
            $current->save();
        }
    }

    public function customUserToken(): HasOne
    {
        return $this->hasOne(CustomUserToken::class, 'user_id', 'id');
    }

    public function getEventsForUser(): array
    {
        if ($this->isAdminOrAbove()) {
            return ProviderEvent::TYPES;
        }

        // TODO: Filter by permissions
        return ProviderEvent::TYPES;
    }

    public function getRentalsForUser(bool $withUncompleted = false): array
    {
        $this->team;

        return GetRentalsIdForUserAction::run(user: $this, withUncompleted: $withUncompleted);
    }

    public function removeRentalAccess(int|array|BaseCollection|null $rentalIds = null): void
    {
        // If no rental ids provided, we assume you want to delete all rentals access
        if (empty($rentalIds)) {
            // To remove access, we delete records for now, we don't put access = 0 (don't know why)
            $this->userRentals()->delete();
        } else {
            $this->userRentals()->onRental($rentalIds)->delete();
        }
    }

    public function giveRentalAccess(int|BaseCollection $rentalIds): void
    {
        if (is_int($rentalIds)) {
            TeamUserRental::create(['user_id' => $this->id, 'rental_id' => $rentalIds, 'access' => true]);
        } else {
            $rentalIds->unique()
                ->map(fn (int $rentalId) => [
                    'rental_id' => $rentalId,
                    'user_id' => $this->id,
                    'access' => true,
                ])
                ->tap(fn (BaseCollection $userRentals) => TeamUserRental::insert($userRentals->toArray()));
        }
    }

    /**
     * Returns the settings for this user, and if not present, creates some new settings.
     */
    public function getSettings(): UserSettings
    {
        $query = UserSettings::whereUserId($this->id);

        if ($query->exists()) {
            return $query->first();
        }

        $tr = (new UserSettings())->forceFill([
            'user_id' => $this->id, // The rest we can use default DB values
        ]);
        $tr->save();

        return $tr;
    }

    /**
     * Route notifications for the Slack channel.
     */
    public function routeNotificationForFirebase(): string
    {
        $settings = TeamSettings::whereTeamId($this->id)->first();

        if (
            $settings != null
            && $settings->active
            && $settings->slack_webhook_url != null
            && filter_var($settings->slack_webhook_url, FILTER_VALIDATE_URL) !== false
        ) {
            return $settings->slack_webhook_url;
        }

        return '';
    }

    public function preferredLocale(): ?string
    {
        return substr($this->locale ?? 'en_US', 0, 2);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'current_team_id');
    }

    public function alertsSnoozeTimes(): HasMany
    {
        return $this->hasMany(AlertsSnoozeTime::class, 'user_id', 'id')
            ->whereDate('snoozed_until', '>', now('UTC'));
    }

    /**
     * Returns all rentals, including deleted rentals. Cannot be filtered here because this model does not include team_id.
     */
    public function userRentals(): HasMany
    {
        return $this
            ->hasMany(TeamUserRental::class, 'user_id', 'id')
            ->where('access', '=', true);
    }

    public function permissions(): HasOne
    {
        return $this->hasOne(TeamUsersPermission::class, 'user_id', 'id');
    }

    public function invitations(): HasMany
    {
        return $this->hasMany(Invitation::class, 'email', 'email');
    }

    public function smartlockAuthorisations(): HasMany
    {
        return $this->hasMany(SmartlockAuthorisation::class, 'user_id', 'id');
    }

    public function provider(): NinjaProvider
    {
        return NinjaProvider::provider($this->providerId());
    }

    public function providerId(): int
    {
        return once(function () {
            if (is_null($this->current_team_id)) {
                return 0;
            }
            /** @var Team $team */
            $team = $this->team;
            if ($team !== null) {
                return $team->provider_id;
            }

            return 0;
        });
    }

    // User may not have a team when re-invited to a team.
    public function config(): NinjaFlavor
    {
        return $this->team?->config() ?? new DefaultFlavor();
    }

    /**
     * Determine if the user can impersonate another user.
     */
    public function canImpersonate(): bool
    {
        if (! RentalNinjaTeam::userHasNovaAccess($this)) {
            return false;
        }

        return true;
    }

    /**
     * @return UserQuery<User>
     */
    public function newEloquentBuilder($query): UserQuery
    {
        return new UserQuery($query);
    }

    public function save(array $options = [])
    {
        // Due to the photo_url accessor, when saving a user, we save the value given by the accessor. We don't want that
        if ($this->photo_url !== null && ! Str::contains($this->photo_url, config('ninja.imgix_url_identifier'))) {
            $this->photo_url = null;
        }

        return parent::save($options);
    }

    /**
     * Send the password reset notification. This is overriding the default laravel behaviour.
     */
    public function sendPasswordResetNotification($token): void
    {
        $this->notify(new NinjaResetPasswordNotification($token));
    }
}
