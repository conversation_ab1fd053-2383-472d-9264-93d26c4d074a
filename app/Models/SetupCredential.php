<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\SetupCredential.
 *
 * @property int $id
 * @property int $setup_company_id
 * @property int|null $team_id
 * @property string $type
 * @property string $service
 * @property string|null $name
 * @property string|null $username
 * @property string|null $password
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SetupCompany $setupCompany
 * @property-read \App\Models\Team|null $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential query()
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential whereService($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential whereSetupCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupCredential whereUsername($value)
 *
 * @mixin \Eloquent
 */
class SetupCredential extends Model
{
    const TYPE_OTA = 'ota';
    const TYPE_CHANNEL_MANAGER = 'channel_manager';
    const TYPE_REVENUE_MANAGER = 'revenue_manager';

    const TYPES = [
        self::TYPE_OTA,
        self::TYPE_CHANNEL_MANAGER,
        self::TYPE_REVENUE_MANAGER,
    ];

    protected $fillable = [
        'type',
        'service',
        'name',
        'username',
        'password',
    ];

    public function setupCompany(): BelongsTo
    {
        return $this->belongsTo(SetupCompany::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    protected static function booted()
    {
        static::creating(function ($setupCredential) {
            $setupCredential->password = encrypt($setupCredential->password);
        });

        static::updating(function ($setupCredential) {
            $setupCredential->password = encrypt($setupCredential->password);
        });
    }
}
