<?php

namespace App\Models;

use Awobaz\Compoships\Compoships;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\ICalOutput.
 *
 * @property int $id
 * @property int $team_id
 * @property int $rental_id
 * @property int|null $source_id
 * @property string $name
 * @property string $passcode
 * @property \Illuminate\Support\Carbon|null $last_synced
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Source|null $source
 * @property-read \App\Models\Team $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput query()
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput whereLastSynced($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput wherePasscode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput whereRentalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput whereSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ICalOutput whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ICalOutput extends Model
{
    use Compoships;

    protected $casts = [
        'last_synced' => 'datetime',
        'input' => 'boolean',
    ];

    protected $guarded = ['id', 'created_at', 'updated_at'];

    protected $with = ['source'];

    public function source(): BelongsTo
    {
        return $this->belongsTo(Source::class, ['source_id', 'team_id'], ['id', 'team_id']);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }
}
