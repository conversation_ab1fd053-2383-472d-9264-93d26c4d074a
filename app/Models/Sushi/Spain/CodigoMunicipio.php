<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Continent.
 *
 * @property int|null $id
 * @property string|null $code
 * @property string|null $name
 * @property-read Collection<int, \App\Models\Country> $countries
 * @property-read int|null $countries_count
 *
 * @method static Builder|Continent newModelQuery()
 * @method static Builder|Continent newQuery()
 * @method static Builder|Continent query()
 * @method static Builder|Continent whereCode($value)
 * @method static Builder|Continent whereId($value)
 * @method static Builder|Continent whereName($value)
 *
 * @mixin Eloquent
 */
class CodigoMunicipio extends Model
{
    use Sushi;

    protected array $rows = [
        [
            'id' => 'af',
            'code' => 'af',
            'name' => 'Africa',
        ],
        [
            'id' => 'an',
            'code' => 'an',
            'name' => 'Antarctica',
        ],
        [
            'id' => 'as',
            'code' => 'as',
            'name' => 'Asia',
        ],
        [
            'id' => 'eu',
            'code' => 'eu',
            'name' => 'Europe',
        ],
        [
            'id' => 'na',
            'code' => 'na',
            'name' => 'North America',
        ],
        [
            'id' => 'oc',
            'code' => 'oc',
            'name' => 'Oceania',
        ],
        [
            'id' => 'sa',
            'code' => 'sa',
            'name' => 'South America',
        ],
    ];
}
