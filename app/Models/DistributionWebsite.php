<?php

namespace App\Models;

use App\Casts\TranslationArrayCaster;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * App\Models\DistributionWebsite.
 *
 * @property int $id
 * @property int $team_id
 * @property string $name
 * @property bool $active
 * @property int $allow_bookings
 * @property string|null $notes
 * @property string|null $domain
 * @property int|null $proxy_id
 * @property bool $domain_status
 * @property string|null $fathom_id
 * @property string|null $fathom_password
 * @property string|null $logo
 * @property string|null $main_image
 * @property array|null $rentals
 * @property int|null $source_id
 * @property float $markup
 * @property \App\DTO\ChannelManager\TranslationArray|null $headline
 * @property \App\DTO\ChannelManager\TranslationArray|null $about
 * @property \App\DTO\ChannelManager\TranslationArray|null $faqs
 * @property \App\DTO\ChannelManager\TranslationArray|null $website_seo_title
 * @property \App\DTO\ChannelManager\TranslationArray|null $website_seo_description
 * @property \App\DTO\ChannelManager\TranslationArray|null $main_image_title
 * @property \App\DTO\ChannelManager\TranslationArray|null $main_image_description
 * @property \App\DTO\ChannelManager\TranslationArray|null $keywords
 * @property string|null $google_ads_tag_id
 * @property string|null $google_ads_conversion_label
 * @property string|null $contact_name
 * @property string|null $contact_email
 * @property string|null $contact_street
 * @property string|null $contact_city
 * @property string|null $contact_zip
 * @property string|null $contact_region
 * @property string|null $contact_country_code
 * @property string|null $contact_support_phone
 * @property string|null $contact_booking_phone
 * @property string|null $social_x
 * @property string|null $social_facebook
 * @property string|null $social_linkedin
 * @property string|null $social_youtube
 * @property string|null $social_pinterest
 * @property string|null $social_instagram
 * @property string|null $social_blog
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \App\DTO\ChannelManager\TranslationArray $contact
 * @property-read bool $domain_verified
 * @property-read string $proxy_host
 * @property-read \App\Models\Team $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite query()
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereAbout($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereAllowBookings($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereContactBookingPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereContactCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereContactCountryCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereContactEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereContactName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereContactRegion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereContactStreet($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereContactSupportPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereContactZip($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereDomain($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereDomainStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereFaqs($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereFathomId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereFathomPassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereGoogleAdsConversionLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereGoogleAdsTagId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereHeadline($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereKeywords($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereLogo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereMainImage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereMainImageDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereMainImageTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereMarkup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereProxyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereRentals($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereSocialBlog($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereSocialFacebook($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereSocialInstagram($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereSocialLinkedin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereSocialPinterest($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereSocialX($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereSocialYoutube($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereWebsiteSeoDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite whereWebsiteSeoTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|DistributionWebsite withoutTrashed()
 *
 * @mixin \Eloquent
 */
class DistributionWebsite extends Model
{
    use SoftDeletes;

    const PROXY_HOST = '*************';

    protected $fillable = [
        'name',
        'active',
        'allow_bookings',
        'notes',
        'domain',
        'logo',
        'main_image',
        'rentals',
        'source_id',
        'markup',
        'headline',
        'about',
        'faqs',
        'website_seo_title',
        'website_seo_description',
        'main_image_title',
        'main_image_description',
        'keywords',
        'google_ads_tag_id',
        'google_ads_conversion_label',
        'contact_name',
        'contact_support_phone',
        'contact_booking_phone',
        'contact_email',
        'contact_street',
        'contact_zip',
        'contact_city',
        'contact_region',
        'contact_country_code',
        'social_x',
        'social_facebook',
        'social_linkedin',
        'social_youtube',
        'social_pinterest',
        'social_instagram',
        'social_blog',
    ];

    protected $casts = [
        'active' => 'boolean',
        'rentals' => 'array',
        'domain_status' => 'boolean',
        'headline' => TranslationArrayCaster::class,
        'about' => TranslationArrayCaster::class,
        'faqs' => TranslationArrayCaster::class,
        'contact' => TranslationArrayCaster::class,
        'website_seo_title' => TranslationArrayCaster::class,
        'website_seo_description' => TranslationArrayCaster::class,
        'main_image_title' => TranslationArrayCaster::class,
        'main_image_description' => TranslationArrayCaster::class,
        'keywords' => TranslationArrayCaster::class,
    ];

    public function domainVerified(): Attribute
    {
        return Attribute::make(get: fn (): bool => ! is_null($this->proxy_id));
    }

    public function proxyHost(): Attribute
    {
        return Attribute::make(get: fn (): string => self::PROXY_HOST);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function rentals(): Collection
    {
        return $this->team->teamRentals()->whereIn('id', $this->rentals)->get();
    }

    public function languages(): array
    {
        return $this->website_seo_title?->languages() ?? [];
    }

    public function isActiveAndReady(): bool
    {
        return $this->active && $this->isReady();
    }

    /** This unpublishes the website in case is not ready. */
    public function isReady(): bool
    {
        return ! empty($this->rentals) &&
            //! $this->rentalsWithError() && We don't want to unpublish the website in case of this error.
            ! is_null($this->headline) &&
            ! is_null($this->about) &&
            ! is_null($this->faqs) &&
            ! is_null($this->website_seo_title) &&
            ! is_null($this->website_seo_description) &&
            ! is_null($this->main_image) &&
            ! is_null($this->main_image_title) &&
            ! is_null($this->main_image_description) &&
            ! is_null($this->domain) &&
            ! is_null($this->logo) &&
            ! is_null($this->source_id) &&
            ! $this->sourcesError();
    }

    public function errors(): array
    {
        $rentalsWithError = $this->rentalsWithError();
        if (count($rentalsWithError) > 0) {
            $rentalsWithErrorNames = Rental::query()
                ->onTeam($this->team)
                ->whereId($rentalsWithError)
                ->pluck('name')
                ->toStringList();
        }

        $googleAdsError = (! empty($this->google_ads_tag_id) && empty($this->google_ads_conversion_label))
            || (empty($this->google_ads_tag_id) && ! empty($this->google_ads_conversion_label));

        return array_values(array_filter([
            ! $this->domain_verified ? __('booking_page.errors.domain_not_verified') : '',
            ! $this->domain_status ? __('booking_page.errors.domain_error') : '',
            empty($this->rentals) ? __('booking_page.errors.rentals_required') : '',
            count($rentalsWithError) > 0 ? __('booking_page.errors.not_enabled_rentals', ['rentals' => $rentalsWithErrorNames]) : '',
            is_null($this->headline) ? __('booking_page.errors.headline_required') : '',
            is_null($this->about) ? __('booking_page.errors.about_required') : '',
            is_null($this->faqs) ? __('booking_page.errors.faqs_required') : '',
            is_null($this->website_seo_title) ? __('booking_page.errors.seo_title_required') : '',
            is_null($this->website_seo_description) ? __('booking_page.errors.seo_description_required') : '',
            is_null($this->main_image) ? __('booking_page.errors.main_image_required') : '',
            is_null($this->main_image_title) ? __('booking_page.errors.main_image_title_required') : '',
            is_null($this->main_image_description) ? __('booking_page.errors.main_image_description_required') : '',
            is_null($this->domain) ? __('booking_page.errors.domain_required') : '',
            is_null($this->logo) ? __('booking_page.errors.logo_required') : '',
            is_null($this->source_id) ? __('booking_page.errors.source_required') : '',
            $this->sourcesError() ? __('booking_page.errors.disabled_sources') : '',
            $googleAdsError ? __('booking_page.errors.google_ads_error') : '',
        ]));
    }

    private function rentalsWithError(): array
    {
        $activeGatewayAccounts = $this->team->paymentGatewayAccounts
            ->where('charges_enabled', true)
            ->whereNotNull('gateway_account_id');

        if ($activeGatewayAccounts->filter(fn (PaymentGatewayAccount $account) => empty($account->rentals))->isNotEmpty()) {
            return []; //If no rentals in Stripe connect, means it's enabled for all
        }

        return array_diff($this->rentals, $activeGatewayAccounts->pluck('rentals')->flatten()->filter()->toArray());
    }

    private function sourcesError(): bool
    {
        $activeGatewayAccounts = $this->team->paymentGatewayAccounts
            ->where('charges_enabled', true)
            ->whereNotNull('gateway_account_id');

        /** @var PaymentGatewayAccount $allRentals */
        $allRentals = $activeGatewayAccounts->filter(fn (PaymentGatewayAccount $account) => empty($account->rentals))->first();
        if ($allRentals !== null) {
            $disabledSources = collect($allRentals->disabled_sources);
        } else {
            $disabledSources = $activeGatewayAccounts
                ->filter(fn (PaymentGatewayAccount $account) => count(array_intersect($account->rentals, $this->rentals)) > 0)
                ->pluck('disabled_sources')
                ->flatten()
                ->filter()
                ->unique();
        }

        return $disabledSources->isNotEmpty() && $disabledSources->contains($this->source_id);
    }

    public function warnings(): array
    {
        return array_values(array_filter([
            empty($this->google_ads_tag_id) && empty($this->google_ads_conversion_label) ? __('booking_page.warnings.google_ads_not_set') : '',
        ]));
    }

    public function locations(): ?array
    {
        $locations = $this->rentalsWithLocation();

        return $locations ? array_unique(array_values($locations)) : null;
    }

    public function rentalsWithLocation(): array
    {
        if (empty($this->rentals)) {
            return [];
        }

        $rentalsWithLocation = $this->team->teamRentals()
            ->whereIn('id', $this->rentals)
            ->pluck('city', 'id');

        return collect($this->rentals)
            ->mapWithKeys(function ($rentalId) use ($rentalsWithLocation) {
                return [$rentalId => $rentalsWithLocation[$rentalId] ?? null];
            })
            ->filter()
            ->toArray();
    }

    // Rental related functions
    public function updateRentals(array $rentals): void
    {
        $this->rentals = array_values(array_unique(array_merge(array_intersect($this->rentals, $rentals), $rentals)));
    }

    public function moveRental($rentalId, $toIndex): bool
    {
        // Step 0: Index to 0-based array
        $toIndex -= 1;

        // Step 1: Find the index of the element by its value
        $array = $this->rentals;
        $fromIndex = array_search($rentalId, $array);

        // Ensure the element exists in the array
        if ($fromIndex === false) {
            return false; // Element not found
        }

        // Ensure the toIndex is within bounds
        $arrayLength = count($array);
        if ($toIndex < 0 || $toIndex >= $arrayLength) {
            return false; // Invalid index
        }

        // Step 2: Remove the element from its current position
        $element = array_splice($array, $fromIndex, 1);

        // Step 3: Insert the element into its new position
        array_splice($array, $toIndex, 0, $element);
        $this->rentals = $array;

        return true; // Successful move
    }
}
