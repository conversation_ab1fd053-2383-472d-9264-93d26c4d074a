<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\ChartmogulData.
 *
 * @property int $id ChartMogul Data ID
 * @property int $team_id Team ID
 * @property string $data_source_uuid ChartMogul Data Source UUID
 * @property string $chartmogul_uuid ChartMogul Customer UUID
 * @property int $chartmogul_id ChartMogul Customer ID
 * @property string|null $chartmogul_subscription_uuid ChartMogul Subscription UUID
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Team $team
 *
 * @method static Builder|ChartmogulData newModelQuery()
 * @method static Builder|ChartmogulData newQuery()
 * @method static Builder|ChartmogulData query()
 * @method static Builder|ChartmogulData whereChartmogulId($value)
 * @method static Builder|ChartmogulData whereChartmogulSubscriptionUuid($value)
 * @method static Builder|ChartmogulData whereChartmogulUuid($value)
 * @method static Builder|ChartmogulData whereCreatedAt($value)
 * @method static Builder|ChartmogulData whereDataSourceUuid($value)
 * @method static Builder|ChartmogulData whereId($value)
 * @method static Builder|ChartmogulData whereTeamId($value)
 * @method static Builder|ChartmogulData whereUpdatedAt($value)
 *
 * @mixin Eloquent
 */
class ChartmogulData extends Model
{
    protected $guarded = ['id'];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }
}
