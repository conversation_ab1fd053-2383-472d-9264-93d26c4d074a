<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\ChartmogulInvoice.
 *
 * @property int $id Internal Reference of the Invoice ID
 * @property int $team_id Team ID
 * @property int $chartmogul_data_id ChartMogul Data ID
 * @property string $chartmogul_invoice_id ChartMogul Invoice ID
 * @property Carbon $invoice_raised_at Invoice Raised At Date
 * @property Carbon $invoice_due_at Invoice Due At Date
 * @property int $is_transaction_created Is Invoice Paid
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\ChartmogulData $data
 * @property-read \App\Models\Team $team
 *
 * @method static Builder|ChartmogulInvoice newModelQuery()
 * @method static Builder|ChartmogulInvoice newQuery()
 * @method static Builder|ChartmogulInvoice query()
 * @method static Builder|ChartmogulInvoice whereChartmogulDataId($value)
 * @method static Builder|ChartmogulInvoice whereChartmogulInvoiceId($value)
 * @method static Builder|ChartmogulInvoice whereCreatedAt($value)
 * @method static Builder|ChartmogulInvoice whereId($value)
 * @method static Builder|ChartmogulInvoice whereInvoiceDueAt($value)
 * @method static Builder|ChartmogulInvoice whereInvoiceRaisedAt($value)
 * @method static Builder|ChartmogulInvoice whereIsTransactionCreated($value)
 * @method static Builder|ChartmogulInvoice whereTeamId($value)
 * @method static Builder|ChartmogulInvoice whereUpdatedAt($value)
 *
 * @mixin Eloquent
 */
class ChartmogulInvoice extends Model
{
    protected $guarded = ['id'];

    protected $casts = [
        'invoice_raised_at' => 'datetime',
        'invoice_due_at' => 'datetime',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function data(): BelongsTo
    {
        return $this->belongsTo(ChartmogulData::class);
    }
}
