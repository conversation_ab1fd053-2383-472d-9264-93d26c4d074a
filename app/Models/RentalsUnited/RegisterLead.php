<?php

namespace App\Models\RentalsUnited;

use App\Flavors\DefaultFlavor;
use App\Flavors\NinjaFlavor;
use App\Traits\NinjaNotifiable;

class RegisterLead
{
    use NinjaNotifiable;

    public function __construct(
        public string $name,
        public string $email,
        public string $company,
    ) {
    }

    public function config(): NinjaFlavor
    {
        return new DefaultFlavor();
    }
}
