<?php

namespace App\Models;

use App\DTO\Team\TeamRegistrationData;
use Illuminate\Database\Eloquent\Model;
use Kreait\Laravel\Firebase\Facades\Firebase;

/**
 * App\Models\TeamRegistrationSteps.
 *
 * @property string $token
 * @property string $status
 * @property int|null $provider_id
 * @property int|null $flavor_id
 * @property string|null $account_id
 * @property array|null $provider_authentication
 * @property \Spatie\LaravelData\Contracts\BaseData|null $account_data
 * @property string|null $team_id
 * @property string|null $team_name
 * @property string|null $team_email
 * @property string|null $owner_name
 * @property string|null $owner_email
 * @property int|null $rentals
 * @property int|null $team_exists
 * @property int|null $billed_by_rn
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps query()
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereAccountData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereBilledByRn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereFlavorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereOwnerEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereOwnerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereProviderAuthentication($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereRentals($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereTeamEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereTeamExists($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereTeamName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamRegistrationSteps whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class TeamRegistrationSteps extends Model
{
    protected $primaryKey = 'token';

    protected $keyType = 'string';

    public $incrementing = false;

    public $guarded = [];

    protected $casts = [
        'provider_authentication' => 'array',
        'account_data' => TeamRegistrationData::class,
    ];

    public function setTeamExists()
    {
        $this->status = 'PROVIDER_AUTHORIZATION_TEAM_EXISTS';
    }

    public function setError()
    {
        $this->status = 'PROVIDER_ERROR';
    }

    public function setProviderAuthorized()
    {
        $this->status = 'PROVIDER_AUTHORIZED';
    }

    public function setTeamCreated(Team $team)
    {
        $this->team_id = $team->id;
        $this->status = 'TEAM_CREATED';
    }

    // We should call it from the listener.
    public function setTeamSubscribed(Team $team)
    {
        $this->team_id = $team->id;
        $this->status = 'TEAM_SUBSCRIBED';
    }

    public function setAskingForProvider()
    {
        $this->status = 'ASKING_FOR_PROVIDER';
    }

    protected static function booted(): void
    {
        static::created(function (TeamRegistrationSteps $data) {
            $path = "registration/$data->token";
            Firebase::database()->getReference($path)->set(['status' => $data->status]);
        });

        static::updated(function (TeamRegistrationSteps $data) {
            $path = "registration/$data->token";
            Firebase::database()->getReference($path)->set(['status' => $data->status]);
        });

        static::deleted(function (TeamRegistrationSteps $data) {
            $path = "registration/$data->token";
            Firebase::database()->getReference($path)->remove();
        });
    }
}
