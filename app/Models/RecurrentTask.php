<?php

namespace App\Models;

use App\Casts\CarbonIntervalCaster;
use App\Enum\RecurrentTaskPeriodicityTypeEnum;
use App\Enum\TeamRolesEnum;
use App\Query\RecurrentTaskQuery;
use Exception;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * App\Models\RecurrentTask.
 *
 * @property int $id
 * @property int $team_id
 * @property int $checklist_id
 * @property bool $active
 * @property string $title
 * @property string|null $description
 * @property bool $is_team_task
 * @property int $priority
 * @property bool $needs_supervision
 * @property TeamRolesEnum|null $role
 * @property int|null $assignee_id
 * @property int|null $supervisor_id
 * @property Carbon $recurrent_start_date
 * @property Carbon|null $recurrent_end_date
 * @property Carbon|null $next_recurrent
 * @property RecurrentTaskPeriodicityTypeEnum $periodicity_type
 * @property int $periodicity_value
 * @property int|null $periodicity_on
 * @property \Carbon\CarbonInterval $time_start
 * @property \Carbon\CarbonInterval $duration
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\User|null $assignee
 * @property-read \App\Models\Checklist $checklist
 * @property-read int $periodicity_on_day_num
 * @property-read int|null $periodicity_on_week_day
 * @property-read int|null $periodicity_on_week_nth
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\RecurrentTaskRental> $recurrentTaskRentals
 * @property-read int|null $recurrent_task_rentals_count
 * @property-read \App\Models\User|null $supervisor
 * @property-read \App\Models\Team $team
 *
 * @method static \Database\Factories\RecurrentTaskFactory factory($count = null, $state = [])
 * @method static RecurrentTaskQuery|RecurrentTask newModelQuery()
 * @method static RecurrentTaskQuery|RecurrentTask newQuery()
 * @method static RecurrentTaskQuery|RecurrentTask onChecklist(int $checklist)
 * @method static RecurrentTaskQuery|RecurrentTask onTeam(\App\Models\Team $team)
 * @method static RecurrentTaskQuery|RecurrentTask onlyActive()
 * @method static \Illuminate\Database\Eloquent\Builder|RecurrentTask onlyTrashed()
 * @method static RecurrentTaskQuery|RecurrentTask query()
 * @method static RecurrentTaskQuery|RecurrentTask whereActive($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereAssigneeId($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereChecklistId($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereCreatedAt($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereDeletedAt($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereDescription($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereDuration($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereId($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereIsTeamTask($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereNeedsSupervision($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereNextRecurrent($value)
 * @method static RecurrentTaskQuery|RecurrentTask wherePeriodicityOn($value)
 * @method static RecurrentTaskQuery|RecurrentTask wherePeriodicityType($value)
 * @method static RecurrentTaskQuery|RecurrentTask wherePeriodicityValue($value)
 * @method static RecurrentTaskQuery|RecurrentTask wherePriority($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereRecurrentEndDate($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereRecurrentStartDate($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereRole($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereSupervisorId($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereTeamId($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereTimeStart($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereTitle($value)
 * @method static RecurrentTaskQuery|RecurrentTask whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RecurrentTask withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|RecurrentTask withoutTrashed()
 *
 * @mixin \Eloquent
 */
class RecurrentTask extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'recurrent_tasks';

    protected $guarded = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [
        'active' => 'boolean',
        'needs_supervision' => 'boolean',
        'role' => TeamRolesEnum::class,
        'is_team_task' => 'boolean',
        'periodicity_type' => RecurrentTaskPeriodicityTypeEnum::class,
        'recurrent_start_date' => 'datetime:Y-m-d',
        'recurrent_end_date' => 'datetime:Y-m-d',
        'next_recurrent' => 'datetime:Y-m-d',
        'time_start' => CarbonIntervalCaster::class,
        'duration' => CarbonIntervalCaster::class,
    ];

    protected function periodicityOnDayNum(): Attribute
    {
        return Attribute::make(
            get: function ($value, $attributes): int {
                if ($attributes['periodicity_type'] == RecurrentTaskPeriodicityTypeEnum::MONTHLY_ON_DAY_NUM->value) {
                    return $attributes['periodicity_on'];
                }

                return $value; // We should never get here because this attribute is only called for monthly_on_day. $value is null because this is not related to a column
            });
    }

    protected function periodicityOnWeekDay(): Attribute
    {
        return Attribute::make(
            get: function ($value, $attributes): ?int {
                if ($attributes['periodicity_type'] == RecurrentTaskPeriodicityTypeEnum::DAILY->value) {
                    return null;
                }
                if ($attributes['periodicity_type'] == RecurrentTaskPeriodicityTypeEnum::MONTHLY_ON_DAY_NUM->value) {
                    return null;
                }

                return ($attributes['periodicity_on'] % 10) % 7;
            });
    }

    protected function periodicityOnWeekNth(): Attribute
    {
        return Attribute::make(
            get: function ($value, $attributes): ?int {
                if ($attributes['periodicity_type'] == RecurrentTaskPeriodicityTypeEnum::DAILY->value) {
                    return null;
                }
                if ($attributes['periodicity_type'] == RecurrentTaskPeriodicityTypeEnum::MONTHLY_ON_DAY_NUM->value) {
                    return null;
                }

                return intdiv($attributes['periodicity_on'], 10);
            });
    }

    public function attachRentals(?array $rentals): self
    {
        $rentals = $rentals ?? [];

        RecurrentTaskRental::query()
            ->onTeam($this->team_id)
            ->whereRecurrentTaskId($this->id)
            ->whereRentalIdNotIn($rentals)
            ->delete();

        $existingRentals = RecurrentTaskRental::query()
            ->onTeam($this->team_id)
            ->whereRecurrentTaskId($this->id)
            ->pluck('rental_id');
        $newRentals = collect($rentals)->diff($existingRentals);

        $new_recurrent_tasks = collect();
        foreach ($newRentals as $rental) {
            $new_recurrent_tasks->add(['team_id' => $this->team_id, 'recurrent_task_id' => $this->id, 'rental_id' => $rental]);
        }
        RecurrentTaskRental::insert($new_recurrent_tasks->toArray());

        return $this;
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function checklist(): BelongsTo
    {
        return $this->belongsTo(Checklist::class);
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function recurrentTaskRentals(): HasMany
    {
        return $this
            ->hasMany(RecurrentTaskRental::class, 'recurrent_task_id', 'id')
            ->whereHas('rental');
    }

    public function newEloquentBuilder($query): RecurrentTaskQuery
    {
        return new RecurrentTaskQuery($query);
    }

    public function getNextDate(): ?Carbon
    {
        $this->next_recurrent = $this->calculateNextDate();

        if ($this->hasEnd() && $this->next_recurrent->isAfter($this->recurrent_end_date)) {
            $this->next_recurrent = null;
        }

        return $this->next_recurrent;
    }

    private function calculateNextDate(): Carbon
    {
        $date = $this->next_recurrent;
        if ($this->periodicity_type == RecurrentTaskPeriodicityTypeEnum::DAILY) {
            return $date->addDays($this->periodicity_value);
        }
        if ($this->periodicity_type == RecurrentTaskPeriodicityTypeEnum::WEEKLY) {
            return $date->addWeeks($this->periodicity_value);
        }
        if ($this->periodicity_type == RecurrentTaskPeriodicityTypeEnum::MONTHLY_ON_DAY_NUM) {
            return $date->addMonthsNoOverflow($this->periodicity_value)
                ->setUnitNoOverflow('day', $this->periodicity_on_day_num, 'month');
        }
        if ($this->periodicity_type == RecurrentTaskPeriodicityTypeEnum::MONTHLY_ON_WEEK_DAY) {
            $date->addMonthsNoOverflow($this->periodicity_value);
            if ($this->periodicity_on_week_nth == 0) {
                return $date->lastOfMonth($this->periodicity_on_week_day);
            }

            return $date->nthOfMonth($this->periodicity_on_week_nth, $this->periodicity_on_week_day);
        }

        throw new Exception('Not able to calculate next date in a recurrent task');
    }

    public function getFirstDate(): ?Carbon
    {
        $firstDay = $this->recurrent_start_date;
        $date = $firstDay->copy();
        if ($this->periodicity_type == RecurrentTaskPeriodicityTypeEnum::DAILY) {
            return $date;
        }
        if ($this->periodicity_type == RecurrentTaskPeriodicityTypeEnum::WEEKLY) {
            return $date->subDay()->next($this->periodicity_on_week_day);
        }
        if ($this->periodicity_type == RecurrentTaskPeriodicityTypeEnum::MONTHLY_ON_DAY_NUM) {
            $date = $date->setUnitNoOverflow('day', $this->periodicity_on_day_num, 'month');
            if ($date < $firstDay) {
                $date = $date->addMonthNoOverflow();
            }

            return $date;
        }
        if ($this->periodicity_type == RecurrentTaskPeriodicityTypeEnum::MONTHLY_ON_WEEK_DAY) {
            if ($this->periodicity_on_week_nth == 0) {
                return $date->lastOfMonth($this->periodicity_on_week_day);
            }

            return $date->nthOfMonth($this->periodicity_on_week_nth, $this->periodicity_on_week_day);
        }

        return null;
    }

    private function hasEnd(): bool
    {
        return ! is_null($this->recurrent_end_date);
    }
}
