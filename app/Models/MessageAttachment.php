<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\MessageAttachment.
 *
 * @property int $id
 * @property int $team_id
 * @property int $message_id
 * @property int|null $external_id
 * @property string $name
 * @property string $url
 * @property int $provider_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Message $message
 * @property-read \App\Models\Team $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment query()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereMessageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereUrl($value)
 *
 * @mixin \Eloquent
 */
class MessageAttachment extends Model
{
    use HasFactory;

    protected $guarded = [
        'id',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }
}
