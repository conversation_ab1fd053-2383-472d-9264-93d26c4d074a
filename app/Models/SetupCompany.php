<?php

namespace App\Models;

use App\Actions\Setups\CreateSetupRentalsFromExistingTeamAction;
use App\Actions\Teams\TeamDetails\GetTeamDetailsAction;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Laravel\Nova\Actions\Actionable;
use Throwable;

/**
 * App\Models\SetupCompany.
 *
 * @property int $id
 * @property int $company_id
 * @property int|null $team_id
 * @property string $status
 * @property string|null $login_hash
 * @property string|null $currency
 * @property string|null $locale
 * @property int|null $airbnb_markup
 * @property int|null $booking_markup
 * @property int|null $team_details_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read Collection<int, \Laravel\Nova\Actions\ActionEvent> $actions
 * @property-read int|null $actions_count
 * @property-read \App\Models\Company $company
 * @property-read Collection<int, \App\Models\SetupAirbnbHost> $setupAirbnbHosts
 * @property-read int|null $setup_airbnb_hosts_count
 * @property-read Collection<int, \App\Models\SetupCredential> $setupCredentials
 * @property-read int|null $setup_credentials_count
 * @property-read Collection<int, \App\Models\SetupRental> $setupRentals
 * @property-read int|null $setup_rentals_count
 * @property-read \App\Models\Team|null $team
 * @property-read \App\Models\TeamDetails|null $teamDetails
 *
 * @method static Builder|SetupCompany newModelQuery()
 * @method static Builder|SetupCompany newQuery()
 * @method static Builder|SetupCompany onlyTrashed()
 * @method static Builder|SetupCompany query()
 * @method static Builder|SetupCompany whereAirbnbMarkup($value)
 * @method static Builder|SetupCompany whereBookingMarkup($value)
 * @method static Builder|SetupCompany whereCompanyId($value)
 * @method static Builder|SetupCompany whereCreatedAt($value)
 * @method static Builder|SetupCompany whereCurrency($value)
 * @method static Builder|SetupCompany whereDeletedAt($value)
 * @method static Builder|SetupCompany whereId($value)
 * @method static Builder|SetupCompany whereLocale($value)
 * @method static Builder|SetupCompany whereLoginHash($value)
 * @method static Builder|SetupCompany whereStatus($value)
 * @method static Builder|SetupCompany whereTeamDetailsId($value)
 * @method static Builder|SetupCompany whereTeamId($value)
 * @method static Builder|SetupCompany whereUpdatedAt($value)
 * @method static Builder|SetupCompany withTrashed()
 * @method static Builder|SetupCompany withoutTrashed()
 *
 * @mixin Eloquent
 */
class SetupCompany extends Model
{
    use Actionable;
    use SoftDeletes;

    public const PENDING = 'pending';
    public const ONGOING = 'ongoing';
    public const COMPLETED = 'completed';
    public const STATUS_NAMES = [
        self::PENDING => 'Pending',
        self::ONGOING => 'Ongoing',
        self::COMPLETED => 'Completed',
    ];

    public $guarded = [
        'id',
        'created_at',
        'updated_at',
    ];

    public function setupRentals(): HasMany
    {
        return $this->hasMany(SetupRental::class);
    }

    public function setupCredentials(): HasMany
    {
        return $this->hasMany(SetupCredential::class);
    }

    public function setupAirbnbHosts(): HasMany
    {
        return $this->hasMany(SetupAirbnbHost::class);
    }

    public function teamDetails(): BelongsTo
    {
        return $this->belongsTo(TeamDetails::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    protected static function booted()
    {
        static::creating(function (SetupCompany $setupCompany) {
            $setupCompany->login_hash = Str::random(20);
        });

        static::created(function (SetupCompany $setupCompany) {
            // Link setup to existing team
            if (! is_null($setupCompany->company->team_id)) {
                $team = $setupCompany->company->team;
                $setupCompany->team_id = $team->id;
                $setupCompany->team_details_id = GetTeamDetailsAction::run($team)->id;
                $setupCompany->save();
            }
        });

        static::updating(function (SetupCompany $setupCompany) {
            try {
                // Link an existing setup to a team
                if ($setupCompany->isDirty('team_id')) {
                    pnLog('[SetupCompany] Team ID changed for company '.$setupCompany->id, $setupCompany->team_id);

                    $team = Team::findOrFail($setupCompany->team_id);
                    $setupCompany->setupRentals()->update(['team_id' => $team->id]);

                    // Ideally team should be new, so map the current team details to the new team. However:
                    // If the setup has no team details, then assign the team details to the setup
                    if (is_null($setupCompany->team_details_id)) {
                        $setupCompany->team_details_id = $team->teamDetails?->id;
                    } // If the setup has team details, we assign it if team has no team details, otherwise we log the error.
                    elseif (is_null($team->teamDetails) && ! is_null($setupCompany->teamDetails)) {
                        $setupCompany->teamDetails->team_id = $team->id;
                        $setupCompany->teamDetails->save();
                    } else {
                        pnLog('[SetupCompany] Team details already exist for setup team', $team->id);
                    }
                    CreateSetupRentalsFromExistingTeamAction::run($setupCompany);
                }
            } catch (Throwable $e) {
                $scId = $setupCompany->id;
                $eMsg = $e->getMessage();
                pnLog("[SetupCompany] Error updating team ID for company $scId ($eMsg)");

                report($e);
            }
        });

        static::deleting(function (SetupCompany $setupCompany) {
            $setupCompany->setupRentals()->delete();
            $setupCompany->setupCredentials()->delete();
            $setupCompany->setupAirbnbHosts()->delete();
        });
    }
}
