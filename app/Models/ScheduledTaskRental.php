<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\ScheduledTaskRental.
 *
 * @property int $id
 * @property int $team_id
 * @property int $scheduled_task_id
 * @property int $rental_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Rental $rental
 * @property-read \App\Models\ScheduledTask $scheduledTask
 *
 * @method static Builder|ScheduledTaskRental newModelQuery()
 * @method static Builder|ScheduledTaskRental newQuery()
 * @method static Builder|ScheduledTaskRental query()
 * @method static Builder|ScheduledTaskRental whereCreatedAt($value)
 * @method static Builder|ScheduledTaskRental whereId($value)
 * @method static Builder|ScheduledTaskRental whereRentalId($value)
 * @method static Builder|ScheduledTaskRental whereScheduledTaskId($value)
 * @method static Builder|ScheduledTaskRental whereTeamId($value)
 * @method static Builder|ScheduledTaskRental whereUpdatedAt($value)
 *
 * @mixin Eloquent
 */
class ScheduledTaskRental extends NinjaProviderModel
{
    protected $table = 'scheduled_tasks_rentals';

    protected $guarded = ['id'];

    /**
     * Get the task.
     */
    public function scheduledTask(): BelongsTo
    {
        return $this->belongsTo(ScheduledTask::class);
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id']);
    }
}
