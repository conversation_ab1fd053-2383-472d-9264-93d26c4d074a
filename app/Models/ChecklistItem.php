<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\ChecklistItem.
 *
 * @property int $id
 * @property int $team_id
 * @property int $checklist_id
 * @property int $order
 * @property string|null $title
 * @property string|null $description
 * @property bool $picture_required
 * @property bool $allow_multiple_pictures
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Team $team
 *
 * @method static Builder|ChecklistItem checklist(int $checklist)
 * @method static \Database\Factories\ChecklistItemFactory factory($count = null, $state = [])
 * @method static Builder|ChecklistItem newModelQuery()
 * @method static Builder|ChecklistItem newQuery()
 * @method static Builder|ChecklistItem query()
 * @method static Builder|ChecklistItem team(int $team)
 * @method static Builder|ChecklistItem whereAllowMultiplePictures($value)
 * @method static Builder|ChecklistItem whereChecklistId($value)
 * @method static Builder|ChecklistItem whereCreatedAt($value)
 * @method static Builder|ChecklistItem whereDescription($value)
 * @method static Builder|ChecklistItem whereId($value)
 * @method static Builder|ChecklistItem whereOrder($value)
 * @method static Builder|ChecklistItem wherePictureRequired($value)
 * @method static Builder|ChecklistItem whereTeamId($value)
 * @method static Builder|ChecklistItem whereTitle($value)
 * @method static Builder|ChecklistItem whereUpdatedAt($value)
 *
 * @mixin Eloquent
 */
class ChecklistItem extends Model
{
    use HasFactory;

    protected $table = 'checklist_items';

    protected $guarded = ['id'];

    protected $casts = [
        'picture_required' => 'boolean',
        'allow_multiple_pictures' => 'boolean',
    ];

    /**
     * TODO Can we delete this?
     * Scope a query to filter by team.
     */
    public function scopeTeam(Builder $query, int $team): Builder
    {
        return $query->where('team_id', $team);
    }

    /**
     * Filter by checklist id.
     */
    public function scopeChecklist(Builder $query, int $checklist): Builder
    {
        return $query->where('checklist_id', $checklist);
    }

    /**
     * Get the team.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }
}
