<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\OpenAiThread.
 *
 * @property int $id
 * @property string|null $thread_id
 * @property string|null $user_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|OpenAiThread newModelQuery()
 * @method static Builder|OpenAiThread newQuery()
 * @method static Builder|OpenAiThread query()
 * @method static Builder|OpenAiThread whereCreatedAt($value)
 * @method static Builder|OpenAiThread whereId($value)
 * @method static Builder|OpenAiThread whereThreadId($value)
 * @method static Builder|OpenAiThread whereUpdatedAt($value)
 * @method static Builder|OpenAiThread whereUserId($value)
 *
 * @mixin Eloquent
 */
class OpenAiThread extends Model
{
    protected $guarded = ['id'];
}
