<?php

namespace App\Models;

use App\DTO\ChannelManager\RentalDiscountsValuesData;
use App\Enum\RentalDiscountEnum;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Support\Carbon;
use Spatie\LaravelData\DataCollection;

/**
 * App\Models\RentalDiscount.
 *
 * @property int $id
 * @property int $team_id
 * @property int $order
 * @property int $rental_id
 * @property RentalDiscountEnum $type
 * @property string $name
 * @property Carbon|null $start_from
 * @property Carbon|null $valid_until
 * @property \Spatie\LaravelData\DataCollection $discounts
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static EloquentBuilder|RentalDiscount newModelQuery()
 * @method static EloquentBuilder|RentalDiscount newQuery()
 * @method static EloquentBuilder|RentalDiscount query()
 * @method static EloquentBuilder|RentalDiscount whereCreatedAt($value)
 * @method static EloquentBuilder|RentalDiscount whereDiscounts($value)
 * @method static EloquentBuilder|RentalDiscount whereId($value)
 * @method static EloquentBuilder|RentalDiscount whereName($value)
 * @method static EloquentBuilder|RentalDiscount whereOrder($value)
 * @method static EloquentBuilder|RentalDiscount whereRentalId($value)
 * @method static EloquentBuilder|RentalDiscount whereStartFrom($value)
 * @method static EloquentBuilder|RentalDiscount whereTeamId($value)
 * @method static EloquentBuilder|RentalDiscount whereType($value)
 * @method static EloquentBuilder|RentalDiscount whereUpdatedAt($value)
 * @method static EloquentBuilder|RentalDiscount whereValidUntil($value)
 *
 * @mixin \Eloquent
 */
class RentalDiscount extends NinjaProviderModel
{
    protected $guarded = ['id'];

    protected $casts = [
        'type' => RentalDiscountEnum::class,
        'start_from' => 'datetime',
        'valid_until' => 'datetime',
        'discounts' => DataCollection::class.':'.RentalDiscountsValuesData::class,
    ];

    public function getLastMinuteDiscount(Carbon $date, Carbon $asDate): ?float
    {
        if ($date < $this->start_from && (is_null($this->valid_until) || $date > $this->valid_until)) {
            return null;
        }
        $daysInAdvance = $date->diffInDays($asDate);
        /** @var RentalDiscountsValuesData $valid */
        $valid = $this->discounts->first(fn (RentalDiscountsValuesData $val) => $val->days >= $daysInAdvance);

        return $valid->discount ?? 0;
    }

    public function getLongStayDiscount(Carbon $date, int $bookingLength): ?float
    {
        if ($date < $this->start_from && (is_null($this->valid_until) || $date > $this->valid_until)) {
            return null;
        }
        /** @var RentalDiscountsValuesData $valid */
        $valid = $this->discounts->first(fn (RentalDiscountsValuesData $val) => $bookingLength >= $val->days);

        return $valid->discount ?? 0;
    }
}
