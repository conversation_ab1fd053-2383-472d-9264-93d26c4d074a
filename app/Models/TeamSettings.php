<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\TeamSettings.
 *
 * @property int $id
 * @property int $team_id
 * @property bool $active
 * @property int|null $sms_price
 * @property int|null $scan_price
 * @property int|null $upscale_price
 * @property int|null $content_ai_price
 * @property int|null $ai_price
 * @property string|null $booking_engine_commission_percentage Decimal of 5 digits with 3 decimal places
 * @property string|null $upsales_commission_percentage Decimal of 5 digits with 3 decimal places
 * @property string|null $white_label_id
 * @property string|null $slack_webhook_url
 * @property bool $alert_notifications
 * @property bool $booking_created_notifications
 * @property bool $booking_canceled_notifications
 * @property bool $payment_created_notification
 * @property bool $picture_added_notification
 * @property bool $comment_created_notification
 * @property bool $check_in_out_time_modified_notification
 * @property bool $task_notification
 * @property bool $lead_created_notification
 * @property bool $new_message_notification
 * @property bool $upsale_purchased_notification
 * @property bool $model_179
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Team $team
 *
 * @method static \Database\Factories\TeamSettingsFactory factory($count = null, $state = [])
 * @method static Builder|TeamSettings newModelQuery()
 * @method static Builder|TeamSettings newQuery()
 * @method static Builder|TeamSettings query()
 * @method static Builder|TeamSettings whereActive($value)
 * @method static Builder|TeamSettings whereAiPrice($value)
 * @method static Builder|TeamSettings whereAlertNotifications($value)
 * @method static Builder|TeamSettings whereBookingCanceledNotifications($value)
 * @method static Builder|TeamSettings whereBookingCreatedNotifications($value)
 * @method static Builder|TeamSettings whereBookingEngineCommissionPercentage($value)
 * @method static Builder|TeamSettings whereCheckInOutTimeModifiedNotification($value)
 * @method static Builder|TeamSettings whereCommentCreatedNotification($value)
 * @method static Builder|TeamSettings whereContentAiPrice($value)
 * @method static Builder|TeamSettings whereCreatedAt($value)
 * @method static Builder|TeamSettings whereId($value)
 * @method static Builder|TeamSettings whereLeadCreatedNotification($value)
 * @method static Builder|TeamSettings whereModel179($value)
 * @method static Builder|TeamSettings whereNewMessageNotification($value)
 * @method static Builder|TeamSettings wherePaymentCreatedNotification($value)
 * @method static Builder|TeamSettings wherePictureAddedNotification($value)
 * @method static Builder|TeamSettings whereScanPrice($value)
 * @method static Builder|TeamSettings whereSlackWebhookUrl($value)
 * @method static Builder|TeamSettings whereSmsPrice($value)
 * @method static Builder|TeamSettings whereTaskNotification($value)
 * @method static Builder|TeamSettings whereTeamId($value)
 * @method static Builder|TeamSettings whereUpdatedAt($value)
 * @method static Builder|TeamSettings whereUpsalePurchasedNotification($value)
 * @method static Builder|TeamSettings whereUpsalesCommissionPercentage($value)
 * @method static Builder|TeamSettings whereUpscalePrice($value)
 * @method static Builder|TeamSettings whereWhiteLabelId($value)
 *
 * @mixin Eloquent
 */
class TeamSettings extends Model
{
    use HasFactory;

    const ALERTS = 'alert_notifications';

    const BOOKING_CREATED = 'booking_created_notifications';

    const BOOKING_CANCELED = 'booking_canceled_notifications';

    const PAYMENT = 'payment_created_notification';

    const PICTURE = 'picture_added_notification';

    const COMMENT = 'comment_created_notification';

    const CHECK_IN = 'check_in_out_time_modified_notification';

    const TASK = 'task_notification';

    const LEAD_CREATED = 'lead_created_notification';

    const NEW_MESSAGE = 'new_message_notification';

    const UPSALE_PURCHASED = 'upsale_purchased_notification';

    const DEFAULT_SMS_PRICE = 9;
    const DEFAULT_SCAN_PRICE = 15;
    const DEFAULT_UPSCALE_PRICE = 50;
    const DEFAULT_AI_PRICE = 1;
    const DEFAULT_CONTENT_AI_PRICE = 1;
    const DEFAULT_BOOKING_ENGINE_COMMISSION_PERCENTAGE = '0.5';
    const DEFAULT_UPSALE_COMMISSION_PERCENTAGE = '1';

    protected $table = 'team_settings';

    protected $casts = [
        'active' => 'boolean',
        'alert_notifications' => 'boolean',
        'booking_created_notifications' => 'boolean',
        'booking_canceled_notifications' => 'boolean',
        'payment_created_notification' => 'boolean',
        'picture_added_notification' => 'boolean',
        'comment_created_notification' => 'boolean',
        'check_in_out_time_modified_notification' => 'boolean',
        'task_notification' => 'boolean',
        'lead_created_notification' => 'boolean',
        'new_message_notification' => 'boolean',
        'upsale_purchased_notification' => 'boolean',
        'model_179' => 'boolean',
    ];

    protected $fillable = [
        'team_id',
        'active',
        'sms_price',
        'scan_price',
        'upscale_price',
        'booking_engine_commission_percentage',
        'upsales_commission_percentage',
        'slack_webhook_url',
        'alert_notifications',
        'booking_created_notifications',
        'booking_canceled_notifications',
        'payment_created_notification',
        'picture_added_notification',
        'comment_created_notification',
        'check_in_out_time_modified_notification',
        'task_notification',
        'lead_created_notification',
        'new_message_notification',
        'upsale_purchased_notification',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function getSmsPriceInCents(): int
    {
        return $this->sms_price ?? self::DEFAULT_SMS_PRICE;
    }

    public function getScanPriceInCents(): int
    {
        return $this->scan_price ?? self::DEFAULT_SCAN_PRICE;
    }

    public function getUpscalePriceInCents(): int
    {
        return $this->upscale_price ?? self::DEFAULT_UPSCALE_PRICE;
    }

    public function getAiPriceInCents(): int
    {
        return $this->ai_price ?? self::DEFAULT_AI_PRICE;
    }

    public function getContentAiPriceInCents(): int
    {
        return $this->content_ai_price ?? self::DEFAULT_CONTENT_AI_PRICE;
    }

    public function getBookingEngineCommissionPercentage(): string
    {
        return $this->booking_engine_commission_percentage ?? self::DEFAULT_BOOKING_ENGINE_COMMISSION_PERCENTAGE;
    }

    public function getUpsalesCommissionPercentage(): string
    {
        return $this->upsales_commission_percentage ?? self::DEFAULT_UPSALE_COMMISSION_PERCENTAGE;
    }
}
