<?php

namespace App\Models;

use App\Enum\PayoutDetailStrategyEnum;
use App\Enum\PayoutDetailTypeEnum;
use Awobaz\Compoships\Database\Eloquent\Relations\BelongsTo;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Carbon;

/**
 * App\Models\PayoutDetail.
 *
 * @property int $id
 * @property int $team_id
 * @property int $payout_id
 * @property int|null $rental_id
 * @property string $name
 * @property float $value
 * @property string $original_type
 * @property float $original_value
 * @property string $original_name
 * @property PayoutDetailStrategyEnum $strategy
 * @property float $strategy_amount
 * @property PayoutDetailTypeEnum $type
 * @property bool $custom_item
 * @property int $sort_value
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Payout $payout
 * @property-read \App\Models\Rental|null $rental
 *
 * @method static \Database\Factories\PayoutDetailFactory factory($count = null, $state = [])
 * @method static Builder|PayoutDetail newModelQuery()
 * @method static Builder|PayoutDetail newQuery()
 * @method static Builder|PayoutDetail query()
 * @method static Builder|PayoutDetail whereCreatedAt($value)
 * @method static Builder|PayoutDetail whereCustomItem($value)
 * @method static Builder|PayoutDetail whereId($value)
 * @method static Builder|PayoutDetail whereName($value)
 * @method static Builder|PayoutDetail whereOriginalName($value)
 * @method static Builder|PayoutDetail whereOriginalType($value)
 * @method static Builder|PayoutDetail whereOriginalValue($value)
 * @method static Builder|PayoutDetail wherePayoutId($value)
 * @method static Builder|PayoutDetail whereRentalId($value)
 * @method static Builder|PayoutDetail whereSortValue($value)
 * @method static Builder|PayoutDetail whereStrategy($value)
 * @method static Builder|PayoutDetail whereStrategyAmount($value)
 * @method static Builder|PayoutDetail whereTeamId($value)
 * @method static Builder|PayoutDetail whereType($value)
 * @method static Builder|PayoutDetail whereUpdatedAt($value)
 * @method static Builder|PayoutDetail whereValue($value)
 *
 * @mixin Eloquent
 */
class PayoutDetail extends NinjaProviderModel
{
    use HasFactory;

    protected $table = 'payout_details';

    protected $fillable = [
        'team_id',
        'payout_id',
        'name',
        'value',
        'type',
        'original_type', //TODO: this could be casted using PayoutDetailTypeEnum
        'original_value',
        'original_name',
        'strategy',
        'strategy_amount',
        'rental_id',
        'custom_item',
        'sort_value',
    ];

    protected $casts = [
        'strategy' => PayoutDetailStrategyEnum::class,
        'type' => PayoutDetailTypeEnum::class,
        'custom_item' => 'boolean',
    ];

    public function payout(): BelongsTo
    {
        return $this->belongsTo(Payout::class);
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id'])->withTrashed();
    }
}
