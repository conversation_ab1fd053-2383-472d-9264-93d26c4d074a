<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\FacturesLocalCarbonera.
 *
 * @property int $id
 * @property int $invoice_id
 * @property Carbon|null $invoice_date
 * @property Carbon|null $from
 * @property Carbon|null $to
 * @property float $vat
 * @property float $amount
 * @property float $total
 * @property string $concept
 * @property string $invoice_url
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|FacturesLocalCarbonera newModelQuery()
 * @method static Builder|FacturesLocalCarbonera newQuery()
 * @method static Builder|FacturesLocalCarbonera query()
 * @method static Builder|FacturesLocalCarbonera whereAmount($value)
 * @method static Builder|FacturesLocalCarbonera whereConcept($value)
 * @method static Builder|FacturesLocalCarbonera whereCreatedAt($value)
 * @method static Builder|FacturesLocalCarbonera whereFrom($value)
 * @method static Builder|FacturesLocalCarbonera whereId($value)
 * @method static Builder|FacturesLocalCarbonera whereInvoiceDate($value)
 * @method static Builder|FacturesLocalCarbonera whereInvoiceId($value)
 * @method static Builder|FacturesLocalCarbonera whereInvoiceUrl($value)
 * @method static Builder|FacturesLocalCarbonera whereTo($value)
 * @method static Builder|FacturesLocalCarbonera whereTotal($value)
 * @method static Builder|FacturesLocalCarbonera whereUpdatedAt($value)
 * @method static Builder|FacturesLocalCarbonera whereVat($value)
 *
 * @mixin Eloquent
 */
class FacturesLocalCarbonera extends Model
{
    protected $table = 'factures_local_carbonera';

    protected $fillable = [
        'invoice_id',
        'invoice_date',
        'from',
        'to',
        'vat',
        'amount',
        'total',
        'concept',
        'invoice_url',
    ];

    protected $casts = [
        'invoice_date' => 'datetime',
        'from' => 'datetime',
        'to' => 'datetime',
    ];
}
