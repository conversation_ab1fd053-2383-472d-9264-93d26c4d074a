<?php

namespace App\Models;

use App\Actions\Accounting\ExpandedSettlements\GetExpandedSettlementAction;
use App\DTO\Settlements\ExpandedSettlementDTO;
use App\Query\SettlementQuery;
use Eloquent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
use Staudenmeir\EloquentJsonRelations\Relations\BelongsToJson;

/**
 * App\Models\Settlement.
 *
 * @property int $id
 * @property int $team_id
 * @property string $currency
 * @property string $name
 * @property Carbon|null $start
 * @property Carbon|null $end
 * @property array $rentals
 * @property array|null $sources
 * @property bool $on_check_out Whether the bookings of the settlement should be pulled based on check-out or check-in
 * @property bool $on_net_income
 * @property int $author_id
 * @property int|null $scheduled_settlement_id If this settlement is created by a scheduled settlement, it's id
 * @property bool $to_review
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read \App\Models\User $author
 * @property-read Collection<int, \App\Models\PayoutDetail> $payoutDetails
 * @property-read int|null $payout_details_count
 * @property-read Collection<int, \App\Models\Payout> $payouts
 * @property-read int|null $payouts_count
 * @property-read \App\Models\ScheduledSettlement|null $scheduledSettlement
 * @property-read \App\Models\Team $team
 * @property-read Collection<int, \App\Models\Payee> $teamPayees
 * @property-read int|null $team_payees_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Rental[] $settlementRentals
 * @property-read int|null $settlement_rentals_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Rental[] $settlementSlimRentals
 * @property-read int|null $settlement_slim_rentals_count
 *
 * @method static SettlementQuery|Settlement between(\Illuminate\Support\Carbon $start, \Illuminate\Support\Carbon $end)
 * @method static SettlementQuery|Settlement newModelQuery()
 * @method static SettlementQuery|Settlement newQuery()
 * @method static SettlementQuery|Settlement onTeam(\App\Models\Team|int $team)
 * @method static \Illuminate\Database\Eloquent\Builder|Settlement onlyTrashed()
 * @method static SettlementQuery|Settlement query()
 * @method static SettlementQuery|Settlement whereAuthorId($value)
 * @method static SettlementQuery|Settlement whereCreatedAt($value)
 * @method static SettlementQuery|Settlement whereCurrency($value)
 * @method static SettlementQuery|Settlement whereDeletedAt($value)
 * @method static SettlementQuery|Settlement whereEnd($value)
 * @method static SettlementQuery|Settlement whereId($value)
 * @method static SettlementQuery|Settlement whereName($value)
 * @method static SettlementQuery|Settlement whereOnCheckOut($value)
 * @method static SettlementQuery|Settlement whereOnNetIncome($value)
 * @method static SettlementQuery|Settlement whereRental(int $rentalId)
 * @method static SettlementQuery|Settlement whereRentals($value)
 * @method static SettlementQuery|Settlement whereScheduledSettlementId($value)
 * @method static SettlementQuery|Settlement whereSources($value)
 * @method static SettlementQuery|Settlement whereStart($value)
 * @method static SettlementQuery|Settlement whereTeamId($value)
 * @method static SettlementQuery|Settlement whereToReview($value)
 * @method static SettlementQuery|Settlement whereUpdatedAt($value)
 * @method static SettlementQuery|Settlement withAuthor()
 * @method static SettlementQuery|Settlement withPayoutDetails()
 * @method static SettlementQuery|Settlement withPayouts()
 * @method static SettlementQuery|Settlement withPayoutsAndPayees()
 * @method static SettlementQuery|Settlement withRentals(int $team_id)
 * @method static SettlementQuery|Settlement withSlimRentals(int $team_id)
 * @method static \Illuminate\Database\Eloquent\Builder|Settlement withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Settlement withoutTrashed()
 *
 * @mixin Eloquent
 */
class Settlement extends Model
{
    use HasJsonRelationships;
    use SoftDeletes;

    protected $table = 'settlements';

    protected $fillable = [
        'team_id',
        'name',
        'currency',
        'start',
        'end',
        'rentals',
        'sources',
        'on_check_out',
        'on_net_income',
        'author_id',
        'scheduled_settlement_id',
        'to_review',
    ];

    protected $casts = [
        'start' => 'datetime',
        'end' => 'datetime',
        'rentals' => 'array',
        'sources' => 'array',
        'on_check_out' => 'boolean',
        'on_net_income' => 'boolean',
        'to_review' => 'boolean',
    ];

    public function delete()
    {
        foreach ($this->payouts as $payout) {
            $payout->delete();
        }

        return parent::delete();
    }

    public function path(): string
    {
        $tId = $this->team_id;
        $id = $this->id;
        $name = $this->getZipExportName();

        return "settlements/{$tId}/{$id}/$name";
    }

    public function getZipExportName(): string
    {
        return str_slug($this->name, '_').'.zip';
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function payouts(): HasMany
    {
        return $this->hasMany(Payout::class, 'settlement_id', 'id');
    }

    public function teamPayees(): HasMany
    {
        return $this->hasMany(Payee::class, 'team_id', 'team_id');
    }

    public function payoutDetails(): HasManyThrough
    {
        return $this->hasManyThrough(PayoutDetail::class, Payout::class, 'settlement_id', 'payout_id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id', 'id');
    }

    public function scheduledSettlement(): BelongsTo
    {
        return $this->belongsTo(ScheduledSettlement::class);
    }

    public function settlementRentals(): BelongsToJson // Gets the rentals of this Settlement (uses different name from the rentals attribute)
    {
        // The belongsToJson relationship doesn't allow us yet to relate to the rentals table by id and team_id. Therefore, we have to filter out
        // results from other team_id after using this relationship. If this relationship is used with lazy loading (once we already have a settlement
        // instance, "$this->team_id" will exist therefore we filter results with the "where" clause below.
        // However, if we are using this relationship with eager loading (we have no settlement instance), the SettlementQuery builder calls this
        // relationship and then filters out the results from other teams (check the withRentals() method).
        // I have opened an issue to the package, because right now this is not efficient as we are querying more records than needed and then filtering them.
        // Keep an eye on the issue to see if they develop it: https://github.com/staudenmeir/eloquent-json-relations/issues/82
        return $this->belongsToJson(Rental::class, 'rentals')
            ->when($this->team_id, fn ($query) => $query->where('team_id', $this->team_id))
            ->withTrashed() // We may have deleted rentals in Settlements! Tested and works, although IDE don't get it.
            ->with('innerPoster');
    }

    public function settlementSlimRentals(): BelongsToJson // Gets the rentals of this Settlement (uses different name from the rentals attribute)
    {
        // TODO: This uses SLIM_VERSION_ATTRIBUTES
        return $this->settlementRentals()
            ->select(Rental::SLIM_VERSION_ATTRIBUTES);
        // Selecting columns after querying for the relationship is not ideal but there is no way to
        // query for this relationship specifying the columns. settlementRentals:id,team_id,... seems to not work.
    }

    /**
     * @return SettlementQuery<Settlement>
     */
    public function newEloquentBuilder($query): SettlementQuery
    {
        return new SettlementQuery($query);
    }

    /**
     * Get the expanded version of a settlement with all the calculations.
     */
    public function expand(bool $isSimple = false, ?Team $team = null): ExpandedSettlementDTO
    {
        return GetExpandedSettlementAction::run(
            settlement: $this,
            team: $team ?? $this->team,
            isSimple: $isSimple,
        );
    }
}
