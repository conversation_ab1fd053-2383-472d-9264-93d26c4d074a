<?php

namespace App\Models;

use Closure;
use DateInterval;
use DatePeriod;
use Exception;
use Illuminate\Support\Carbon;

/** TODO: this class can probably be deleted and use CarbonPeriod official class, or extend it.*/
class CarbonPeriod
{
    /**
     * Create a new CarbonPeriod instance.
     */
    public function __construct(
        protected ?Carbon $startDate = null,
        protected ?Carbon $endDate = null
    ) {
        $this->endDate ??= $this->startDate?->copy()->addDay()->startOfDay();

        return $this->order();
    }

    private function order(): self
    {
        if ($this->startDate > $this->endDate) {
            $tmp = clone $this->startDate;
            $this->startDate = clone $this->endDate;
            $this->endDate = clone $tmp;
            unset($tmp);
        }

        return $this;
    }

    /**
     * Create a CarbonPeriod from start of this day to end of this day.
     */
    public static function thisDay(): static
    {
        return new static(Carbon::today(), Carbon::now()->endOfDay());
    }

    /**
     * Create a CarbonPeriod of last full week.
     */
    public static function lastWeek(): static
    {
        $start = Carbon::parse('last week');

        return new static($start->startOfDay(), $start->copy()
            ->addWeek());
    }

    /**
     * Create a CarbonPeriod of last full month.
     */
    public static function lastMonth(): static
    {
        $start = Carbon::parse('first day of last month');

        return new static($start->startOfDay(), $start->copy()
            ->addMonth());
    }

    /**
     * Create a CarbonPeriod from closest monday to today.
     */
    public static function thisWeek(): static
    {
        $start = Carbon::today();
        $end = Carbon::tomorrow();
        if ($start->dayOfWeek !== Carbon::MONDAY) {
            $start->modify('last monday');
        }

        return new static($start, $end);
    }

    /**
     * Create a CarbonPeriod instance from first day of current month to current date.
     */
    public static function thisMonth(): static
    {
        $start = Carbon::parse('first day of now');

        return new static($start->startOfDay(), $start->copy()
            ->tomorrow());
    }

    /**
     * Create a CarbonPeriod instance from 1 January of current year to current date.
     */
    public static function thisYear(): self
    {
        $start = Carbon::parse('1 January');

        return new static($start, $start->copy()
            ->tomorrow());
    }

    public static function subtractYears(self $period, int $years): self
    {
        $s = $period->start()->subYears($years);
        $e = $period->end()->subYears($years);

        return self::instance($s, $e);
    }

    /**
     * Get the start date from the instance.
     */
    public function start(): Carbon
    {
        return $this->startDate->copy();
    }

    /**
     * Get the end date from the instance.
     */
    public function end(): ?Carbon
    {
        return $this->endDate->copy();
    }

    /**
     * This is an alias for the constructor
     * that allows better fluent syntax as it allows you to do
     * CarbonPeriod::instance()->fn() rather than
     * (new CarbonPeriod())->fn().
     */
    public static function instance(?Carbon $startDate = null, ?Carbon $endDate = null): static
    {
        return new static($startDate, $endDate);
    }

    public static function subtractQuarters(self $period, int $quarters): self
    {
        $s = $period->start()->subMonths($quarters * 3);
        $e = $period->end()->subMonths($quarters * 3)->endOfMonth();

        return self::instance($s, $e);
    }

    public static function subtractMonths(self $period, int $months): self
    {
        $s = $period->start()->subMonths($months);
        $e = $period->end()->subMonths($months)->endOfMonth();

        return self::instance($s, $e);
    }

    public static function subtractDays(self $period, int $days): self
    {
        $x = $period->start()->subDays(($days * $period->lengthInDays()) + $days);
        $e = $period->end()->subDays(($days * $period->lengthInDays()) + $days);

        return self::instance($x, $e);
    }

    /**
     * Get the difference between start and end dates of the period in days.
     */
    public function lengthInDays(): int
    {
        return $this->startDate->diffInDays($this->endDate);
    }

    public function isYear(): bool
    {
        $startOfYear = $this->start()->startOfYear();
        $endOfYear = $this->end()->endOfYear();

        return $startOfYear->eq($this->start()) && $endOfYear->eq($this->end());
    }

    public function isQuarter(): bool
    {
        $startOfQuarter = $this->start()->startOfQuarter();
        $endOfQuarter = $this->end()->endOfQuarter();

        return $startOfQuarter->eq($this->start()) && $endOfQuarter->eq($this->end());
    }

    public function isMonth(): bool
    {
        $startOfMonth = $this->start()->startOfMonth();
        $endOfMonth = $this->end()->endOfMonth();

        return $startOfMonth->eq($this->start()) && $endOfMonth->eq($this->end());
    }

    /**
     * Set the internal iterator with week interval for the instance.
     *
     * @return $this|Carbon
     */
    public function eachWeeks(int $weeks, Closure $callback, bool $onlyFullWeek = false)
    {
        if ($this->lengthInWeeks() > 0) {
            return $this->each(new DateInterval("P{$weeks}W"),
                function (self $period) use ($weeks, $callback, $onlyFullWeek) {
                    if (! $onlyFullWeek || $period->lengthInWeeks() === $weeks) {
                        call_user_func_array($callback, func_get_args());
                    }
                });
        }

        return $this;
    }

    /**
     * Get the difference between start and end dates of the period in weeks.
     */
    public function lengthInWeeks(): int
    {
        return $this->startDate->diffInWeeks($this->endDate);
    }

    /**
     * Set the internal iterator with interval for the instance.
     */
    public function each(DateInterval $interval, Closure $callback): self
    {
        $period = new static($this->start(), $this->start()->add($interval));
        do {
            $callback(new static($period->start(), $period->end() > $this->endDate ? $this->endDate : $period->end()));
        } while ($period->add($interval)->start() < $this->endDate);

        return $this;
    }

    /**
     * Add \DateInterval to the instance.
     */
    public function add(DateInterval $interval): static
    {
        $this->startDate->add($interval);
        $this->endDate->add($interval);

        return $this;
    }

    /**
     * Set the internal iterator with month interval for the instance.
     */
    public function eachMonths(int $months, Closure $callback, bool $onlyFullMonth = false): self
    {
        if ($this->lengthInMonths() > 0) {
            return $this->each(new DateInterval("P{$months}M"),
                function (self $period) use ($months, $callback, $onlyFullMonth) {
                    if (! $onlyFullMonth || $period->lengthInMonths() === $months) {
                        call_user_func_array($callback, func_get_args());
                    }
                });
        }

        return $this;
    }

    /**
     * Get the difference between start and end dates of the period in months.
     */
    public function lengthInMonths(): int
    {
        return $this->startDate->diffInMonths($this->endDate);
    }

    /**
     * Set the internal iterator for day of week for the instance.
     */
    public function eachDayOfWeek($dayOfWeek, Closure $callback): self
    {
        $start = $this->startDate->copy();
        if ($start->dayOfWeek !== $dayOfWeek) {
            $start->next($dayOfWeek);
        }

        if ($start < $this->endDate) {
            $period = new static($start, $this->endDate);

            $period->eachDays(Carbon::DAYS_PER_WEEK, function (self $period) use ($callback) {
                $callback(new static($period->start(), $period->start()
                    ->addDay()));
            });
        }

        return $this;
    }

    /**
     * Set the internal iterator with day interval for the instance.
     *
     * @throws Exception
     */
    public function eachDays(int $days, Closure $callback)
    {
        return $this->each(new DateInterval("P{$days}D"), $callback);
    }

    /**
     * Get the difference between start and end dates of the period in years.
     */
    public function lengthInYears(): int
    {
        return $this->startDate->diffInYears($this->endDate);
    }

    /**
     * Get the difference between start and end dates of the period in hours.
     */
    public function lengthInHours(): int
    {
        return $this->startDate->diffInHours($this->endDate);
    }

    /**
     * Get the difference between start and end dates of the period in minutes.
     */
    public function lengthInMinutes()
    {
        return $this->startDate->diffInMinutes($this->endDate);
    }

    /**
     * Get the difference between start and end dates of the period in seconds.
     */
    public function lengthInSeconds(): int
    {
        return $this->startDate->diffInSeconds($this->endDate);
    }

    /**
     * Add years to the period.
     *
     *
     * @throws Exception
     */
    public function addYears($value): CarbonPeriod
    {
        return $this->add(new DateInterval("P{$value}Y"));
    }

    /**
     * Remove years from the period.
     *
     *
     * @throws Exception
     */
    public function subYears($value): CarbonPeriod
    {
        return $this->sub(new DateInterval("P{$value}Y"));
    }

    /**
     * Sub \DateInterval from the instance.
     */
    public function sub(DateInterval $interval): static
    {
        $this->startDate->sub($interval);
        $this->endDate->sub($interval);

        return $this;
    }

    /**
     * Add months to the period.
     *
     *
     * @throws Exception
     */
    public function addMonths($value): CarbonPeriod
    {
        return $this->add(new DateInterval("P{$value}M"));
    }

    /**
     * Remove months from the period.
     *
     *
     * @throws Exception
     */
    public function subMonths($value): CarbonPeriod
    {
        return $this->sub(new DateInterval("P{$value}M"));
    }

    /**
     * Add days to the period.
     *
     *
     * @throws Exception
     */
    public function addDays($value): CarbonPeriod
    {
        return $this->add(new DateInterval("P{$value}D"));
    }

    /**
     * Remove days from the period.
     *
     *
     * @throws Exception
     */
    public function subDays($value): CarbonPeriod
    {
        return $this->sub(new DateInterval("P{$value}D"));
    }

    /**
     * Determines if the instances contains a date.
     *
     * @param  bool  $equal  Indicates if a > and < comparison should be used or <= or >=
     *
     * @see \Illuminate\Support\Carbon::between
     */
    public function contains(Carbon $date, bool $equal = true): bool
    {
        return $date->between($this->startDate, $this->endDate, $equal);
    }

    /**
     * Iterate period over each day.
     *
     *
     * @throws Exception
     */
    public function iterateDates(Closure $callback): CarbonPeriod
    {
        $interval = new DateInterval('P1D');

        $period = new DatePeriod($this->start()
            ->copy()
            ->startOfDay(), $interval, $this->end()
            ->copy()
            ->startOfDay());

        foreach ($period as $date) {
            $callback($date);
        }

        return $this;
    }

    public function quarterLabel(): string
    {
        return $this->start()->format('M').' - '.$this->end()->format('M');
    }

    public function yearLabel(): string
    {
        return $this->start()->format('Y');
    }

    public function monthLabel(): string
    {
        return $this->start()->format('M Y');
    }

    public function subtractedRange(): self
    {
        return new self($this->start()->startOfDay(), $this->end()->subHour()->endOfDay());
    }

    public function toDateTimeString(): string
    {
        return $this->start()->toDateTimeString().' <--->'.$this->end()->toDateTimeString();
    }
}
