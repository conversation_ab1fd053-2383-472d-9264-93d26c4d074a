<?php

namespace App\Models;

use App\Query\RecurrentTaskRentalQuery;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RecurrentTaskRental.
 *
 * @property int $id
 * @property int $team_id
 * @property int $recurrent_task_id
 * @property int $rental_id
 * @property-read \App\Models\RecurrentTask $recurrentTask
 * @property-read \App\Models\Rental $rental
 *
 * @method static \Database\Factories\RecurrentTaskRentalFactory factory($count = null, $state = [])
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental newModelQuery()
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental newQuery()
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental onTeam(\App\Models\Team|int $team)
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental query()
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental whereId($value)
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental whereRecurrentTaskId($value)
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental whereRentalId($value)
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental whereRentalIdIn(array $rentals)
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental whereRentalIdNotIn(array $rentals)
 * @method static RecurrentTaskRentalQuery|RecurrentTaskRental whereTeamId($value)
 *
 * @mixin \Eloquent
 */
class RecurrentTaskRental extends NinjaProviderModel
{
    use HasFactory;

    public $timestamps = false;

    public function recurrentTask(): BelongsTo
    {
        return $this->belongsTo(RecurrentTask::class);
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id']);
    }

    public function newEloquentBuilder($query): RecurrentTaskRentalQuery
    {
        return new RecurrentTaskRentalQuery($query);
    }
}
