<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\SetupAirbnbHost.
 *
 * @property int $id
 * @property int $setup_company_id
 * @property string $name
 * @property string $airbnb_user_id
 * @property bool $has_access
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SetupCompany $setupCompany
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost query()
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost whereAirbnbUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost whereHasAccess($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost whereSetupCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SetupAirbnbHost whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class SetupAirbnbHost extends Model
{
    public $guarded = [
        'id',
        'created_at',
        'updated_at',
    ];

    public $casts = [
        'has_access' => 'boolean',
    ];

    public function setupCompany(): BelongsTo
    {
        return $this->belongsTo(SetupCompany::class);
    }

    public function getFullName(): string
    {
        return $this->name.' - '.$this->airbnb_user_id;
    }
}
