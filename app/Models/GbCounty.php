<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\GbCounty.
 *
 * @property int|null $id
 * @property string|null $code
 * @property string|null $name
 * @property string|null $region_id
 *
 * @method static Builder|GbCounty newModelQuery()
 * @method static Builder|GbCounty newQuery()
 * @method static Builder|GbCounty query()
 * @method static Builder|GbCounty whereCode($value)
 * @method static Builder|GbCounty whereId($value)
 * @method static Builder|GbCounty whereName($value)
 * @method static Builder|GbCounty whereRegionId($value)
 *
 * @mixin Eloquent
 */
class GbCounty extends Model
{
    use Sushi;

    protected $rows = [
        [
            'id' => 'gb-abd',
            'code' => 'gb-abd',
            'name' => 'Aberdeenshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-abe',
            'code' => 'gb-abe',
            'name' => 'Aberdeen City',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-agb',
            'code' => 'gb-agb',
            'name' => 'Argyll and Bute',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-agy',
            'code' => 'gb-agy',
            'name' => "Isle of Anglesey (Sir Ynys M\u00f4n)",
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-ans',
            'code' => 'gb-ans',
            'name' => 'Angus',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-ant',
            'code' => 'gb-ant',
            'name' => 'Antrim',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-ard',
            'code' => 'gb-ard',
            'name' => 'Ards',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-arm',
            'code' => 'gb-arm',
            'name' => 'Armagh',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-bas',
            'code' => 'gb-bas',
            'name' => 'Bath and North East Somerset',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-bbd',
            'code' => 'gb-bbd',
            'name' => 'Blackburn with Darwen',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-bdf',
            'code' => 'gb-bdf',
            'name' => 'Bedford',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-bfs',
            'code' => 'gb-bfs',
            'name' => 'Belfast',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-bge',
            'code' => 'gb-bge',
            'name' => 'Bridgend (Pen-y-bont ar Ogwr)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-bgw',
            'code' => 'gb-bgw',
            'name' => 'Blaenau Gwent',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-bkm',
            'code' => 'gb-bkm',
            'name' => 'Buckinghamshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-bla',
            'code' => 'gb-bla',
            'name' => 'Ballymena',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-bly',
            'code' => 'gb-bly',
            'name' => 'Ballymoney',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-bmh',
            'code' => 'gb-bmh',
            'name' => 'Bournemouth',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-bnb',
            'code' => 'gb-bnb',
            'name' => 'Banbridge',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-bnh',
            'code' => 'gb-bnh',
            'name' => 'Brighton and Hove',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-bpl',
            'code' => 'gb-bpl',
            'name' => 'Blackpool',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-brc',
            'code' => 'gb-brc',
            'name' => 'Bracknell Forest',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-bst',
            'code' => 'gb-bst',
            'name' => 'Bristol',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-cam',
            'code' => 'gb-cam',
            'name' => 'Cambridgeshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-cay',
            'code' => 'gb-cay',
            'name' => 'Caerphilly (Caerffili)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-cbf',
            'code' => 'gb-cbf',
            'name' => 'Central Bedfordshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-cgn',
            'code' => 'gb-cgn',
            'name' => 'Ceredigion (Sir Ceredigion)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-cgv',
            'code' => 'gb-cgv',
            'name' => 'Craigavon',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-che',
            'code' => 'gb-che',
            'name' => 'Cheshire East',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-chw',
            'code' => 'gb-chw',
            'name' => 'Cheshire West and Chester',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-ckf',
            'code' => 'gb-ckf',
            'name' => 'Carrickfergus',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-ckt',
            'code' => 'gb-ckt',
            'name' => 'Cookstown',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-clk',
            'code' => 'gb-clk',
            'name' => 'Clackmannanshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-clr',
            'code' => 'gb-clr',
            'name' => 'Coleraine',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-cma',
            'code' => 'gb-cma',
            'name' => 'Cumbria',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-cmn',
            'code' => 'gb-cmn',
            'name' => 'Carmarthenshire (Sir Gaerfyrddin)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-con',
            'code' => 'gb-con',
            'name' => 'Cornwall',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-crf',
            'code' => 'gb-crf',
            'name' => 'Cardiff (Caerdydd)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-csr',
            'code' => 'gb-csr',
            'name' => 'Castlereagh',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-cwy',
            'code' => 'gb-cwy',
            'name' => 'Conwy',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-dal',
            'code' => 'gb-dal',
            'name' => 'Darlington',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-dby',
            'code' => 'gb-dby',
            'name' => 'Derbyshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-den',
            'code' => 'gb-den',
            'name' => 'Denbighshire (Sir Ddinbych)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-der',
            'code' => 'gb-der',
            'name' => 'Derby',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-dev',
            'code' => 'gb-dev',
            'name' => 'Devon',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-dgn',
            'code' => 'gb-dgn',
            'name' => 'Dungannon and South Tyrone',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-dgy',
            'code' => 'gb-dgy',
            'name' => 'Dumfries and Galloway',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-dnd',
            'code' => 'gb-dnd',
            'name' => 'Dundee City',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-dor',
            'code' => 'gb-dor',
            'name' => 'Dorset',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-dow',
            'code' => 'gb-dow',
            'name' => 'Down',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-dry',
            'code' => 'gb-dry',
            'name' => 'Derry',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-dur',
            'code' => 'gb-dur',
            'name' => 'Durham',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-eay',
            'code' => 'gb-eay',
            'name' => 'East Ayrshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-edh',
            'code' => 'gb-edh',
            'name' => 'Edinburgh',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-edu',
            'code' => 'gb-edu',
            'name' => 'East Dunbartonshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-eln',
            'code' => 'gb-eln',
            'name' => 'East Lothian',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-els',
            'code' => 'gb-els',
            'name' => 'Eilean Siar',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-erw',
            'code' => 'gb-erw',
            'name' => 'East Renfrewshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-ery',
            'code' => 'gb-ery',
            'name' => 'East Riding of Yorkshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-ess',
            'code' => 'gb-ess',
            'name' => 'Essex',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-esx',
            'code' => 'gb-esx',
            'name' => 'East Sussex',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-fal',
            'code' => 'gb-fal',
            'name' => 'Falkirk',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-fer',
            'code' => 'gb-fer',
            'name' => 'Fermanagh',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-fif',
            'code' => 'gb-fif',
            'name' => 'Fife',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-fln',
            'code' => 'gb-fln',
            'name' => 'Flintshire (Sir y Fflint)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-glg',
            'code' => 'gb-glg',
            'name' => 'Glasgow City',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-gls',
            'code' => 'gb-gls',
            'name' => 'Gloucestershire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-gwn',
            'code' => 'gb-gwn',
            'name' => 'Gwynedd',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-hal',
            'code' => 'gb-hal',
            'name' => 'Halton',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-ham',
            'code' => 'gb-ham',
            'name' => 'Hampshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-hef',
            'code' => 'gb-hef',
            'name' => 'Herefordshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-hld',
            'code' => 'gb-hld',
            'name' => 'Highland',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-hpl',
            'code' => 'gb-hpl',
            'name' => 'Hartlepool',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-hrt',
            'code' => 'gb-hrt',
            'name' => 'Hertfordshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-iow',
            'code' => 'gb-iow',
            'name' => 'Isle of Wight',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-ivc',
            'code' => 'gb-ivc',
            'name' => 'Inverclyde',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-ken',
            'code' => 'gb-ken',
            'name' => 'Kent',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-khl',
            'code' => 'gb-khl',
            'name' => 'Kingston upon Hull',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-lan',
            'code' => 'gb-lan',
            'name' => 'Lancashire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-lce',
            'code' => 'gb-lce',
            'name' => 'Leicester',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-lec',
            'code' => 'gb-lec',
            'name' => 'Leicestershire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-lin',
            'code' => 'gb-lin',
            'name' => 'Lincolnshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-lmv',
            'code' => 'gb-lmv',
            'name' => 'Limavady',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-lrn',
            'code' => 'gb-lrn',
            'name' => 'Larne',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-lsb',
            'code' => 'gb-lsb',
            'name' => 'Lisburn',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-lut',
            'code' => 'gb-lut',
            'name' => 'Luton',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-mdb',
            'code' => 'gb-mdb',
            'name' => 'Middlesbrough',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-mdw',
            'code' => 'gb-mdw',
            'name' => 'Medway',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-mft',
            'code' => 'gb-mft',
            'name' => 'Magherafelt',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-mik',
            'code' => 'gb-mik',
            'name' => 'Milton Keynes',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-mln',
            'code' => 'gb-mln',
            'name' => 'Midlothian',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-mon',
            'code' => 'gb-mon',
            'name' => 'Monmouthshire (Sir Fynwy)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-mry',
            'code' => 'gb-mry',
            'name' => 'Moray',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-mty',
            'code' => 'gb-mty',
            'name' => 'Merthyr Tydfil (Merthyr Tudful)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-myl',
            'code' => 'gb-myl',
            'name' => 'Moyle',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-nay',
            'code' => 'gb-nay',
            'name' => 'North Ayrshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-nbl',
            'code' => 'gb-nbl',
            'name' => 'Northumberland',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-ndn',
            'code' => 'gb-ndn',
            'name' => 'North Down',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-nel',
            'code' => 'gb-nel',
            'name' => 'North East Lincolnshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-nfk',
            'code' => 'gb-nfk',
            'name' => 'Norfolk',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-ngm',
            'code' => 'gb-ngm',
            'name' => 'Nottingham',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-nlk',
            'code' => 'gb-nlk',
            'name' => 'North Lanarkshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-nln',
            'code' => 'gb-nln',
            'name' => 'North Lincolnshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-nsm',
            'code' => 'gb-nsm',
            'name' => 'North Somerset',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-nta',
            'code' => 'gb-nta',
            'name' => 'Newtownabbey',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-nth',
            'code' => 'gb-nth',
            'name' => 'Northamptonshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-ntl',
            'code' => 'gb-ntl',
            'name' => 'Neath Port Talbot (Castell-nedd Port Talbot)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-ntt',
            'code' => 'gb-ntt',
            'name' => 'Nottinghamshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-nwp',
            'code' => 'gb-nwp',
            'name' => 'Newport (Casnewydd)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-nyk',
            'code' => 'gb-nyk',
            'name' => 'North Yorkshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-nym',
            'code' => 'gb-nym',
            'name' => 'Newry and Mourne District',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-omh',
            'code' => 'gb-omh',
            'name' => 'Omagh',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-ork',
            'code' => 'gb-ork',
            'name' => 'Orkney Islands',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-oxf',
            'code' => 'gb-oxf',
            'name' => 'Oxfordshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-pem',
            'code' => 'gb-pem',
            'name' => 'Pembrokeshire (Sir Benfro)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-pkn',
            'code' => 'gb-pkn',
            'name' => 'Perth and Kinross',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-ply',
            'code' => 'gb-ply',
            'name' => 'Plymouth',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-pol',
            'code' => 'gb-pol',
            'name' => 'Poole',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-por',
            'code' => 'gb-por',
            'name' => 'Portsmouth',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-pow',
            'code' => 'gb-pow',
            'name' => 'Powys',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-pte',
            'code' => 'gb-pte',
            'name' => 'Peterborough',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-rcc',
            'code' => 'gb-rcc',
            'name' => 'Redcar and Cleveland',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-rct',
            'code' => 'gb-rct',
            'name' => 'Rhondda Cynon Taff',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-rdg',
            'code' => 'gb-rdg',
            'name' => 'Reading',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-rfw',
            'code' => 'gb-rfw',
            'name' => 'Renfrewshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-rut',
            'code' => 'gb-rut',
            'name' => 'Rutland',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-say',
            'code' => 'gb-say',
            'name' => 'South Ayrshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-scb',
            'code' => 'gb-scb',
            'name' => 'Scottish Borders',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-sfk',
            'code' => 'gb-sfk',
            'name' => 'Suffolk',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-sgc',
            'code' => 'gb-sgc',
            'name' => 'South Gloucestershire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-shr',
            'code' => 'gb-shr',
            'name' => 'Shropshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-slg',
            'code' => 'gb-slg',
            'name' => 'Slough',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-slk',
            'code' => 'gb-slk',
            'name' => 'South Lanarkshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-som',
            'code' => 'gb-som',
            'name' => 'Somerset',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-sos',
            'code' => 'gb-sos',
            'name' => 'Southend-on-Sea',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-sry',
            'code' => 'gb-sry',
            'name' => 'Surrey',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-stb',
            'code' => 'gb-stb',
            'name' => 'Strabane',
            'region_id' => 'gb-nir',
        ],
        [
            'id' => 'gb-ste',
            'code' => 'gb-ste',
            'name' => 'Stoke-on-Trent',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-stg',
            'code' => 'gb-stg',
            'name' => 'Stirling',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-sth',
            'code' => 'gb-sth',
            'name' => 'Southampton',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-sts',
            'code' => 'gb-sts',
            'name' => 'Staffordshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-stt',
            'code' => 'gb-stt',
            'name' => 'Stockton-on-Tees',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-swa',
            'code' => 'gb-swa',
            'name' => 'Swansea (Abertawe)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-swd',
            'code' => 'gb-swd',
            'name' => 'Swindon',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-tfw',
            'code' => 'gb-tfw',
            'name' => 'Telford and Wrekin',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-thr',
            'code' => 'gb-thr',
            'name' => 'Thurrock',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-tob',
            'code' => 'gb-tob',
            'name' => 'Torbay',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-tof',
            'code' => 'gb-tof',
            'name' => 'Torfaen (Tor-faen)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-vgl',
            'code' => 'gb-vgl',
            'name' => 'Vale of Glamorgan (Bro Morgannwg)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-war',
            'code' => 'gb-war',
            'name' => 'Warwickshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-wbk',
            'code' => 'gb-wbk',
            'name' => 'West Berkshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-wdu',
            'code' => 'gb-wdu',
            'name' => 'West Dunbartonshire',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-wil',
            'code' => 'gb-wil',
            'name' => 'Wiltshire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-wln',
            'code' => 'gb-wln',
            'name' => 'West Lothian',
            'region_id' => 'gb-sct',
        ],
        [
            'id' => 'gb-wnm',
            'code' => 'gb-wnm',
            'name' => 'Windsor and Maidenhead',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-wok',
            'code' => 'gb-wok',
            'name' => 'Wokingham',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-wor',
            'code' => 'gb-wor',
            'name' => 'Worcestershire',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-wrt',
            'code' => 'gb-wrt',
            'name' => 'Warrington',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-wrx',
            'code' => 'gb-wrx',
            'name' => 'Wrexham (Wrecsam)',
            'region_id' => 'gb-wls',
        ],
        [
            'id' => 'gb-wsx',
            'code' => 'gb-wsx',
            'name' => 'West Sussex',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-yor',
            'code' => 'gb-yor',
            'name' => 'York',
            'region_id' => 'gb-eng',
        ],
        [
            'id' => 'gb-zet',
            'code' => 'gb-zet',
            'name' => 'Shetland Islands',
            'region_id' => 'gb-sct',
        ],
    ];
}
