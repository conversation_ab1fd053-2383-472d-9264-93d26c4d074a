<?php

namespace App\Models;

use DateTimeInterface;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\BookingTax.
 *
 * @property int $id
 * @property int $team_id
 * @property int $booking_id
 * @property int|null $tax_id
 * @property float|null $amount
 * @property string|null $name
 * @property float|null $percentage
 * @property bool $tax_included_in_price
 * @property string|null $taxable_type
 * @property int $taxable_id
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property Carbon|null $canceled_at
 * @property-read \App\Models\Team $team
 *
 * @method static \Database\Factories\BookingTaxFactory factory($count = null, $state = [])
 * @method static Builder|BookingTax newModelQuery()
 * @method static Builder|BookingTax newQuery()
 * @method static Builder|BookingTax query()
 * @method static Builder|BookingTax whereAmount($value)
 * @method static Builder|BookingTax whereBookingId($value)
 * @method static Builder|BookingTax whereCanceledAt($value)
 * @method static Builder|BookingTax whereCreatedAt($value)
 * @method static Builder|BookingTax whereId($value)
 * @method static Builder|BookingTax whereName($value)
 * @method static Builder|BookingTax wherePercentage($value)
 * @method static Builder|BookingTax whereTaxId($value)
 * @method static Builder|BookingTax whereTaxIncludedInPrice($value)
 * @method static Builder|BookingTax whereTaxableId($value)
 * @method static Builder|BookingTax whereTaxableType($value)
 * @method static Builder|BookingTax whereTeamId($value)
 * @method static Builder|BookingTax whereUpdatedAt($value)
 *
 * @mixin Eloquent
 */
class BookingTax extends NinjaProviderModel
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'booking_tax';

    protected $fillable = [
        'id',
        'team_id',
        'booking_id',
        'tax_id',
        'amount',
        'name',
        'percentage',
        'tax_included_in_price',
    ];

    protected $casts = [
        'tax_included_in_price' => 'boolean',
        'canceled_at' => 'datetime',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Prepare a date for array / JSON serialization.
     */
    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }
}
