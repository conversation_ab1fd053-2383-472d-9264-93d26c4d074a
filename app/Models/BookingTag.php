<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\BookingTag.
 *
 * @property int $id
 * @property int $team_id
 * @property string $name
 * @property string|null $color
 * @property string|null $logo
 * @property int $created_at
 * @property int $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag query()
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag whereLogo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|BookingTag withoutTrashed()
 *
 * @mixin \Eloquent
 */
class BookingTag extends NinjaProviderModel
{
    use SoftDeletes;

    public $timestamps = false;

    protected $fillable = [
        'id',
        'team_id',
        'name',
        'color',
        'logo',
        'created_at',
        'updated_at',
    ];
}
