<?php

namespace App\Models;

use App\Actions\Support\Errors\GetParentClassAction;
use App\Actions\Tasks\TaskIsBeingUpdatedAction;
use App\Enum\TeamRolesEnum;
use App\Http\Resources\SlimUserResource;
use App\Query\TaskQuery;
use Eloquent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

use function once;

/**
 * App\Models\Task.
 *
 * @property int $id
 * @property int $team_id
 * @property int|null $booking_id
 * @property int|null $rental_id
 * @property int|null $scheduled_task_id
 * @property int|null $recurrent_task_id
 * @property TeamRolesEnum|null $role
 * @property int|null $assignee_id
 * @property int|null $supervisor_id
 * @property string|null $title
 * @property string|null $description
 * @property string $can_start
 * @property string $due_date
 * @property int $priority
 * @property Carbon|null $start_from
 * @property Carbon|null $finish_before
 * @property Carbon|null $start_at
 * @property Carbon|null $end_at
 * @property bool $has_timer
 * @property int $max_time
 * @property Carbon|null $completed_at
 * @property Carbon|null $supervised_at
 * @property bool $remove_on_cancellation
 * @property bool $manually_modified
 * @property array|null $manually_modified_attributes
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property string|null $deleted_by
 * @property-read \App\Models\User|null $assignee
 * @property-read \App\Models\Booking|null $booking
 * @property-read Collection<int, \App\Models\TaskItem> $items
 * @property-read int|null $items_count
 * @property-read \App\Models\Rental|null $rental
 * @property-read \App\Models\ScheduledTask $scheduled
 * @property-read \App\Models\User|null $supervisor
 * @property-read \App\Models\Team $team
 *
 * @method static TaskQuery|Task assignedTo(\App\Models\User|int $user)
 * @method static TaskQuery|Task assignedToUser(\App\Models\User|int $user)
 * @method static TaskQuery|Task assignedToUsersOrRoles(?array $users, ?array $roles)
 * @method static \Database\Factories\TaskFactory factory($count = null, $state = [])
 * @method static TaskQuery|Task filterByJobTitle(?string $job_title = null)
 * @method static TaskQuery|Task filterByScheduledTasks(?array $scheduled_ids = [])
 * @method static TaskQuery|Task filterByScheduledTasksAndJobTitle(?string $job_title = null, ?array $scheduled_ids = [])
 * @method static TaskQuery|Task filterByScheduledTasksOrJobTitle(?string $job_title = null, ?array $scheduled_ids = [])
 * @method static TaskQuery|Task filterByUsersAndRoles(?array $users, ?array $roles)
 * @method static TaskQuery|Task finishingBefore(?\Illuminate\Support\Carbon $date)
 * @method static TaskQuery|Task finishingOnOrAfter(?\Illuminate\Support\Carbon $date)
 * @method static TaskQuery|Task finishingOnOrBefore(?\Illuminate\Support\Carbon $date)
 * @method static TaskQuery|Task newModelQuery()
 * @method static TaskQuery|Task newQuery()
 * @method static TaskQuery|Task onRentals(array|int $rentals)
 * @method static TaskQuery|Task onRentalsOrAdmin(array|int $rentals, bool $includeAdmin)
 * @method static TaskQuery|Task onTeam(\App\Models\Team|int $team)
 * @method static \Illuminate\Database\Eloquent\Builder|Task onlyTrashed()
 * @method static TaskQuery|Task orWhereAdminTask()
 * @method static TaskQuery|Task query()
 * @method static TaskQuery|Task softDeleteWithDeletedBy(string $deleted_by)
 * @method static TaskQuery|Task startingAfter(?\Illuminate\Support\Carbon $date)
 * @method static TaskQuery|Task startingOnOrAfter(?\Illuminate\Support\Carbon $date)
 * @method static TaskQuery|Task startingOnOrBefore(?\Illuminate\Support\Carbon $date)
 * @method static TaskQuery|Task supervisedBy(\App\Models\User|int $user)
 * @method static TaskQuery|Task whereAdminTask()
 * @method static TaskQuery|Task whereAssigneeId($value)
 * @method static TaskQuery|Task whereBooking(\App\Models\Booking|int $booking)
 * @method static TaskQuery|Task whereBookingDoesNotExistOnRental()
 * @method static TaskQuery|Task whereBookingId($value)
 * @method static TaskQuery|Task whereBookingIsNotNull()
 * @method static TaskQuery|Task whereBookingIsNull()
 * @method static TaskQuery|Task whereCanStart($value)
 * @method static TaskQuery|Task whereCompleted()
 * @method static TaskQuery|Task whereCompletedAt($value)
 * @method static TaskQuery|Task whereCreatedAt($value)
 * @method static TaskQuery|Task whereDeletedAt($value)
 * @method static TaskQuery|Task whereDeletedBy($value)
 * @method static TaskQuery|Task whereDescription($value)
 * @method static TaskQuery|Task whereDueDate($value)
 * @method static TaskQuery|Task whereEndAt($value)
 * @method static TaskQuery|Task whereFinishBefore($value)
 * @method static TaskQuery|Task whereHasTimer($value)
 * @method static TaskQuery|Task whereId($value)
 * @method static TaskQuery|Task whereIncomplete()
 * @method static TaskQuery|Task whereManuallyModified($value)
 * @method static TaskQuery|Task whereManuallyModifiedAttributes($value)
 * @method static TaskQuery|Task whereMaxTime($value)
 * @method static TaskQuery|Task whereNotSupervised()
 * @method static TaskQuery|Task wherePriority($value)
 * @method static TaskQuery|Task whereRecurrentTask(\App\Models\RecurrentTask|int $task)
 * @method static TaskQuery|Task whereRecurrentTaskId($value)
 * @method static TaskQuery|Task whereRemoveOnCancellation($value)
 * @method static TaskQuery|Task whereRental(\App\Models\Rental|int $rental)
 * @method static TaskQuery|Task whereRentalId($value)
 * @method static TaskQuery|Task whereRole($value)
 * @method static TaskQuery|Task whereScheduledTask(\App\Models\ScheduledTask|int $task)
 * @method static TaskQuery|Task whereScheduledTaskId($value)
 * @method static TaskQuery|Task whereScheduledTaskIsNotNull()
 * @method static TaskQuery|Task whereStartAt($value)
 * @method static TaskQuery|Task whereStartFrom($value)
 * @method static TaskQuery|Task whereSupervised()
 * @method static TaskQuery|Task whereSupervisedAt($value)
 * @method static TaskQuery|Task whereSupervisorId($value)
 * @method static TaskQuery|Task whereTeamId($value)
 * @method static TaskQuery|Task whereTitle($value)
 * @method static TaskQuery|Task whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Task withoutTrashed()
 *
 * @mixin Eloquent
 */
class Task extends NinjaProviderModel
{
    use HasFactory;
    use SoftDeletes {
        SoftDeletes::forceDelete as forceDeleteOfSoftDeletes;
    }

    // Relative start options
    const START_BEFORE_CI_BUT_AFTER_PREVIOUS_CO_MAX_48H = 'before-CI-but-after-prev-CO-max-48h';

    const START_AFTER_CI = 'after-CI';

    const START_BEFORE_CO_BUT_AFTER_CI_MAX_48H = 'before-CO-but-after-CI-max-48h';

    const START_AFTER_CO = 'after-CO';

    const START_CUSTOM = 'custom';

    const START_OPTIONS = [
        self::START_BEFORE_CI_BUT_AFTER_PREVIOUS_CO_MAX_48H,
        self::START_AFTER_CI,
        self::START_BEFORE_CO_BUT_AFTER_CI_MAX_48H,
        self::START_AFTER_CO,
        self::START_CUSTOM,
    ];

    // Relative end options
    const END_BEFORE_CI = 'before-CI';

    const END_AFTER_CI = 'after-CI';

    const END_BEFORE_CO = 'before-CO';

    const END_BEFORE_NEXT_CI = 'before-next-CI';

    const END_CUSTOM = 'custom';

    const END_OPTIONS = [
        self::END_BEFORE_CI,
        self::END_AFTER_CI,
        self::END_BEFORE_CO,
        self::END_BEFORE_NEXT_CI,
        self::END_CUSTOM,
    ];

    protected $table = 'tasks';

    protected $guarded = ['id'];

    protected $casts = [
        'role' => TeamRolesEnum::class,
        'start_from' => 'datetime',
        'finish_before' => 'datetime',
        'start_at' => 'datetime',
        'end_at' => 'datetime',
        'completed_at' => 'datetime',
        'supervised_at' => 'datetime',
        'remove_on_cancellation' => 'boolean',
        'create_for_blocks' => 'boolean',
        'has_timer' => 'boolean',
        'manually_modified' => 'boolean',
        'manually_modified_attributes' => 'array',
    ];

    private $users;

    protected static function boot()
    {
        parent::boot();
        // We are not allowed to change stuff that has been manually modified... So make sure this is the case
        self::updating(fn (Task $task) => TaskIsBeingUpdatedAction::run($task));
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function scheduled(): BelongsTo
    {
        return $this->belongsTo(ScheduledTask::class);
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class, ['booking_id', 'team_id'], ['id', 'team_id'])->withoutGlobalScopes();
    }

    public function rental(): BelongsTo
    {
        return $this
            ->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id'])
            ->withTrashed();
    }

    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(TaskItem::class)->orderBy('order');
    }

    public function delete(): ?bool
    {
        // This is a soft delete. Should we delete children?
        // $this->items->each(fn (TaskItem $ti) => $ti->delete());

        $parentClass = GetParentClassAction::run();
        $this->deleted_by = "Task Deleted from the Task model. Called from $parentClass.";
        $this->save();

        return parent::delete();
    }

    public function forceDelete(): ?bool
    {
        $this->items->each(fn (TaskItem $ti) => $ti->forceDelete());

        // Save the most relevant information from this task for further investigation in the future, as we have sometimes customers claiming why his task was deleted
        $forceDeletedTask = new ForceDeletedTask();
        $forceDeletedTask->team_id = $this->team_id;
        $forceDeletedTask->booking_id = $this->booking_id;
        $forceDeletedTask->rental_id = $this->rental_id;
        $forceDeletedTask->scheduled_task_id = $this->scheduled_task_id;
        $forceDeletedTask->recurrent_task_id = $this->recurrent_task_id;
        $forceDeletedTask->role = $this->role;
        $forceDeletedTask->assignee_id = $this->assignee_id;
        $forceDeletedTask->title = $this->title;

        $backtrace = collect(debug_backtrace(limit: 5))->map(fn (array $trace) => [
            'file' => $trace['file'],
            'line' => $trace['line'],
            'function' => $trace['function'],
        ]);

        $forceDeletedTask->delete_stacktrace = $backtrace;
        $forceDeletedTask->was_deleted_at = now();
        $forceDeletedTask->save();

        return $this->forceDeleteOfSoftDeletes(); // This ensures we call the trait, which is the one which really forces the delete
    }

    public function fillForScheduled(ScheduledTask $task, $includeUsers = true, $includeDates = true): self
    {
        if ($includeUsers) {
            $this->role = $task->role;
            $this->assignee_id = $task->assignee_id;
            $this->supervisor_id = $task->supervisor_id;
        }

        if ($includeDates) {
            $this->can_start = $task->can_start;
            $this->due_date = $task->due_date;
        }

        $this->title = $task->title;
        $this->description = $task->description;
        $this->has_timer = $task->has_timer;
        $this->max_time = $task->max_time;
        $this->remove_on_cancellation = $task->remove_on_cancellation;
        $this->priority = $task->priority;

        return $this;
    }

    public function fillForRecurrent(RecurrentTask $task, Carbon $date, $includeUsers = true): self
    {
        if ($includeUsers) {
            $this->role = $task->role;
            $this->assignee_id = $task->assignee_id;
            $this->supervisor_id = $task->supervisor_id;
        }

        $this->start_from = $date->copy()->copyTime($task->time_start);
        $this->finish_before = $this->start_from->copy()->add($task->duration);
        $this->max_time = $task->duration->hours;
        $this->can_start = self::START_CUSTOM;
        $this->due_date = self::START_CUSTOM;

        $this->title = $task->title;
        $this->description = $task->description;
        $this->priority = $task->priority;
        $this->remove_on_cancellation = false;

        return $this;
    }

    public function canCompleteInDate(Carbon $date): bool
    {
        $start = $date->copy()->startOfDay();
        $end = $date->copy()->endOfDay();

        return $this->finish_before->greaterThanOrEqualTo($start) && $this->start_from->lessThanOrEqualTo($end);
    }

    public function dataForTasks(Carbon $date, Carbon $first, Carbon $last, bool $assignee = true): array
    {
        $start = $this->start_from->toDateString();
        $radius_left = true;
        $radius_right = true;
        $margin_left = 0;
        $width = null;
        $absolute_left = 0;

        if ($this->start_from->lt($first) && $this->finish_before->gt($last)) {
            $radius_left = false;
            $radius_right = false;
            $minutes = $first->diffInMinutes($last);
            $width = $this->pixels($minutes);
        } // The task started before the month started.
        elseif ($this->start_from->lt($first)) {
            // Calculate the rest of the days only.
            $minutes = $date->diffInMinutes($this->finish_before);
            $width = $this->pixels($minutes);
            $radius_left = false;
        } // If It starts today
        elseif ($start == $date->toDateString()) {
            // Calculate the elapsed time from the task start and the start of the day.
            $minutes_from_start_of_day = $date->copy()
                ->startOfDay()
                ->diffInMinutes($this->start_from);
            $minutes_from_Start_of_month = $first->copy()
                ->startOfDay()
                ->diffInMinutes($this->start_from);
            $margin_left = $this->pixels($minutes_from_start_of_day);

            //
            if ($this->finish_before->lte($last)) {
                $duration_of_task_in_minutes = $this->start_from->diffInMinutes($this->finish_before);
                $width = $this->pixels($duration_of_task_in_minutes);
            } else {
                $duration_of_task_in_minutes = $this->start_from->diffInMinutes($last);
                $width = $this->pixels($duration_of_task_in_minutes);
                $radius_right = false;
            }

            $absolute_left = $this->pixels($minutes_from_Start_of_month);
        }

        $end_at_from_left_width = ($width >= 45 ? $width : 45);
        $end_at_from_left = $absolute_left + $end_at_from_left_width;
        //        $total_margin_from_left = $margin_left == 0 ? 0 : $this->pixels($first->copy()->diffInMinutes($start));

        $tr = [
            'radius_left' => $radius_left,
            'radius_right' => $radius_right,
            'margin_left' => $margin_left,
            'absolute_left' => $absolute_left,
            'end_at_from_left' => $end_at_from_left,
            'width' => $width,
            // start_from and finish_before are local times in the server! When you say to save a manual job starting at 3pm it is saved at 3pm in the server although I'm not in UTC time.
            // Thus, we should not convert it to zulu as then the comparison with now in the frontend gets wrong
            'start_from' => $this->start_from->toDateTimeLocalString(),
            'finish_before' => $this->finish_before->toDateTimeLocalString(),
            'completed_at' => $this->completed_at?->toIso8601ZuluString(),
            // This time is UTC in the server
            'supervised_at' => $this->supervised_at?->toIso8601ZuluString(),
            // This time is UTC in the server
            'avatars' => $this->avatars(),
            'supervisor' => is_null($this->supervisor) ? null : new SlimUserResource($this->supervisor),
        ];
        if ($assignee) {
            $tr['assignee'] = is_null($this->assignee) ? null : new SlimUserResource($this->assignee);
        }

        return array_merge($this->only(
            'id',
            'team_id',
            'rental_id',
            'booking_id',
            'role',
            'assignee_id',
            'supervisor_id',
            'title',
            'priority'), $tr);
    }

    private function pixels($value): int
    {
        return intval((floor($value * 120) / 1440) ?? 0);
    }

    /**
     * Returns a list of avatars for this model.
     */
    public function avatars(): array
    {
        return once(function () {
            if ($this->role == TeamRolesEnum::OWNER) {
                $this->role = null;
                $this->assignee_id = $this->team->owner_id;
                $this->save();
            }
            if (isset($this->assignee_id)) {
                return [
                    optional($this->assignee, fn (User $user) => url($user->photo_url)),
                ];
            } else {
                return $this->team->avatars()
                    ->where('role', $this->role)
                    ->when($this->rental_id,
                        fn ($c) => $c->filter(fn (array $user) => in_array($this->rental_id, $user['userRentals'])))
                    ->take(4)
                    ->filter()
                    ->map(fn ($user) => url($user['photo_url']))
                    ->values()
                    ->toArray();
            }
        });
    }

    public function itemsCompleted(): bool
    {
        $comp = true;
        foreach ($this->items as $item) {
            if (! $item->isCompleted()) {
                $comp = false;
                break;
            }
        }

        return $comp;
    }

    public function isCompleted(): bool
    {
        return $this->completed_at != null;
    }

    /**
     * @return UserNotificationToken[]|Collection
     */
    public function assigneeTokens(?User $except = null): Collection|array
    {
        $query = (new UserNotificationToken())
            ->with('user')
            ->whereIn('user_id', $this->assigneeUsers());

        if ($except != null) {
            $query->whereNotIn('user_id', [$except->id]);
        }

        return $query->get();
    }

    /**
     * This is a costly method; it makes multiple queries.
     * If you just want to check if a task is assigned to a user, no need to get all the users
     * from this task. Just isAssignedTo() method below.
     */
    public function assigneeUsers(): array
    {
        if ($this->users != null) {
            return $this->users;
        }

        /// Owners must not be verified.
        if ($this->role == TeamRolesEnum::OWNER) {
            $this->role = null;
            $this->assignee_id = $this->team->owner_id;
            $this->save();
        }

        if ($this->assignee_id != null) {
            $this->users = User::query()
                ->whereId($this->assignee_id)
                ->select('id')
                ->get()
                ->pluck('id')
                ->toArray();
        } else {
            /** @var int[] $u */
            $uid = TeamUser::query()
                ->whereTeamId($this->team_id)
                ->whereRole($this->role)
                ->select('user_id')
                ->pluck('user_id')
                ->toArray();

            $uid = TeamUserRental::query()
                ->whereIn('user_id', $uid)
                ->onRental($this->rental_id)
                ->select('user_id')
                ->pluck('user_id')
                ->toArray();

            $this->users = $uid;
        }

        return $this->users;
    }

    public function canBeSeenBy(User $user): bool
    {
        return
            // Admin tasks can be seen by: owners or admins, or if assigned.
            (is_null($this->rental_id) && ($user->isAdminOrAbove() || $this->isAssignedTo($user))) ||
            // Rental tasks can be seen if: user has access to rental, and if >= manager or if assigned.
            (in_array($this->rental_id, $user->getRentalsForUser())
            && ($user->isRentalManagerOrAbove() || $this->isAssignedTo($user)));
    }

    public function isAssignedTo(User $user): bool
    {
        if ($user->current_team_id != $this->team_id) {
            return false;
        }

        if (! empty($this->assignee_id)) {
            return $this->assignee_id == $user->id;
        } elseif (! empty($this->role)) {
            return $this->role == $user->ninja_role;
        } else {
            return false;
        }
    }

    /**
     * @return UserNotificationToken[]|Collection
     */
    public function relatedUserTokens(?User $except = null): Collection|array
    {
        $users = $this->assigneeUsers();
        if ($this->supervisor_id != null) {
            $users[] = $this->supervisor_id;
        }
        $query = (new UserNotificationToken())->with('user')
            ->whereIn('user_id', $users);

        if ($except != null) {
            $query->whereNotIn('user_id', [$except->id]);
        }

        return $query->get();
    }

    public function isOwnedByBookingAndScheduledTask(Booking $booking, ScheduledTask $scheduledTask): bool
    {
        return $this->team_id == $booking->team_id &&
            $this->booking_id == $booking->id &&
            $this->rental_id == $booking->rental_id &&
            $this->scheduled_task_id == $scheduledTask->id;
    }

    public function isScheduledTask(): bool
    {
        return ! is_null($this->scheduled_task_id);
    }

    public function newEloquentBuilder($query): TaskQuery
    {
        return new TaskQuery($query);
    }
}
