<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Amenity.
 *
 * @property int $id
 * @property int $external_id
 * @property string $default_name
 * @property string $key
 * @property string|null $icon
 * @property int $max_units
 * @property bool $popular
 * @property int $public
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity query()
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity whereDefaultName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity whereMaxUnits($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity wherePopular($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity wherePublic($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Amenity whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Amenity extends Model
{
    // Rooms
    public const WC = 53;
    public const Bathroom = 81;
    public const KitchenOpen = 94;
    public const Kitchen = 101;
    public const LivingRoom = 249;
    public const Bedroom = 257;
    public const LivingBedRoom = 372;
    public const BedLivingKitchen = 517;

    public const ROOMS = [
        self::WC,
        self::Bathroom,
        self::KitchenOpen,
        self::Kitchen,
        self::LivingRoom,
        self::Bedroom,
        self::LivingBedRoom,
        self::BedLivingKitchen,
    ];

    public const BedsAndNumbers = [
        61 => 2,  // Double bed
        200 => 2, // Double sofa bed
        209 => 1, // Extra bed
        237 => 1, // Sofa bed
        323 => 1, // Single bed
        324 => 2, // King size
        384 => 3, // 3 bunk bed
        428 => 1, // Armchair
        440 => 2, // Pair of twin
        444 => 2,
        455 => 2,
        485 => 2,
        501 => 1,
        515 => 1,
        517 => 2,
        624 => 1,
        778 => 1,
        779 => 1,
        939 => 1,
        957 => 1,
        1210 => 1,
        1345 => 1,
        1350 => 1,
    ];

    protected $casts = [
        'popular' => 'boolean',
    ];

    protected $guarded = ['id'];
}
