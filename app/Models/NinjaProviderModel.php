<?php

namespace App\Models;

use <PERSON>wobaz\Compoships\Compoships;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

/**
 * App\Models\NinjaProviderModel.
 *
 * @property-read \App\Models\ProviderAccount $providerAccount
 *
 * @method static Builder|NinjaProviderModel newModelQuery()
 * @method static Builder|NinjaProviderModel newQuery()
 * @method static Builder|NinjaProviderModel query()
 *
 * @mixin Eloquent
 */
class NinjaProviderModel extends Model
{
    use Compoships;

    /**
     * @noinspection PhpMissingParentCallCommonInspection
     */
    protected function setKeysForSaveQuery($query): Builder
    {
        return $query
            ->where('id', '=', $this->id)
            ->where('team_id', '=', $this->team_id);
    }

    protected function setKeysForSelectQuery($query): Builder
    {
        return $query
            ->where('id', '=', $this->id)
            ->where('team_id', '=', $this->team_id);
    }

    public static function mapExternalIdsToInternals(int $teamId, array|Collection $external): Collection
    {
        // Array/Collection query
        if (is_array($external)) {
            $external = collect($external);
        }
        $external = $external->unique()->filter()->values(); // Remove duplicated and nulls

        return self::query()
            ->withTrashed() // Tokeet (example) sends us webhooks from deleted rentals
            ->where('team_id', '=', $teamId)
            ->whereIn('external_id', $external)
            ->pluck('id', 'external_id');
    }
}
