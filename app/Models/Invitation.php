<?php

namespace App\Models;

use App\Enum\TeamRolesEnum;
use App\Flavors\NinjaFlavor;
use App\Traits\NinjaNotifiable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\Invitation.
 *
 * @property string $id
 * @property int $team_id
 * @property int|null $user_id
 * @property string $email
 * @property array|null $rentals
 * @property string $token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property TeamRolesEnum|null $role
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \App\Models\Team $team
 * @property-read \App\Models\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation query()
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation whereRentals($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invitation whereUserId($value)
 *
 * @mixin \Eloquent
 */
class Invitation extends Model
{
    use NinjaNotifiable;

    protected $table = 'invitations';

    public $incrementing = false;

    protected $keyType = 'string';

    protected $guarded = [];

    protected $hidden = [];

    protected $casts = [
        'role' => TeamRolesEnum::class,
        'rentals' => 'array',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    public function user(): BelongsTo
    {
        // We point this relationship to the email instead of user_id because user_id is populated at the moment of the creation of the Invitation.
        // It may happen that, after invitation is sent and no user was found, another user picks that email, thus now this invitation has a user.
        // It could happen the other way round, the invitation is to an existing user, but at the moment of accepting it, the original user changed the email to a different one.
        return $this->belongsTo(User::class, 'email', 'email');
    }

    /**
     * Determine if the invitation is expired.
     */
    public function isExpired(): bool
    {
        return Carbon::now()->subWeek()->gte($this->created_at);
    }

    public function config(): NinjaFlavor
    {
        return $this->team->config();
    }
}
