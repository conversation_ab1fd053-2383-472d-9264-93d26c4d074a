<?php

namespace App\Models;

use App;
use App\Actions\Pictures\HandleTeamOrUserProfilePictureAction;
use App\Actions\Support\Dates\FormatHourAction;
use App\DataProviders\Providers\NinjaProvider;
use App\Domains\Automations\Models\Automation;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use App\Domains\HomeAutomation\Models\HomeAutomationAccount;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Domains\TrainingSessions\Models\TeamTrainingSession;
use App\Enum\PlanCodeEnum;
use App\Enum\TeamRolesEnum;
use App\Enum\TeamStatusEnum;
use App\Query\RentalQuery;
use App\Query\TeamQuery;
use App\Traits\NinjaNotifiable;
use App\Traits\NinjaSyncable;
use App\Traits\NinjaTeamHasProvider;
use App\Traits\PaymentProcessor\NinjaBillableManager;
use App\Traits\PaymentProcessor\NinjaManagesPaymentGateway;
use Awobaz\Compoships\Compoships;
use Eloquent;
use Exception;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Laravel\Nova\Actions\Actionable;
use Laravel\Nova\Actions\ActionEvent;

/**
 * App\Models\Team.
 *
 * @property int $id
 * @property int $owner_id
 * @property string $name
 * @property TeamStatusEnum $status
 * @property string $email
 * @property bool $cm_enabled
 * @property bool $verified
 * @property string|null $country
 * @property string $communication_locale
 * @property string $app_locale
 * @property string|null $website
 * @property-read string $photo_url
 * @property string|null $stripe_id
 * @property PlanCodeEnum|null $current_rn_plan
 * @property Carbon|null $last_stripe_usage_reported_at
 * @property string|null $pm_type
 * @property string|null $pm_last_four
 * @property string|null $card_country
 * @property string|null $billing_address
 * @property string|null $billing_address_line_2
 * @property string|null $billing_city
 * @property string|null $billing_state
 * @property string|null $billing_zip
 * @property string|null $billing_country
 * @property string|null $vat_id
 * @property string|null $extra_billing_information
 * @property Carbon|null $trial_ends_at
 * @property string|null $external_id
 * @property int|null $rentals
 * @property int $arrival_time
 * @property int $departure_time
 * @property string|null $currency
 * @property string|null $wheelhouse_api_key
 * @property string|null $rn_api_key
 * @property int $migrating
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $last_sync_attempt_at
 * @property Carbon|null $synced_at
 * @property Carbon|null $force_synced_at
 * @property int $provider_id
 * @property int $flavor_id
 * @property-read \App\Models\AccountingAdvancedSettings|null $accountingAdvancedSettings
 * @property-read EloquentCollection<int, ActionEvent> $actions
 * @property-read int|null $actions_count
 * @property-read EloquentCollection<int, \App\Models\Alert> $alerts
 * @property-read int|null $alerts_count
 * @property-read EloquentCollection<int, \App\Models\Booking> $allBookings
 * @property-read int|null $all_bookings_count
 * @property-read EloquentCollection<int, Automation> $automations
 * @property-read int|null $automations_count
 * @property-read EloquentCollection<int, \App\Models\BillableAddon> $billableAddons
 * @property-read int|null $billable_addons_count
 * @property-read EloquentCollection<int, \App\Models\BookingPayment> $bookingPayments
 * @property-read int|null $booking_payments_count
 * @property-read EloquentCollection<int, \App\Models\BookingTag> $bookingTags
 * @property-read int|null $booking_tags_count
 * @property-read EloquentCollection<int, \App\Models\Booking> $bookings
 * @property-read int|null $bookings_count
 * @property-read EloquentCollection<int, \App\Models\ChannelRental> $channelRentals
 * @property-read int|null $channel_rentals_count
 * @property-read EloquentCollection<int, \App\Models\Checklist> $checklists
 * @property-read int|null $checklists_count
 * @property-read EloquentCollection<int, \App\Models\Client> $clients
 * @property-read int|null $clients_count
 * @property-read \App\Models\Company|null $company
 * @property-read EloquentCollection<int, \App\Models\Rental> $connectedRentals
 * @property-read int|null $connected_rentals_count
 * @property-read EloquentCollection<int, \App\Models\Rental> $disConnectedRentals
 * @property-read int|null $dis_connected_rentals_count
 * @property-read EloquentCollection<int, \App\Models\DistributionWebsite> $distributionWebsites
 * @property-read int|null $distribution_websites_count
 * @property-read EloquentCollection<int, \App\Models\Fee> $fees
 * @property-read int|null $fees_count
 * @property-read \App\Models\GuestsApplicationSettings|null $guestApplicationSettings
 * @property-read EloquentCollection<int, \App\Models\GuestsApplicationRentalSettings> $guestsApplicationRentalSettings
 * @property-read int|null $guests_application_rental_settings_count
 * @property-read EloquentCollection<int, HomeAutomationAccount> $homeAutomationAccounts
 * @property-read int|null $home_automation_accounts_count
 * @property-read EloquentCollection<int, HomeAutomationDevice> $homeAutomationDevices
 * @property-read int|null $home_automation_devices_count
 * @property-read EloquentCollection<int, \App\Models\ICalInput> $iCalInputs
 * @property-read int|null $i_cal_inputs_count
 * @property-read EloquentCollection<int, \App\Models\ICalOutput> $iCalOutputs
 * @property-read int|null $i_cal_outputs_count
 * @property-read EloquentCollection<int, \App\Models\Invitation> $invitations
 * @property-read int|null $invitations_count
 * @property-read EloquentCollection<int, \App\Models\MessageAttachment> $messageAttachments
 * @property-read int|null $message_attachments_count
 * @property-read DatabaseNotificationCollection<int, DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \App\Models\User $owner
 * @property-read EloquentCollection<int, \App\Models\PayeeInvoice> $payeeInvoices
 * @property-read int|null $payee_invoices_count
 * @property-read EloquentCollection<int, \App\Models\Payee> $payees
 * @property-read int|null $payees_count
 * @property-read EloquentCollection<int, \App\Models\PaymentGatewayAccount> $paymentGatewayAccounts
 * @property-read int|null $payment_gateway_accounts_count
 * @property-read EloquentCollection<int, \App\Models\Payout> $payouts
 * @property-read int|null $payouts_count
 * @property-read EloquentCollection<int, \App\Models\NinjaPicture> $pictures
 * @property-read int|null $pictures_count
 * @property-read EloquentCollection<int, \App\Models\PreCheckInForm> $preCheckInForms
 * @property-read int|null $pre_check_in_forms_count
 * @property-read EloquentCollection<int, \App\Models\ProfessionalPlanDemo> $professionalPlanDemo
 * @property-read int|null $professional_plan_demo_count
 * @property-read EloquentCollection<int, \App\Models\ProviderAccount> $providerAccounts
 * @property-read int|null $provider_accounts_count
 * @property-read EloquentCollection<int, \App\Models\RecurrentTask> $recurrentTasks
 * @property-read int|null $recurrent_tasks_count
 * @property-read EloquentCollection<int, \App\Models\Invoice> $rentalNinjaInvoices
 * @property-read int|null $rental_ninja_invoices_count
 * @property-read EloquentCollection<int, \App\Models\RentalOwner> $rentalOwners
 * @property-read int|null $rental_owners_count
 * @property-read EloquentCollection<int, \App\Models\ScheduledSettlement> $scheduledSettlements
 * @property-read int|null $scheduled_settlements_count
 * @property-read EloquentCollection<int, \App\Models\ScheduledTask> $scheduledTasks
 * @property-read int|null $scheduled_tasks_count
 * @property-read EloquentCollection<int, \App\Models\Settlement> $settlements
 * @property-read int|null $settlements_count
 * @property-read EloquentCollection<int, \App\Models\SourceCommissionStrategy> $sourceCommissionStrategies
 * @property-read int|null $source_commission_strategies_count
 * @property-read EloquentCollection<int, \App\Models\Source> $sources
 * @property-read int|null $sources_count
 * @property-read EloquentCollection<int, \App\Models\TeamSubscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read EloquentCollection<int, \App\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \App\Models\TeamDetails|null $teamDetails
 * @property-read EloquentCollection<int, \App\Models\Rental> $teamRentals
 * @property-read int|null $team_rentals_count
 * @property-read \App\Models\TeamSettings|null $teamSettings
 * @property-read EloquentCollection<int, TeamTrainingSession> $trainingSessions
 * @property-read int|null $training_sessions_count
 * @property-read EloquentCollection<int, \App\Models\Task> $trashedTasks
 * @property-read int|null $trashed_tasks_count
 * @property-read EloquentCollection<int, \App\Models\User> $users
 * @property-read int|null $users_count
 *
 * @method static TeamQuery|Team activeTeams()
 * @method static \Database\Factories\TeamFactory factory($count = null, $state = [])
 * @method static TeamQuery|Team hasExpiredGenericTrial()
 * @method static TeamQuery|Team newModelQuery()
 * @method static TeamQuery|Team newQuery()
 * @method static TeamQuery|Team notActiveTeams()
 * @method static TeamQuery|Team onGenericTrial()
 * @method static TeamQuery|Team onGrowthPlan()
 * @method static TeamQuery|Team onProConnectedPlan()
 * @method static TeamQuery|Team onProPlan()
 * @method static TeamQuery|Team onProfessionalMonthlyPlan()
 * @method static TeamQuery|Team onProfessionalYearlyPlan()
 * @method static TeamQuery|Team professionalTeams()
 * @method static TeamQuery|Team query()
 * @method static TeamQuery|Team starterTeams(bool $excludePPDTeams = false)
 * @method static TeamQuery|Team subscribedTeams(bool $usesSubscriptionsTable = false)
 * @method static TeamQuery|Team trialingTeams()
 * @method static TeamQuery|Team whereAppLocale($value)
 * @method static TeamQuery|Team whereArrivalTime($value)
 * @method static TeamQuery|Team whereBillingAddress($value)
 * @method static TeamQuery|Team whereBillingAddressLine2($value)
 * @method static TeamQuery|Team whereBillingCity($value)
 * @method static TeamQuery|Team whereBillingCountry($value)
 * @method static TeamQuery|Team whereBillingState($value)
 * @method static TeamQuery|Team whereBillingZip($value)
 * @method static TeamQuery|Team whereCardCountry($value)
 * @method static TeamQuery|Team whereCmEnabled($value)
 * @method static TeamQuery|Team whereCommunicationLocale($value)
 * @method static TeamQuery|Team whereCountry($value)
 * @method static TeamQuery|Team whereCreatedAt($value)
 * @method static TeamQuery|Team whereCurrency($value)
 * @method static TeamQuery|Team whereCurrentRnPlan($value)
 * @method static TeamQuery|Team whereDepartureTime($value)
 * @method static TeamQuery|Team whereEmail($value)
 * @method static TeamQuery|Team whereExternalId($value)
 * @method static TeamQuery|Team whereExtraBillingInformation($value)
 * @method static TeamQuery|Team whereFlavorId($value)
 * @method static TeamQuery|Team whereForceSyncedAt($value)
 * @method static TeamQuery|Team whereForcedAtBeforeThan(\Illuminate\Support\Carbon $date)
 * @method static TeamQuery|Team whereHasGatewayDepositsNotificationListNotEmpty()
 * @method static TeamQuery|Team whereHasPublishedRentals()
 * @method static TeamQuery|Team whereId($value)
 * @method static TeamQuery|Team whereLastStripeUsageReportedAt($value)
 * @method static TeamQuery|Team whereLastSyncAttemptAt($value)
 * @method static TeamQuery|Team whereMigrating($value)
 * @method static TeamQuery|Team whereName($value)
 * @method static TeamQuery|Team whereOwnerId($value)
 * @method static TeamQuery|Team wherePaymentGatewayEnabled(?int $rentalId = null)
 * @method static TeamQuery|Team wherePhotoUrl($value)
 * @method static TeamQuery|Team wherePmLastFour($value)
 * @method static TeamQuery|Team wherePmType($value)
 * @method static TeamQuery|Team whereProviderId($value)
 * @method static TeamQuery|Team whereRentals($value)
 * @method static TeamQuery|Team whereRnApiKey($value)
 * @method static TeamQuery|Team whereStatus($value)
 * @method static TeamQuery|Team whereStripeId($value)
 * @method static TeamQuery|Team whereSubscriptionStatusIsPastDue()
 * @method static TeamQuery|Team whereSyncedAt($value)
 * @method static TeamQuery|Team whereTeamIsProviderEnabled()
 * @method static TeamQuery|Team whereTrialEndsAt($value)
 * @method static TeamQuery|Team whereUpdatedAt($value)
 * @method static TeamQuery|Team whereVatId($value)
 * @method static TeamQuery|Team whereVerified($value)
 * @method static TeamQuery|Team whereWebsite($value)
 * @method static TeamQuery|Team whereWheelhouseApiKey($value)
 *
 * @mixin Eloquent
 */
class Team extends Model
{
    use Actionable;
    use Compoships;
    use HasFactory;
    use NinjaTeamHasProvider;
    use NinjaManagesPaymentGateway;
    use NinjaNotifiable;
    use NinjaBillableManager;
    use NinjaSyncable;

    protected $fillable = [
        'name',
        'photo_url',
        'email',
        'country',
        'current_rn_plan',
        'communication_locale',
        'app_locale',
        'website',
        'arrival_time',
        'currency',
        'wheelhouse_api_key',
    ];

    protected $hidden = [
        'pm_type',
        'pm_last_four',
        'card_country',
        'billing_address',
        'billing_address_line_2',
        'billing_city',
        'billing_state',
        'billing_zip',
        'billing_country',
        'vat_id',
        'extra_billing_information',
        'stripe_id',
    ];
    protected $casts = [
        'cm_enabled' => 'boolean',
        'verified' => 'boolean',
        'current_rn_plan' => PlanCodeEnum::class,
        'last_stripe_usage_reported_at' => 'datetime',
        'status' => TeamStatusEnum::class,
        'trial_ends_at' => 'datetime',
        'last_sync_attempt_at' => 'datetime',
        'synced_at' => 'datetime',
        'force_synced_at' => 'datetime',
    ];

    public function email(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value): string => is_null($value) ? $this->owner->email : $value,
            set: fn (?string $value): ?string => $value,
        );
    }

    protected function photoUrl(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value): string => HandleTeamOrUserProfilePictureAction::run($this, $value),
        );
    }

    /**
     * Checks if this team has an avatar that can be rendered inside the PDFs.
     */
    public function hasValidImgixPhoto(): bool
    {
        return $this->imgixPhoto() != null && ! Str::contains($this->imgixPhoto(), ['svg', 'avatars.dicebear.com']);
    }

    public function imgixPhoto(): ?string
    {
        return $this->photo_url;
    }

    /**
     * Route notifications for the Slack channel.
     */
    public function routeNotificationForSlack(): string
    {
        $settings = TeamSettings::whereTeamId($this->id)
            ->first();
        if (
            $settings != null && $settings->active && $settings->slack_webhook_url != null
            && filter_var($settings->slack_webhook_url,
                FILTER_VALIDATE_URL) !== false
        ) {
            return $settings->slack_webhook_url;
        }

        return '';
    }

    public function canReceiveSlackNotifications($type): bool
    {
        if (isLocal()) {
            return false;
        }
        if (! $this->isProfessional()) {
            return false;
        }

        $settings = TeamSettings::whereTeamId($this->id)
            ->first();
        if (
            $settings != null && $settings->active && $settings->slack_webhook_url != null
            && filter_var($settings->slack_webhook_url,
                FILTER_VALIDATE_URL) !== false
        ) {
            return boolval($settings->{$type});
        }

        return false;
    }

    public function canSendSMS(): bool
    {
        if (! $this->canSendPaidNotifications() || is_null($this->guestApplicationSettings)) {
            return false;
        }

        return $this->guestApplicationSettings->sms;
    }

    public function hasCentralizedInbox(): bool
    {
        $rentalProviderIds = $this->teamRentals()->select('provider_id')->pluck('provider_id')->unique()->filter();

        // Return true if all rentals have provider_id = 0. This means the team is new and still has rentals to connect. But show it anyway for them to know is there.
        if ($rentalProviderIds->contains(0) && $rentalProviderIds->count() === 1) {
            return true;
        }

        // If at lest one rental of the team has access to our inbox, we can show it. This will allow it too for, for instance, Smily teams with one rental with us.
        return $rentalProviderIds->map(fn (int $providerId) => NinjaProvider::provider($providerId)->usingOurInboxUiIsAllowed())
            ->containsStrict(true);
    }

    public function gatewayActivated(int $rentalId): bool
    {
        $gatewayAccount = $this->findPaymentGateway($rentalId);
        if ($gatewayAccount === null) {
            return false;
        }

        return $gatewayAccount->charges_enabled && $gatewayAccount->gateway_account_id !== null;
    }

    public function findPaymentGateway(int $rentalId): ?PaymentGatewayAccount
    {
        return $this->paymentGatewayAccounts
            ->firstWhere(fn (PaymentGatewayAccount $account) => empty($account->rentals) || in_array($rentalId, $account->rentals));
    }

    /**
     * Returns a collection with all the avatars in the current team with:
     * The user id.
     * The user email.
     * The photo url.
     * The role they have.
     * A list of rental ids they have assigned.
     *
     * Mainly used for Tasks visualization
     */
    public function avatars(): Collection
    {
        return once(fn () => $this->users()
            ->select('id', 'photo_url', 'email', 'ninja_role', 'current_team_id')
            ->with(['team', 'team.users', 'userRentals'])
            ->get()
            ->map(fn (User $user) => [
                'id' => $user->id,
                'photo_url' => $user->photo_url,
                'role' => $user->ninja_role,
                'userRentals' => $user->getRentalsForUser(),
                'email' => $user->email,
            ]));
    }

    public function assignTeamLocale(): void
    {
        $team_locale = $this->getTeamLocale();
        App::setLocale(substr($team_locale, 0, 2)); // cut locale just in case we get a provider with long locale
    }

    public function getTeamLocale(): string
    {
        return empty($this->communication_locale) ? 'en' : $this->communication_locale;
    }

    public function defaultArrivalTime(): string
    {
        return FormatHourAction::run($this->arrival_time);
    }

    public function defaultDepartureTime(): string
    {
        return FormatHourAction::run($this->departure_time);
    }

    public function teamRentalIds(bool $includeDeleted = false, bool $withUncompleted = false): array
    {
        return once(function () use ($includeDeleted, $withUncompleted): array {
            return $this->teamRentals()
                ->select('id')
                ->when($includeDeleted, fn (RentalQuery $query) => $query->withTrashed())
                ->when($withUncompleted, fn (RentalQuery $query) => $query->withUncompleted())
                ->pluck('id')
                ->unique()
                ->sortBy('id')
                ->toArray();
        });
    }

    /**
     * Convert the model instance to an array.
     *
     * @throws Exception
     */
    public function toArray(): array
    {
        $tr = $this->attributesToArray();
        $owner = cache()->remember("teams.$this->id.owner",
            now()->addHours(10),
            fn () => $this->relationLoaded('owner') ? $this->owner : $this->owner()->first()
        );

        $tr = array_merge($tr, ['owner' => $this->slimUser($owner, TeamRolesEnum::OWNER)]);

        if (isset($this->relations['users'])) {
            $users = User::query()
                ->where('current_team_id', '=', $this->id)
                ->select([
                    'id',
                    'name',
                    'photo_url',
                    'email',
                    'current_team_id',
                    'ninja_role',
                    'updated_at',
                    'created_at',
                ])
                ->whereNotNull('ninja_role')
                ->get();
            $slim = $this->slimUser($users);
            $tr = array_merge($tr, ['users' => $slim]);
        }
        if (isset($this->relations['subscriptions'])) {
            $tr = array_merge($tr, ['subscriptions' => $this->subscriptions]);
        }

        return $tr;
    }

    private function slimUser($user, ?TeamRolesEnum $role = null): array
    {
        if ($user instanceof User) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'photo_url' => $this->photo_url,
                'photo' => $this->photo_url,
                'email' => $user->email,
                'role' => $role ? $role->value : $user->ninja_role->value,
                'current_team_id' => $user->current_team_id,
                'updated_at' => $user->updated_at->toDateTimeString(),
                'created_at' => $user->created_at->toDateTimeString(),
            ];
        } elseif ($user instanceof Collection) {
            return $user->map(function ($user) {
                /** @var User $user */
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'photo_url' => $this->photo_url,
                    'photo' => $this->photo_url,
                    'email' => $user->email,
                    'role' => $user->ninja_role->value,
                    'current_team_id' => $user->current_team_id,
                    'updated_at' => Carbon::parse($user->updated_at)
                        ->toDateTimeString(),
                    'created_at' => Carbon::parse($user->created_at)
                        ->toDateTimeString(),
                ];
            })
                ->toArray();
        }

        return [];
    }

    /** RELATIONS DOWN BELOW */

    /**
     * Get all of the users that belong to the team.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'current_team_id');
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function subscriptions(): HasMany
    {
        return $this->hasMany(TeamSubscription::class, 'team_id')->orderBy('created_at', 'desc');
    }

    public function clients(): HasMany|App\Query\ClientQuery
    {
        return $this->hasMany(Client::class);
    }

    public function fees(): HasMany
    {
        return $this->hasMany(Fee::class);
    }

    public function bookingPayments(): HasMany|App\Query\BookingPaymentQuery
    {
        return $this->hasMany(BookingPayment::class);
    }

    public function preCheckInForms(): HasMany
    {
        return $this->hasMany(PreCheckInForm::class);
    }

    public function rentalOwners(): HasMany
    {
        return $this->hasMany(RentalOwner::class);
    }

    public function rentalNinjaInvoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function guestApplicationSettings(): HasOne
    {
        return $this->hasOne(GuestsApplicationSettings::class, 'team_id', 'id');
    }

    public function guestsApplicationRentalSettings(): HasMany
    {
        return $this->hasMany(GuestsApplicationRentalSettings::class);
    }

    public function teamSettings(): HasOne
    {
        return $this->hasOne(TeamSettings::class);
    }

    public function teamDetails(): HasOne
    {
        return $this->hasOne(TeamDetails::class);
    }

    public function bookings(): HasMany|App\Query\BookingQuery
    {
        return $this->hasMany(Booking::class);
    }

    public function allBookings(): HasMany|App\Query\BookingQuery
    {
        return $this->hasMany(Booking::class)->withoutGlobalScopes();
    }

    public function pictures(): HasMany
    {
        return $this->hasMany(NinjaPicture::class);
    }

    public function settlements(): HasMany
    {
        return $this->hasMany(Settlement::class);
    }

    public function scheduledSettlements(): HasMany
    {
        return $this->hasMany(ScheduledSettlement::class);
    }

    public function accountingAdvancedSettings(): HasOne
    {
        return $this->hasOne(AccountingAdvancedSettings::class);
    }

    public function sourceCommissionStrategies(): HasMany
    {
        return $this->hasMany(SourceCommissionStrategy::class);
    }

    public function professionalPlanDemo(): HasMany
    {
        return $this->hasMany(ProfessionalPlanDemo::class);
    }

    public function scheduledTasks(): HasMany
    {
        return $this->hasMany(ScheduledTask::class);
    }

    public function recurrentTasks(): HasMany
    {
        return $this->hasMany(RecurrentTask::class);
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    public function trashedTasks(): HasMany
    {
        return $this->hasMany(Task::class)->withTrashed();
    }

    public function checklists(): HasMany
    {
        return $this->hasMany(Checklist::class);
    }

    public function sources(): HasMany
    {
        return $this->hasMany(Source::class);
    }

    public function bookingTags(): HasMany
    {
        return $this->hasMany(BookingTag::class);
    }

    public function automations(): HasMany|Automation
    {
        return $this->hasMany(Automation::class);
    }

    public function payees(): HasMany
    {
        return $this->hasMany(Payee::class);
    }

    public function payeeInvoices(): HasMany
    {
        return $this->hasMany(PayeeInvoice::class);
    }

    public function payouts(): HasMany
    {
        return $this->hasMany(Payout::class);
    }

    public function iCalInputs(): HasMany
    {
        return $this->hasMany(ICalInput::class);
    }

    public function iCalOutputs(): HasMany
    {
        return $this->hasMany(ICalOutput::class);
    }

    public function company(): HasOne
    {
        return $this->hasOne(Company::class);
    }

    public function teamRentals(): HasMany
    {
        return $this->hasMany(Rental::class)->withUncompleted();
    }

    public function connectedRentals(): HasMany
    {
        return $this->hasMany(Rental::class)->wherePublished()->withUncompleted();
    }

    public function disConnectedRentals(): HasMany
    {
        return $this->hasMany(Rental::class)->whereNotPublished()->withUncompleted();
    }

    public function channelRentals(): HasMany
    {
        return $this->hasMany(ChannelRental::class);
    }

    public function invitations(): HasMany
    {
        return $this->hasMany(Invitation::class);
    }

    public function billableAddons(): HasMany
    {
        return $this->hasMany(BillableAddon::class);
    }

    public function distributionWebsites(): HasMany
    {
        return $this->hasMany(DistributionWebsite::class);
    }

    public function paymentGatewayAccounts(): HasMany
    {
        return $this->hasMany(PaymentGatewayAccount::class);
    }

    public function messageAttachments(): HasMany
    {
        return $this->hasMany(MessageAttachment::class);
    }

    public function trainingSessions(): HasMany
    {
        return $this->hasMany(TeamTrainingSession::class);
    }

    public function homeAutomationAccounts(): HasMany
    {
        return $this->hasMany(HomeAutomationAccount::class);
    }

    public function homeAutomationDevices(): HasMany
    {
        return $this->hasMany(HomeAutomationDevice::class);
    }

    public function billableHomeAutomationDevices()
    {
        return $this->homeAutomationDevices()->billable();
    }

    public function guestsRegistrationAuthorities(): HasMany
    {
        return $this->hasMany(GuestsRegistrationAuthority::class);
    }

    public function alerts(): HasMany
    {
        return $this->hasMany(Alert::class);
    }

    public function newEloquentBuilder($query): TeamQuery
    {
        return new TeamQuery($query);
    }

    public function save(array $options = [])
    {
        // Due to the photo_url accessor, when saving a user, we save the value given by the accessor. We don't want that
        if ($this->photo_url !== null && ! Str::contains($this->photo_url, config('ninja.imgix_url_identifier'))) {
            $this->photo_url = null;
        }

        return parent::save($options);
    }

    public function getAllProviders(): Collection
    {
        return $this->providerAccounts->map(fn (ProviderAccount $a) => $a->provider());
    }
}
