<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Airline.
 *
 * @property int|null $id
 * @property string|null $alias
 * @property string|null $call_sign
 * @property float|null $code_iata
 * @property string|null $code_icao
 * @property string|null $country_id
 * @property string|null $name
 * @property-read \App\Models\Continent|null $continent
 * @property-read \App\Models\Country|null $country
 *
 * @method static Builder|Airline newModelQuery()
 * @method static Builder|Airline newQuery()
 * @method static Builder|Airline query()
 * @method static Builder|Airline whereAlias($value)
 * @method static Builder|Airline whereCallSign($value)
 * @method static Builder|Airline whereCodeIata($value)
 * @method static Builder|Airline whereCodeIcao($value)
 * @method static Builder|Airline whereCountryId($value)
 * @method static Builder|Airline whereId($value)
 * @method static Builder|Airline whereName($value)
 *
 * @mixin Eloquent
 */
class Airline extends Model
{
    use Sushi;

    protected $rows = [
        [
            'id' => '1.0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '1.0',
            'code_icao' => '',
            'country_id' => 'de',
            'name' => 'Bobb Air Freight',
        ],
        [
            'id' => '0b',
            'alias' => '',
            'call_sign' => 'BLUE TRANSPORT',
            'code_iata' => '0b',
            'code_icao' => 'jor',
            'country_id' => 'ro',
            'name' => 'Blue Air',
        ],
        [
            'id' => '0d',
            'alias' => '',
            'call_sign' => 'DARWIN',
            'code_iata' => '0d',
            'code_icao' => 'dwt',
            'country_id' => 'ch',
            'name' => 'Darwin Airline',
        ],
        [
            'id' => '0g',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '0g',
            'code_icao' => 'sar',
            'country_id' => 'pl',
            'name' => 'Sprintair',
        ],
        [
            'id' => '0x',
            'alias' => '',
            'call_sign' => 'Copex',
            'code_iata' => '0x',
            'code_icao' => 'cx0',
            'country_id' => 'dk',
            'name' => 'Copenhagen Express',
        ],
        [
            'id' => '11.0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '11.0',
            'code_icao' => '',
            'country_id' => 'de',
            'name' => 'TUIfly (X3)',
        ],
        [
            'id' => '12.0',
            'alias' => '',
            'call_sign' => '12N',
            'code_iata' => '12.0',
            'code_icao' => 'n12',
            'country_id' => 'in',
            'name' => '12 North',
        ],
        [
            'id' => '13.0',
            'alias' => '',
            'call_sign' => 'EAVA',
            'code_iata' => '13.0',
            'code_icao' => 'eav',
            'country_id' => 'us',
            'name' => 'Eastern Atlantic Virtual Airlines',
        ],
        [
            'id' => '1b',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '1b',
            'code_icao' => '',
            'country_id' => 'sg',
            'name' => 'Abacus International',
        ],
        [
            'id' => '1c',
            'alias' => 'OneChina',
            'call_sign' => '',
            'code_iata' => '1c',
            'code_icao' => '1ch',
            'country_id' => 'cn',
            'name' => 'OneChina',
        ],
        [
            'id' => '1f',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '1f',
            'code_icao' => 'cif',
            'country_id' => 'gb',
            'name' => 'CB Airways UK ( Interliging Flights )',
        ],
        [
            'id' => '1i',
            'alias' => '',
            'call_sign' => 'FRACTION',
            'code_iata' => '1i',
            'code_icao' => 'nje',
            'country_id' => 'pt',
            'name' => 'NetJets Europe',
        ],
        [
            'id' => '1i',
            'alias' => '',
            'call_sign' => 'EXECJET',
            'code_iata' => '1i',
            'code_icao' => 'eja',
            'country_id' => 'us',
            'name' => 'NetJets',
        ],
        [
            'id' => '1i',
            'alias' => '',
            'call_sign' => 'NAVIGATOR',
            'code_iata' => '1i',
            'code_icao' => 'nvr',
            'country_id' => 'se',
            'name' => 'Novair',
        ],
        [
            'id' => '1t',
            'alias' => '',
            'call_sign' => 'NEXTIME',
            'code_iata' => '1t',
            'code_icao' => 'rnx',
            'country_id' => 'za',
            'name' => '1Time Airline',
        ],
        [
            'id' => '20.0',
            'alias' => '',
            'call_sign' => 'AIR SALONE',
            'code_iata' => '20.0',
            'code_icao' => 'rne',
            'country_id' => 'sl',
            'name' => 'Air Salone',
        ],
        [
            'id' => '24.0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '24.0',
            'code_icao' => '',
            'country_id' => 'de',
            'name' => 'Euro Jet',
        ],
        [
            'id' => '2a',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '2a',
            'code_icao' => '',
            'country_id' => 'de',
            'name' => 'Deutsche Bahn',
        ],
        [
            'id' => '2b',
            'alias' => '',
            'call_sign' => 'AEROCONDOR',
            'code_iata' => '2b',
            'code_icao' => 'ard',
            'country_id' => 'pt',
            'name' => 'Aerocondor',
        ],
        [
            'id' => '2d',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '2d',
            'code_icao' => '',
            'country_id' => 'pt',
            'name' => 'Aero VIP (2D)',
        ],
        [
            'id' => '2f',
            'alias' => '',
            'call_sign' => 'FRONTIER-AIR',
            'code_iata' => '2f',
            'code_icao' => 'fta',
            'country_id' => 'us',
            'name' => 'Frontier Flying Service',
        ],
        [
            'id' => '2i',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '2i',
            'code_icao' => '',
            'country_id' => 'pe',
            'name' => 'Star Peru (2I)',
        ],
        [
            'id' => '2j',
            'alias' => '',
            'call_sign' => 'BURKINA',
            'code_iata' => '2j',
            'code_icao' => 'vbw',
            'country_id' => 'bf',
            'name' => 'Air Burkina',
        ],
        [
            'id' => '2k',
            'alias' => '',
            'call_sign' => 'AEROGAL',
            'code_iata' => '2k',
            'code_icao' => 'glg',
            'country_id' => 'ec',
            'name' => 'Aerolineas Galapagos (Aerogal)',
        ],
        [
            'id' => '2l',
            'alias' => '',
            'call_sign' => 'HELVETIC',
            'code_iata' => '2l',
            'code_icao' => 'oaw',
            'country_id' => 'ch',
            'name' => 'Helvetic Airways',
        ],
        [
            'id' => '2m',
            'alias' => '',
            'call_sign' => 'MOLDAVIAN',
            'code_iata' => '2m',
            'code_icao' => 'mdv',
            'country_id' => 'md',
            'name' => 'Moldavian Airlines',
        ],
        [
            'id' => '2n',
            'alias' => '',
            'call_sign' => 'NEXTJET',
            'code_iata' => '2n',
            'code_icao' => 'ntj',
            'country_id' => 'se',
            'name' => 'NextJet',
        ],
        [
            'id' => '2o',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '2o',
            'code_icao' => '',
            'country_id' => 'sl',
            'name' => 'Air Salone',
        ],
        [
            'id' => '2p',
            'alias' => '',
            'call_sign' => 'ORIENT PACIFIC',
            'code_iata' => '2p',
            'code_icao' => 'gap',
            'country_id' => 'ph',
            'name' => 'Air Philippines',
        ],
        [
            'id' => '2q',
            'alias' => '',
            'call_sign' => 'NIGHT CARGO',
            'code_iata' => '2q',
            'code_icao' => 'snc',
            'country_id' => 'us',
            'name' => 'Air Cargo Carriers',
        ],
        [
            'id' => '2t',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '2t',
            'code_icao' => 'ham',
            'country_id' => 'ht',
            'name' => 'Haiti Ambassador Airlines',
        ],
        [
            'id' => '2u',
            'alias' => '',
            'call_sign' => 'FUTURE EXPRESS',
            'code_iata' => '2u',
            'code_icao' => 'gip',
            'country_id' => 'gn',
            'name' => 'Air Guinee Express',
        ],
        [
            'id' => '2w',
            'alias' => '',
            'call_sign' => 'WELCOMEAIR',
            'code_iata' => '2w',
            'code_icao' => 'wlc',
            'country_id' => 'at',
            'name' => 'Welcome Air',
        ],
        [
            'id' => '2x',
            'alias' => 'Regionalia Uruguay',
            'call_sign' => '',
            'code_iata' => '2x',
            'code_icao' => '2k2',
            'country_id' => 'uy',
            'name' => 'Regionalia Uruguay',
        ],
        [
            'id' => '2y',
            'alias' => '',
            'call_sign' => 'MYINDO',
            'code_iata' => '2y',
            'code_icao' => 'myu',
            'country_id' => 'id',
            'name' => 'My Indo Airlines',
        ],
        [
            'id' => '3b',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '3b',
            'code_icao' => '',
            'country_id' => 'cz',
            'name' => 'JobAir',
        ],
        [
            'id' => '3e',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '3e',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Air Choice One',
        ],
        [
            'id' => '3f',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '3f',
            'code_icao' => '3ff',
            'country_id' => 'co',
            'name' => 'Fly Colombia ( Interliging Flights )',
        ],
        [
            'id' => '3g',
            'alias' => '',
            'call_sign' => 'ATLANT-SOYUZ',
            'code_iata' => '3g',
            'code_icao' => 'ayz',
            'country_id' => 'ru',
            'name' => 'Atlant-Soyuz Airlines',
        ],
        [
            'id' => '3g',
            'alias' => '',
            'call_sign' => 'ASIA CARGO',
            'code_iata' => '3g',
            'code_icao' => 'cxm',
            'country_id' => 'my',
            'name' => 'AsiaCargo Express',
        ],
        [
            'id' => '3i',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '3i',
            'code_icao' => '',
            'country_id' => 'cl',
            'name' => 'Air Comet Chile',
        ],
        [
            'id' => '3k',
            'alias' => '',
            'call_sign' => 'JETSTAR ASIA',
            'code_iata' => '3k',
            'code_icao' => 'jsa',
            'country_id' => 'sg',
            'name' => 'Jetstar Asia Airways',
        ],
        [
            'id' => '3l',
            'alias' => '',
            'call_sign' => 'INTERSKY',
            'code_iata' => '3l',
            'code_icao' => 'isk',
            'country_id' => 'at',
            'name' => 'Intersky',
        ],
        [
            'id' => '3o',
            'alias' => '',
            'call_sign' => 'Air Arabia',
            'code_iata' => '3o',
            'code_icao' => '',
            'country_id' => 'ma',
            'name' => 'Air Arabia Maroc',
        ],
        [
            'id' => '3p',
            'alias' => '',
            'call_sign' => 'TIARA',
            'code_iata' => '3p',
            'code_icao' => 'tnm',
            'country_id' => 'aw',
            'name' => 'Tiara Air',
        ],
        [
            'id' => '3q',
            'alias' => '',
            'call_sign' => 'YUNNAN',
            'code_iata' => '3q',
            'code_icao' => 'cyh',
            'country_id' => 'cn',
            'name' => 'Yunnan Airlines',
        ],
        [
            'id' => '3r',
            'alias' => '',
            'call_sign' => 'GROMOV AIRLINE',
            'code_iata' => '3r',
            'code_icao' => 'gai',
            'country_id' => 'ru',
            'name' => 'Moskovia Airlines',
        ],
        [
            'id' => '3s',
            'alias' => '',
            'call_sign' => 'GREEN BIRD',
            'code_iata' => '3s',
            'code_icao' => '',
            'country_id' => 'gp',
            'name' => 'Air Antilles Express',
        ],
        [
            'id' => '3t',
            'alias' => '',
            'call_sign' => 'TURAN',
            'code_iata' => '3t',
            'code_icao' => 'urn',
            'country_id' => 'az',
            'name' => 'Turan Air',
        ],
        [
            'id' => '3u',
            'alias' => '',
            'call_sign' => 'SI CHUAN',
            'code_iata' => '3u',
            'code_icao' => 'csc',
            'country_id' => 'cn',
            'name' => 'Sichuan Airlines',
        ],
        [
            'id' => '3w',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '3w',
            'code_icao' => '',
            'country_id' => 'mw',
            'name' => 'Malawian Airlines',
        ],
        [
            'id' => '47.0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '47.0',
            'code_icao' => 'vvn',
            'country_id' => 'cy',
            'name' => '88',
        ],
        [
            'id' => '4a',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '4a',
            'code_icao' => 'akl',
            'country_id' => 'ki',
            'name' => 'Air Kiribati',
        ],
        [
            'id' => '4c',
            'alias' => '',
            'call_sign' => ' Aerovias de Integracion Regional',
            'code_iata' => '4c',
            'code_icao' => 'are',
            'country_id' => '',
            'name' => 'Aires',
        ],
        [
            'id' => '4d',
            'alias' => '',
            'call_sign' => 'AIR SINAI',
            'code_iata' => '4d',
            'code_icao' => 'asd',
            'country_id' => 'eg',
            'name' => 'Air Sinai',
        ],
        [
            'id' => '4g',
            'alias' => '',
            'call_sign' => 'GAZPROMAVIA',
            'code_iata' => '4g',
            'code_icao' => 'gzp',
            'country_id' => 'ru',
            'name' => 'Gazpromavia',
        ],
        [
            'id' => '4h',
            'alias' => '',
            'call_sign' => 'UNITED BANGLADESH',
            'code_iata' => '4h',
            'code_icao' => 'ubd',
            'country_id' => 'bd',
            'name' => 'United Airways',
        ],
        [
            'id' => '4k',
            'alias' => '',
            'call_sign' => 'AL-AAS',
            'code_iata' => '4k',
            'code_icao' => 'aas',
            'country_id' => 'pk',
            'name' => 'Askari Aviation',
        ],
        [
            'id' => '4l',
            'alias' => '',
            'call_sign' => 'GEO-LINE',
            'code_iata' => '4l',
            'code_icao' => 'mjx',
            'country_id' => 'ge',
            'name' => 'Euroline',
        ],
        [
            'id' => '4m',
            'alias' => '',
            'call_sign' => 'LAN AR',
            'code_iata' => '4m',
            'code_icao' => 'dsm',
            'country_id' => 'ar',
            'name' => 'LAN Argentina',
        ],
        [
            'id' => '4n',
            'alias' => '',
            'call_sign' => 'AIR NORTH',
            'code_iata' => '4n',
            'code_icao' => 'ant',
            'country_id' => 'ca',
            'name' => 'Air North Charter - Canada',
        ],
        [
            'id' => '4o',
            'alias' => '',
            'call_sign' => 'INTERJET',
            'code_iata' => '4o',
            'code_icao' => '',
            'country_id' => 'mx',
            'name' => 'Interjet (ABC Aerolineas)',
        ],
        [
            'id' => '4p',
            'alias' => '',
            'call_sign' => 'AFRICAN BUSINESS',
            'code_iata' => '4p',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'Business Aviation',
        ],
        [
            'id' => '4q',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '4q',
            'code_icao' => '',
            'country_id' => 'af',
            'name' => 'Safi Airlines',
        ],
        [
            'id' => '4r',
            'alias' => '',
            'call_sign' => 'HAMBURG JET',
            'code_iata' => '4r',
            'code_icao' => 'hhi',
            'country_id' => 'de',
            'name' => 'Hamburg International',
        ],
        [
            'id' => '4t',
            'alias' => '',
            'call_sign' => 'BELAIR',
            'code_iata' => '4t',
            'code_icao' => 'bhp',
            'country_id' => 'ch',
            'name' => 'Belair Airlines',
        ],
        [
            'id' => '4u',
            'alias' => '',
            'call_sign' => 'GERMAN WINGS',
            'code_iata' => '4u',
            'code_icao' => 'gwi',
            'country_id' => 'de',
            'name' => 'Germanwings',
        ],
        [
            'id' => '4x',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '4x',
            'code_icao' => '',
            'country_id' => 'mx',
            'name' => 'Red Jet Mexico',
        ],
        [
            'id' => '4z',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '4z',
            'code_icao' => '',
            'country_id' => 'za',
            'name' => 'Airlink (SAA)',
        ],
        [
            'id' => '5c',
            'alias' => '',
            'call_sign' => 'CAL',
            'code_iata' => '5c',
            'code_icao' => 'icl',
            'country_id' => 'il',
            'name' => 'CAL Cargo Air Lines',
        ],
        [
            'id' => '5d',
            'alias' => '',
            'call_sign' => 'COSTERA',
            'code_iata' => '5d',
            'code_icao' => 'sli',
            'country_id' => 'mx',
            'name' => 'Aerolitoral',
        ],
        [
            'id' => '5d',
            'alias' => '',
            'call_sign' => 'DONBASS AERO',
            'code_iata' => '5d',
            'code_icao' => 'udc',
            'country_id' => 'ua',
            'name' => 'DonbassAero',
        ],
        [
            'id' => '5e',
            'alias' => '',
            'call_sign' => 'SIAM',
            'code_iata' => '5e',
            'code_icao' => '',
            'country_id' => 'th',
            'name' => 'SGA Airlines',
        ],
        [
            'id' => '5g',
            'alias' => '',
            'call_sign' => 'SKYTOUR',
            'code_iata' => '5g',
            'code_icao' => 'ssv',
            'country_id' => 'ca',
            'name' => 'Skyservice Airlines',
        ],
        [
            'id' => '5h',
            'alias' => '',
            'call_sign' => 'SWIFT TANGO',
            'code_iata' => '5h',
            'code_icao' => 'ffv',
            'country_id' => 'ke',
            'name' => 'Fly540',
        ],
        [
            'id' => '5j',
            'alias' => '',
            'call_sign' => 'CEBU AIR',
            'code_iata' => '5j',
            'code_icao' => 'ceb',
            'country_id' => 'ph',
            'name' => 'Cebu Pacific',
        ],
        [
            'id' => '5k',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '5k',
            'code_icao' => '',
            'country_id' => 'pt',
            'name' => 'Hi Fly (5K)',
        ],
        [
            'id' => '5l',
            'alias' => '',
            'call_sign' => 'AEROSUR',
            'code_iata' => '5l',
            'code_icao' => 'rsu',
            'country_id' => '',
            'name' => 'Aerosur',
        ],
        [
            'id' => '5m',
            'alias' => '',
            'call_sign' => 'SIBAVIA',
            'code_iata' => '5m',
            'code_icao' => 'sib',
            'country_id' => 'ru',
            'name' => 'Sibaviatrans',
        ],
        [
            'id' => '5n',
            'alias' => '',
            'call_sign' => 'DVINA',
            'code_iata' => '5n',
            'code_icao' => 'aul',
            'country_id' => 'ru',
            'name' => 'Aeroflot-Nord',
        ],
        [
            'id' => '5p',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '5p',
            'code_icao' => '',
            'country_id' => 'cl',
            'name' => 'Pal airlines',
        ],
        [
            'id' => '5q',
            'alias' => 'Buquebus',
            'call_sign' => '',
            'code_iata' => '5q',
            'code_icao' => '',
            'country_id' => 'uy',
            'name' => 'BQB Lineas Aereas',
        ],
        [
            'id' => '5t',
            'alias' => '',
            'call_sign' => 'EMPRESS',
            'code_iata' => '5t',
            'code_icao' => 'mpe',
            'country_id' => 'ca',
            'name' => 'Canadian North',
        ],
        [
            'id' => '5w',
            'alias' => '',
            'call_sign' => 'FLYSTAR',
            'code_iata' => '5w',
            'code_icao' => 'aeu',
            'country_id' => 'gb',
            'name' => 'Astraeus',
        ],
        [
            'id' => '5y',
            'alias' => '',
            'call_sign' => 'GIANT',
            'code_iata' => '5y',
            'code_icao' => 'gti',
            'country_id' => 'us',
            'name' => 'Atlas Air',
        ],
        [
            'id' => '5z',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '5z',
            'code_icao' => 'vvc',
            'country_id' => 'co',
            'name' => 'VivaColombia',
        ],
        [
            'id' => '69.0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '69.0',
            'code_icao' => '',
            'country_id' => 'gb',
            'name' => 'Royal European Airlines',
        ],
        [
            'id' => '6a',
            'alias' => '',
            'call_sign' => 'AVIACSA',
            'code_iata' => '6a',
            'code_icao' => 'chp',
            'country_id' => 'mx',
            'name' => 'Consorcio Aviaxsa',
        ],
        [
            'id' => '6b',
            'alias' => '',
            'call_sign' => 'BLUESCAN',
            'code_iata' => '6b',
            'code_icao' => 'blx',
            'country_id' => 'se',
            'name' => 'TUIfly Nordic',
        ],
        [
            'id' => '6c',
            'alias' => 'Vuela Cuba',
            'call_sign' => '',
            'code_iata' => '6c',
            'code_icao' => '6cc',
            'country_id' => 'cu',
            'name' => 'Vuela Cuba',
        ],
        [
            'id' => '6d',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '6d',
            'code_icao' => '',
            'country_id' => 'id',
            'name' => 'Pelita',
        ],
        [
            'id' => '6e',
            'alias' => '',
            'call_sign' => 'IFLY',
            'code_iata' => '6e',
            'code_icao' => 'igo',
            'country_id' => 'in',
            'name' => 'IndiGo Airlines',
        ],
        [
            'id' => '6f',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '6f',
            'code_icao' => 'mkd',
            'country_id' => 'mk',
            'name' => 'MAT Airways',
        ],
        [
            'id' => '6g',
            'alias' => '',
            'call_sign' => 'RED DRAGON',
            'code_iata' => '6g',
            'code_icao' => 'aww',
            'country_id' => 'gb',
            'name' => 'Air Wales',
        ],
        [
            'id' => '6h',
            'alias' => '',
            'call_sign' => 'ISRAIR',
            'code_iata' => '6h',
            'code_icao' => 'isr',
            'country_id' => 'il',
            'name' => 'Israir',
        ],
        [
            'id' => '6i',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '6i',
            'code_icao' => '',
            'country_id' => 'sl',
            'name' => 'Fly 6ix',
        ],
        [
            'id' => '6j',
            'alias' => '',
            'call_sign' => 'NEWSKY',
            'code_iata' => '6j',
            'code_icao' => 'snj',
            'country_id' => 'jp',
            'name' => 'Skynet Asia Airways',
        ],
        [
            'id' => '6k',
            'alias' => '',
            'call_sign' => 'ASIAN SPIRIT',
            'code_iata' => '6k',
            'code_icao' => 'rit',
            'country_id' => 'ph',
            'name' => 'Asian Spirit',
        ],
        [
            'id' => '6p',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '6p',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Gryphon Airlines',
        ],
        [
            'id' => '6r',
            'alias' => '',
            'call_sign' => 'MIRNY',
            'code_iata' => '6r',
            'code_icao' => 'dru',
            'country_id' => 'ru',
            'name' => 'Alrosa Mirny Air Enterprise',
        ],
        [
            'id' => '6t',
            'alias' => '',
            'call_sign' => 'Six Tango',
            'code_iata' => '6t',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'Air Mandalay',
        ],
        [
            'id' => '6u',
            'alias' => '',
            'call_sign' => 'Loadmaster',
            'code_iata' => '6u',
            'code_icao' => '',
            'country_id' => 'de',
            'name' => 'Air Cargo Germany',
        ],
        [
            'id' => '6w',
            'alias' => '',
            'call_sign' => 'SARATOV AIR',
            'code_iata' => '6w',
            'code_icao' => 'sov',
            'country_id' => 'ru',
            'name' => 'Saratov Aviation Division',
        ],
        [
            'id' => '6y',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '6y',
            'code_icao' => '',
            'country_id' => 'lv',
            'name' => 'SmartLynx Airlines',
        ],
        [
            'id' => '76.0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '76.0',
            'code_icao' => 'sjs',
            'country_id' => 'us',
            'name' => 'Southjet',
        ],
        [
            'id' => '77.0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '77.0',
            'code_icao' => 'zcs',
            'country_id' => 'us',
            'name' => 'Southjet connect',
        ],
        [
            'id' => '78.0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '78.0',
            'code_icao' => 'xan',
            'country_id' => 'us',
            'name' => 'Southjet cargo',
        ],
        [
            'id' => '7b',
            'alias' => '',
            'call_sign' => 'KRASNOJARSKY AIR',
            'code_iata' => '7b',
            'code_icao' => 'kjc',
            'country_id' => 'ru',
            'name' => 'Krasnojarsky Airlines',
        ],
        [
            'id' => '7c',
            'alias' => '',
            'call_sign' => 'JEJU AIR',
            'code_iata' => '7c',
            'code_icao' => 'jja',
            'country_id' => '',
            'name' => 'Jeju Air',
        ],
        [
            'id' => '7e',
            'alias' => '',
            'call_sign' => 'SYLT-AIR',
            'code_iata' => '7e',
            'code_icao' => 'awu',
            'country_id' => 'de',
            'name' => 'Aeroline GmbH',
        ],
        [
            'id' => '7f',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '7f',
            'code_icao' => 'fab',
            'country_id' => 'ca',
            'name' => 'First Air',
        ],
        [
            'id' => '7g',
            'alias' => '',
            'call_sign' => 'STARFLYER',
            'code_iata' => '7g',
            'code_icao' => 'sfj',
            'country_id' => 'jp',
            'name' => 'Star Flyer',
        ],
        [
            'id' => '7h',
            'alias' => '',
            'call_sign' => 'ERAH',
            'code_iata' => '7h',
            'code_icao' => 'err',
            'country_id' => 'us',
            'name' => 'Era Alaska',
        ],
        [
            'id' => '7i',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '7i',
            'code_icao' => '',
            'country_id' => 'an',
            'name' => 'Insel Air (7I/INC) (Priv)',
        ],
        [
            'id' => '7j',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '7j',
            'code_icao' => '',
            'country_id' => 'tj',
            'name' => 'Tajik Air',
        ],
        [
            'id' => '7k',
            'alias' => '',
            'call_sign' => 'KOGALYM',
            'code_iata' => '7k',
            'code_icao' => 'kgl',
            'country_id' => 'ru',
            'name' => 'Kogalymavia Air Company',
        ],
        [
            'id' => '7l',
            'alias' => '',
            'call_sign' => 'ECHO ROMEO',
            'code_iata' => '7l',
            'code_icao' => 'ero',
            'country_id' => 'il',
            'name' => "Sun D'Or",
        ],
        [
            'id' => '7m',
            'alias' => 'Mongol Air',
            'call_sign' => 'Mongol_AIr',
            'code_iata' => '7m',
            'code_icao' => 'ztf',
            'country_id' => 'mn',
            'name' => 'Mongolian International Air Lines',
        ],
        [
            'id' => '7o',
            'alias' => 'All Colombia',
            'call_sign' => '',
            'code_iata' => '7o',
            'code_icao' => '7kk',
            'country_id' => 'co',
            'name' => 'All Colombia',
        ],
        [
            'id' => '7p',
            'alias' => '',
            'call_sign' => 'BATAVIA',
            'code_iata' => '7p',
            'code_icao' => 'btv',
            'country_id' => 'id',
            'name' => 'Metro Batavia',
        ],
        [
            'id' => '7q',
            'alias' => 'PAWA Dominicana',
            'call_sign' => 'PAWA',
            'code_iata' => '7q',
            'code_icao' => '',
            'country_id' => 'do',
            'name' => 'Pan Am World Airways Dominicana',
        ],
        [
            'id' => '7t',
            'alias' => '',
            'call_sign' => 'AIR GLACIERS',
            'code_iata' => '7t',
            'code_icao' => 'agv',
            'country_id' => 'ch',
            'name' => 'Air Glaciers',
        ],
        [
            'id' => '7w',
            'alias' => '',
            'call_sign' => 'WAYRAPER',
            'code_iata' => '7w',
            'code_icao' => '',
            'country_id' => 'pe',
            'name' => 'Wayraper',
        ],
        [
            'id' => '7y',
            'alias' => '',
            'call_sign' => 'FLYING CARPET',
            'code_iata' => '7y',
            'code_icao' => '',
            'country_id' => 'lb',
            'name' => 'Med Airways',
        ],
        [
            'id' => '7z',
            'alias' => '',
            'call_sign' => 'CREOLE',
            'code_iata' => '7z',
            'code_icao' => '',
            'country_id' => 'cv',
            'name' => 'Halcyonair',
        ],
        [
            'id' => '88.0',
            'alias' => 'All Australia',
            'call_sign' => '',
            'code_iata' => '88.0',
            'code_icao' => '8k8',
            'country_id' => 'au',
            'name' => 'All Australia',
        ],
        [
            'id' => '8a',
            'alias' => '',
            'call_sign' => 'ATLAS BLUE',
            'code_iata' => '8a',
            'code_icao' => 'bmm',
            'country_id' => 'ma',
            'name' => 'Atlas Blue',
        ],
        [
            'id' => '8b',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '8b',
            'code_icao' => 'bcc',
            'country_id' => 'th',
            'name' => 'BusinessAir',
        ],
        [
            'id' => '8d',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '8d',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'Astair',
        ],
        [
            'id' => '8e',
            'alias' => '',
            'call_sign' => 'BERING AIR',
            'code_iata' => '8e',
            'code_icao' => 'brg',
            'country_id' => 'us',
            'name' => 'Bering Air',
        ],
        [
            'id' => '8f',
            'alias' => '',
            'call_sign' => 'SAOTOME AIRWAYS',
            'code_iata' => '8f',
            'code_icao' => 'stp',
            'country_id' => 'st',
            'name' => 'STP Airways',
        ],
        [
            'id' => '8h',
            'alias' => '',
            'call_sign' => 'HELIFRANCE',
            'code_iata' => '8h',
            'code_icao' => 'hfr',
            'country_id' => 'fr',
            'name' => 'Heli France',
        ],
        [
            'id' => '8i',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '8i',
            'code_icao' => '',
            'country_id' => 'it',
            'name' => 'Myway Airlines',
        ],
        [
            'id' => '8j',
            'alias' => '',
            'call_sign' => 'ARGAN',
            'code_iata' => '8j',
            'code_icao' => 'jfu',
            'country_id' => 'ma',
            'name' => 'Jet4You',
        ],
        [
            'id' => '8k',
            'alias' => 'Voestar Brasil',
            'call_sign' => '',
            'code_iata' => '8k',
            'code_icao' => 'k88',
            'country_id' => 'br',
            'name' => 'Voestar',
        ],
        [
            'id' => '8l',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '8l',
            'code_icao' => 'cgp',
            'country_id' => 'ae',
            'name' => 'Cargo Plus Aviation',
        ],
        [
            'id' => '8m',
            'alias' => '',
            'call_sign' => 'MAXAIR',
            'code_iata' => '8m',
            'code_icao' => 'mxl',
            'country_id' => 'se',
            'name' => 'Maxair',
        ],
        [
            'id' => '8m',
            'alias' => '',
            'call_sign' => 'MYANMAR',
            'code_iata' => '8m',
            'code_icao' => 'mma',
            'country_id' => 'mm',
            'name' => 'Myanmar Airways International',
        ],
        [
            'id' => '8m',
            'alias' => '',
            'call_sign' => 'assignment postponed',
            'code_iata' => '8m',
            'code_icao' => 'mmm',
            'country_id' => 'mm',
            'name' => 'Myanmar Airways International',
        ],
        [
            'id' => '8n',
            'alias' => '',
            'call_sign' => 'NORDFLIGHT',
            'code_iata' => '8n',
            'code_icao' => 'nkf',
            'country_id' => 'se',
            'name' => 'Barents AirLink',
        ],
        [
            'id' => '8o',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '8o',
            'code_icao' => '',
            'country_id' => 'ca',
            'name' => 'West Coast Air',
        ],
        [
            'id' => '8p',
            'alias' => '',
            'call_sign' => 'PASCO',
            'code_iata' => '8p',
            'code_icao' => 'pco',
            'country_id' => 'ca',
            'name' => 'Pacific Coastal Airline',
        ],
        [
            'id' => '8q',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '8q',
            'code_icao' => '',
            'country_id' => 'mv',
            'name' => 'Maldivian Air Taxi',
        ],
        [
            'id' => '8q',
            'alias' => '',
            'call_sign' => 'ONUR AIR',
            'code_iata' => '8q',
            'code_icao' => 'ohy',
            'country_id' => 'tr',
            'name' => 'Onur Air',
        ],
        [
            'id' => '8r',
            'alias' => '',
            'call_sign' => 'TRIP',
            'code_iata' => '8r',
            'code_icao' => 'tib',
            'country_id' => 'br',
            'name' => 'TRIP Linhas A',
        ],
        [
            'id' => '8t',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '8t',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'Air Tindi',
        ],
        [
            'id' => '8u',
            'alias' => '',
            'call_sign' => 'AFRIQIYAH',
            'code_iata' => '8u',
            'code_icao' => 'aaw',
            'country_id' => '',
            'name' => 'Afriqiyah Airways',
        ],
        [
            'id' => '8v',
            'alias' => '',
            'call_sign' => 'ASTRAL CARGO',
            'code_iata' => '8v',
            'code_icao' => 'acp',
            'country_id' => 'ke',
            'name' => 'Astral Aviation',
        ],
        [
            'id' => '8z',
            'alias' => '',
            'call_sign' => 'WIZZBUL',
            'code_iata' => '8z',
            'code_icao' => 'wvl',
            'country_id' => 'bg',
            'name' => 'Wizz Air Hungary',
        ],
        [
            'id' => '99.0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => '99.0',
            'code_icao' => '',
            'country_id' => 'it',
            'name' => 'Ciao Air',
        ],
        [
            'id' => '9a',
            'alias' => 'All Africa',
            'call_sign' => '',
            'code_iata' => '9a',
            'code_icao' => '99f',
            'country_id' => 'za',
            'name' => 'All Africa',
        ],
        [
            'id' => '9c',
            'alias' => 'Chunqiu Airlines',
            'call_sign' => '',
            'code_iata' => '9c',
            'code_icao' => '',
            'country_id' => 'cn',
            'name' => 'China SSS',
        ],
        [
            'id' => '9e',
            'alias' => '',
            'call_sign' => 'FLAGSHIP',
            'code_iata' => '9e',
            'code_icao' => 'flg',
            'country_id' => 'us',
            'name' => 'Pinnacle Airlines',
        ],
        [
            'id' => '9h',
            'alias' => '',
            'call_sign' => 'MDLR',
            'code_iata' => '9h',
            'code_icao' => '',
            'country_id' => 'in',
            'name' => 'MDLR Airlines',
        ],
        [
            'id' => '9j',
            'alias' => 'Regionalia Chile',
            'call_sign' => '',
            'code_iata' => '9j',
            'code_icao' => 'cr1',
            'country_id' => 'cl',
            'name' => 'Regionalia Chile',
        ],
        [
            'id' => '9k',
            'alias' => '',
            'call_sign' => 'CAIR',
            'code_iata' => '9k',
            'code_icao' => 'kap',
            'country_id' => 'us',
            'name' => 'Cape Air',
        ],
        [
            'id' => '9l',
            'alias' => '',
            'call_sign' => 'COLGAN',
            'code_iata' => '9l',
            'code_icao' => 'cjc',
            'country_id' => 'us',
            'name' => 'Colgan Air',
        ],
        [
            'id' => '9n',
            'alias' => 'Regional Air Iceland',
            'call_sign' => '',
            'code_iata' => '9n',
            'code_icao' => 'n78',
            'country_id' => 'is',
            'name' => 'Regional Air Iceland',
        ],
        [
            'id' => '9q',
            'alias' => '',
            'call_sign' => 'PEEBEE AIR',
            'code_iata' => '9q',
            'code_icao' => 'pba',
            'country_id' => 'th',
            'name' => 'PB Air',
        ],
        [
            'id' => '9r',
            'alias' => '',
            'call_sign' => 'SATENA',
            'code_iata' => '9r',
            'code_icao' => 'nse',
            'country_id' => 'co',
            'name' => 'SATENA',
        ],
        [
            'id' => '9s',
            'alias' => '',
            'call_sign' => 'AIR SPRING',
            'code_iata' => '9s',
            'code_icao' => 'cqh',
            'country_id' => 'cn',
            'name' => 'Spring Airlines',
        ],
        [
            'id' => '9t',
            'alias' => '',
            'call_sign' => 'ATHABASKA',
            'code_iata' => '9t',
            'code_icao' => 'abs',
            'country_id' => 'ca',
            'name' => 'Transwest Air',
        ],
        [
            'id' => '9u',
            'alias' => '',
            'call_sign' => 'AIR MOLDOVA',
            'code_iata' => '9u',
            'code_icao' => 'mld',
            'country_id' => 'md',
            'name' => 'Air Moldova',
        ],
        [
            'id' => '9w',
            'alias' => '',
            'call_sign' => 'JET AIRWAYS',
            'code_iata' => '9w',
            'code_icao' => 'jai',
            'country_id' => 'in',
            'name' => 'Jet Airways',
        ],
        [
            'id' => '9x',
            'alias' => 'Regionalia Venezuela',
            'call_sign' => '',
            'code_iata' => '9x',
            'code_icao' => '9xx',
            'country_id' => '',
            'name' => 'Regionalia Venezuela',
        ],
        [
            'id' => '9y',
            'alias' => '',
            'call_sign' => 'Kazakh',
            'code_iata' => '9y',
            'code_icao' => 'kzk',
            'country_id' => 'kz',
            'name' => 'Air Kazakhstan',
        ],
        [
            'id' => 'a1',
            'alias' => '',
            'call_sign' => 'atifly',
            'code_iata' => 'a1',
            'code_icao' => 'a1f',
            'country_id' => 'us',
            'name' => 'Atifly',
        ],
        [
            'id' => 'a2',
            'alias' => 'All America',
            'call_sign' => '',
            'code_iata' => 'a2',
            'code_icao' => 'al2',
            'country_id' => 'us',
            'name' => 'All America',
        ],
        [
            'id' => 'a3',
            'alias' => '',
            'call_sign' => 'AEGEAN',
            'code_iata' => 'a3',
            'code_icao' => 'aee',
            'country_id' => 'gr',
            'name' => 'Aegean Airlines',
        ],
        [
            'id' => 'a4',
            'alias' => '',
            'call_sign' => 'SOUTHERN WINDS',
            'code_iata' => 'a4',
            'code_icao' => 'swd',
            'country_id' => 'ar',
            'name' => 'Southern Winds Airlines',
        ],
        [
            'id' => 'a5',
            'alias' => '',
            'call_sign' => 'AIRLINAIR',
            'code_iata' => 'a5',
            'code_icao' => 'rla',
            'country_id' => 'fr',
            'name' => 'Airlinair',
        ],
        [
            'id' => 'a6',
            'alias' => '',
            'call_sign' => 'ALPAV',
            'code_iata' => 'a6',
            'code_icao' => '',
            'country_id' => 'at',
            'name' => 'Air Alps Aviation (A6)',
        ],
        [
            'id' => 'a7',
            'alias' => '',
            'call_sign' => 'RED COMET',
            'code_iata' => 'a7',
            'code_icao' => 'mpd',
            'country_id' => 'es',
            'name' => 'Air Plus Comet',
        ],
        [
            'id' => 'a9',
            'alias' => '',
            'call_sign' => 'TAMAZI',
            'code_iata' => 'a9',
            'code_icao' => 'tgz',
            'country_id' => 'ge',
            'name' => 'Georgian Airways',
        ],
        [
            'id' => 'aa',
            'alias' => '',
            'call_sign' => 'AMERICAN',
            'code_iata' => 'aa',
            'code_icao' => 'aal',
            'country_id' => 'us',
            'name' => 'American Airlines',
        ],
        [
            'id' => 'ab',
            'alias' => '',
            'call_sign' => 'AIR BERLIN',
            'code_iata' => 'ab',
            'code_icao' => 'ber',
            'country_id' => 'de',
            'name' => 'Air Berlin',
        ],
        [
            'id' => 'ac',
            'alias' => '',
            'call_sign' => 'AIR CANADA',
            'code_iata' => 'ac',
            'code_icao' => 'aca',
            'country_id' => 'ca',
            'name' => 'Air Canada',
        ],
        [
            'id' => 'ad',
            'alias' => 'Azul Linhas Aéreas Brasileiras',
            'call_sign' => '',
            'code_iata' => 'ad',
            'code_icao' => 'azu',
            'country_id' => 'br',
            'name' => 'Azul',
        ],
        [
            'id' => 'ae',
            'alias' => '',
            'call_sign' => 'Mandarin',
            'code_iata' => 'ae',
            'code_icao' => 'mda',
            'country_id' => 'tw',
            'name' => 'Mandarin Airlines',
        ],
        [
            'id' => 'af',
            'alias' => '',
            'call_sign' => 'AIRFRANS',
            'code_iata' => 'af',
            'code_icao' => 'afr',
            'country_id' => 'fr',
            'name' => 'Air France',
        ],
        [
            'id' => 'ah',
            'alias' => '',
            'call_sign' => 'AIR ALGERIE',
            'code_iata' => 'ah',
            'code_icao' => 'dah',
            'country_id' => 'dz',
            'name' => 'Air Algerie',
        ],
        [
            'id' => 'ai',
            'alias' => '',
            'call_sign' => 'AIRINDIA',
            'code_iata' => 'ai',
            'code_icao' => 'aic',
            'country_id' => 'in',
            'name' => 'Air India Limited',
        ],
        [
            'id' => 'aj',
            'alias' => '',
            'call_sign' => 'AEROLINE',
            'code_iata' => 'aj',
            'code_icao' => 'nig',
            'country_id' => 'ng',
            'name' => 'Aero Contractors',
        ],
        [
            'id' => 'ak',
            'alias' => 'Air Asia',
            'call_sign' => 'ASIAN EXPRESS',
            'code_iata' => 'ak',
            'code_icao' => 'axm',
            'country_id' => 'my',
            'name' => 'AirAsia',
        ],
        [
            'id' => 'al',
            'alias' => '',
            'call_sign' => 'SKYWAY-EX',
            'code_iata' => 'al',
            'code_icao' => 'syx',
            'country_id' => 'us',
            'name' => 'Skywalk Airlines',
        ],
        [
            'id' => 'am',
            'alias' => '',
            'call_sign' => 'AEROMEXICO',
            'code_iata' => 'am',
            'code_icao' => 'amx',
            'country_id' => 'mx',
            'name' => 'AeroMéxico',
        ],
        [
            'id' => 'an',
            'alias' => '',
            'call_sign' => 'ANSETT',
            'code_iata' => 'an',
            'code_icao' => 'aaa',
            'country_id' => 'au',
            'name' => 'Ansett Australia',
        ],
        [
            'id' => 'ao',
            'alias' => '',
            'call_sign' => 'Nova',
            'code_iata' => 'ao',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'Avianova (Russia)',
        ],
        [
            'id' => 'ap',
            'alias' => '',
            'call_sign' => 'HERON',
            'code_iata' => 'ap',
            'code_icao' => 'adh',
            'country_id' => 'it',
            'name' => 'Air One',
        ],
        [
            'id' => 'aq',
            'alias' => '',
            'call_sign' => 'ALOHA',
            'code_iata' => 'aq',
            'code_icao' => 'aah',
            'country_id' => 'us',
            'name' => 'Aloha Airlines',
        ],
        [
            'id' => 'ar',
            'alias' => '',
            'call_sign' => 'ARGENTINA',
            'code_iata' => 'ar',
            'code_icao' => 'arg',
            'country_id' => 'ar',
            'name' => 'Aerolineas Argentinas',
        ],
        [
            'id' => 'as',
            'alias' => '',
            'call_sign' => ' Inc.',
            'code_iata' => 'as',
            'code_icao' => 'asa',
            'country_id' => '',
            'name' => 'Alaska Airlines',
        ],
        [
            'id' => 'at',
            'alias' => '',
            'call_sign' => 'ROYALAIR MAROC',
            'code_iata' => 'at',
            'code_icao' => 'ram',
            'country_id' => 'ma',
            'name' => 'Royal Air Maroc',
        ],
        [
            'id' => 'au',
            'alias' => '',
            'call_sign' => 'AUSTRAL',
            'code_iata' => 'au',
            'code_icao' => 'aut',
            'country_id' => 'ar',
            'name' => 'Austral Lineas Aereas',
        ],
        [
            'id' => 'av',
            'alias' => '',
            'call_sign' => ' S.A.',
            'code_iata' => 'av',
            'code_icao' => 'ava',
            'country_id' => '',
            'name' => 'Avianca - Aerovias Nacionales de Colombia',
        ],
        [
            'id' => 'aw',
            'alias' => '',
            'call_sign' => 'Asian Star',
            'code_iata' => 'aw',
            'code_icao' => 'awm',
            'country_id' => '',
            'name' => 'Asian Wings Airways',
        ],
        [
            'id' => 'ax',
            'alias' => '',
            'call_sign' => 'WATERSKI',
            'code_iata' => 'ax',
            'code_icao' => 'lof',
            'country_id' => 'us',
            'name' => 'Trans States Airlines',
        ],
        [
            'id' => 'ay',
            'alias' => '',
            'call_sign' => 'FINNAIR',
            'code_iata' => 'ay',
            'code_icao' => 'fin',
            'country_id' => 'fi',
            'name' => 'Finnair',
        ],
        [
            'id' => 'az',
            'alias' => '',
            'call_sign' => 'ALITALIA',
            'code_iata' => 'az',
            'code_icao' => 'aza',
            'country_id' => 'it',
            'name' => 'Alitalia',
        ],
        [
            'id' => 'b0',
            'alias' => '',
            'call_sign' => 'aws',
            'code_iata' => 'b0',
            'code_icao' => '666',
            'country_id' => 'us',
            'name' => 'Aws express',
        ],
        [
            'id' => 'b1',
            'alias' => '',
            'call_sign' => 'Baltic',
            'code_iata' => 'b1',
            'code_icao' => 'ba1',
            'country_id' => 'lv',
            'name' => 'Baltic Air lines',
        ],
        [
            'id' => 'b2',
            'alias' => '',
            'call_sign' => 'BELARUS AVIA',
            'code_iata' => 'b2',
            'code_icao' => 'bru',
            'country_id' => 'by',
            'name' => 'Belavia Belarusian Airlines',
        ],
        [
            'id' => 'b3',
            'alias' => '',
            'call_sign' => 'BHUTAN AIR',
            'code_iata' => 'b3',
            'code_icao' => 'btn',
            'country_id' => 'bt',
            'name' => 'Bhutan Airlines',
        ],
        [
            'id' => 'b3',
            'alias' => '',
            'call_sign' => 'BELLVIEW AIRLINES',
            'code_iata' => 'b3',
            'code_icao' => 'blv',
            'country_id' => 'ng',
            'name' => 'Bellview Airlines',
        ],
        [
            'id' => 'b4',
            'alias' => '',
            'call_sign' => 'GLOBESPAN',
            'code_iata' => 'b4',
            'code_icao' => 'gsm',
            'country_id' => 'gb',
            'name' => 'Flyglobespan',
        ],
        [
            'id' => 'b5',
            'alias' => '',
            'call_sign' => 'FLIGHTLINE',
            'code_iata' => 'b5',
            'code_icao' => 'flt',
            'country_id' => 'gb',
            'name' => 'Flightline',
        ],
        [
            'id' => 'b6',
            'alias' => '',
            'call_sign' => 'JETBLUE',
            'code_iata' => 'b6',
            'code_icao' => 'jbu',
            'country_id' => 'us',
            'name' => 'JetBlue Airways',
        ],
        [
            'id' => 'b7',
            'alias' => '',
            'call_sign' => 'Glory',
            'code_iata' => 'b7',
            'code_icao' => 'uia',
            'country_id' => 'tw',
            'name' => 'Uni Air',
        ],
        [
            'id' => 'b8',
            'alias' => '',
            'call_sign' => 'ERITREAN',
            'code_iata' => 'b8',
            'code_icao' => 'ert',
            'country_id' => 'er',
            'name' => 'Eritrean Airlines',
        ],
        [
            'id' => 'b9',
            'alias' => '',
            'call_sign' => 'AIR BANGLA',
            'code_iata' => 'b9',
            'code_icao' => 'bgd',
            'country_id' => 'bd',
            'name' => 'Air Bangladesh',
        ],
        [
            'id' => 'b9',
            'alias' => '',
            'call_sign' => 'IRAN AIRTOUR',
            'code_iata' => 'b9',
            'code_icao' => 'irb',
            'country_id' => 'bt',
            'name' => 'Iran Airtour',
        ],
        [
            'id' => 'ba',
            'alias' => '',
            'call_sign' => 'SPEEDBIRD',
            'code_iata' => 'ba',
            'code_icao' => 'baw',
            'country_id' => 'gb',
            'name' => 'British Airways',
        ],
        [
            'id' => 'bb',
            'alias' => '',
            'call_sign' => 'SEABORNE',
            'code_iata' => 'bb',
            'code_icao' => 'sbs',
            'country_id' => 'us',
            'name' => 'Seaborne Airlines',
        ],
        [
            'id' => 'bc',
            'alias' => '',
            'call_sign' => 'SKYMARK',
            'code_iata' => 'bc',
            'code_icao' => 'sky',
            'country_id' => 'jp',
            'name' => 'Skymark Airlines',
        ],
        [
            'id' => 'bd',
            'alias' => 'bmi British Midland',
            'call_sign' => 'MIDLAND',
            'code_iata' => 'bd',
            'code_icao' => 'bma',
            'country_id' => 'gb',
            'name' => 'bmi',
        ],
        [
            'id' => 'be',
            'alias' => '',
            'call_sign' => 'JERSEY',
            'code_iata' => 'be',
            'code_icao' => 'bee',
            'country_id' => 'gb',
            'name' => 'Flybe',
        ],
        [
            'id' => 'bf',
            'alias' => '',
            'call_sign' => 'CONGOSERV',
            'code_iata' => 'bf',
            'code_icao' => 'rsr',
            'country_id' => '',
            'name' => 'Aero-Service',
        ],
        [
            'id' => 'bg',
            'alias' => '',
            'call_sign' => 'BANGLADESH',
            'code_iata' => 'bg',
            'code_icao' => 'bbc',
            'country_id' => 'bd',
            'name' => 'Biman Bangladesh Airlines',
        ],
        [
            'id' => 'bh',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'bh',
            'code_icao' => '',
            'country_id' => 'ca',
            'name' => 'Hawkair',
        ],
        [
            'id' => 'bi',
            'alias' => '',
            'call_sign' => 'BRUNEI',
            'code_iata' => 'bi',
            'code_icao' => 'rba',
            'country_id' => '',
            'name' => 'Royal Brunei Airlines',
        ],
        [
            'id' => 'bj',
            'alias' => '',
            'call_sign' => 'NOUVELAIR',
            'code_iata' => 'bj',
            'code_icao' => 'lbt',
            'country_id' => 'tn',
            'name' => 'Nouvel Air Tunisie',
        ],
        [
            'id' => 'bk',
            'alias' => '',
            'call_sign' => 'DISTRICT',
            'code_iata' => 'bk',
            'code_icao' => 'pdc',
            'country_id' => 'us',
            'name' => 'Potomac Air',
        ],
        [
            'id' => 'bl',
            'alias' => 'Pacific Airlines',
            'call_sign' => 'PACIFIC AIRLINES',
            'code_iata' => 'bl',
            'code_icao' => 'pic',
            'country_id' => 'vn',
            'name' => 'Jetstar Pacific',
        ],
        [
            'id' => 'bm',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'bm',
            'code_icao' => '',
            'country_id' => 'it',
            'name' => 'Air Sicilia',
        ],
        [
            'id' => 'bn',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'bn',
            'code_icao' => 'hza',
            'country_id' => 'au',
            'name' => 'Horizon Airlines',
        ],
        [
            'id' => 'bp',
            'alias' => '',
            'call_sign' => 'BOTSWANA',
            'code_iata' => 'bp',
            'code_icao' => 'bot',
            'country_id' => 'bw',
            'name' => 'Air Botswana',
        ],
        [
            'id' => 'bq',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'bq',
            'code_icao' => 'bqb',
            'country_id' => 'uy',
            'name' => 'Buquebus Líneas Aéreas',
        ],
        [
            'id' => 'br',
            'alias' => '',
            'call_sign' => 'EVA',
            'code_iata' => 'br',
            'code_icao' => 'eva',
            'country_id' => 'tw',
            'name' => 'EVA Air',
        ],
        [
            'id' => 'bs',
            'alias' => '',
            'call_sign' => 'BANGLA STAR',
            'code_iata' => 'bs',
            'code_icao' => 'ubg',
            'country_id' => 'bd',
            'name' => 'US-Bangla Airlines',
        ],
        [
            'id' => 'bs',
            'alias' => '',
            'call_sign' => 'BRINTEL',
            'code_iata' => 'bs',
            'code_icao' => 'bih',
            'country_id' => 'gb',
            'name' => 'British International Helicopters',
        ],
        [
            'id' => 'bt',
            'alias' => '',
            'call_sign' => 'AIRBALTIC',
            'code_iata' => 'bt',
            'code_icao' => 'bti',
            'country_id' => 'lv',
            'name' => 'Air Baltic',
        ],
        [
            'id' => 'bu',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'bu',
            'code_icao' => 'buu',
            'country_id' => '',
            'name' => 'Baikotovitchestrian Airlines',
        ],
        [
            'id' => 'bv',
            'alias' => '',
            'call_sign' => 'BLUE PANOROMA',
            'code_iata' => 'bv',
            'code_icao' => 'bpa',
            'country_id' => 'it',
            'name' => 'Blue Panorama Airlines',
        ],
        [
            'id' => 'bw',
            'alias' => '',
            'call_sign' => 'CARIBBEAN AIRLINES',
            'code_iata' => 'bw',
            'code_icao' => 'bwa',
            'country_id' => 'tt',
            'name' => 'Caribbean Airlines',
        ],
        [
            'id' => 'bx',
            'alias' => '',
            'call_sign' => 'Air Busan',
            'code_iata' => 'bx',
            'code_icao' => 'abl',
            'country_id' => '',
            'name' => 'Air Busan',
        ],
        [
            'id' => 'by',
            'alias' => '',
            'call_sign' => 'TOMSON',
            'code_iata' => 'by',
            'code_icao' => 'tom',
            'country_id' => 'gb',
            'name' => 'Thomsonfly',
        ],
        [
            'id' => 'bz',
            'alias' => '',
            'call_sign' => 'Stallion',
            'code_iata' => 'bz',
            'code_icao' => 'bsa',
            'country_id' => 'us',
            'name' => 'Black Stallion Airways',
        ],
        [
            'id' => 'c0',
            'alias' => '',
            'call_sign' => 'CENTRALWINGS',
            'code_iata' => 'c0',
            'code_icao' => 'clw',
            'country_id' => 'pl',
            'name' => 'Centralwings',
        ],
        [
            'id' => 'c1',
            'alias' => '',
            'call_sign' => 'CAX',
            'code_iata' => 'c1',
            'code_icao' => 'ca1',
            'country_id' => 'ca',
            'name' => 'CanXpress',
        ],
        [
            'id' => 'c2',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'c2',
            'code_icao' => 'cap',
            'country_id' => 'ca',
            'name' => 'CanXplorer',
        ],
        [
            'id' => 'c3',
            'alias' => 'Contactair',
            'call_sign' => 'CONTACTAIR',
            'code_iata' => 'c3',
            'code_icao' => 'kis',
            'country_id' => 'de',
            'name' => 'Contact Air',
        ],
        [
            'id' => 'c3',
            'alias' => 'qatXpress',
            'call_sign' => '',
            'code_iata' => 'c3',
            'code_icao' => 'qax',
            'country_id' => 'qa',
            'name' => 'QatXpress',
        ],
        [
            'id' => 'c4',
            'alias' => 'lionXpress',
            'call_sign' => 'LIX',
            'code_iata' => 'c4',
            'code_icao' => 'lix',
            'country_id' => 'cm',
            'name' => 'LionXpress',
        ],
        [
            'id' => 'c5',
            'alias' => '',
            'call_sign' => 'COMMUTAIR',
            'code_iata' => 'c5',
            'code_icao' => 'uca',
            'country_id' => 'us',
            'name' => 'CommutAir',
        ],
        [
            'id' => 'c7',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'c7',
            'code_icao' => 'cr7',
            'country_id' => '',
            'name' => 'Sky Wing Pacific',
        ],
        [
            'id' => 'c9',
            'alias' => '',
            'call_sign' => 'CIRRUS AIR',
            'code_iata' => 'c9',
            'code_icao' => 'rus',
            'country_id' => 'de',
            'name' => 'Cirrus Airlines',
        ],
        [
            'id' => 'ca',
            'alias' => '',
            'call_sign' => 'AIR CHINA',
            'code_iata' => 'ca',
            'code_icao' => 'cca',
            'country_id' => 'cn',
            'name' => 'Air China',
        ],
        [
            'id' => 'cb',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'cb',
            'code_icao' => 'ccc',
            'country_id' => 'co',
            'name' => 'CCML Airlines',
        ],
        [
            'id' => 'cc',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'cc',
            'code_icao' => 'mck',
            'country_id' => 'au',
            'name' => 'Macair Airlines',
        ],
        [
            'id' => 'cc',
            'alias' => '',
            'call_sign' => 'ATLANTA',
            'code_iata' => 'cc',
            'code_icao' => 'abd',
            'country_id' => 'is',
            'name' => 'Air Atlanta Icelandic',
        ],
        [
            'id' => 'cd',
            'alias' => '',
            'call_sign' => 'ALLIED',
            'code_iata' => 'cd',
            'code_icao' => '',
            'country_id' => 'in',
            'name' => 'Air India Regional',
        ],
        [
            'id' => 'ce',
            'alias' => '',
            'call_sign' => 'NATIONWIDE',
            'code_iata' => 'ce',
            'code_icao' => 'ntw',
            'country_id' => 'za',
            'name' => 'Nationwide Airlines',
        ],
        [
            'id' => 'cf',
            'alias' => '',
            'call_sign' => 'SWEDESTAR',
            'code_iata' => 'cf',
            'code_icao' => 'sdr',
            'country_id' => 'se',
            'name' => 'City Airline',
        ],
        [
            'id' => 'cg',
            'alias' => '',
            'call_sign' => 'BALUS',
            'code_iata' => 'cg',
            'code_icao' => 'tok',
            'country_id' => 'pg',
            'name' => 'Airlines PNG',
        ],
        [
            'id' => 'ch',
            'alias' => '',
            'call_sign' => 'BEMIDJI',
            'code_iata' => 'ch',
            'code_icao' => 'bmj',
            'country_id' => 'us',
            'name' => 'Bemidji Airlines',
        ],
        [
            'id' => 'ci',
            'alias' => '',
            'call_sign' => 'DYNASTY',
            'code_iata' => 'ci',
            'code_icao' => 'cal',
            'country_id' => 'tw',
            'name' => 'China Airlines',
        ],
        [
            'id' => 'cj',
            'alias' => '',
            'call_sign' => 'FLYER',
            'code_iata' => 'cj',
            'code_icao' => 'cfe',
            'country_id' => 'gb',
            'name' => 'BA CityFlyer',
        ],
        [
            'id' => 'cl',
            'alias' => '',
            'call_sign' => 'HANSALINE',
            'code_iata' => 'cl',
            'code_icao' => 'clh',
            'country_id' => 'de',
            'name' => 'Lufthansa CityLine',
        ],
        [
            'id' => 'cm',
            'alias' => '',
            'call_sign' => 'COPA',
            'code_iata' => 'cm',
            'code_icao' => 'cmp',
            'country_id' => 'pa',
            'name' => 'Copa Airlines',
        ],
        [
            'id' => 'cn',
            'alias' => '',
            'call_sign' => 'CaNational',
            'code_iata' => 'cn',
            'code_icao' => 'ycp',
            'country_id' => 'ca',
            'name' => 'Canadian National Airways',
        ],
        [
            'id' => 'co',
            'alias' => '',
            'call_sign' => 'JETLINK',
            'code_iata' => 'co',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Continental Express',
        ],
        [
            'id' => 'co',
            'alias' => '',
            'call_sign' => 'CONTINENTAL',
            'code_iata' => 'co',
            'code_icao' => 'coa',
            'country_id' => 'us',
            'name' => 'Continental Airlines',
        ],
        [
            'id' => 'cp',
            'alias' => '',
            'call_sign' => 'Compass Rose',
            'code_iata' => 'cp',
            'code_icao' => 'cpz',
            'country_id' => 'us',
            'name' => 'Compass Airlines',
        ],
        [
            'id' => 'cp',
            'alias' => '',
            'call_sign' => 'CANADIAN',
            'code_iata' => 'cp',
            'code_icao' => 'cdn',
            'country_id' => 'ca',
            'name' => 'Canadian Airlines',
        ],
        [
            'id' => 'cq',
            'alias' => 'SOCHI',
            'call_sign' => 'SLOW FROG',
            'code_iata' => 'cq',
            'code_icao' => 'kol',
            'country_id' => 'ru',
            'name' => 'SOCHI AIR',
        ],
        [
            'id' => 'cs',
            'alias' => '',
            'call_sign' => 'AIR MIKE',
            'code_iata' => 'cs',
            'code_icao' => 'cmi',
            'country_id' => 'us',
            'name' => 'Continental Micronesia',
        ],
        [
            'id' => 'ct',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'ct',
            'code_icao' => '',
            'country_id' => 'it',
            'name' => 'Alitalia Cityliner',
        ],
        [
            'id' => 'cu',
            'alias' => '',
            'call_sign' => 'CUBANA',
            'code_iata' => 'cu',
            'code_icao' => 'cub',
            'country_id' => 'cu',
            'name' => 'Cubana de Aviación',
        ],
        [
            'id' => 'cv',
            'alias' => '',
            'call_sign' => 'CHATHAM',
            'code_iata' => 'cv',
            'code_icao' => 'cva',
            'country_id' => 'nz',
            'name' => 'Air Chathams',
        ],
        [
            'id' => 'cw',
            'alias' => '',
            'call_sign' => 'AIR MARSHALLS',
            'code_iata' => 'cw',
            'code_icao' => 'cwm',
            'country_id' => 'mh',
            'name' => 'Air Marshall Islands',
        ],
        [
            'id' => 'cx',
            'alias' => '',
            'call_sign' => 'CATHAY',
            'code_iata' => 'cx',
            'code_icao' => 'cpa',
            'country_id' => '',
            'name' => 'Cathay Pacific',
        ],
        [
            'id' => 'cy',
            'alias' => '',
            'call_sign' => 'CYPRUS',
            'code_iata' => 'cy',
            'code_icao' => 'cyp',
            'country_id' => 'cy',
            'name' => 'Cyprus Airways',
        ],
        [
            'id' => 'cz',
            'alias' => '',
            'call_sign' => 'CHINA SOUTHERN',
            'code_iata' => 'cz',
            'code_icao' => 'csn',
            'country_id' => 'cn',
            'name' => 'China Southern Airlines',
        ],
        [
            'id' => 'd1',
            'alias' => 'Domenican',
            'call_sign' => 'Domenican',
            'code_iata' => 'd1',
            'code_icao' => 'mdo',
            'country_id' => 'do',
            'name' => 'Domenican Airlines',
        ],
        [
            'id' => 'd3',
            'alias' => '',
            'call_sign' => 'DALO AIRLINES',
            'code_iata' => 'd3',
            'code_icao' => 'dao',
            'country_id' => 'dj',
            'name' => 'Daallo Airlines',
        ],
        [
            'id' => 'd6',
            'alias' => '',
            'call_sign' => 'INLINE',
            'code_iata' => 'd6',
            'code_icao' => 'iln',
            'country_id' => 'za',
            'name' => 'Interair South Africa',
        ],
        [
            'id' => 'd7',
            'alias' => 'FlyAsianXpress',
            'call_sign' => 'XANADU',
            'code_iata' => 'd7',
            'code_icao' => 'xax',
            'country_id' => 'my',
            'name' => 'AirAsia X',
        ],
        [
            'id' => 'd8',
            'alias' => '',
            'call_sign' => 'DJIBOUTI AIR',
            'code_iata' => 'd8',
            'code_icao' => 'djb',
            'country_id' => 'dj',
            'name' => 'Djibouti Airlines',
        ],
        [
            'id' => 'd9',
            'alias' => '',
            'call_sign' => 'DONAVIA',
            'code_iata' => 'd9',
            'code_icao' => 'dnv',
            'country_id' => 'ru',
            'name' => 'Aeroflot-Don',
        ],
        [
            'id' => 'da',
            'alias' => '',
            'call_sign' => 'DANACO',
            'code_iata' => 'da',
            'code_icao' => '',
            'country_id' => 'ng',
            'name' => 'Dana Air',
        ],
        [
            'id' => 'db',
            'alias' => '',
            'call_sign' => 'BRITAIR',
            'code_iata' => 'db',
            'code_icao' => 'bzh',
            'country_id' => 'fr',
            'name' => 'Brit Air',
        ],
        [
            'id' => 'dc',
            'alias' => '',
            'call_sign' => 'GOLDEN',
            'code_iata' => 'dc',
            'code_icao' => 'gao',
            'country_id' => 'se',
            'name' => 'Golden Air',
        ],
        [
            'id' => 'dd',
            'alias' => '',
            'call_sign' => 'NOK AIR',
            'code_iata' => 'dd',
            'code_icao' => 'nok',
            'country_id' => 'th',
            'name' => 'Nok Air',
        ],
        [
            'id' => 'de',
            'alias' => '',
            'call_sign' => 'CONDOR',
            'code_iata' => 'de',
            'code_icao' => 'cfg',
            'country_id' => 'de',
            'name' => 'Condor Flugdienst',
        ],
        [
            'id' => 'df',
            'alias' => 'Javi',
            'call_sign' => 'MJG',
            'code_iata' => 'df',
            'code_icao' => 'mjg',
            'country_id' => 'pr',
            'name' => 'Michael Airlines',
        ],
        [
            'id' => 'dg',
            'alias' => '',
            'call_sign' => 'SEAIR',
            'code_iata' => 'dg',
            'code_icao' => 'srq',
            'country_id' => 'ph',
            'name' => 'South East Asian Airlines',
        ],
        [
            'id' => 'dh',
            'alias' => 'Dennis Sky Holding',
            'call_sign' => 'DSY',
            'code_iata' => 'dh',
            'code_icao' => 'dsy',
            'country_id' => 'il',
            'name' => 'Dennis Sky',
        ],
        [
            'id' => 'di',
            'alias' => '',
            'call_sign' => 'SPEEDWAY',
            'code_iata' => 'di',
            'code_icao' => 'bag',
            'country_id' => 'de',
            'name' => 'dba',
        ],
        [
            'id' => 'dk',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'dk',
            'code_icao' => 'ela',
            'country_id' => 'au',
            'name' => 'Eastland Air',
        ],
        [
            'id' => 'dl',
            'alias' => '',
            'call_sign' => 'DELTA',
            'code_iata' => 'dl',
            'code_icao' => 'dal',
            'country_id' => 'us',
            'name' => 'Delta Air Lines',
        ],
        [
            'id' => 'dm',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'dm',
            'code_icao' => '',
            'country_id' => 'dk',
            'name' => 'Maersk',
        ],
        [
            'id' => 'dn',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'dn',
            'code_icao' => 'sgg',
            'country_id' => 'sn',
            'name' => 'Senegal Airlines',
        ],
        [
            'id' => 'do',
            'alias' => '',
            'call_sign' => 'DOMINICANA',
            'code_iata' => 'do',
            'code_icao' => 'doa',
            'country_id' => 'do',
            'name' => 'Dominicana de Aviaci',
        ],
        [
            'id' => 'dp',
            'alias' => '',
            'call_sign' => 'JETSET',
            'code_iata' => 'dp',
            'code_icao' => 'fca',
            'country_id' => 'gb',
            'name' => 'First Choice Airways',
        ],
        [
            'id' => 'dq',
            'alias' => '',
            'call_sign' => 'U.S. Virgin Islands',
            'code_iata' => 'dq',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Coastal Air',
        ],
        [
            'id' => 'dr',
            'alias' => '',
            'call_sign' => 'MEDITERRANEE',
            'code_iata' => 'dr',
            'code_icao' => 'bie',
            'country_id' => 'fr',
            'name' => 'Air Mediterranee',
        ],
        [
            'id' => 'ds',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'ds',
            'code_icao' => '',
            'country_id' => 'ch',
            'name' => 'EasyJet (DS)',
        ],
        [
            'id' => 'dt',
            'alias' => '',
            'call_sign' => 'DTA',
            'code_iata' => 'dt',
            'code_icao' => 'dta',
            'country_id' => 'ao',
            'name' => 'TAAG Angola Airlines',
        ],
        [
            'id' => 'dv',
            'alias' => '',
            'call_sign' => 'VLASTA',
            'code_iata' => 'dv',
            'code_icao' => 'vsv',
            'country_id' => 'kz',
            'name' => 'Scat Air',
        ],
        [
            'id' => 'dx',
            'alias' => '',
            'call_sign' => 'DANISH',
            'code_iata' => 'dx',
            'code_icao' => 'dtr',
            'country_id' => 'dk',
            'name' => 'DAT Danish Air Transport',
        ],
        [
            'id' => 'dy',
            'alias' => '',
            'call_sign' => 'NOR SHUTTLE',
            'code_iata' => 'dy',
            'code_icao' => 'nax',
            'country_id' => 'no',
            'name' => 'Norwegian Air Shuttle',
        ],
        [
            'id' => 'dz',
            'alias' => '',
            'call_sign' => 'ALUNK',
            'code_iata' => 'dz',
            'code_icao' => '',
            'country_id' => 'kz',
            'name' => 'Starline.kz',
        ],
        [
            'id' => 'e1',
            'alias' => 'USky',
            'call_sign' => 'USKY',
            'code_iata' => 'e1',
            'code_icao' => 'es2',
            'country_id' => 'us',
            'name' => 'Usa Sky Cargo',
        ],
        [
            'id' => 'e3',
            'alias' => '',
            'call_sign' => 'DOMODEDOVO',
            'code_iata' => 'e3',
            'code_icao' => 'dmo',
            'country_id' => 'ru',
            'name' => 'Domodedovo Airlines',
        ],
        [
            'id' => 'e4',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'e4',
            'code_icao' => 'gie',
            'country_id' => 'cm',
            'name' => 'Elysian Airlines',
        ],
        [
            'id' => 'e5',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'e5',
            'code_icao' => 'rbg',
            'country_id' => 'eg',
            'name' => 'Air Arabia Egypt',
        ],
        [
            'id' => 'e9',
            'alias' => '',
            'call_sign' => 'AFRICOMPANY',
            'code_iata' => 'e9',
            'code_icao' => '',
            'country_id' => '',
            'name' => "Compagnie Africaine d'Aviation",
        ],
        [
            'id' => 'ea',
            'alias' => '',
            'call_sign' => 'STAR WING',
            'code_iata' => 'ea',
            'code_icao' => 'eal',
            'country_id' => 'de',
            'name' => 'European Air Express',
        ],
        [
            'id' => 'ec',
            'alias' => '',
            'call_sign' => 'TWINARROW',
            'code_iata' => 'ec',
            'code_icao' => 'twn',
            'country_id' => 'uz',
            'name' => 'Avialeasing Aviation Company',
        ],
        [
            'id' => 'ed',
            'alias' => '',
            'call_sign' => 'PAKBLUE',
            'code_iata' => 'ed',
            'code_icao' => 'abq',
            'country_id' => 'pk',
            'name' => 'Airblue',
        ],
        [
            'id' => 'ef',
            'alias' => '',
            'call_sign' => 'Far Eastern',
            'code_iata' => 'ef',
            'code_icao' => 'efa',
            'country_id' => 'tw',
            'name' => 'Far Eastern Air Transport',
        ],
        [
            'id' => 'eg',
            'alias' => '',
            'call_sign' => 'ASIA',
            'code_iata' => 'eg',
            'code_icao' => 'jaa',
            'country_id' => 'jp',
            'name' => 'Japan Asia Airways',
        ],
        [
            'id' => 'ei',
            'alias' => '',
            'call_sign' => 'SHAMROCK',
            'code_iata' => 'ei',
            'code_icao' => 'ein',
            'country_id' => 'ie',
            'name' => 'Aer Lingus',
        ],
        [
            'id' => 'ej',
            'alias' => '',
            'call_sign' => 'NEW ENGLAND',
            'code_iata' => 'ej',
            'code_icao' => 'nea',
            'country_id' => 'us',
            'name' => 'New England Airlines',
        ],
        [
            'id' => 'ek',
            'alias' => 'Emirates Airlines',
            'call_sign' => 'EMIRATES',
            'code_iata' => 'ek',
            'code_icao' => 'uae',
            'country_id' => 'ae',
            'name' => 'Emirates',
        ],
        [
            'id' => 'el',
            'alias' => '',
            'call_sign' => 'ANK AIR',
            'code_iata' => 'el',
            'code_icao' => 'ank',
            'country_id' => 'jp',
            'name' => 'Air Nippon',
        ],
        [
            'id' => 'em',
            'alias' => '',
            'call_sign' => 'AEROBEN',
            'code_iata' => 'em',
            'code_icao' => 'aeb',
            'country_id' => 'bj',
            'name' => 'Aero Benin',
        ],
        [
            'id' => 'en',
            'alias' => '',
            'call_sign' => 'DOLOMOTI',
            'code_iata' => 'en',
            'code_icao' => 'dla',
            'country_id' => 'it',
            'name' => 'Air Dolomiti',
        ],
        [
            'id' => 'eo',
            'alias' => '',
            'call_sign' => 'LONGHORN',
            'code_iata' => 'eo',
            'code_icao' => 'lhn',
            'country_id' => 'us',
            'name' => 'Express One International',
        ],
        [
            'id' => 'ep',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'ep',
            'code_icao' => 'irc',
            'country_id' => '',
            'name' => 'Iran Aseman Airlines',
        ],
        [
            'id' => 'eq',
            'alias' => '',
            'call_sign' => 'TAME',
            'code_iata' => 'eq',
            'code_icao' => 'tae',
            'country_id' => 'ec',
            'name' => 'TAME',
        ],
        [
            'id' => 'er',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'er',
            'code_icao' => 'rww',
            'country_id' => 'es',
            'name' => 'Fly Europa',
        ],
        [
            'id' => 'es',
            'alias' => '',
            'call_sign' => 'EuropeSky',
            'code_iata' => 'es',
            'code_icao' => 'euv',
            'country_id' => 'de',
            'name' => 'EuropeSky',
        ],
        [
            'id' => 'et',
            'alias' => '',
            'call_sign' => 'ETHIOPIAN',
            'code_iata' => 'et',
            'code_icao' => 'eth',
            'country_id' => 'et',
            'name' => 'Ethiopian Airlines',
        ],
        [
            'id' => 'eu',
            'alias' => '',
            'call_sign' => 'ECUATORIANA',
            'code_iata' => 'eu',
            'code_icao' => 'eea',
            'country_id' => 'ec',
            'name' => 'Empresa Ecuatoriana De Aviacion',
        ],
        [
            'id' => 'ev',
            'alias' => '',
            'call_sign' => 'ACEY',
            'code_iata' => 'ev',
            'code_icao' => 'asq',
            'country_id' => 'us',
            'name' => 'Atlantic Southeast Airlines',
        ],
        [
            'id' => 'ew',
            'alias' => '',
            'call_sign' => 'EUROWINGS',
            'code_iata' => 'ew',
            'code_icao' => 'ewg',
            'country_id' => 'de',
            'name' => 'Eurowings',
        ],
        [
            'id' => 'ey',
            'alias' => '',
            'call_sign' => 'ETIHAD',
            'code_iata' => 'ey',
            'code_icao' => 'etd',
            'country_id' => 'ae',
            'name' => 'Etihad Airways',
        ],
        [
            'id' => 'ez',
            'alias' => '',
            'call_sign' => 'EVERGREEN',
            'code_iata' => 'ez',
            'code_icao' => 'eia',
            'country_id' => 'us',
            'name' => 'Evergreen International Airlines',
        ],
        [
            'id' => 'f1',
            'alias' => 'Fly Brasil',
            'call_sign' => 'FBL',
            'code_iata' => 'f1',
            'code_icao' => 'fbl',
            'country_id' => 'br',
            'name' => 'Fly Brasil',
        ],
        [
            'id' => 'f7',
            'alias' => '',
            'call_sign' => 'BABOO',
            'code_iata' => 'f7',
            'code_icao' => 'bbo',
            'country_id' => 'ch',
            'name' => 'Flybaboo',
        ],
        [
            'id' => 'f8',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'f8',
            'code_icao' => '',
            'country_id' => 'nz',
            'name' => 'Air2there',
        ],
        [
            'id' => 'f9',
            'alias' => '',
            'call_sign' => 'FRONTIER FLIGHT',
            'code_iata' => 'f9',
            'code_icao' => 'fft',
            'country_id' => 'us',
            'name' => 'Frontier Airlines',
        ],
        [
            'id' => 'fa',
            'alias' => 'Epic Holidays',
            'call_sign' => 'Epic',
            'code_iata' => 'fa',
            'code_icao' => '4aa',
            'country_id' => 'us',
            'name' => 'Epic Holiday',
        ],
        [
            'id' => 'fb',
            'alias' => '',
            'call_sign' => 'FLYING BULGARIA',
            'code_iata' => 'fb',
            'code_icao' => 'lzb',
            'country_id' => 'bg',
            'name' => 'Bulgaria Air',
        ],
        [
            'id' => 'fc',
            'alias' => '',
            'call_sign' => 'WESTBIRD',
            'code_iata' => 'fc',
            'code_icao' => 'wba',
            'country_id' => 'fi',
            'name' => 'Finncomm Airlines',
        ],
        [
            'id' => 'fd',
            'alias' => 'Thai Air Asia',
            'call_sign' => 'THAI ASIA',
            'code_iata' => 'fd',
            'code_icao' => 'aiq',
            'country_id' => 'th',
            'name' => 'Thai AirAsia',
        ],
        [
            'id' => 'fg',
            'alias' => '',
            'call_sign' => 'ARIANA',
            'code_iata' => 'fg',
            'code_icao' => 'afg',
            'country_id' => 'af',
            'name' => 'Ariana Afghan Airlines',
        ],
        [
            'id' => 'fh',
            'alias' => '',
            'call_sign' => 'FLYHIRELAND',
            'code_iata' => 'fh',
            'code_icao' => 'fhi',
            'country_id' => 'ie',
            'name' => 'FlyHigh Airlines Ireland (FH)',
        ],
        [
            'id' => 'fi',
            'alias' => '',
            'call_sign' => 'ICEAIR',
            'code_iata' => 'fi',
            'code_icao' => 'ice',
            'country_id' => 'is',
            'name' => 'Icelandair',
        ],
        [
            'id' => 'fj',
            'alias' => '',
            'call_sign' => 'PACIFIC',
            'code_iata' => 'fj',
            'code_icao' => 'fji',
            'country_id' => 'fj',
            'name' => 'Air Pacific',
        ],
        [
            'id' => 'fk',
            'alias' => '',
            'call_sign' => 'WEST TOGO',
            'code_iata' => 'fk',
            'code_icao' => 'wta',
            'country_id' => 'tg',
            'name' => 'Africa West',
        ],
        [
            'id' => 'fl',
            'alias' => '',
            'call_sign' => 'CITRUS',
            'code_iata' => 'fl',
            'code_icao' => 'trs',
            'country_id' => 'us',
            'name' => 'AirTran Airways',
        ],
        [
            'id' => 'fm',
            'alias' => '',
            'call_sign' => 'SHANGHAI AIR',
            'code_iata' => 'fm',
            'code_icao' => 'csh',
            'country_id' => 'cn',
            'name' => 'Shanghai Airlines',
        ],
        [
            'id' => 'fn',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'fn',
            'code_icao' => '',
            'country_id' => 'ma',
            'name' => 'Regional Airlines',
        ],
        [
            'id' => 'fo',
            'alias' => '',
            'call_sign' => 'AIRTAS',
            'code_iata' => 'fo',
            'code_icao' => 'atm',
            'country_id' => 'au',
            'name' => 'Airlines Of Tasmania',
        ],
        [
            'id' => 'fp',
            'alias' => '',
            'call_sign' => 'FREEDOM',
            'code_iata' => 'fp',
            'code_icao' => 'fre',
            'country_id' => 'us',
            'name' => 'Freedom Air',
        ],
        [
            'id' => 'fq',
            'alias' => '',
            'call_sign' => 'THOMAS COOK',
            'code_iata' => 'fq',
            'code_icao' => 'tcw',
            'country_id' => 'be',
            'name' => 'Thomas Cook Airlines',
        ],
        [
            'id' => 'fr',
            'alias' => '',
            'call_sign' => 'RYANAIR',
            'code_iata' => 'fr',
            'code_icao' => 'ryr',
            'country_id' => 'ie',
            'name' => 'Ryanair',
        ],
        [
            'id' => 'fs',
            'alias' => '',
            'call_sign' => 'FUEGUINO',
            'code_iata' => 'fs',
            'code_icao' => 'stu',
            'country_id' => 'ar',
            'name' => 'Servicios de Transportes A',
        ],
        [
            'id' => 'ft',
            'alias' => '',
            'call_sign' => 'SIEMREAP AIR',
            'code_iata' => 'ft',
            'code_icao' => 'srh',
            'country_id' => 'kh',
            'name' => 'Siem Reap Airways',
        ],
        [
            'id' => 'fu',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'fu',
            'code_icao' => 'fxx',
            'country_id' => 'ye',
            'name' => 'Felix Airways',
        ],
        [
            'id' => 'fv',
            'alias' => 'Pulkovo Aviation Enterprise',
            'call_sign' => 'PULKOVO',
            'code_iata' => 'fv',
            'code_icao' => 'sdm',
            'country_id' => 'ru',
            'name' => 'Rossiya-Russian Airlines',
        ],
        [
            'id' => 'fw',
            'alias' => '',
            'call_sign' => 'IBEX',
            'code_iata' => 'fw',
            'code_icao' => 'ibx',
            'country_id' => 'jp',
            'name' => 'Ibex Airlines',
        ],
        [
            'id' => 'fy',
            'alias' => '',
            'call_sign' => 'FIREFLY',
            'code_iata' => 'fy',
            'code_icao' => 'ffm',
            'country_id' => 'my',
            'name' => 'Firefly',
        ],
        [
            'id' => 'fz',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'fz',
            'code_icao' => 'fdb',
            'country_id' => 'ae',
            'name' => 'Fly Dubai',
        ],
        [
            'id' => 'g0',
            'alias' => '',
            'call_sign' => 'GHANA AIRLINES',
            'code_iata' => 'g0',
            'code_icao' => 'ghb',
            'country_id' => 'gh',
            'name' => 'Ghana International Airlines',
        ],
        [
            'id' => 'g1',
            'alias' => 'Indya1',
            'call_sign' => 'Indya1',
            'code_iata' => 'g1',
            'code_icao' => 'ig1',
            'country_id' => 'in',
            'name' => 'Indya Airline Group',
        ],
        [
            'id' => 'g3',
            'alias' => '',
            'call_sign' => 'CONNEXION',
            'code_iata' => 'g3',
            'code_icao' => 'cix',
            'country_id' => 'bi',
            'name' => 'City Connexion Airlines',
        ],
        [
            'id' => 'g3',
            'alias' => '',
            'call_sign' => 'AIR CRETE',
            'code_iata' => 'g3',
            'code_icao' => 'seh',
            'country_id' => 'gr',
            'name' => 'Sky Express',
        ],
        [
            'id' => 'g3',
            'alias' => '',
            'call_sign' => 'GOL TRANSPORTE',
            'code_iata' => 'g3',
            'code_icao' => 'glo',
            'country_id' => 'br',
            'name' => 'Gol Transportes Aéreos',
        ],
        [
            'id' => 'g4',
            'alias' => '',
            'call_sign' => 'ALLEGIANT',
            'code_iata' => 'g4',
            'code_icao' => 'aay',
            'country_id' => 'us',
            'name' => 'Allegiant Air',
        ],
        [
            'id' => 'g5',
            'alias' => 'HUAXIA',
            'call_sign' => '',
            'code_iata' => 'g5',
            'code_icao' => '',
            'country_id' => 'cn',
            'name' => 'Huaxia',
        ],
        [
            'id' => 'g6',
            'alias' => '',
            'call_sign' => 'GOUMRAK',
            'code_iata' => 'g6',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'Air Volga',
        ],
        [
            'id' => 'g7',
            'alias' => '',
            'call_sign' => 'GATEWAY',
            'code_iata' => 'g7',
            'code_icao' => 'gjs',
            'country_id' => 'us',
            'name' => 'GoJet Airlines',
        ],
        [
            'id' => 'g8',
            'alias' => '',
            'call_sign' => 'GOAIR',
            'code_iata' => 'g8',
            'code_icao' => 'gow',
            'country_id' => 'in',
            'name' => 'Go Air',
        ],
        [
            'id' => 'g9',
            'alias' => '',
            'call_sign' => 'ARABIA',
            'code_iata' => 'g9',
            'code_icao' => 'aby',
            'country_id' => 'ae',
            'name' => 'Air Arabia',
        ],
        [
            'id' => 'ga',
            'alias' => '',
            'call_sign' => 'INDONESIA',
            'code_iata' => 'ga',
            'code_icao' => 'gia',
            'country_id' => 'id',
            'name' => 'Garuda Indonesia',
        ],
        [
            'id' => 'gb',
            'alias' => 'BRAZIL AIR',
            'call_sign' => 'BRAZIL AIR',
            'code_iata' => 'gb',
            'code_icao' => 'bze',
            'country_id' => 'br',
            'name' => 'BRAZIL AIR',
        ],
        [
            'id' => 'ge',
            'alias' => '',
            'call_sign' => 'TransAsia',
            'code_iata' => 'ge',
            'code_icao' => 'tna',
            'country_id' => 'tw',
            'name' => 'TransAsia Airways',
        ],
        [
            'id' => 'gf',
            'alias' => '',
            'call_sign' => 'GULF BAHRAIN',
            'code_iata' => 'gf',
            'code_icao' => 'gba',
            'country_id' => 'bh',
            'name' => 'Gulf Air Bahrain',
        ],
        [
            'id' => 'gg',
            'alias' => '',
            'call_sign' => 'GREEN BIRD',
            'code_iata' => 'gg',
            'code_icao' => 'guy',
            'country_id' => 'gf',
            'name' => 'Air Guyane',
        ],
        [
            'id' => 'gh',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'gh',
            'code_icao' => 'glp',
            'country_id' => 'ru',
            'name' => 'Globus',
        ],
        [
            'id' => 'gi',
            'alias' => '',
            'call_sign' => 'ITEK-AIR',
            'code_iata' => 'gi',
            'code_icao' => 'ika',
            'country_id' => 'kg',
            'name' => 'Itek Air',
        ],
        [
            'id' => 'gj',
            'alias' => '',
            'call_sign' => 'EUROFLY',
            'code_iata' => 'gj',
            'code_icao' => 'eeu',
            'country_id' => 'it',
            'name' => 'Eurofly Service',
        ],
        [
            'id' => 'gk',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'gk',
            'code_icao' => '',
            'country_id' => 'pk',
            'name' => 'Genesis',
        ],
        [
            'id' => 'gl',
            'alias' => '',
            'call_sign' => 'GREENLAND',
            'code_iata' => 'gl',
            'code_icao' => 'grl',
            'country_id' => 'dk',
            'name' => 'Air Greenland',
        ],
        [
            'id' => 'gm',
            'alias' => 'Germanair',
            'call_sign' => '',
            'code_iata' => 'gm',
            'code_icao' => 'ger',
            'country_id' => 'de',
            'name' => 'German International Air Lines',
        ],
        [
            'id' => 'gn',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'gn',
            'code_icao' => '',
            'country_id' => 'br',
            'name' => 'GNB Linhas Aereas',
        ],
        [
            'id' => 'go',
            'alias' => '',
            'call_sign' => 'KUZU CARGO',
            'code_iata' => 'go',
            'code_icao' => 'kzu',
            'country_id' => 'tr',
            'name' => 'Kuzu Airlines Cargo',
        ],
        [
            'id' => 'gp',
            'alias' => '',
            'call_sign' => 'GADAIR',
            'code_iata' => 'gp',
            'code_icao' => 'gdr',
            'country_id' => 'es',
            'name' => 'Gadair European Airlines',
        ],
        [
            'id' => 'gr',
            'alias' => '',
            'call_sign' => 'AYLINE',
            'code_iata' => 'gr',
            'code_icao' => 'aur',
            'country_id' => 'gb',
            'name' => 'Aurigny Air Services',
        ],
        [
            'id' => 'gs',
            'alias' => '',
            'call_sign' => 'FOYLE',
            'code_iata' => 'gs',
            'code_icao' => 'upa',
            'country_id' => 'gb',
            'name' => 'Air Foyle',
        ],
        [
            'id' => 'gt',
            'alias' => '',
            'call_sign' => 'GEEBEE AIRWAYS',
            'code_iata' => 'gt',
            'code_icao' => 'gbl',
            'country_id' => 'gb',
            'name' => 'GB Airways',
        ],
        [
            'id' => 'gv',
            'alias' => '',
            'call_sign' => 'Aero Fox',
            'code_iata' => 'gv',
            'code_icao' => 'arf',
            'country_id' => 'de',
            'name' => 'Aero Flight',
        ],
        [
            'id' => 'gw',
            'alias' => '',
            'call_sign' => 'AIR KUBAN',
            'code_iata' => 'gw',
            'code_icao' => 'kil',
            'country_id' => 'ru',
            'name' => 'Kuban Airlines',
        ],
        [
            'id' => 'gy',
            'alias' => '',
            'call_sign' => 'GABON AIRLINES',
            'code_iata' => 'gy',
            'code_icao' => 'gbk',
            'country_id' => 'ga',
            'name' => 'Gabon Airlines',
        ],
        [
            'id' => 'gz',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'gz',
            'code_icao' => 'rar',
            'country_id' => 'ck',
            'name' => 'Air Rarotonga',
        ],
        [
            'id' => 'h1',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'h1',
            'code_icao' => 'ha1',
            'country_id' => 'us',
            'name' => 'Hankook Air US',
        ],
        [
            'id' => 'h2',
            'alias' => '',
            'call_sign' => 'AEROSKY',
            'code_iata' => 'h2',
            'code_icao' => 'sku',
            'country_id' => 'cl',
            'name' => 'Sky Airline',
        ],
        [
            'id' => 'h3',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'h3',
            'code_icao' => '',
            'country_id' => 'ca',
            'name' => 'Harbour Air (Priv)',
        ],
        [
            'id' => 'h5',
            'alias' => '',
            'call_sign' => 'RUSSIAN SKY',
            'code_iata' => 'h5',
            'code_icao' => 'rsy',
            'country_id' => 'ru',
            'name' => 'I-Fly',
        ],
        [
            'id' => 'h6',
            'alias' => '',
            'call_sign' => 'HAGELAND',
            'code_iata' => 'h6',
            'code_icao' => 'hag',
            'country_id' => 'us',
            'name' => 'Hageland Aviation Services',
        ],
        [
            'id' => 'h7',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'h7',
            'code_icao' => '',
            'country_id' => 'ug',
            'name' => 'Eagle Air',
        ],
        [
            'id' => 'h8',
            'alias' => '',
            'call_sign' => 'DALAVIA',
            'code_iata' => 'h8',
            'code_icao' => 'khb',
            'country_id' => 'ru',
            'name' => 'Dalavia',
        ],
        [
            'id' => 'h9',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'h9',
            'code_icao' => '',
            'country_id' => 'tr',
            'name' => 'PEGASUS AIRLINES-',
        ],
        [
            'id' => 'ha',
            'alias' => '',
            'call_sign' => 'HAWAIIAN',
            'code_iata' => 'ha',
            'code_icao' => 'hal',
            'country_id' => 'us',
            'name' => 'Hawaiian Airlines',
        ],
        [
            'id' => 'hc',
            'alias' => 'Himalaya',
            'call_sign' => 'Himalayan',
            'code_iata' => 'hc',
            'code_icao' => 'hym',
            'country_id' => 'np',
            'name' => 'Himalayan Airlines',
        ],
        [
            'id' => 'hd',
            'alias' => '',
            'call_sign' => 'AIR DO',
            'code_iata' => 'hd',
            'code_icao' => 'ado',
            'country_id' => 'jp',
            'name' => 'Hokkaido International Airlines',
        ],
        [
            'id' => 'he',
            'alias' => '',
            'call_sign' => 'WALTER',
            'code_iata' => 'he',
            'code_icao' => 'lgw',
            'country_id' => 'de',
            'name' => 'Luftfahrtgesellschaft Walter',
        ],
        [
            'id' => 'hf',
            'alias' => '',
            'call_sign' => 'HAPAG LLOYD',
            'code_iata' => 'hf',
            'code_icao' => 'hlf',
            'country_id' => 'de',
            'name' => 'Hapagfly',
        ],
        [
            'id' => 'hg',
            'alias' => '',
            'call_sign' => 'FLYNIKI',
            'code_iata' => 'hg',
            'code_icao' => 'nly',
            'country_id' => 'at',
            'name' => 'Niki',
        ],
        [
            'id' => 'hh',
            'alias' => '',
            'call_sign' => 'Air Hamburg',
            'code_iata' => 'hh',
            'code_icao' => 'aho',
            'country_id' => 'de',
            'name' => 'Air Hamburg (AHO)',
        ],
        [
            'id' => 'hi',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'hi',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Papillon Grand Canyon Helicopters',
        ],
        [
            'id' => 'hk',
            'alias' => '',
            'call_sign' => 'Hotel Kilo',
            'code_iata' => 'hk',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'Yangon Airways',
        ],
        [
            'id' => 'hm',
            'alias' => '',
            'call_sign' => 'SEYCHELLES',
            'code_iata' => 'hm',
            'code_icao' => 'sey',
            'country_id' => 'sc',
            'name' => 'Air Seychelles',
        ],
        [
            'id' => 'hn',
            'alias' => '',
            'call_sign' => 'HNX',
            'code_iata' => 'hn',
            'code_icao' => 'hnx',
            'country_id' => '',
            'name' => 'Hankook Airline',
        ],
        [
            'id' => 'ho',
            'alias' => '',
            'call_sign' => 'JUNEYAO AIRLINES',
            'code_iata' => 'ho',
            'code_icao' => 'dkh',
            'country_id' => 'cn',
            'name' => 'Juneyao Airlines',
        ],
        [
            'id' => 'hp',
            'alias' => '',
            'call_sign' => 'CACTUS',
            'code_iata' => 'hp',
            'code_icao' => 'awe',
            'country_id' => 'us',
            'name' => 'America West Airlines',
        ],
        [
            'id' => 'hr',
            'alias' => '',
            'call_sign' => 'LIANHANG',
            'code_iata' => 'hr',
            'code_icao' => 'cua',
            'country_id' => 'cn',
            'name' => 'China United Airlines',
        ],
        [
            'id' => 'ht',
            'alias' => '',
            'call_sign' => 'IMPERIAL',
            'code_iata' => 'ht',
            'code_icao' => 'imp',
            'country_id' => 'gr',
            'name' => 'Hellenic Imperial Airways',
        ],
        [
            'id' => 'hu',
            'alias' => '',
            'call_sign' => 'HAINAN',
            'code_iata' => 'hu',
            'code_icao' => 'chh',
            'country_id' => 'cn',
            'name' => 'Hainan Airlines',
        ],
        [
            'id' => 'hv',
            'alias' => '',
            'call_sign' => 'TRANSAVIA',
            'code_iata' => 'hv',
            'code_icao' => 'tra',
            'country_id' => 'nl',
            'name' => 'Transavia Holland',
        ],
        [
            'id' => 'hw',
            'alias' => '',
            'call_sign' => 'FLYHELLO',
            'code_iata' => 'hw',
            'code_icao' => 'fhe',
            'country_id' => 'ch',
            'name' => 'Hello',
        ],
        [
            'id' => 'hx',
            'alias' => '',
            'call_sign' => 'BAUHINIA',
            'code_iata' => 'hx',
            'code_icao' => 'crk',
            'country_id' => '',
            'name' => 'Hong Kong Airlines',
        ],
        [
            'id' => 'hy',
            'alias' => '',
            'call_sign' => 'UZBEK',
            'code_iata' => 'hy',
            'code_icao' => 'uzb',
            'country_id' => 'uz',
            'name' => 'Uzbekistan Airways',
        ],
        [
            'id' => 'hz',
            'alias' => '',
            'call_sign' => 'SATCO',
            'code_iata' => 'hz',
            'code_icao' => 'soz',
            'country_id' => 'kz',
            'name' => 'Sat Airlines',
        ],
        [
            'id' => 'i2',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'i2',
            'code_icao' => 'ibs',
            'country_id' => 'es',
            'name' => 'Iberia Express',
        ],
        [
            'id' => 'i5',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'i5',
            'code_icao' => 'ids',
            'country_id' => 'id',
            'name' => 'Indonesia Sky',
        ],
        [
            'id' => 'i6',
            'alias' => '',
            'call_sign' => 'LINK',
            'code_iata' => 'i6',
            'code_icao' => 'mxi',
            'country_id' => 'mx',
            'name' => 'MexicanaLink',
        ],
        [
            'id' => 'i7',
            'alias' => '',
            'call_sign' => 'PARAWAY',
            'code_iata' => 'i7',
            'code_icao' => 'pmw',
            'country_id' => 'in',
            'name' => 'Paramount Airways',
        ],
        [
            'id' => 'i8',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'i8',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'IzAvia',
        ],
        [
            'id' => 'i9',
            'alias' => '',
            'call_sign' => 'AIR ITALY',
            'code_iata' => 'i9',
            'code_icao' => 'aey',
            'country_id' => 'it',
            'name' => 'Air Italy',
        ],
        [
            'id' => 'i9',
            'alias' => '',
            'call_sign' => 'INDIGO BLUE',
            'code_iata' => 'i9',
            'code_icao' => 'ibu',
            'country_id' => 'us',
            'name' => 'Indigo',
        ],
        [
            'id' => 'ia',
            'alias' => '',
            'call_sign' => 'IRAQI',
            'code_iata' => 'ia',
            'code_icao' => 'iaw',
            'country_id' => 'iq',
            'name' => 'Iraqi Airways',
        ],
        [
            'id' => 'ib',
            'alias' => '',
            'call_sign' => 'IBERIA',
            'code_iata' => 'ib',
            'code_icao' => 'ibe',
            'country_id' => 'es',
            'name' => 'Iberia Airlines',
        ],
        [
            'id' => 'ic',
            'alias' => '',
            'call_sign' => 'INDAIR',
            'code_iata' => 'ic',
            'code_icao' => 'iac',
            'country_id' => 'in',
            'name' => 'Indian Airlines',
        ],
        [
            'id' => 'id',
            'alias' => '',
            'call_sign' => 'INTERLINK',
            'code_iata' => 'id',
            'code_icao' => 'itk',
            'country_id' => 'za',
            'name' => 'Interlink Airlines',
        ],
        [
            'id' => 'ie',
            'alias' => '',
            'call_sign' => 'SOLOMON',
            'code_iata' => 'ie',
            'code_icao' => 'sol',
            'country_id' => 'sb',
            'name' => 'Solomon Airlines',
        ],
        [
            'id' => 'if',
            'alias' => '',
            'call_sign' => 'PINTADERA',
            'code_iata' => 'if',
            'code_icao' => 'isw',
            'country_id' => 'es',
            'name' => 'Islas Airways',
        ],
        [
            'id' => 'ig',
            'alias' => '',
            'call_sign' => 'MERAIR',
            'code_iata' => 'ig',
            'code_icao' => 'iss',
            'country_id' => 'it',
            'name' => 'Meridiana',
        ],
        [
            'id' => 'ii',
            'alias' => 'Moskva-air',
            'call_sign' => 'moose',
            'code_iata' => 'ii',
            'code_icao' => 'uww',
            'country_id' => 'ru',
            'name' => 'LSM International',
        ],
        [
            'id' => 'ik',
            'alias' => '',
            'call_sign' => 'IMPROTEX',
            'code_iata' => 'ik',
            'code_icao' => 'itx',
            'country_id' => 'az',
            'name' => 'Imair Airlines',
        ],
        [
            'id' => 'in',
            'alias' => '',
            'call_sign' => 'MAKAVIO',
            'code_iata' => 'in',
            'code_icao' => 'mak',
            'country_id' => 'mk',
            'name' => 'MAT Macedonian Airlines',
        ],
        [
            'id' => 'io',
            'alias' => '',
            'call_sign' => 'INDO LINES',
            'code_iata' => 'io',
            'code_icao' => 'iaa',
            'country_id' => 'id',
            'name' => 'Indonesian Airlines',
        ],
        [
            'id' => 'ip',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'ip',
            'code_icao' => 'isx',
            'country_id' => 'is',
            'name' => 'Island Spirit',
        ],
        [
            'id' => 'iq',
            'alias' => '',
            'call_sign' => 'AUGSBURG-AIR',
            'code_iata' => 'iq',
            'code_icao' => 'aub',
            'country_id' => 'de',
            'name' => 'Augsburg Airways',
        ],
        [
            'id' => 'ir',
            'alias' => '',
            'call_sign' => 'IRANAIR',
            'code_iata' => 'ir',
            'code_icao' => 'ira',
            'country_id' => '',
            'name' => 'Iran Air',
        ],
        [
            'id' => 'is',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'is',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Island Airlines',
        ],
        [
            'id' => 'it',
            'alias' => '',
            'call_sign' => 'KINGFISHER',
            'code_iata' => 'it',
            'code_icao' => 'kfr',
            'country_id' => 'in',
            'name' => 'Kingfisher Airlines',
        ],
        [
            'id' => 'iv',
            'alias' => '',
            'call_sign' => 'GHIBLI',
            'code_iata' => 'iv',
            'code_icao' => 'jet',
            'country_id' => 'it',
            'name' => 'Wind Jet',
        ],
        [
            'id' => 'iw',
            'alias' => '',
            'call_sign' => 'WINGS ABADI',
            'code_iata' => 'iw',
            'code_icao' => 'won',
            'country_id' => 'id',
            'name' => 'Wings Air',
        ],
        [
            'id' => 'ix',
            'alias' => '',
            'call_sign' => 'EXPRESS INDIA',
            'code_iata' => 'ix',
            'code_icao' => 'axb',
            'country_id' => 'in',
            'name' => 'Air India Express',
        ],
        [
            'id' => 'iy',
            'alias' => '',
            'call_sign' => 'YEMENI',
            'code_iata' => 'iy',
            'code_icao' => 'iye',
            'country_id' => 'ye',
            'name' => 'Yemenia',
        ],
        [
            'id' => 'iz',
            'alias' => '',
            'call_sign' => 'ARKIA',
            'code_iata' => 'iz',
            'code_icao' => 'aiz',
            'country_id' => 'il',
            'name' => 'Arkia Israel Airlines',
        ],
        [
            'id' => 'j2',
            'alias' => '',
            'call_sign' => 'AZAL',
            'code_iata' => 'j2',
            'code_icao' => 'ahy',
            'country_id' => 'az',
            'name' => 'Azerbaijan Airlines',
        ],
        [
            'id' => 'j3',
            'alias' => '',
            'call_sign' => 'POLARIS',
            'code_iata' => 'j3',
            'code_icao' => 'plr',
            'country_id' => 'ca',
            'name' => 'Northwestern Air',
        ],
        [
            'id' => 'j4',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'j4',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'ALAK',
        ],
        [
            'id' => 'j5',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'j5',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Alaska Seaplane Service',
        ],
        [
            'id' => 'j7',
            'alias' => 'FlyNonstop',
            'call_sign' => 'DNM',
            'code_iata' => 'j7',
            'code_icao' => '',
            'country_id' => 'no',
            'name' => 'Denim Air',
        ],
        [
            'id' => 'j8',
            'alias' => '',
            'call_sign' => 'BERJAYA',
            'code_iata' => 'j8',
            'code_icao' => 'bvt',
            'country_id' => 'my',
            'name' => 'Berjaya Air',
        ],
        [
            'id' => 'j9',
            'alias' => '',
            'call_sign' => 'JAZEERA',
            'code_iata' => 'j9',
            'code_icao' => 'jzr',
            'country_id' => 'kw',
            'name' => 'Jazeera Airways',
        ],
        [
            'id' => 'ja',
            'alias' => '',
            'call_sign' => 'AIR BOSNA',
            'code_iata' => 'ja',
            'code_icao' => 'bon',
            'country_id' => 'ba',
            'name' => 'Air Bosna',
        ],
        [
            'id' => 'jb',
            'alias' => '',
            'call_sign' => 'HELIJET',
            'code_iata' => 'jb',
            'code_icao' => 'jba',
            'country_id' => 'ca',
            'name' => 'Helijet',
        ],
        [
            'id' => 'jc',
            'alias' => '',
            'call_sign' => 'JANEX',
            'code_iata' => 'jc',
            'code_icao' => 'jex',
            'country_id' => 'jp',
            'name' => 'JAL Express',
        ],
        [
            'id' => 'jd',
            'alias' => '',
            'call_sign' => 'Air System',
            'code_iata' => 'jd',
            'code_icao' => 'jas',
            'country_id' => 'jp',
            'name' => 'Japan Air System',
        ],
        [
            'id' => 'je',
            'alias' => '',
            'call_sign' => 'TULCA',
            'code_iata' => 'je',
            'code_icao' => 'mno',
            'country_id' => 'za',
            'name' => 'Mango',
        ],
        [
            'id' => 'jf',
            'alias' => '',
            'call_sign' => 'BEAUTY',
            'code_iata' => 'jf',
            'code_icao' => 'jaf',
            'country_id' => 'be',
            'name' => 'Jetairfly',
        ],
        [
            'id' => 'jh',
            'alias' => '',
            'call_sign' => 'FUJI DREAM',
            'code_iata' => 'jh',
            'code_icao' => '',
            'country_id' => 'jp',
            'name' => 'Fuji Dream Airlines',
        ],
        [
            'id' => 'ji',
            'alias' => '',
            'call_sign' => 'MIDWAY',
            'code_iata' => 'ji',
            'code_icao' => 'mdw',
            'country_id' => 'us',
            'name' => 'Midway Airlines',
        ],
        [
            'id' => 'jj',
            'alias' => '',
            'call_sign' => 'TAM',
            'code_iata' => 'jj',
            'code_icao' => 'tam',
            'country_id' => 'br',
            'name' => 'TAM Brazilian Airlines',
        ],
        [
            'id' => 'jk',
            'alias' => '',
            'call_sign' => 'SPANAIR',
            'code_iata' => 'jk',
            'code_icao' => 'jkk',
            'country_id' => 'es',
            'name' => 'Spanair',
        ],
        [
            'id' => 'jl',
            'alias' => '',
            'call_sign' => 'J-BIRD',
            'code_iata' => 'jl',
            'code_icao' => 'jal',
            'country_id' => 'jp',
            'name' => 'Japan Airlines Domestic',
        ],
        [
            'id' => 'jl',
            'alias' => 'JAL Japan Airlines',
            'call_sign' => 'JAPANAIR',
            'code_iata' => 'jl',
            'code_icao' => 'jal',
            'country_id' => 'jp',
            'name' => 'Japan Airlines',
        ],
        [
            'id' => 'jm',
            'alias' => '',
            'call_sign' => 'JAMAICA',
            'code_iata' => 'jm',
            'code_icao' => 'ajm',
            'country_id' => 'jm',
            'name' => 'Air Jamaica',
        ],
        [
            'id' => 'jn',
            'alias' => '',
            'call_sign' => 'EXPO',
            'code_iata' => 'jn',
            'code_icao' => 'xla',
            'country_id' => 'gb',
            'name' => 'Excel Airways',
        ],
        [
            'id' => 'jo',
            'alias' => '',
            'call_sign' => 'JALWAYS',
            'code_iata' => 'jo',
            'code_icao' => 'jaz',
            'country_id' => 'jp',
            'name' => 'JALways',
        ],
        [
            'id' => 'jp',
            'alias' => '',
            'call_sign' => 'ADRIA',
            'code_iata' => 'jp',
            'code_icao' => 'adr',
            'country_id' => 'si',
            'name' => 'Adria Airways',
        ],
        [
            'id' => 'jq',
            'alias' => '',
            'call_sign' => 'JETSTAR',
            'code_iata' => 'jq',
            'code_icao' => 'jst',
            'country_id' => 'au',
            'name' => 'Jetstar Airways',
        ],
        [
            'id' => 'jr',
            'alias' => '',
            'call_sign' => 'JOY AIR',
            'code_iata' => 'jr',
            'code_icao' => 'joy',
            'country_id' => 'cn',
            'name' => 'Joy Air',
        ],
        [
            'id' => 'js',
            'alias' => '',
            'call_sign' => 'AIR KORYO',
            'code_iata' => 'js',
            'code_icao' => 'kor',
            'country_id' => '',
            'name' => 'Air Koryo',
        ],
        [
            'id' => 'jt',
            'alias' => '',
            'call_sign' => 'LION INTER',
            'code_iata' => 'jt',
            'code_icao' => 'lni',
            'country_id' => 'id',
            'name' => 'Lion Mentari Airlines',
        ],
        [
            'id' => 'ju',
            'alias' => '',
            'call_sign' => 'JAT',
            'code_iata' => 'ju',
            'code_icao' => 'jat',
            'country_id' => 'rs',
            'name' => 'Jat Airways',
        ],
        [
            'id' => 'jv',
            'alias' => '',
            'call_sign' => 'BEARSKIN',
            'code_iata' => 'jv',
            'code_icao' => 'bls',
            'country_id' => 'ca',
            'name' => 'Bearskin Lake Air Service',
        ],
        [
            'id' => 'jw',
            'alias' => '',
            'call_sign' => 'BIG A',
            'code_iata' => 'jw',
            'code_icao' => 'apw',
            'country_id' => 'us',
            'name' => 'Arrow Air',
        ],
        [
            'id' => 'jx',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'jx',
            'code_icao' => 'jsr',
            'country_id' => 'eg',
            'name' => 'Jusur airways',
        ],
        [
            'id' => 'jy',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'jy',
            'code_icao' => 'axz',
            'country_id' => 'it',
            'name' => 'Aereonautica militare',
        ],
        [
            'id' => 'jz',
            'alias' => '',
            'call_sign' => 'SKY EXPRESS',
            'code_iata' => 'jz',
            'code_icao' => 'skx',
            'country_id' => 'se',
            'name' => 'Skyways Express',
        ],
        [
            'id' => 'k1',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'k1',
            'code_icao' => 'koq',
            'country_id' => 'ru',
            'name' => 'Kostromskie avialinii',
        ],
        [
            'id' => 'k2',
            'alias' => '',
            'call_sign' => 'EUROLOT',
            'code_iata' => 'k2',
            'code_icao' => 'elo',
            'country_id' => 'pl',
            'name' => 'Eurolot',
        ],
        [
            'id' => 'k5',
            'alias' => '',
            'call_sign' => 'SASQUATCH',
            'code_iata' => 'k5',
            'code_icao' => 'sqh',
            'country_id' => 'us',
            'name' => 'SeaPort Airlines',
        ],
        [
            'id' => 'k6',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'k6',
            'code_icao' => '',
            'country_id' => 'kh',
            'name' => 'Cambodia Angkor Air (K6)',
        ],
        [
            'id' => 'k7',
            'alias' => '',
            'call_sign' => 'KORAL BLUE',
            'code_iata' => 'k7',
            'code_icao' => 'kbr',
            'country_id' => 'eg',
            'name' => 'KoralBlue Airlines',
        ],
        [
            'id' => 'k8',
            'alias' => '',
            'call_sign' => 'ZAMBIA SKIES',
            'code_iata' => 'k8',
            'code_icao' => '',
            'country_id' => 'zm',
            'name' => 'Zambia Skyways',
        ],
        [
            'id' => 'ka',
            'alias' => '',
            'call_sign' => ' Hong Kong Dragon Airlines',
            'code_iata' => 'ka',
            'code_icao' => 'hda',
            'country_id' => '',
            'name' => 'Dragonair',
        ],
        [
            'id' => 'kb',
            'alias' => '',
            'call_sign' => 'ROYAL BHUTAN',
            'code_iata' => 'kb',
            'code_icao' => 'drk',
            'country_id' => 'bt',
            'name' => 'Druk Air',
        ],
        [
            'id' => 'kc',
            'alias' => '',
            'call_sign' => 'ASTANALINE',
            'code_iata' => 'kc',
            'code_icao' => 'kzr',
            'country_id' => 'kz',
            'name' => 'Air Astana',
        ],
        [
            'id' => 'kd',
            'alias' => '',
            'call_sign' => 'KALININGRAD AIR',
            'code_iata' => 'kd',
            'code_icao' => 'kni',
            'country_id' => 'ru',
            'name' => 'KD Avia',
        ],
        [
            'id' => 'ke',
            'alias' => '',
            'call_sign' => 'KOREANAIR',
            'code_iata' => 'ke',
            'code_icao' => 'kal',
            'country_id' => '',
            'name' => 'Korean Air',
        ],
        [
            'id' => 'kf',
            'alias' => '',
            'call_sign' => 'BLUEFIN',
            'code_iata' => 'kf',
            'code_icao' => 'blf',
            'country_id' => 'fi',
            'name' => 'Blue1',
        ],
        [
            'id' => 'kg',
            'alias' => 'Royal Inc.',
            'call_sign' => 'RAW',
            'code_iata' => 'kg',
            'code_icao' => 'raw',
            'country_id' => 'us',
            'name' => 'Royal Airways',
        ],
        [
            'id' => 'kh',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'kh',
            'code_icao' => 'khk',
            'country_id' => 'ua',
            'name' => 'Kharkiv Airlines',
        ],
        [
            'id' => 'ki',
            'alias' => '',
            'call_sign' => 'ADAM SKY',
            'code_iata' => 'ki',
            'code_icao' => 'dhi',
            'country_id' => 'id',
            'name' => 'Adam Air',
        ],
        [
            'id' => 'kj',
            'alias' => '',
            'call_sign' => 'BEE MED',
            'code_iata' => 'kj',
            'code_icao' => 'laj',
            'country_id' => 'gb',
            'name' => 'British Mediterranean Airways',
        ],
        [
            'id' => 'kk',
            'alias' => '',
            'call_sign' => 'ATLASJET',
            'code_iata' => 'kk',
            'code_icao' => 'kkk',
            'country_id' => 'tr',
            'name' => 'Atlasjet',
        ],
        [
            'id' => 'kl',
            'alias' => '',
            'call_sign' => 'KLM',
            'code_iata' => 'kl',
            'code_icao' => 'klm',
            'country_id' => 'nl',
            'name' => 'KLM Royal Dutch Airlines',
        ],
        [
            'id' => 'km',
            'alias' => '',
            'call_sign' => 'AIR MALTA',
            'code_iata' => 'km',
            'code_icao' => 'amc',
            'country_id' => 'mt',
            'name' => 'Air Malta',
        ],
        [
            'id' => 'kn',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'kn',
            'code_icao' => '',
            'country_id' => 'cn',
            'name' => 'China United',
        ],
        [
            'id' => 'ko',
            'alias' => '',
            'call_sign' => 'ACE AIR',
            'code_iata' => 'ko',
            'code_icao' => 'aer',
            'country_id' => 'us',
            'name' => 'Alaska Central Express',
        ],
        [
            'id' => 'kq',
            'alias' => '',
            'call_sign' => 'KENYA',
            'code_iata' => 'kq',
            'code_icao' => 'kqa',
            'country_id' => 'ke',
            'name' => 'Kenya Airways',
        ],
        [
            'id' => 'kr',
            'alias' => '',
            'call_sign' => 'CONTICOM',
            'code_iata' => 'kr',
            'code_icao' => 'cwk',
            'country_id' => 'km',
            'name' => 'Comores Airlines',
        ],
        [
            'id' => 'ks',
            'alias' => '',
            'call_sign' => 'PENINSULA',
            'code_iata' => 'ks',
            'code_icao' => 'pen',
            'country_id' => 'us',
            'name' => 'Peninsula Airways',
        ],
        [
            'id' => 'kt',
            'alias' => '',
            'call_sign' => 'Vickjet',
            'code_iata' => 'kt',
            'code_icao' => 'vkj',
            'country_id' => 'fr',
            'name' => 'VickJet',
        ],
        [
            'id' => 'ku',
            'alias' => '',
            'call_sign' => 'KUWAITI',
            'code_iata' => 'ku',
            'code_icao' => 'kac',
            'country_id' => 'kw',
            'name' => 'Kuwait Airways',
        ],
        [
            'id' => 'kv',
            'alias' => '',
            'call_sign' => 'AIR MINVODY',
            'code_iata' => 'kv',
            'code_icao' => 'mvd',
            'country_id' => 'ru',
            'name' => 'Kavminvodyavia',
        ],
        [
            'id' => 'kw',
            'alias' => '',
            'call_sign' => 'Carnival Air',
            'code_iata' => 'kw',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Carnival Air Lines',
        ],
        [
            'id' => 'kx',
            'alias' => '',
            'call_sign' => 'CAYMAN',
            'code_iata' => 'kx',
            'code_icao' => 'cay',
            'country_id' => 'ky',
            'name' => 'Cayman Airways',
        ],
        [
            'id' => 'ky',
            'alias' => 'Kreta Sky',
            'call_sign' => 'KSY',
            'code_iata' => 'ky',
            'code_icao' => 'ksy',
            'country_id' => 'gr',
            'name' => 'KSY',
        ],
        [
            'id' => 'l1',
            'alias' => 'All Argentina',
            'call_sign' => '',
            'code_iata' => 'l1',
            'code_icao' => 'al1',
            'country_id' => 'ar',
            'name' => 'All Argentina',
        ],
        [
            'id' => 'l3',
            'alias' => '',
            'call_sign' => 'BILLA TRANSPORT',
            'code_iata' => 'l3',
            'code_icao' => 'lto',
            'country_id' => 'at',
            'name' => 'LTU Austria',
        ],
        [
            'id' => 'l4',
            'alias' => 'Air luch',
            'call_sign' => 'russian sky',
            'code_iata' => 'l4',
            'code_icao' => 'ljj',
            'country_id' => 'ru',
            'name' => 'Luchsh Airlines',
        ],
        [
            'id' => 'l5',
            'alias' => '',
            'call_sign' => 'LUFT TRANSPORT',
            'code_iata' => 'l5',
            'code_icao' => 'ltr',
            'country_id' => 'no',
            'name' => 'Lufttransport',
        ],
        [
            'id' => 'l6',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'l6',
            'code_icao' => 'mai',
            'country_id' => 'mr',
            'name' => 'Mauritania Airlines International',
        ],
        [
            'id' => 'l7',
            'alias' => '',
            'call_sign' => 'ENTERPRISE LUHANSK',
            'code_iata' => 'l7',
            'code_icao' => '',
            'country_id' => 'ua',
            'name' => 'Lugansk Airlines',
        ],
        [
            'id' => 'l8',
            'alias' => '',
            'call_sign' => 'Bluebird',
            'code_iata' => 'l8',
            'code_icao' => 'lbl',
            'country_id' => 'de',
            'name' => 'Line Blue',
        ],
        [
            'id' => 'l9',
            'alias' => 'All Asia',
            'call_sign' => '',
            'code_iata' => 'l9',
            'code_icao' => 'al3',
            'country_id' => 'cn',
            'name' => 'All Asia',
        ],
        [
            'id' => 'la',
            'alias' => '',
            'call_sign' => 'LAN',
            'code_iata' => 'la',
            'code_icao' => 'lan',
            'country_id' => 'cl',
            'name' => 'LAN Airlines',
        ],
        [
            'id' => 'lb',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'lb',
            'code_icao' => '',
            'country_id' => 'in',
            'name' => 'Air Costa',
        ],
        [
            'id' => 'lc',
            'alias' => '',
            'call_sign' => 'VELOG',
            'code_iata' => 'lc',
            'code_icao' => 'vlo',
            'country_id' => 'br',
            'name' => 'Varig Log',
        ],
        [
            'id' => 'lf',
            'alias' => '',
            'call_sign' => 'NORDIC',
            'code_iata' => 'lf',
            'code_icao' => 'ndc',
            'country_id' => 'se',
            'name' => 'FlyNordic',
        ],
        [
            'id' => 'lg',
            'alias' => '',
            'call_sign' => 'LUXAIR',
            'code_iata' => 'lg',
            'code_icao' => 'lgl',
            'country_id' => 'lu',
            'name' => 'Luxair',
        ],
        [
            'id' => 'lh',
            'alias' => '',
            'call_sign' => 'LUFTHANSA',
            'code_iata' => 'lh',
            'code_icao' => 'dlh',
            'country_id' => 'de',
            'name' => 'Lufthansa',
        ],
        [
            'id' => 'lh',
            'alias' => '',
            'call_sign' => 'LUFTHANSA CARGO',
            'code_iata' => 'lh',
            'code_icao' => 'gec',
            'country_id' => 'de',
            'name' => 'Lufthansa Cargo',
        ],
        [
            'id' => 'li',
            'alias' => '',
            'call_sign' => 'LIAT',
            'code_iata' => 'li',
            'code_icao' => 'lia',
            'country_id' => 'ag',
            'name' => 'Leeward Islands Air Transport',
        ],
        [
            'id' => 'lj',
            'alias' => '',
            'call_sign' => 'Jin Air',
            'code_iata' => 'lj',
            'code_icao' => 'jna',
            'country_id' => '',
            'name' => 'Jin Air',
        ],
        [
            'id' => 'lk',
            'alias' => '',
            'call_sign' => 'AIRLUXOR',
            'code_iata' => 'lk',
            'code_icao' => 'lxr',
            'country_id' => 'pt',
            'name' => 'Air Luxor',
        ],
        [
            'id' => 'lm',
            'alias' => '',
            'call_sign' => 'MOZAMBIQUE',
            'code_iata' => 'lm',
            'code_icao' => 'lam',
            'country_id' => 'mz',
            'name' => 'Linhas A',
        ],
        [
            'id' => 'ln',
            'alias' => '',
            'call_sign' => 'LIBAIR',
            'code_iata' => 'ln',
            'code_icao' => 'laa',
            'country_id' => '',
            'name' => 'Libyan Arab Airlines',
        ],
        [
            'id' => 'lo',
            'alias' => '',
            'call_sign' => 'POLLOT',
            'code_iata' => 'lo',
            'code_icao' => 'lot',
            'country_id' => 'pl',
            'name' => 'LOT Polish Airlines',
        ],
        [
            'id' => 'lp',
            'alias' => '',
            'call_sign' => 'LANPERU',
            'code_iata' => 'lp',
            'code_icao' => 'lpe',
            'country_id' => 'pe',
            'name' => 'LAN Peru',
        ],
        [
            'id' => 'lq',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'lq',
            'code_icao' => 'lmm',
            'country_id' => 'ru',
            'name' => 'LCM AIRLINES',
        ],
        [
            'id' => 'lr',
            'alias' => '',
            'call_sign' => 'LACSA',
            'code_iata' => 'lr',
            'code_icao' => 'lrc',
            'country_id' => 'cr',
            'name' => 'LACSA',
        ],
        [
            'id' => 'ls',
            'alias' => '',
            'call_sign' => 'CHANNEX',
            'code_iata' => 'ls',
            'code_icao' => 'exs',
            'country_id' => 'gb',
            'name' => 'Jet2.com',
        ],
        [
            'id' => 'lt',
            'alias' => '',
            'call_sign' => 'LTU',
            'code_iata' => 'lt',
            'code_icao' => 'ltu',
            'country_id' => 'de',
            'name' => 'LTU International',
        ],
        [
            'id' => 'lu',
            'alias' => '',
            'call_sign' => 'LANEX',
            'code_iata' => 'lu',
            'code_icao' => 'lxp',
            'country_id' => 'cl',
            'name' => 'LAN Express',
        ],
        [
            'id' => 'lv',
            'alias' => '',
            'call_sign' => 'ALBANIAN',
            'code_iata' => 'lv',
            'code_icao' => 'lbc',
            'country_id' => 'al',
            'name' => 'Albanian Airlines',
        ],
        [
            'id' => 'lw',
            'alias' => '',
            'call_sign' => 'TSUNAMI',
            'code_iata' => 'lw',
            'code_icao' => 'nmi',
            'country_id' => 'us',
            'name' => 'Pacific Wings',
        ],
        [
            'id' => 'lx',
            'alias' => 'Swiss Airlines',
            'call_sign' => 'SWISS',
            'code_iata' => 'lx',
            'code_icao' => 'swr',
            'country_id' => 'ch',
            'name' => 'Swiss International Air Lines',
        ],
        [
            'id' => 'ly',
            'alias' => '',
            'call_sign' => 'ELAL',
            'code_iata' => 'ly',
            'code_icao' => 'ely',
            'country_id' => 'il',
            'name' => 'El Al Israel Airlines',
        ],
        [
            'id' => 'lz',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'lz',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'Balkan Bulgarian Airlines',
        ],
        [
            'id' => 'm1',
            'alias' => 'Maryland',
            'call_sign' => 'Maryland Flight',
            'code_iata' => 'm1',
            'code_icao' => 'm1f',
            'country_id' => 'us',
            'name' => 'Maryland Air',
        ],
        [
            'id' => 'm2',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'm2',
            'code_icao' => '',
            'country_id' => 'de',
            'name' => 'MHS Aviation GmbH',
        ],
        [
            'id' => 'm3',
            'alias' => '',
            'call_sign' => 'ABSA Cargo',
            'code_iata' => 'm3',
            'code_icao' => 'tus',
            'country_id' => 'br',
            'name' => 'ABSA - Aerolinhas Brasileiras',
        ],
        [
            'id' => 'm4',
            'alias' => 'MARYSYA AIRLINES',
            'call_sign' => 'MARSHAK AIR',
            'code_iata' => 'm4',
            'code_icao' => '1qa',
            'country_id' => 'ru',
            'name' => 'Marysya Airlines',
        ],
        [
            'id' => 'm5',
            'alias' => '',
            'call_sign' => 'KENMORE',
            'code_iata' => 'm5',
            'code_icao' => 'ken',
            'country_id' => 'us',
            'name' => 'Kenmore Air',
        ],
        [
            'id' => 'm7',
            'alias' => '',
            'call_sign' => 'MAS CARGA',
            'code_iata' => 'm7',
            'code_icao' => 'maa',
            'country_id' => 'mx',
            'name' => 'MasAir',
        ],
        [
            'id' => 'm9',
            'alias' => '',
            'call_sign' => 'MOTOR SICH',
            'code_iata' => 'm9',
            'code_icao' => 'msi',
            'country_id' => 'ua',
            'name' => 'Motor Sich',
        ],
        [
            'id' => 'ma',
            'alias' => '',
            'call_sign' => 'MALEV',
            'code_iata' => 'ma',
            'code_icao' => 'mah',
            'country_id' => 'hu',
            'name' => 'Malév',
        ],
        [
            'id' => 'mb',
            'alias' => '',
            'call_sign' => 'BLACK SEA',
            'code_iata' => 'mb',
            'code_icao' => 'mnb',
            'country_id' => 'tr',
            'name' => 'MNG Airlines',
        ],
        [
            'id' => 'md',
            'alias' => '',
            'call_sign' => 'AIR MADAGASCAR',
            'code_iata' => 'md',
            'code_icao' => 'mdg',
            'country_id' => 'mg',
            'name' => 'Air Madagascar',
        ],
        [
            'id' => 'me',
            'alias' => '',
            'call_sign' => 'CEDAR JET',
            'code_iata' => 'me',
            'code_icao' => 'mea',
            'country_id' => 'lb',
            'name' => 'Middle East Airlines',
        ],
        [
            'id' => 'mf',
            'alias' => '',
            'call_sign' => 'XIAMEN AIR',
            'code_iata' => 'mf',
            'code_icao' => 'cxa',
            'country_id' => 'cn',
            'name' => 'Xiamen Airlines',
        ],
        [
            'id' => 'mh',
            'alias' => '',
            'call_sign' => 'MALAYSIAN',
            'code_iata' => 'mh',
            'code_icao' => 'mas',
            'country_id' => 'my',
            'name' => 'Malaysia Airlines',
        ],
        [
            'id' => 'mi',
            'alias' => '',
            'call_sign' => 'SILKAIR',
            'code_iata' => 'mi',
            'code_icao' => 'slk',
            'country_id' => 'sg',
            'name' => 'SilkAir',
        ],
        [
            'id' => 'mj',
            'alias' => '',
            'call_sign' => 'LAPA',
            'code_iata' => 'mj',
            'code_icao' => 'lpr',
            'country_id' => 'ar',
            'name' => 'L',
        ],
        [
            'id' => 'mk',
            'alias' => '',
            'call_sign' => 'AIRMAURITIUS',
            'code_iata' => 'mk',
            'code_icao' => 'mau',
            'country_id' => 'mu',
            'name' => 'Air Mauritius',
        ],
        [
            'id' => 'ml',
            'alias' => '',
            'call_sign' => 'Maldivo',
            'code_iata' => 'ml',
            'code_icao' => 'mav',
            'country_id' => 'mv',
            'name' => 'Maldivo Airlines',
        ],
        [
            'id' => 'mm',
            'alias' => '',
            'call_sign' => 'Air Peach',
            'code_iata' => 'mm',
            'code_icao' => '',
            'country_id' => 'jp',
            'name' => 'Peach Aviation',
        ],
        [
            'id' => 'mn',
            'alias' => '',
            'call_sign' => 'COMMERCIAL',
            'code_iata' => 'mn',
            'code_icao' => 'caw',
            'country_id' => 'za',
            'name' => 'Comair',
        ],
        [
            'id' => 'mo',
            'alias' => '',
            'call_sign' => 'SULTAN',
            'code_iata' => 'mo',
            'code_icao' => 'auh',
            'country_id' => 'ae',
            'name' => 'Abu Dhabi Amiri Flight',
        ],
        [
            'id' => 'mp',
            'alias' => '',
            'call_sign' => 'MARTINAIR',
            'code_iata' => 'mp',
            'code_icao' => 'mph',
            'country_id' => 'nl',
            'name' => 'Martinair',
        ],
        [
            'id' => 'mq',
            'alias' => '',
            'call_sign' => 'EAGLE FLIGHT',
            'code_iata' => 'mq',
            'code_icao' => 'egf',
            'country_id' => 'us',
            'name' => 'American Eagle Airlines',
        ],
        [
            'id' => 'mr',
            'alias' => 'Homer Sky',
            'call_sign' => '',
            'code_iata' => 'mr',
            'code_icao' => 'ome',
            'country_id' => 'de',
            'name' => 'Homer Air',
        ],
        [
            'id' => 'ms',
            'alias' => '',
            'call_sign' => 'EGYPTAIR',
            'code_iata' => 'ms',
            'code_icao' => 'msr',
            'country_id' => 'eg',
            'name' => 'Egyptair',
        ],
        [
            'id' => 'mt',
            'alias' => '',
            'call_sign' => 'KESTREL',
            'code_iata' => 'mt',
            'code_icao' => 'tcx',
            'country_id' => 'gb',
            'name' => 'Thomas Cook Airlines',
        ],
        [
            'id' => 'mu',
            'alias' => '',
            'call_sign' => 'CHINA EASTERN',
            'code_iata' => 'mu',
            'code_icao' => 'ces',
            'country_id' => 'cn',
            'name' => 'China Eastern Airlines',
        ],
        [
            'id' => 'mw',
            'alias' => '',
            'call_sign' => 'MYLAND',
            'code_iata' => 'mw',
            'code_icao' => 'myd',
            'country_id' => 'bz',
            'name' => 'Maya Island Air',
        ],
        [
            'id' => 'mx',
            'alias' => '',
            'call_sign' => 'MEXICANA',
            'code_iata' => 'mx',
            'code_icao' => 'mxa',
            'country_id' => 'mx',
            'name' => 'Mexicana de Aviaci',
        ],
        [
            'id' => 'my',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'my',
            'code_icao' => 'mwa',
            'country_id' => 'eg',
            'name' => 'Midwest Airlines (Egypt)',
        ],
        [
            'id' => 'mz',
            'alias' => '',
            'call_sign' => 'MERPATI',
            'code_iata' => 'mz',
            'code_icao' => 'mna',
            'country_id' => 'id',
            'name' => 'Merpati Nusantara Airlines',
        ],
        [
            'id' => 'n0',
            'alias' => 'NORTE',
            'call_sign' => '',
            'code_iata' => 'n0',
            'code_icao' => '',
            'country_id' => 'ar',
            'name' => 'Norte Lineas Aereas',
        ],
        [
            'id' => 'n1',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'n1',
            'code_icao' => '',
            'country_id' => 'pe',
            'name' => 'N1',
        ],
        [
            'id' => 'n4',
            'alias' => 'Regionalia México',
            'call_sign' => '',
            'code_iata' => 'n4',
            'code_icao' => 'j88',
            'country_id' => 'mx',
            'name' => 'Regionalia México',
        ],
        [
            'id' => 'n5',
            'alias' => '',
            'call_sign' => 'SKAGWAY AIR',
            'code_iata' => 'n5',
            'code_icao' => 'sgy',
            'country_id' => 'us',
            'name' => 'Skagway Air Service',
        ],
        [
            'id' => 'n7',
            'alias' => 'All Spain',
            'call_sign' => '',
            'code_iata' => 'n7',
            'code_icao' => 'n77',
            'country_id' => 'es',
            'name' => 'All Spain',
        ],
        [
            'id' => 'n8',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'n8',
            'code_icao' => 'ncr',
            'country_id' => 'us',
            'name' => 'National Air Cargo',
        ],
        [
            'id' => 'n9',
            'alias' => 'All Europe',
            'call_sign' => '',
            'code_iata' => 'n9',
            'code_icao' => 'n99',
            'country_id' => 'gb',
            'name' => 'All Europe',
        ],
        [
            'id' => 'na',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'na',
            'code_icao' => '',
            'country_id' => 'iq',
            'name' => 'Al-Naser Airlines',
        ],
        [
            'id' => 'nb',
            'alias' => '',
            'call_sign' => 'STERLING',
            'code_iata' => 'nb',
            'code_icao' => 'snb',
            'country_id' => 'dk',
            'name' => 'Sterling Airlines',
        ],
        [
            'id' => 'nc',
            'alias' => '',
            'call_sign' => 'NATIONAL JET',
            'code_iata' => 'nc',
            'code_icao' => 'njs',
            'country_id' => 'au',
            'name' => 'National Jet Systems',
        ],
        [
            'id' => 'ne',
            'alias' => '',
            'call_sign' => 'RELAX',
            'code_iata' => 'ne',
            'code_icao' => 'esk',
            'country_id' => 'sk',
            'name' => 'SkyEurope',
        ],
        [
            'id' => 'nf',
            'alias' => '',
            'call_sign' => 'AIR VAN',
            'code_iata' => 'nf',
            'code_icao' => 'avn',
            'country_id' => 'vu',
            'name' => 'Air Vanuatu',
        ],
        [
            'id' => 'ng',
            'alias' => '',
            'call_sign' => 'LAUDA AIR',
            'code_iata' => 'ng',
            'code_icao' => 'lda',
            'country_id' => 'at',
            'name' => 'Lauda Air',
        ],
        [
            'id' => 'nh',
            'alias' => 'ANA All Nippon Airways',
            'call_sign' => 'ALL NIPPON',
            'code_iata' => 'nh',
            'code_icao' => 'ana',
            'country_id' => 'jp',
            'name' => 'All Nippon Airways',
        ],
        [
            'id' => 'ni',
            'alias' => '',
            'call_sign' => 'PORTUGALIA',
            'code_iata' => 'ni',
            'code_icao' => 'pga',
            'country_id' => 'pt',
            'name' => 'Portugalia',
        ],
        [
            'id' => 'nj',
            'alias' => '',
            'call_sign' => 'Nordic Global',
            'code_iata' => 'nj',
            'code_icao' => 'ngb',
            'country_id' => 'fi',
            'name' => 'Nordic Global Airlines',
        ],
        [
            'id' => 'nk',
            'alias' => '',
            'call_sign' => 'SPIRIT WINGS',
            'code_iata' => 'nk',
            'code_icao' => 'nks',
            'country_id' => 'us',
            'name' => 'Spirit Airlines',
        ],
        [
            'id' => 'nl',
            'alias' => '',
            'call_sign' => 'SHAHEEN AIR',
            'code_iata' => 'nl',
            'code_icao' => 'sai',
            'country_id' => 'pk',
            'name' => 'Shaheen Air International',
        ],
        [
            'id' => 'nm',
            'alias' => '',
            'call_sign' => 'ALADA AIR',
            'code_iata' => 'nm',
            'code_icao' => 'drd',
            'country_id' => 'es',
            'name' => 'Air Madrid',
        ],
        [
            'id' => 'nn',
            'alias' => '',
            'call_sign' => 'MOV AIR',
            'code_iata' => 'nn',
            'code_icao' => 'mov',
            'country_id' => 'ru',
            'name' => 'VIM Airlines',
        ],
        [
            'id' => 'np',
            'alias' => '',
            'call_sign' => 'NILEBIRD',
            'code_iata' => 'np',
            'code_icao' => 'nia',
            'country_id' => 'eg',
            'name' => 'Nile Air',
        ],
        [
            'id' => 'nq',
            'alias' => '',
            'call_sign' => 'AIR JAPAN',
            'code_iata' => 'nq',
            'code_icao' => 'ajx',
            'country_id' => 'jp',
            'name' => 'Air Japan',
        ],
        [
            'id' => 'nr',
            'alias' => 'Jettor',
            'call_sign' => 'JETHAPPY',
            'code_iata' => 'nr',
            'code_icao' => 'jto',
            'country_id' => 'hk',
            'name' => 'Jettor Airlines',
        ],
        [
            'id' => 'ns',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'ns',
            'code_icao' => '',
            'country_id' => 'ge',
            'name' => 'Caucasus Airlines',
        ],
        [
            'id' => 'nt',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'nt',
            'code_icao' => 'ibb',
            'country_id' => 'es',
            'name' => 'Binter Canarias',
        ],
        [
            'id' => 'nu',
            'alias' => '',
            'call_sign' => 'JAI OCEAN',
            'code_iata' => 'nu',
            'code_icao' => 'jta',
            'country_id' => 'jp',
            'name' => 'Japan Transocean Air',
        ],
        [
            'id' => 'nw',
            'alias' => '',
            'call_sign' => 'NORTHWEST',
            'code_iata' => 'nw',
            'code_icao' => 'nwa',
            'country_id' => 'us',
            'name' => 'Northwest Airlines',
        ],
        [
            'id' => 'nx',
            'alias' => '',
            'call_sign' => 'AIR MACAO',
            'code_iata' => 'nx',
            'code_icao' => 'amu',
            'country_id' => 'mo',
            'name' => 'Air Macau',
        ],
        [
            'id' => 'ny',
            'alias' => '',
            'call_sign' => 'FAXI',
            'code_iata' => 'ny',
            'code_icao' => 'fxi',
            'country_id' => 'is',
            'name' => 'Air Iceland',
        ],
        [
            'id' => 'nz',
            'alias' => '',
            'call_sign' => 'NEW ZEALAND',
            'code_iata' => 'nz',
            'code_icao' => 'anz',
            'country_id' => 'nz',
            'name' => 'Air New Zealand',
        ],
        [
            'id' => 'o1',
            'alias' => 'Orbit Azerbaijan',
            'call_sign' => 'Orbitaz',
            'code_iata' => 'o1',
            'code_icao' => 'oab',
            'country_id' => 'az',
            'name' => 'Orbit Airlines Azerbaijan',
        ],
        [
            'id' => 'o2',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'o2',
            'code_icao' => '',
            'country_id' => 'gn',
            'name' => 'Oceanic Airlines',
        ],
        [
            'id' => 'o3',
            'alias' => '',
            'call_sign' => 'SHUN FENG',
            'code_iata' => 'o3',
            'code_icao' => 'css',
            'country_id' => 'cn',
            'name' => 'SF Airlines',
        ],
        [
            'id' => 'o6',
            'alias' => '',
            'call_sign' => 'OCEANAIR',
            'code_iata' => 'o6',
            'code_icao' => 'one',
            'country_id' => 'br',
            'name' => 'Oceanair',
        ],
        [
            'id' => 'o7',
            'alias' => '',
            'call_sign' => 'AUSJET',
            'code_iata' => 'o7',
            'code_icao' => 'ozj',
            'country_id' => 'au',
            'name' => 'Ozjet Airlines',
        ],
        [
            'id' => 'o8',
            'alias' => '',
            'call_sign' => 'OASIS',
            'code_iata' => 'o8',
            'code_icao' => 'ohk',
            'country_id' => 'hk',
            'name' => 'Oasis Hong Kong Airlines',
        ],
        [
            'id' => 'oa',
            'alias' => '',
            'call_sign' => 'OLYMPIC',
            'code_iata' => 'oa',
            'code_icao' => 'oal',
            'country_id' => 'gr',
            'name' => 'Olympic Airlines',
        ],
        [
            'id' => 'ob',
            'alias' => '',
            'call_sign' => 'AIR ASTRAKHAN',
            'code_iata' => 'ob',
            'code_icao' => 'asz',
            'country_id' => 'ru',
            'name' => 'Astrakhan Airlines',
        ],
        [
            'id' => 'oc',
            'alias' => '',
            'call_sign' => 'CATOVAIR',
            'code_iata' => 'oc',
            'code_icao' => '',
            'country_id' => 'mu',
            'name' => 'Catovair',
        ],
        [
            'id' => 'od',
            'alias' => '',
            'call_sign' => 'Malindo',
            'code_iata' => 'od',
            'code_icao' => 'mxd',
            'country_id' => 'my',
            'name' => 'Malindo Air',
        ],
        [
            'id' => 'of',
            'alias' => '',
            'call_sign' => 'AIR FINLAND',
            'code_iata' => 'of',
            'code_icao' => 'fif',
            'country_id' => 'fi',
            'name' => 'Air Finland',
        ],
        [
            'id' => 'og',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'og',
            'code_icao' => '',
            'country_id' => 'ua',
            'name' => 'AirOnix',
        ],
        [
            'id' => 'oh',
            'alias' => '',
            'call_sign' => 'COMAIR',
            'code_iata' => 'oh',
            'code_icao' => 'com',
            'country_id' => 'us',
            'name' => 'Comair',
        ],
        [
            'id' => 'oi',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'oi',
            'code_icao' => 'orc',
            'country_id' => 'au',
            'name' => 'Orchid Airlines',
        ],
        [
            'id' => 'oj',
            'alias' => '',
            'call_sign' => 'OVERLAND',
            'code_iata' => 'oj',
            'code_icao' => 'ola',
            'country_id' => 'ng',
            'name' => 'Overland Airways',
        ],
        [
            'id' => 'ok',
            'alias' => 'CSA Czech Airlines',
            'call_sign' => 'CSA-LINES',
            'code_iata' => 'ok',
            'code_icao' => 'csa',
            'country_id' => 'cz',
            'name' => 'Czech Airlines',
        ],
        [
            'id' => 'ol',
            'alias' => '',
            'call_sign' => 'OLTRA',
            'code_iata' => 'ol',
            'code_icao' => 'olt',
            'country_id' => 'de',
            'name' => 'Ostfriesische Lufttransport',
        ],
        [
            'id' => 'om',
            'alias' => '',
            'call_sign' => 'MONGOL AIR',
            'code_iata' => 'om',
            'code_icao' => 'mgl',
            'country_id' => 'mn',
            'name' => 'MIAT Mongolian Airlines',
        ],
        [
            'id' => 'on',
            'alias' => '',
            'call_sign' => 'AIR NAURU',
            'code_iata' => 'on',
            'code_icao' => 'ron',
            'country_id' => 'nr',
            'name' => 'Nauru Air Corporation',
        ],
        [
            'id' => 'oo',
            'alias' => '',
            'call_sign' => 'SKYWEST',
            'code_iata' => 'oo',
            'code_icao' => 'skw',
            'country_id' => 'us',
            'name' => 'SkyWest',
        ],
        [
            'id' => 'oq',
            'alias' => '',
            'call_sign' => 'CHONG QING',
            'code_iata' => 'oq',
            'code_icao' => 'cqn',
            'country_id' => 'cn',
            'name' => 'Chongqing Airlines',
        ],
        [
            'id' => 'or',
            'alias' => '',
            'call_sign' => 'ARKEFLY',
            'code_iata' => 'or',
            'code_icao' => 'tfl',
            'country_id' => 'nl',
            'name' => 'Arkefly',
        ],
        [
            'id' => 'os',
            'alias' => '',
            'call_sign' => 'AUSTRIAN',
            'code_iata' => 'os',
            'code_icao' => 'aua',
            'country_id' => 'at',
            'name' => 'Austrian Airlines',
        ],
        [
            'id' => 'ot',
            'alias' => '',
            'call_sign' => 'PELICAN',
            'code_iata' => 'ot',
            'code_icao' => 'pel',
            'country_id' => 'au',
            'name' => 'Aeropelican Air Services',
        ],
        [
            'id' => 'ou',
            'alias' => '',
            'call_sign' => 'CROATIA',
            'code_iata' => 'ou',
            'code_icao' => 'ctn',
            'country_id' => 'hr',
            'name' => 'Croatia Airlines',
        ],
        [
            'id' => 'ov',
            'alias' => '',
            'call_sign' => 'ESTONIAN',
            'code_iata' => 'ov',
            'code_icao' => 'ell',
            'country_id' => 'ee',
            'name' => 'Estonian Air',
        ],
        [
            'id' => 'ox',
            'alias' => '',
            'call_sign' => 'ORIENT THAI',
            'code_iata' => 'ox',
            'code_icao' => 'oea',
            'country_id' => 'th',
            'name' => 'Orient Thai Airlines',
        ],
        [
            'id' => 'oy',
            'alias' => '',
            'call_sign' => 'OMNI-EXPRESS',
            'code_iata' => 'oy',
            'code_icao' => 'oae',
            'country_id' => 'us',
            'name' => 'Omni Air International',
        ],
        [
            'id' => 'oz',
            'alias' => '',
            'call_sign' => 'ASIANA',
            'code_iata' => 'oz',
            'code_icao' => 'aar',
            'country_id' => '',
            'name' => 'Asiana Airlines',
        ],
        [
            'id' => 'p0',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'p0',
            'code_icao' => '',
            'country_id' => 'zm',
            'name' => 'Proflight Commuter Services',
        ],
        [
            'id' => 'p4',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'p4',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Patriot Airways',
        ],
        [
            'id' => 'p5',
            'alias' => '',
            'call_sign' => 'AEROREPUBLICA',
            'code_iata' => 'p5',
            'code_icao' => 'rpb',
            'country_id' => 'co',
            'name' => 'AeroRep',
        ],
        [
            'id' => 'p7',
            'alias' => '',
            'call_sign' => 'REGIOPAR',
            'code_iata' => 'p7',
            'code_icao' => 'rep',
            'country_id' => 'py',
            'name' => 'Regional Paraguaya',
        ],
        [
            'id' => 'p8',
            'alias' => '',
            'call_sign' => 'Air Mekong',
            'code_iata' => 'p8',
            'code_icao' => 'mkg',
            'country_id' => 'vn',
            'name' => 'Air Mekong',
        ],
        [
            'id' => 'p9',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'p9',
            'code_icao' => '',
            'country_id' => 'pe',
            'name' => 'Peruvian Airlines',
        ],
        [
            'id' => 'pa',
            'alias' => '',
            'call_sign' => 'IPV',
            'code_iata' => 'pa',
            'code_icao' => 'ipv',
            'country_id' => '',
            'name' => 'Parmiss Airlines (IPV)',
        ],
        [
            'id' => 'pc',
            'alias' => '',
            'call_sign' => 'SUNTURK',
            'code_iata' => 'pc',
            'code_icao' => 'pgt',
            'country_id' => 'tr',
            'name' => 'Pegasus Airlines',
        ],
        [
            'id' => 'pd',
            'alias' => '',
            'call_sign' => 'PORTER AIR',
            'code_iata' => 'pd',
            'code_icao' => 'poe',
            'country_id' => 'ca',
            'name' => 'Porter Airlines',
        ],
        [
            'id' => 'pe',
            'alias' => '',
            'call_sign' => 'AIR EUROPE',
            'code_iata' => 'pe',
            'code_icao' => 'ael',
            'country_id' => 'it',
            'name' => 'Air Europe',
        ],
        [
            'id' => 'pf',
            'alias' => '',
            'call_sign' => 'PRIMERA',
            'code_iata' => 'pf',
            'code_icao' => '',
            'country_id' => 'is',
            'name' => 'Primera Air',
        ],
        [
            'id' => 'pg',
            'alias' => '',
            'call_sign' => 'BANGKOK AIR',
            'code_iata' => 'pg',
            'code_icao' => 'bkp',
            'country_id' => 'th',
            'name' => 'Bangkok Airways',
        ],
        [
            'id' => 'ph',
            'alias' => '',
            'call_sign' => 'POLYNESIAN',
            'code_iata' => 'ph',
            'code_icao' => 'pao',
            'country_id' => 'ws',
            'name' => 'Polynesian Airlines',
        ],
        [
            'id' => 'pi',
            'alias' => '',
            'call_sign' => 'PIEDMONT',
            'code_iata' => 'pi',
            'code_icao' => 'pdt',
            'country_id' => 'us',
            'name' => 'Piedmont Airlines (1948-1989)',
        ],
        [
            'id' => 'pj',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'pj',
            'code_icao' => 'spm',
            'country_id' => 'fr',
            'name' => 'Air Saint Pierre',
        ],
        [
            'id' => 'pk',
            'alias' => 'PIA Pakistan International',
            'call_sign' => 'PAKISTAN',
            'code_iata' => 'pk',
            'code_icao' => 'pia',
            'country_id' => 'pk',
            'name' => 'Pakistan International Airlines',
        ],
        [
            'id' => 'pl',
            'alias' => '',
            'call_sign' => 'Aeroperu',
            'code_iata' => 'pl',
            'code_icao' => 'pli',
            'country_id' => 'pe',
            'name' => 'Aeroper',
        ],
        [
            'id' => 'pm',
            'alias' => '',
            'call_sign' => 'TROPISER',
            'code_iata' => 'pm',
            'code_icao' => 'tos',
            'country_id' => 'bz',
            'name' => 'Tropic Air',
        ],
        [
            'id' => 'pn',
            'alias' => '',
            'call_sign' => 'WEST CHINA',
            'code_iata' => 'pn',
            'code_icao' => 'chb',
            'country_id' => 'cn',
            'name' => 'West Air China',
        ],
        [
            'id' => 'po',
            'alias' => '',
            'call_sign' => 'FlyPortugal',
            'code_iata' => 'po',
            'code_icao' => 'fpt',
            'country_id' => 'pt',
            'name' => 'FlyPortugal',
        ],
        [
            'id' => 'pp',
            'alias' => 'Indus Airlines Pak',
            'call_sign' => 'AIPL',
            'code_iata' => 'pp',
            'code_icao' => 'ai0',
            'country_id' => 'pk',
            'name' => 'Air Indus',
        ],
        [
            'id' => 'pq',
            'alias' => 'slowbird',
            'call_sign' => 'slowbird',
            'code_iata' => 'pq',
            'code_icao' => 'loo',
            'country_id' => 'ru',
            'name' => 'LSM Airlines',
        ],
        [
            'id' => 'pr',
            'alias' => '',
            'call_sign' => 'PHILIPPINE',
            'code_iata' => 'pr',
            'code_icao' => 'pal',
            'country_id' => 'ph',
            'name' => 'Philippine Airlines',
        ],
        [
            'id' => 'ps',
            'alias' => '',
            'call_sign' => 'UKRAINE INTERNATIONAL',
            'code_iata' => 'ps',
            'code_icao' => 'aui',
            'country_id' => 'ua',
            'name' => 'Ukraine International Airlines',
        ],
        [
            'id' => 'pt',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'pt',
            'code_icao' => '',
            'country_id' => 'pe',
            'name' => 'Red Jet Andes',
        ],
        [
            'id' => 'pu',
            'alias' => '',
            'call_sign' => 'PLUNA',
            'code_iata' => 'pu',
            'code_icao' => 'pua',
            'country_id' => 'uy',
            'name' => 'PLUNA',
        ],
        [
            'id' => 'pv',
            'alias' => '',
            'call_sign' => 'SKYJET',
            'code_iata' => 'pv',
            'code_icao' => 'pnr',
            'country_id' => 'es',
            'name' => 'PAN Air',
        ],
        [
            'id' => 'pw',
            'alias' => '',
            'call_sign' => 'PRECISION AIR',
            'code_iata' => 'pw',
            'code_icao' => 'prf',
            'country_id' => '',
            'name' => 'Precision Air',
        ],
        [
            'id' => 'px',
            'alias' => '',
            'call_sign' => 'NUIGINI',
            'code_iata' => 'px',
            'code_icao' => 'ang',
            'country_id' => 'pg',
            'name' => 'Air Niugini',
        ],
        [
            'id' => 'py',
            'alias' => '',
            'call_sign' => 'SURINAM',
            'code_iata' => 'py',
            'code_icao' => 'slm',
            'country_id' => 'sr',
            'name' => 'Surinam Airways',
        ],
        [
            'id' => 'pz',
            'alias' => '',
            'call_sign' => 'PARAGUAYA',
            'code_iata' => 'pz',
            'code_icao' => 'lap',
            'country_id' => 'py',
            'name' => 'TAM Mercosur',
        ],
        [
            'id' => 'q2',
            'alias' => '',
            'call_sign' => 'ISLAND AVIATION',
            'code_iata' => 'q2',
            'code_icao' => '',
            'country_id' => 'mv',
            'name' => 'Maldivian',
        ],
        [
            'id' => 'q3',
            'alias' => 'Sochi Air',
            'call_sign' => 'russian doll',
            'code_iata' => 'q3',
            'code_icao' => 'qer',
            'country_id' => 'ru',
            'name' => 'SOCHI AIR CHATER',
        ],
        [
            'id' => 'q5',
            'alias' => '',
            'call_sign' => 'MILE-AIR',
            'code_iata' => 'q5',
            'code_icao' => 'mla',
            'country_id' => 'us',
            'name' => '40-Mile Air',
        ],
        [
            'id' => 'q6',
            'alias' => '',
            'call_sign' => 'CONDOR-PERU',
            'code_iata' => 'q6',
            'code_icao' => 'cdp',
            'country_id' => 'pe',
            'name' => 'Aero Condor Peru',
        ],
        [
            'id' => 'q8',
            'alias' => '',
            'call_sign' => 'PAC-EAST CARGO',
            'code_iata' => 'q8',
            'code_icao' => 'pec',
            'country_id' => 'ph',
            'name' => 'Pacific East Asia Cargo Airlines',
        ],
        [
            'id' => 'q9',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'q9',
            'code_icao' => 'nak',
            'country_id' => 'ne',
            'name' => 'Arik Niger',
        ],
        [
            'id' => 'qa',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'qa',
            'code_icao' => '',
            'country_id' => 'mx',
            'name' => 'Click (Mexicana)',
        ],
        [
            'id' => 'qb',
            'alias' => '',
            'call_sign' => 'NATIONAL',
            'code_iata' => 'qb',
            'code_icao' => 'gfg',
            'country_id' => 'ge',
            'name' => 'Georgian National Airlines',
        ],
        [
            'id' => 'qc',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'qc',
            'code_icao' => '',
            'country_id' => 'cm',
            'name' => 'Camair-co',
        ],
        [
            'id' => 'qd',
            'alias' => 'Добролёт',
            'call_sign' => 'DOBROLET',
            'code_iata' => 'qd',
            'code_icao' => 'dob',
            'country_id' => 'ru',
            'name' => 'Dobrolet',
        ],
        [
            'id' => 'qe',
            'alias' => '',
            'call_sign' => 'QREX',
            'code_iata' => 'qe',
            'code_icao' => 'qqe',
            'country_id' => 'qa',
            'name' => 'Qatar Executive',
        ],
        [
            'id' => 'qf',
            'alias' => 'Qantas Airways',
            'call_sign' => 'QANTAS',
            'code_iata' => 'qf',
            'code_icao' => 'qfa',
            'country_id' => 'au',
            'name' => 'Qantas',
        ],
        [
            'id' => 'qg',
            'alias' => '',
            'call_sign' => 'SUPERGREEN',
            'code_iata' => 'qg',
            'code_icao' => '',
            'country_id' => 'id',
            'name' => 'Citilink Indonesia',
        ],
        [
            'id' => 'qh',
            'alias' => '',
            'call_sign' => 'AIR FLORIDA',
            'code_iata' => 'qh',
            'code_icao' => 'flz',
            'country_id' => 'us',
            'name' => 'Air Florida',
        ],
        [
            'id' => 'qi',
            'alias' => '',
            'call_sign' => 'CIMBER',
            'code_iata' => 'qi',
            'code_icao' => 'cim',
            'country_id' => 'dk',
            'name' => 'Cimber Air',
        ],
        [
            'id' => 'qj',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'qj',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Jet Airways',
        ],
        [
            'id' => 'qk',
            'alias' => '',
            'call_sign' => 'JAZZ',
            'code_iata' => 'qk',
            'code_icao' => 'jza',
            'country_id' => 'ca',
            'name' => 'Air Canada Jazz',
        ],
        [
            'id' => 'ql',
            'alias' => '',
            'call_sign' => 'AERO LANKA',
            'code_iata' => 'ql',
            'code_icao' => 'rln',
            'country_id' => 'lk',
            'name' => 'Aero Lanka',
        ],
        [
            'id' => 'qm',
            'alias' => '',
            'call_sign' => 'MALAWI',
            'code_iata' => 'qm',
            'code_icao' => 'aml',
            'country_id' => 'mw',
            'name' => 'Air Malawi',
        ],
        [
            'id' => 'qo',
            'alias' => '',
            'call_sign' => 'ORIGIN',
            'code_iata' => 'qo',
            'code_icao' => 'ogn',
            'country_id' => 'nz',
            'name' => 'Origin Pacific Airways',
        ],
        [
            'id' => 'qq',
            'alias' => '',
            'call_sign' => 'UNITY',
            'code_iata' => 'qq',
            'code_icao' => 'uty',
            'country_id' => 'au',
            'name' => 'Alliance Airlines',
        ],
        [
            'id' => 'qr',
            'alias' => '',
            'call_sign' => 'QATARI',
            'code_iata' => 'qr',
            'code_icao' => 'qtr',
            'country_id' => 'qa',
            'name' => 'Qatar Airways',
        ],
        [
            'id' => 'qs',
            'alias' => '',
            'call_sign' => 'SKYTRAVEL',
            'code_iata' => 'qs',
            'code_icao' => 'tvs',
            'country_id' => 'cz',
            'name' => 'Travel Service',
        ],
        [
            'id' => 'qt',
            'alias' => '',
            'call_sign' => 'TAMPA',
            'code_iata' => 'qt',
            'code_icao' => 'tpa',
            'country_id' => 'co',
            'name' => 'TAMPA',
        ],
        [
            'id' => 'qu',
            'alias' => '',
            'call_sign' => 'CRANE',
            'code_iata' => 'qu',
            'code_icao' => 'ugx',
            'country_id' => 'ug',
            'name' => 'East African',
        ],
        [
            'id' => 'qv',
            'alias' => '',
            'call_sign' => 'LAO',
            'code_iata' => 'qv',
            'code_icao' => 'lao',
            'country_id' => '',
            'name' => 'Lao Airlines',
        ],
        [
            'id' => 'qw',
            'alias' => '',
            'call_sign' => 'BLUE WINGS',
            'code_iata' => 'qw',
            'code_icao' => 'bwg',
            'country_id' => 'de',
            'name' => 'Blue Wings',
        ],
        [
            'id' => 'qx',
            'alias' => 'Horizon Airlines',
            'call_sign' => 'HORIZON AIR',
            'code_iata' => 'qx',
            'code_icao' => 'qxe',
            'country_id' => 'us',
            'name' => 'Horizon Air',
        ],
        [
            'id' => 'qy',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'qy',
            'code_icao' => '',
            'country_id' => 'ca',
            'name' => 'Red Jet Canada',
        ],
        [
            'id' => 'qz',
            'alias' => '',
            'call_sign' => 'WAGON AIR',
            'code_iata' => 'qz',
            'code_icao' => 'awq',
            'country_id' => 'id',
            'name' => 'Indonesia AirAsia',
        ],
        [
            'id' => 'r2',
            'alias' => '',
            'call_sign' => 'ORENBURG',
            'code_iata' => 'r2',
            'code_icao' => 'orb',
            'country_id' => 'ru',
            'name' => 'Orenburg Airlines',
        ],
        [
            'id' => 'r3',
            'alias' => '',
            'call_sign' => 'AIR YAKUTIA',
            'code_iata' => 'r3',
            'code_icao' => 'syl',
            'country_id' => 'ru',
            'name' => 'Aircompany Yakutia',
        ],
        [
            'id' => 'r4',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'r4',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'Rossiya',
        ],
        [
            'id' => 'r5',
            'alias' => '',
            'call_sign' => 'MALTA CHARTER',
            'code_iata' => 'r5',
            'code_icao' => 'mac',
            'country_id' => 'mt',
            'name' => 'Malta Air Charter',
        ],
        [
            'id' => 'r6',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'r6',
            'code_icao' => '',
            'country_id' => 'gt',
            'name' => 'RACSA',
        ],
        [
            'id' => 'r7',
            'alias' => '',
            'call_sign' => 'AROSCA',
            'code_iata' => 'r7',
            'code_icao' => 'oca',
            'country_id' => '',
            'name' => 'Aserca Airlines',
        ],
        [
            'id' => 'r8',
            'alias' => 'RussianConector',
            'call_sign' => 'russiancloud',
            'code_iata' => 'r8',
            'code_icao' => 'rrj',
            'country_id' => 'ru',
            'name' => 'AirRussia',
        ],
        [
            'id' => 'ra',
            'alias' => '',
            'call_sign' => 'ROYAL NEPAL',
            'code_iata' => 'ra',
            'code_icao' => 'rna',
            'country_id' => 'np',
            'name' => 'Royal Nepal Airlines',
        ],
        [
            'id' => 'ra',
            'alias' => '',
            'call_sign' => 'ROYAL NEPAL',
            'code_iata' => 'ra',
            'code_icao' => 'rna',
            'country_id' => 'np',
            'name' => 'Nepal Airlines',
        ],
        [
            'id' => 'rb',
            'alias' => '',
            'call_sign' => 'SYRIANAIR',
            'code_iata' => 'rb',
            'code_icao' => 'syr',
            'country_id' => 'sy',
            'name' => 'Syrian Arab Airlines',
        ],
        [
            'id' => 'rc',
            'alias' => '',
            'call_sign' => 'FAROELINE',
            'code_iata' => 'rc',
            'code_icao' => 'fli',
            'country_id' => 'fo',
            'name' => 'Atlantic Airways',
        ],
        [
            'id' => 'rd',
            'alias' => '',
            'call_sign' => 'RYAN INTERNATIONAL',
            'code_iata' => 'rd',
            'code_icao' => 'ryn',
            'country_id' => 'us',
            'name' => 'Ryan International Airlines',
        ],
        [
            'id' => 're',
            'alias' => '',
            'call_sign' => 'AER ARANN',
            'code_iata' => 're',
            'code_icao' => 'rea',
            'country_id' => 'ie',
            'name' => 'Aer Arann',
        ],
        [
            'id' => 'rf',
            'alias' => '',
            'call_sign' => 'FLO WEST',
            'code_iata' => 'rf',
            'code_icao' => 'fwl',
            'country_id' => 'us',
            'name' => 'Florida West International Airways',
        ],
        [
            'id' => 'rg',
            'alias' => 'Varig',
            'call_sign' => 'VARIG',
            'code_iata' => 'rg',
            'code_icao' => 'vrn',
            'country_id' => 'br',
            'name' => 'VRG Linhas Aereas',
        ],
        [
            'id' => 'rh',
            'alias' => '',
            'call_sign' => 'PUBLIC EXPRESS',
            'code_iata' => 'rh',
            'code_icao' => 'rph',
            'country_id' => 'id',
            'name' => 'Republic Express Airlines',
        ],
        [
            'id' => 'rh',
            'alias' => '',
            'call_sign' => 'MASCOT',
            'code_iata' => 'rh',
            'code_icao' => 'hkc',
            'country_id' => 'hk',
            'name' => 'Hong Kong Air Cargo',
        ],
        [
            'id' => 'ri',
            'alias' => '',
            'call_sign' => 'MANDALA',
            'code_iata' => 'ri',
            'code_icao' => 'mdl',
            'country_id' => 'id',
            'name' => 'Mandala Airlines',
        ],
        [
            'id' => 'rj',
            'alias' => '',
            'call_sign' => 'JORDANIAN',
            'code_iata' => 'rj',
            'code_icao' => 'rja',
            'country_id' => 'jo',
            'name' => 'Royal Jordanian',
        ],
        [
            'id' => 'rk',
            'alias' => '',
            'call_sign' => 'AIRAFRIC',
            'code_iata' => 'rk',
            'code_icao' => 'rka',
            'country_id' => '',
            'name' => 'Air Afrique',
        ],
        [
            'id' => 'rl',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'rl',
            'code_icao' => 'rfj',
            'country_id' => 'jo',
            'name' => 'Royal Falcon',
        ],
        [
            'id' => 'rm',
            'alias' => 'Rainbow Air US',
            'call_sign' => 'Rainbow Air',
            'code_iata' => 'rm',
            'code_icao' => 'rny',
            'country_id' => 'us',
            'name' => 'Rainbow Air US',
        ],
        [
            'id' => 'rn',
            'alias' => 'Rainbow Air (RAI)',
            'call_sign' => 'Rainbow',
            'code_iata' => 'rn',
            'code_icao' => 'rab',
            'country_id' => 'us',
            'name' => 'Rainbow Air (RAI)',
        ],
        [
            'id' => 'ro',
            'alias' => '',
            'call_sign' => 'TAROM',
            'code_iata' => 'ro',
            'code_icao' => 'rot',
            'country_id' => 'ro',
            'name' => 'Tarom',
        ],
        [
            'id' => 'rp',
            'alias' => '',
            'call_sign' => 'CHAUTAUQUA',
            'code_iata' => 'rp',
            'code_icao' => 'chq',
            'country_id' => 'us',
            'name' => 'Chautauqua Airlines',
        ],
        [
            'id' => 'rq',
            'alias' => '',
            'call_sign' => 'KAMGAR',
            'code_iata' => 'rq',
            'code_icao' => 'kmf',
            'country_id' => 'af',
            'name' => 'Kam Air',
        ],
        [
            'id' => 'rs',
            'alias' => 'Air Canada Express',
            'call_sign' => 'Sky Regional',
            'code_iata' => 'rs',
            'code_icao' => '',
            'country_id' => 'ca',
            'name' => 'Sky Regional',
        ],
        [
            'id' => 'ru',
            'alias' => 'Rainbow Air EU',
            'call_sign' => 'Rainbow Air',
            'code_iata' => 'ru',
            'code_icao' => 'rue',
            'country_id' => 'gb',
            'name' => 'Rainbow Air Euro',
        ],
        [
            'id' => 'rv',
            'alias' => '',
            'call_sign' => 'CASPIAN',
            'code_iata' => 'rv',
            'code_icao' => 'cpn',
            'country_id' => '',
            'name' => 'Caspian Airlines',
        ],
        [
            'id' => 'rw',
            'alias' => '',
            'call_sign' => 'BRICKYARD',
            'code_iata' => 'rw',
            'code_icao' => 'rpa',
            'country_id' => 'us',
            'name' => 'Republic Airlines',
        ],
        [
            'id' => 'rx',
            'alias' => 'Rainbow Air POL',
            'call_sign' => 'Rainbow Air',
            'code_iata' => 'rx',
            'code_icao' => 'rpo',
            'country_id' => 'us',
            'name' => 'Rainbow Air Polynesia',
        ],
        [
            'id' => 'rx',
            'alias' => '',
            'call_sign' => 'REGENT',
            'code_iata' => 'rx',
            'code_icao' => 'rge',
            'country_id' => 'bd',
            'name' => 'Regent Airways',
        ],
        [
            'id' => 'ry',
            'alias' => 'Rainbow Air CAN',
            'call_sign' => 'Rainbow CAN',
            'code_iata' => 'ry',
            'code_icao' => 'ray',
            'country_id' => 'ca',
            'name' => 'Rainbow Air Canada',
        ],
        [
            'id' => 'rz',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'rz',
            'code_icao' => '',
            'country_id' => 'se',
            'name' => 'Euro Exec Express',
        ],
        [
            'id' => 's0',
            'alias' => 'Aero Spike',
            'call_sign' => 'Spike Air',
            'code_iata' => 's0',
            'code_icao' => 'sal',
            'country_id' => 'us',
            'name' => 'Spike Airlines',
        ],
        [
            'id' => 's1',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 's1',
            'code_icao' => 'sa1',
            'country_id' => 'rs',
            'name' => 'Serbian Airlines',
        ],
        [
            'id' => 's2',
            'alias' => '',
            'call_sign' => 'SAHARA',
            'code_iata' => 's2',
            'code_icao' => 'rsh',
            'country_id' => 'in',
            'name' => 'Air Sahara',
        ],
        [
            'id' => 's3',
            'alias' => '',
            'call_sign' => 'SANTA BARBARA',
            'code_iata' => 's3',
            'code_icao' => 'bbr',
            'country_id' => '',
            'name' => 'Santa Barbara Airlines',
        ],
        [
            'id' => 's4',
            'alias' => '',
            'call_sign' => 'AIR AZORES',
            'code_iata' => 's4',
            'code_icao' => 'rzo',
            'country_id' => 'pt',
            'name' => 'SATA International',
        ],
        [
            'id' => 's5',
            'alias' => '',
            'call_sign' => 'MERCURY',
            'code_iata' => 's5',
            'code_icao' => 'tcf',
            'country_id' => 'us',
            'name' => 'Shuttle America',
        ],
        [
            'id' => 's6',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 's6',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Salmon Air',
        ],
        [
            'id' => 's7',
            'alias' => 'Sibir Airlines',
            'call_sign' => 'SIBERIAN AIRLINES',
            'code_iata' => 's7',
            'code_icao' => 'sbi',
            'country_id' => 'ru',
            'name' => 'S7 Airlines',
        ],
        [
            'id' => 's8',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 's8',
            'code_icao' => 'sbd',
            'country_id' => 'fi',
            'name' => 'Snowbird Airlines',
        ],
        [
            'id' => 's9',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 's9',
            'code_icao' => '',
            'country_id' => 'gh',
            'name' => 'Starbow Airlines',
        ],
        [
            'id' => 'sa',
            'alias' => 'SAA South African Airways',
            'call_sign' => 'SPRINGBOK',
            'code_iata' => 'sa',
            'code_icao' => 'saa',
            'country_id' => 'za',
            'name' => 'South African Airways',
        ],
        [
            'id' => 'sb',
            'alias' => '',
            'call_sign' => 'AIRCALIN',
            'code_iata' => 'sb',
            'code_icao' => 'aci',
            'country_id' => 'fr',
            'name' => 'Air Caledonie International',
        ],
        [
            'id' => 'sc',
            'alias' => '',
            'call_sign' => 'SHANDONG',
            'code_iata' => 'sc',
            'code_icao' => 'cdg',
            'country_id' => 'cn',
            'name' => 'Shandong Airlines',
        ],
        [
            'id' => 'sd',
            'alias' => '',
            'call_sign' => 'SUDANAIR',
            'code_iata' => 'sd',
            'code_icao' => 'sud',
            'country_id' => 'sd',
            'name' => 'Sudan Airways',
        ],
        [
            'id' => 'se',
            'alias' => '',
            'call_sign' => 'STARWAY',
            'code_iata' => 'se',
            'code_icao' => 'seu',
            'country_id' => 'fr',
            'name' => 'XL Airways France',
        ],
        [
            'id' => 'sg',
            'alias' => '',
            'call_sign' => 'SPICEJET',
            'code_iata' => 'sg',
            'code_icao' => 'sej',
            'country_id' => 'in',
            'name' => 'Spicejet',
        ],
        [
            'id' => 'sh',
            'alias' => '',
            'call_sign' => 'SHARP',
            'code_iata' => 'sh',
            'code_icao' => 'sha',
            'country_id' => 'au',
            'name' => 'Sharp Airlines',
        ],
        [
            'id' => 'si',
            'alias' => '',
            'call_sign' => 'BLUEJET',
            'code_iata' => 'si',
            'code_icao' => 'sih',
            'country_id' => 'ie',
            'name' => 'Skynet Airlines',
        ],
        [
            'id' => 'sj',
            'alias' => '',
            'call_sign' => 'SRIWIJAYA',
            'code_iata' => 'sj',
            'code_icao' => 'sjy',
            'country_id' => 'id',
            'name' => 'Sriwijaya Air',
        ],
        [
            'id' => 'sk',
            'alias' => 'SAS Scandinavian Airlines',
            'call_sign' => 'SCANDINAVIAN',
            'code_iata' => 'sk',
            'code_icao' => 'sas',
            'country_id' => 'se',
            'name' => 'Scandinavian Airlines System',
        ],
        [
            'id' => 'sl',
            'alias' => '',
            'call_sign' => 'MENTARI',
            'code_iata' => 'sl',
            'code_icao' => 'tlm',
            'country_id' => 'th',
            'name' => 'Thai Lion Air',
        ],
        [
            'id' => 'sm',
            'alias' => '',
            'call_sign' => 'MANILA SKY',
            'code_iata' => 'sm',
            'code_icao' => 'mnp',
            'country_id' => 'ph',
            'name' => 'Spirit of Manila Airlines',
        ],
        [
            'id' => 'sn',
            'alias' => 'SN Brussels Airlines',
            'call_sign' => 'BEE-LINE',
            'code_iata' => 'sn',
            'code_icao' => 'dat',
            'country_id' => 'be',
            'name' => 'Brussels Airlines',
        ],
        [
            'id' => 'so',
            'alias' => '',
            'call_sign' => 'SALSA',
            'code_iata' => 'so',
            'code_icao' => 'slc',
            'country_id' => 'ht',
            'name' => "Salsa d'Haiti",
        ],
        [
            'id' => 'sp',
            'alias' => '',
            'call_sign' => 'SATA',
            'code_iata' => 'sp',
            'code_icao' => 'sat',
            'country_id' => 'pt',
            'name' => 'SATA Air Acores',
        ],
        [
            'id' => 'sq',
            'alias' => '',
            'call_sign' => 'SINGCARGO',
            'code_iata' => 'sq',
            'code_icao' => 'sqc',
            'country_id' => 'sg',
            'name' => 'Singapore Airlines Cargo',
        ],
        [
            'id' => 'sq',
            'alias' => '',
            'call_sign' => 'SINGAPORE',
            'code_iata' => 'sq',
            'code_icao' => 'sia',
            'country_id' => 'sg',
            'name' => 'Singapore Airlines',
        ],
        [
            'id' => 'sr',
            'alias' => '',
            'call_sign' => 'Swissair',
            'code_iata' => 'sr',
            'code_icao' => 'swr',
            'country_id' => 'ch',
            'name' => 'Swissair',
        ],
        [
            'id' => 'ss',
            'alias' => '',
            'call_sign' => 'CORSAIR',
            'code_iata' => 'ss',
            'code_icao' => 'crl',
            'country_id' => 'fr',
            'name' => 'Corsairfly',
        ],
        [
            'id' => 'st',
            'alias' => '',
            'call_sign' => 'GERMANIA',
            'code_iata' => 'st',
            'code_icao' => 'gmi',
            'country_id' => 'de',
            'name' => 'Germania',
        ],
        [
            'id' => 'su',
            'alias' => '',
            'call_sign' => 'AEROFLOT',
            'code_iata' => 'su',
            'code_icao' => 'afl',
            'country_id' => 'ru',
            'name' => 'Aeroflot Russian Airlines',
        ],
        [
            'id' => 'sv',
            'alias' => '',
            'call_sign' => 'SAUDIA',
            'code_iata' => 'sv',
            'code_icao' => 'sva',
            'country_id' => 'sa',
            'name' => 'Saudi Arabian Airlines',
        ],
        [
            'id' => 'sw',
            'alias' => '',
            'call_sign' => 'NAMIBIA',
            'code_iata' => 'sw',
            'code_icao' => 'nmb',
            'country_id' => 'na',
            'name' => 'Air Namibia',
        ],
        [
            'id' => 'sx',
            'alias' => '',
            'call_sign' => 'SKYFOX',
            'code_iata' => 'sx',
            'code_icao' => '',
            'country_id' => 'ch',
            'name' => 'SkyWork Airlines',
        ],
        [
            'id' => 'sy',
            'alias' => '',
            'call_sign' => 'SUN COUNTRY',
            'code_iata' => 'sy',
            'code_icao' => 'scx',
            'country_id' => 'us',
            'name' => 'Sun Country Airlines',
        ],
        [
            'id' => 'sz',
            'alias' => 'SZA',
            'call_sign' => 'SZA',
            'code_iata' => 'sz',
            'code_icao' => '',
            'country_id' => 'at',
            'name' => 'Salzburg arrows',
        ],
        [
            'id' => 't0',
            'alias' => '',
            'call_sign' => 'TACA PERU',
            'code_iata' => 't0',
            'code_icao' => '',
            'country_id' => 'pe',
            'name' => 'TACA Peru',
        ],
        [
            'id' => 't2',
            'alias' => '',
            'call_sign' => 'THAI CARGO',
            'code_iata' => 't2',
            'code_icao' => 'tcg',
            'country_id' => 'th',
            'name' => 'Thai Air Cargo',
        ],
        [
            'id' => 't3',
            'alias' => '',
            'call_sign' => 'EASTFLIGHT',
            'code_iata' => 't3',
            'code_icao' => 'eze',
            'country_id' => 'gb',
            'name' => 'Eastern Airways',
        ],
        [
            'id' => 't4',
            'alias' => '',
            'call_sign' => 'HELLAS JET',
            'code_iata' => 't4',
            'code_icao' => 'hej',
            'country_id' => 'gr',
            'name' => 'Hellas Jet',
        ],
        [
            'id' => 't5',
            'alias' => 'Turkmenhovayollary',
            'call_sign' => 'TURKMENISTAN',
            'code_iata' => 't5',
            'code_icao' => 'tua',
            'country_id' => 'tm',
            'name' => 'Turkmenistan Airlines',
        ],
        [
            'id' => 't6',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 't6',
            'code_icao' => 'tp6',
            'country_id' => 'us',
            'name' => 'Trans Pas Air',
        ],
        [
            'id' => 't7',
            'alias' => '',
            'call_sign' => 'TWINJET',
            'code_iata' => 't7',
            'code_icao' => 'tjt',
            'country_id' => 'fr',
            'name' => 'Twin Jet',
        ],
        [
            'id' => 'ta',
            'alias' => 'TACA',
            'call_sign' => 'TACA-COSTARICA',
            'code_iata' => 'ta',
            'code_icao' => 'tat',
            'country_id' => 'cr',
            'name' => 'Grupo TACA',
        ],
        [
            'id' => 'tb',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'tb',
            'code_icao' => 'tbz',
            'country_id' => 'br',
            'name' => 'TrasBrasil',
        ],
        [
            'id' => 'tc',
            'alias' => '',
            'call_sign' => 'TANZANIA',
            'code_iata' => 'tc',
            'code_icao' => 'atc',
            'country_id' => '',
            'name' => 'Air Tanzania',
        ],
        [
            'id' => 'td',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'td',
            'code_icao' => 'lur',
            'country_id' => 'am',
            'name' => 'Atlantis European Airways',
        ],
        [
            'id' => 'te',
            'alias' => '',
            'call_sign' => 'LITHUANIA AIR',
            'code_iata' => 'te',
            'code_icao' => 'lil',
            'country_id' => 'lt',
            'name' => 'FlyLal',
        ],
        [
            'id' => 'tf',
            'alias' => '',
            'call_sign' => 'Scanwings',
            'code_iata' => 'tf',
            'code_icao' => 'scw',
            'country_id' => 'se',
            'name' => 'Malmö Aviation',
        ],
        [
            'id' => 'tg',
            'alias' => '',
            'call_sign' => 'THAI',
            'code_iata' => 'tg',
            'code_icao' => 'tha',
            'country_id' => 'th',
            'name' => 'Thai Airways International',
        ],
        [
            'id' => 'th',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'th',
            'code_icao' => 'ths',
            'country_id' => 'br',
            'name' => 'TransBrasil Airlines',
        ],
        [
            'id' => 'ti',
            'alias' => 'Trans',
            'call_sign' => '',
            'code_iata' => 'ti',
            'code_icao' => 'thi',
            'country_id' => 'br',
            'name' => 'TransHolding',
        ],
        [
            'id' => 'tj',
            'alias' => '',
            'call_sign' => 'T.J. Air',
            'code_iata' => 'tj',
            'code_icao' => 'tja',
            'country_id' => 'us',
            'name' => 'T.J. Air',
        ],
        [
            'id' => 'tk',
            'alias' => '',
            'call_sign' => 'TURKAIR',
            'code_iata' => 'tk',
            'code_icao' => 'thy',
            'country_id' => 'tr',
            'name' => 'Turkish Airlines',
        ],
        [
            'id' => 'tl',
            'alias' => '',
            'call_sign' => 'TANGO LIMA',
            'code_iata' => 'tl',
            'code_icao' => 'tma',
            'country_id' => 'lb',
            'name' => 'Trans Mediterranean Airlines',
        ],
        [
            'id' => 'tl',
            'alias' => '',
            'call_sign' => 'TOPEND',
            'code_iata' => 'tl',
            'code_icao' => 'ano',
            'country_id' => 'au',
            'name' => 'Airnorth',
        ],
        [
            'id' => 'tm',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'tm',
            'code_icao' => '',
            'country_id' => 'mz',
            'name' => 'Air Mozambique',
        ],
        [
            'id' => 'tn',
            'alias' => '',
            'call_sign' => 'TAHITI AIRLINES',
            'code_iata' => 'tn',
            'code_icao' => 'tht',
            'country_id' => 'fr',
            'name' => 'Air Tahiti Nui',
        ],
        [
            'id' => 'to',
            'alias' => '',
            'call_sign' => 'FRENCH SUN',
            'code_iata' => 'to',
            'code_icao' => 'tvf',
            'country_id' => 'fr',
            'name' => 'Transavia France',
        ],
        [
            'id' => 'tp',
            'alias' => 'TAP Air Portugal',
            'call_sign' => 'AIR PORTUGAL',
            'code_iata' => 'tp',
            'code_icao' => 'tap',
            'country_id' => 'pt',
            'name' => 'TAP Portugal',
        ],
        [
            'id' => 'tq',
            'alias' => '',
            'call_sign' => 'TXW',
            'code_iata' => 'tq',
            'code_icao' => 'txw',
            'country_id' => 'us',
            'name' => 'Texas Wings',
        ],
        [
            'id' => 'tr',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'tr',
            'code_icao' => 'sco',
            'country_id' => 'sg',
            'name' => 'Scoot',
        ],
        [
            'id' => 'tr',
            'alias' => '',
            'call_sign' => 'GO CAT',
            'code_iata' => 'tr',
            'code_icao' => 'tgw',
            'country_id' => 'sg',
            'name' => 'Tiger Airways',
        ],
        [
            'id' => 'ts',
            'alias' => '',
            'call_sign' => 'TRANSAT',
            'code_iata' => 'ts',
            'code_icao' => 'tsc',
            'country_id' => 'ca',
            'name' => 'Air Transat',
        ],
        [
            'id' => 'tt',
            'alias' => '',
            'call_sign' => 'TIGGOZ',
            'code_iata' => 'tt',
            'code_icao' => 'tgg',
            'country_id' => 'au',
            'name' => 'Tigerair Australia',
        ],
        [
            'id' => 'tu',
            'alias' => '',
            'call_sign' => 'TUNAIR',
            'code_iata' => 'tu',
            'code_icao' => 'tar',
            'country_id' => 'tn',
            'name' => 'Tunisair',
        ],
        [
            'id' => 'tv',
            'alias' => '',
            'call_sign' => 'VIRGIN EXPRESS',
            'code_iata' => 'tv',
            'code_icao' => 'vex',
            'country_id' => 'be',
            'name' => 'Virgin Express',
        ],
        [
            'id' => 'tw',
            'alias' => '',
            'call_sign' => 'TWAY AIR',
            'code_iata' => 'tw',
            'code_icao' => 'twb',
            'country_id' => '',
            'name' => 'Tway Airlines',
        ],
        [
            'id' => 'tx',
            'alias' => '',
            'call_sign' => 'FRENCH WEST',
            'code_iata' => 'tx',
            'code_icao' => 'fwi',
            'country_id' => 'fr',
            'name' => 'Air Caraïbes',
        ],
        [
            'id' => 'ty',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'ty',
            'code_icao' => 'iwd',
            'country_id' => 'es',
            'name' => 'Iberworld',
        ],
        [
            'id' => 'u1',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'u1',
            'code_icao' => 'abi',
            'country_id' => 'ru',
            'name' => 'Aviabus',
        ],
        [
            'id' => 'u2',
            'alias' => 'EasyJet Airline',
            'call_sign' => 'EASY',
            'code_iata' => 'u2',
            'code_icao' => 'ezy',
            'country_id' => 'gb',
            'name' => 'easyJet',
        ],
        [
            'id' => 'u3',
            'alias' => '',
            'call_sign' => 'AVIES',
            'code_iata' => 'u3',
            'code_icao' => 'aia',
            'country_id' => 'ee',
            'name' => 'Avies',
        ],
        [
            'id' => 'u4',
            'alias' => '',
            'call_sign' => 'MULTITRADE',
            'code_iata' => 'u4',
            'code_icao' => 'pmt',
            'country_id' => 'kh',
            'name' => 'PMTair',
        ],
        [
            'id' => 'u5',
            'alias' => '',
            'call_sign' => 'GETAWAY',
            'code_iata' => 'u5',
            'code_icao' => 'gwy',
            'country_id' => 'us',
            'name' => 'USA3000 Airlines',
        ],
        [
            'id' => 'u6',
            'alias' => '',
            'call_sign' => 'SVERDLOVSK AIR',
            'code_iata' => 'u6',
            'code_icao' => 'svr',
            'country_id' => 'ru',
            'name' => 'Ural Airlines',
        ],
        [
            'id' => 'u7',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'u7',
            'code_icao' => '',
            'country_id' => 'ca',
            'name' => 'Northern Dene Airways',
        ],
        [
            'id' => 'u8',
            'alias' => '',
            'call_sign' => 'ARMAVIA',
            'code_iata' => 'u8',
            'code_icao' => 'rnv',
            'country_id' => 'am',
            'name' => 'Armavia',
        ],
        [
            'id' => 'u9',
            'alias' => '',
            'call_sign' => 'TATARSTAN',
            'code_iata' => 'u9',
            'code_icao' => 'tak',
            'country_id' => 'ru',
            'name' => 'Tatarstan Airlines',
        ],
        [
            'id' => 'ua',
            'alias' => '',
            'call_sign' => 'UNITED',
            'code_iata' => 'ua',
            'code_icao' => 'ual',
            'country_id' => 'us',
            'name' => 'United Airlines',
        ],
        [
            'id' => 'ub',
            'alias' => '',
            'call_sign' => 'UNIONAIR',
            'code_iata' => 'ub',
            'code_icao' => 'uba',
            'country_id' => 'mm',
            'name' => 'Myanma Airways',
        ],
        [
            'id' => 'ud',
            'alias' => '',
            'call_sign' => 'HEX AIRLINE',
            'code_iata' => 'ud',
            'code_icao' => 'her',
            'country_id' => 'fr',
            'name' => "Hex'Air",
        ],
        [
            'id' => 'ue',
            'alias' => '',
            'call_sign' => 'NASAIRWAYS',
            'code_iata' => 'ue',
            'code_icao' => 'nas',
            'country_id' => 'er',
            'name' => 'Nasair',
        ],
        [
            'id' => 'uf',
            'alias' => '',
            'call_sign' => 'UKRAINE MEDITERRANEE',
            'code_iata' => 'uf',
            'code_icao' => 'ukm',
            'country_id' => 'ua',
            'name' => 'UM Airlines',
        ],
        [
            'id' => 'ug',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'ug',
            'code_icao' => 'tui',
            'country_id' => 'tn',
            'name' => 'Tuninter',
        ],
        [
            'id' => 'uh',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'uh',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'US Helicopter Corporation',
        ],
        [
            'id' => 'ui',
            'alias' => '',
            'call_sign' => 'EUROCYPRIA',
            'code_iata' => 'ui',
            'code_icao' => 'eca',
            'country_id' => 'cy',
            'name' => 'Eurocypria Airlines',
        ],
        [
            'id' => 'uj',
            'alias' => '',
            'call_sign' => 'ALMASRIA',
            'code_iata' => 'uj',
            'code_icao' => 'lmu',
            'country_id' => 'eg',
            'name' => 'AlMasria Universal Airlines',
        ],
        [
            'id' => 'uk',
            'alias' => '',
            'call_sign' => 'VISTARA',
            'code_iata' => 'uk',
            'code_icao' => 'vti',
            'country_id' => 'in',
            'name' => 'Vistera',
        ],
        [
            'id' => 'ul',
            'alias' => '',
            'call_sign' => 'SRILANKAN',
            'code_iata' => 'ul',
            'code_icao' => 'alk',
            'country_id' => 'lk',
            'name' => 'SriLankan Airlines',
        ],
        [
            'id' => 'um',
            'alias' => '',
            'call_sign' => 'AIR ZIMBABWE',
            'code_iata' => 'um',
            'code_icao' => 'azw',
            'country_id' => 'zw',
            'name' => 'Air Zimbabwe',
        ],
        [
            'id' => 'un',
            'alias' => '',
            'call_sign' => 'TRANSOVIET',
            'code_iata' => 'un',
            'code_icao' => 'tso',
            'country_id' => 'ru',
            'name' => 'Transaero Airlines',
        ],
        [
            'id' => 'uo',
            'alias' => '',
            'call_sign' => 'HONGKONG SHUTTLE',
            'code_iata' => 'uo',
            'code_icao' => 'hke',
            'country_id' => '',
            'name' => 'Hong Kong Express Airways',
        ],
        [
            'id' => 'up',
            'alias' => '',
            'call_sign' => 'BAHAMAS',
            'code_iata' => 'up',
            'code_icao' => 'bhs',
            'country_id' => 'bs',
            'name' => 'Bahamasair',
        ],
        [
            'id' => 'uq',
            'alias' => '',
            'call_sign' => 'GREEN CITY',
            'code_iata' => 'uq',
            'code_icao' => 'cbg',
            'country_id' => 'cn',
            'name' => 'Guangxi Beibu Gulf Airlines',
        ],
        [
            'id' => 'uq',
            'alias' => '',
            'call_sign' => 'LOU LAN',
            'code_iata' => 'uq',
            'code_icao' => 'cuh',
            'country_id' => 'cn',
            'name' => 'Urumqi Air',
        ],
        [
            'id' => 'uq',
            'alias' => '',
            'call_sign' => 'SKYJET',
            'code_iata' => 'uq',
            'code_icao' => 'sju',
            'country_id' => 'ug',
            'name' => 'Skyjet Airlines',
        ],
        [
            'id' => 'ur',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'ur',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'UTair-Express',
        ],
        [
            'id' => 'us',
            'alias' => '',
            'call_sign' => 'U S AIR',
            'code_iata' => 'us',
            'code_icao' => 'usa',
            'country_id' => 'us',
            'name' => 'US Airways',
        ],
        [
            'id' => 'ut',
            'alias' => '',
            'call_sign' => 'UTAIR',
            'code_iata' => 'ut',
            'code_icao' => 'uta',
            'country_id' => 'ru',
            'name' => 'UTair Aviation',
        ],
        [
            'id' => 'uu',
            'alias' => '',
            'call_sign' => 'REUNION',
            'code_iata' => 'uu',
            'code_icao' => 'reu',
            'country_id' => 'fr',
            'name' => 'Air Austral',
        ],
        [
            'id' => 'ux',
            'alias' => '',
            'call_sign' => 'EUROPA',
            'code_iata' => 'ux',
            'code_icao' => 'aea',
            'country_id' => 'es',
            'name' => 'Air Europa',
        ],
        [
            'id' => 'uz',
            'alias' => '',
            'call_sign' => 'BURAQAIR',
            'code_iata' => 'uz',
            'code_icao' => 'brq',
            'country_id' => '',
            'name' => 'El-Buraq Air Transport',
        ],
        [
            'id' => 'v0',
            'alias' => '',
            'call_sign' => 'CONVIASA',
            'code_iata' => 'v0',
            'code_icao' => 'vcv',
            'country_id' => '',
            'name' => 'Conviasa',
        ],
        [
            'id' => 'v2',
            'alias' => '',
            'call_sign' => 'RUBY',
            'code_iata' => 'v2',
            'code_icao' => 'rby',
            'country_id' => 'us',
            'name' => 'Vision Airlines (V2)',
        ],
        [
            'id' => 'v3',
            'alias' => '',
            'call_sign' => 'CARPATAIR',
            'code_iata' => 'v3',
            'code_icao' => 'krp',
            'country_id' => 'ro',
            'name' => 'Carpatair',
        ],
        [
            'id' => 'v5',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'v5',
            'code_icao' => '',
            'country_id' => 'sk',
            'name' => 'Danube Wings (V5)',
        ],
        [
            'id' => 'v6',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'v6',
            'code_icao' => '',
            'country_id' => 'ec',
            'name' => 'VIP Ecuador',
        ],
        [
            'id' => 'v8',
            'alias' => '',
            'call_sign' => 'ATRAN',
            'code_iata' => 'v8',
            'code_icao' => 'vas',
            'country_id' => '',
            'name' => 'ATRAN Cargo Airlines',
        ],
        [
            'id' => 'v9',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'v9',
            'code_icao' => 'hcw',
            'country_id' => 'lt',
            'name' => 'Star1 Airlines',
        ],
        [
            'id' => 'va',
            'alias' => '',
            'call_sign' => 'VIRGIN',
            'code_iata' => 'va',
            'code_icao' => 'voz',
            'country_id' => 'au',
            'name' => 'Virgin Australia',
        ],
        [
            'id' => 'vc',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'vc',
            'code_icao' => '',
            'country_id' => 'au',
            'name' => 'Strategic Airlines',
        ],
        [
            'id' => 'vd',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'vd',
            'code_icao' => '',
            'country_id' => 'fr',
            'name' => 'Air Libert',
        ],
        [
            'id' => 've',
            'alias' => '',
            'call_sign' => 'VOLA',
            'code_iata' => 've',
            'code_icao' => 'vle',
            'country_id' => 'it',
            'name' => 'Volare Airlines',
        ],
        [
            'id' => 'vf',
            'alias' => '',
            'call_sign' => 'VALUAIR',
            'code_iata' => 'vf',
            'code_icao' => 'vlu',
            'country_id' => 'sg',
            'name' => 'Valuair',
        ],
        [
            'id' => 'vg',
            'alias' => '',
            'call_sign' => 'RUBENS',
            'code_iata' => 'vg',
            'code_icao' => 'vlm',
            'country_id' => 'be',
            'name' => 'VLM Airlines',
        ],
        [
            'id' => 'vh',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'vh',
            'code_icao' => 'vnp',
            'country_id' => 'fj',
            'name' => 'Virgin Pacific',
        ],
        [
            'id' => 'vi',
            'alias' => '',
            'call_sign' => 'VOLGA-DNEPR',
            'code_iata' => 'vi',
            'code_icao' => 'vda',
            'country_id' => 'ru',
            'name' => 'Volga-Dnepr Airlines',
        ],
        [
            'id' => 'vj',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'vj',
            'code_icao' => 'rac',
            'country_id' => 'kh',
            'name' => 'Royal Air Cambodge',
        ],
        [
            'id' => 'vk',
            'alias' => '',
            'call_sign' => 'VIRGIN NIGERIA',
            'code_iata' => 'vk',
            'code_icao' => 'vgn',
            'country_id' => 'ng',
            'name' => 'Virgin Nigeria Airways',
        ],
        [
            'id' => 'vl',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'vl',
            'code_icao' => 'vim',
            'country_id' => 'bg',
            'name' => 'Air VIA',
        ],
        [
            'id' => 'vn',
            'alias' => '',
            'call_sign' => 'VIET NAM AIRLINES',
            'code_iata' => 'vn',
            'code_icao' => 'hvn',
            'country_id' => 'vn',
            'name' => 'Vietnam Airlines',
        ],
        [
            'id' => 'vo',
            'alias' => '',
            'call_sign' => 'TYROLEAN',
            'code_iata' => 'vo',
            'code_icao' => 'tyr',
            'country_id' => 'at',
            'name' => 'Tyrolean Airways',
        ],
        [
            'id' => 'vp',
            'alias' => '',
            'call_sign' => 'VASP',
            'code_iata' => 'vp',
            'code_icao' => 'vsp',
            'country_id' => 'br',
            'name' => 'VASP',
        ],
        [
            'id' => 'vq',
            'alias' => '',
            'call_sign' => 'DELPHI',
            'code_iata' => 'vq',
            'code_icao' => 'vkh',
            'country_id' => 'gr',
            'name' => 'Viking Hellas',
        ],
        [
            'id' => 'vr',
            'alias' => '',
            'call_sign' => 'CABOVERDE',
            'code_iata' => 'vr',
            'code_icao' => 'tcv',
            'country_id' => 'pt',
            'name' => 'TACV',
        ],
        [
            'id' => 'vs',
            'alias' => '',
            'call_sign' => 'VIRGIN',
            'code_iata' => 'vs',
            'code_icao' => 'vir',
            'country_id' => 'gb',
            'name' => 'Virgin Atlantic Airways',
        ],
        [
            'id' => 'vt',
            'alias' => '',
            'call_sign' => 'AIR TAHITI',
            'code_iata' => 'vt',
            'code_icao' => 'vta',
            'country_id' => 'pf',
            'name' => 'Air Tahiti',
        ],
        [
            'id' => 'vu',
            'alias' => '',
            'call_sign' => 'AIRIVOIRE',
            'code_iata' => 'vu',
            'code_icao' => 'vun',
            'country_id' => '',
            'name' => 'Air Ivoire',
        ],
        [
            'id' => 'vv',
            'alias' => '',
            'call_sign' => 'AEROSVIT',
            'code_iata' => 'vv',
            'code_icao' => 'aew',
            'country_id' => 'ua',
            'name' => 'Aerosvit Airlines',
        ],
        [
            'id' => 'vw',
            'alias' => '',
            'call_sign' => 'TRANS-AEROMAR',
            'code_iata' => 'vw',
            'code_icao' => 'tao',
            'country_id' => 'mx',
            'name' => 'Aeromar',
        ],
        [
            'id' => 'vx',
            'alias' => '',
            'call_sign' => 'REDWOOD',
            'code_iata' => 'vx',
            'code_icao' => 'vrd',
            'country_id' => 'us',
            'name' => 'Virgin America',
        ],
        [
            'id' => 'vy',
            'alias' => '',
            'call_sign' => 'VUELING',
            'code_iata' => 'vy',
            'code_icao' => 'vlg',
            'country_id' => 'es',
            'name' => 'Vueling Airlines',
        ],
        [
            'id' => 'vy',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'vy',
            'code_icao' => 'fos',
            'country_id' => 'tw',
            'name' => 'Formosa Airlines',
        ],
        [
            'id' => 'vz',
            'alias' => '',
            'call_sign' => 'KESTREL',
            'code_iata' => 'vz',
            'code_icao' => 'myt',
            'country_id' => 'gb',
            'name' => 'MyTravel Airways',
        ],
        [
            'id' => 'w1',
            'alias' => 'WEA',
            'call_sign' => 'WEA',
            'code_iata' => 'w1',
            'code_icao' => 'we1',
            'country_id' => 'ca',
            'name' => 'World Experience Airline',
        ],
        [
            'id' => 'w2',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'w2',
            'code_icao' => '',
            'country_id' => 'nl',
            'name' => 'Maastricht Airlines',
        ],
        [
            'id' => 'w4',
            'alias' => 'Sovet Air',
            'call_sign' => 'sovet',
            'code_iata' => 'w4',
            'code_icao' => 'wer',
            'country_id' => 'ru',
            'name' => 'AeroWorld',
        ],
        [
            'id' => 'w5',
            'alias' => '',
            'call_sign' => 'MAHAN AIR',
            'code_iata' => 'w5',
            'code_icao' => 'irm',
            'country_id' => '',
            'name' => 'Mahan Air',
        ],
        [
            'id' => 'w6',
            'alias' => '',
            'call_sign' => 'WIZZ AIR',
            'code_iata' => 'w6',
            'code_icao' => 'wzz',
            'country_id' => 'hu',
            'name' => 'Wizz Air',
        ],
        [
            'id' => 'w7',
            'alias' => 'Austral Brasil lineas aereas',
            'call_sign' => '',
            'code_iata' => 'w7',
            'code_icao' => '',
            'country_id' => 'br',
            'name' => 'Austral Brasil',
        ],
        [
            'id' => 'w9',
            'alias' => '',
            'call_sign' => 'AIR BAGAN',
            'code_iata' => 'w9',
            'code_icao' => 'jab',
            'country_id' => 'mm',
            'name' => 'Air Bagan',
        ],
        [
            'id' => 'wa',
            'alias' => '',
            'call_sign' => 'CITY',
            'code_iata' => 'wa',
            'code_icao' => 'klc',
            'country_id' => 'nl',
            'name' => 'KLM Cityhopper',
        ],
        [
            'id' => 'wa',
            'alias' => '',
            'call_sign' => 'WESTERN',
            'code_iata' => 'wa',
            'code_icao' => 'wal',
            'country_id' => 'us',
            'name' => 'Western Airlines',
        ],
        [
            'id' => 'wb',
            'alias' => '',
            'call_sign' => 'RWANDAIR',
            'code_iata' => 'wb',
            'code_icao' => 'rwd',
            'country_id' => 'rw',
            'name' => 'Rwandair Express',
        ],
        [
            'id' => 'wc',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'wc',
            'code_icao' => 'isv',
            'country_id' => 'hn',
            'name' => 'Islena De Inversiones',
        ],
        [
            'id' => 'wd',
            'alias' => '',
            'call_sign' => 'AMSTEL',
            'code_iata' => 'wd',
            'code_icao' => 'aan',
            'country_id' => 'nl',
            'name' => 'Amsterdam Airlines',
        ],
        [
            'id' => 'wf',
            'alias' => '',
            'call_sign' => 'WIDEROE',
            'code_iata' => 'wf',
            'code_icao' => 'wif',
            'country_id' => 'no',
            'name' => 'Widerøe',
        ],
        [
            'id' => 'wg',
            'alias' => '',
            'call_sign' => 'sunwing',
            'code_iata' => 'wg',
            'code_icao' => '',
            'country_id' => 'ca',
            'name' => 'Sunwing',
        ],
        [
            'id' => 'wh',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'wh',
            'code_icao' => '',
            'country_id' => 'cn',
            'name' => 'China Northwest Airlines (WH)',
        ],
        [
            'id' => 'wj',
            'alias' => '',
            'call_sign' => 'WEB-BRASIL',
            'code_iata' => 'wj',
            'code_icao' => 'web',
            'country_id' => 'br',
            'name' => 'WebJet Linhas A',
        ],
        [
            'id' => 'wk',
            'alias' => '',
            'call_sign' => 'EDELWEISS',
            'code_iata' => 'wk',
            'code_icao' => 'edw',
            'country_id' => 'ch',
            'name' => 'Edelweiss Air',
        ],
        [
            'id' => 'wm',
            'alias' => '',
            'call_sign' => 'Winair',
            'code_iata' => 'wm',
            'code_icao' => '',
            'country_id' => 'an',
            'name' => 'Windward Islands Airways',
        ],
        [
            'id' => 'wn',
            'alias' => '',
            'call_sign' => 'SOUTHWEST',
            'code_iata' => 'wn',
            'code_icao' => 'swa',
            'country_id' => 'us',
            'name' => 'Southwest Airlines',
        ],
        [
            'id' => 'wo',
            'alias' => '',
            'call_sign' => 'WORLD',
            'code_iata' => 'wo',
            'code_icao' => 'woa',
            'country_id' => 'us',
            'name' => 'World Airways',
        ],
        [
            'id' => 'wp',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'wp',
            'code_icao' => 'mku',
            'country_id' => 'us',
            'name' => 'Island Air (WP)',
        ],
        [
            'id' => 'wq',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'wq',
            'code_icao' => 'pqw',
            'country_id' => 'us',
            'name' => 'PanAm World Airways',
        ],
        [
            'id' => 'ws',
            'alias' => '',
            'call_sign' => 'WESTJET',
            'code_iata' => 'ws',
            'code_icao' => 'wja',
            'country_id' => 'ca',
            'name' => 'WestJet',
        ],
        [
            'id' => 'wu',
            'alias' => '',
            'call_sign' => 'WIZZAIR UKRAINE',
            'code_iata' => 'wu',
            'code_icao' => 'wau',
            'country_id' => 'ua',
            'name' => 'Wizz Air Ukraine',
        ],
        [
            'id' => 'wv',
            'alias' => '',
            'call_sign' => 'FLYING SWEDE',
            'code_iata' => 'wv',
            'code_icao' => 'swv',
            'country_id' => 'se',
            'name' => 'Swe Fly',
        ],
        [
            'id' => 'ww',
            'alias' => '',
            'call_sign' => 'BABY',
            'code_iata' => 'ww',
            'code_icao' => 'bmi',
            'country_id' => 'gb',
            'name' => 'bmibaby',
        ],
        [
            'id' => 'wx',
            'alias' => '',
            'call_sign' => 'CITY-IRELAND',
            'code_iata' => 'wx',
            'code_icao' => 'bcy',
            'country_id' => 'ie',
            'name' => 'CityJet',
        ],
        [
            'id' => 'wy',
            'alias' => '',
            'call_sign' => 'OMAN AIR',
            'code_iata' => 'wy',
            'code_icao' => 'oma',
            'country_id' => 'om',
            'name' => 'Oman Air',
        ],
        [
            'id' => 'wz',
            'alias' => 'Avialinii 400',
            'call_sign' => 'AIR RED',
            'code_iata' => 'wz',
            'code_icao' => 'rwz',
            'country_id' => 'ru',
            'name' => 'Red Wings',
        ],
        [
            'id' => 'x3',
            'alias' => '',
            'call_sign' => 'YELLOW CAB',
            'code_iata' => 'x3',
            'code_icao' => 'hlx',
            'country_id' => 'de',
            'name' => 'TUIfly',
        ],
        [
            'id' => 'xa',
            'alias' => '',
            'call_sign' => 'XAIR',
            'code_iata' => 'xa',
            'code_icao' => 'xau',
            'country_id' => 'us',
            'name' => 'XAIR USA',
        ],
        [
            'id' => 'xb',
            'alias' => 'NEXT',
            'call_sign' => 'XB',
            'code_iata' => 'xb',
            'code_icao' => 'nxb',
            'country_id' => 'br',
            'name' => 'NEXT Brasil',
        ],
        [
            'id' => 'xe',
            'alias' => '',
            'call_sign' => 'JET LINK',
            'code_iata' => 'xe',
            'code_icao' => 'bta',
            'country_id' => 'us',
            'name' => 'ExpressJet',
        ],
        [
            'id' => 'xf',
            'alias' => '',
            'call_sign' => 'VLADAIR',
            'code_iata' => 'xf',
            'code_icao' => 'vlk',
            'country_id' => 'ru',
            'name' => 'Vladivostok Air',
        ],
        [
            'id' => 'xg',
            'alias' => '',
            'call_sign' => 'CALIMA',
            'code_iata' => 'xg',
            'code_icao' => 'cli',
            'country_id' => 'es',
            'name' => 'Calima Aviacion',
        ],
        [
            'id' => 'xj',
            'alias' => '',
            'call_sign' => 'MESABA',
            'code_iata' => 'xj',
            'code_icao' => 'mes',
            'country_id' => 'us',
            'name' => 'Mesaba Airlines',
        ],
        [
            'id' => 'xk',
            'alias' => '',
            'call_sign' => 'CORSICA',
            'code_iata' => 'xk',
            'code_icao' => 'ccm',
            'country_id' => 'fr',
            'name' => 'Corse-Mediterranee',
        ],
        [
            'id' => 'xl',
            'alias' => '',
            'call_sign' => 'LAN ECUADOR',
            'code_iata' => 'xl',
            'code_icao' => 'lne',
            'country_id' => 'ec',
            'name' => 'Aerolane',
        ],
        [
            'id' => 'xm',
            'alias' => '',
            'call_sign' => 'ALIEXPRESS',
            'code_iata' => 'xm',
            'code_icao' => 'smx',
            'country_id' => 'it',
            'name' => 'Alitalia Express',
        ],
        [
            'id' => 'xn',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'xn',
            'code_icao' => '',
            'country_id' => 'id',
            'name' => 'Xpressair',
        ],
        [
            'id' => 'xo',
            'alias' => '',
            'call_sign' => 'FUN JET',
            'code_iata' => 'xo',
            'code_icao' => 'lte',
            'country_id' => 'es',
            'name' => 'LTE International Airways',
        ],
        [
            'id' => 'xp',
            'alias' => 'XPTO',
            'call_sign' => 'XPTO',
            'code_iata' => 'xp',
            'code_icao' => 'xpt',
            'country_id' => 'pt',
            'name' => 'XPTO',
        ],
        [
            'id' => 'xq',
            'alias' => '',
            'call_sign' => 'SUNEXPRESS',
            'code_iata' => 'xq',
            'code_icao' => 'sxs',
            'country_id' => 'tr',
            'name' => 'SunExpress',
        ],
        [
            'id' => 'xr',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'xr',
            'code_icao' => '',
            'country_id' => 'au',
            'name' => 'Skywest Australia',
        ],
        [
            'id' => 'xt',
            'alias' => '',
            'call_sign' => 'EXEL COMMUTER',
            'code_iata' => 'xt',
            'code_icao' => 'axl',
            'country_id' => 'nl',
            'name' => 'Air Exel',
        ],
        [
            'id' => 'xt',
            'alias' => '',
            'call_sign' => 'RED PHOENIX',
            'code_iata' => 'xt',
            'code_icao' => 'idx',
            'country_id' => 'id',
            'name' => 'Indonesia AirAsia X',
        ],
        [
            'id' => 'xv',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'xv',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'BVI Airways',
        ],
        [
            'id' => 'xw',
            'alias' => 'SkyExpress',
            'call_sign' => 'SKYSTORM',
            'code_iata' => 'xw',
            'code_icao' => 'sxr',
            'country_id' => 'ru',
            'name' => 'Sky Express',
        ],
        [
            'id' => 'xx',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'xx',
            'code_icao' => 'gfy',
            'country_id' => 'es',
            'name' => 'Greenfly',
        ],
        [
            'id' => 'xy',
            'alias' => '',
            'call_sign' => 'NAS EXPRESS',
            'code_iata' => 'xy',
            'code_icao' => 'kne',
            'country_id' => 'sa',
            'name' => 'Nas Air',
        ],
        [
            'id' => 'xz',
            'alias' => '',
            'call_sign' => 'EXPRESSWAYS',
            'code_iata' => 'xz',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'Congo Express',
        ],
        [
            'id' => 'y1',
            'alias' => '',
            'call_sign' => 'YCS',
            'code_iata' => 'y1',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'Yellowstone Club Private Shuttle',
        ],
        [
            'id' => 'y4',
            'alias' => '',
            'call_sign' => 'VOLARIS',
            'code_iata' => 'y4',
            'code_icao' => 'voi',
            'country_id' => 'mx',
            'name' => 'Volaris',
        ],
        [
            'id' => 'y5',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'y5',
            'code_icao' => 'awa',
            'country_id' => 'kz',
            'name' => 'Asia Wings',
        ],
        [
            'id' => 'y7',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'y7',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'NordStar Airlines',
        ],
        [
            'id' => 'y8',
            'alias' => 'Marusya Air',
            'call_sign' => 'snowball',
            'code_iata' => 'y8',
            'code_icao' => 'mrs',
            'country_id' => 'ru',
            'name' => 'Marusya Airways',
        ],
        [
            'id' => 'y9',
            'alias' => '',
            'call_sign' => 'KISHAIR',
            'code_iata' => 'y9',
            'code_icao' => 'irk',
            'country_id' => '',
            'name' => 'Kish Air',
        ],
        [
            'id' => 'yc',
            'alias' => '',
            'call_sign' => 'Ciel',
            'code_iata' => 'yc',
            'code_icao' => 'ycc',
            'country_id' => 'ca',
            'name' => 'Ciel Canadien',
        ],
        [
            'id' => 'yd',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'yd',
            'code_icao' => '',
            'country_id' => 'by',
            'name' => 'Gomelavia',
        ],
        [
            'id' => 'ye',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'ye',
            'code_icao' => 'yel',
            'country_id' => 'us',
            'name' => 'Yellowtail',
        ],
        [
            'id' => 'yh',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'yh',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'Yangon Airways Ltd.',
        ],
        [
            'id' => 'yk',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'yk',
            'code_icao' => '',
            'country_id' => 'tr',
            'name' => 'Cyprus Turkish Airlines',
        ],
        [
            'id' => 'yl',
            'alias' => '',
            'call_sign' => 'YAMAL',
            'code_iata' => 'yl',
            'code_icao' => 'llm',
            'country_id' => 'ru',
            'name' => 'Yamal Airlines',
        ],
        [
            'id' => 'ym',
            'alias' => '',
            'call_sign' => 'MONTAIR',
            'code_iata' => 'ym',
            'code_icao' => 'mgx',
            'country_id' => 'me',
            'name' => 'Montenegro Airlines',
        ],
        [
            'id' => 'yo',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'yo',
            'code_icao' => 'tys',
            'country_id' => 'br',
            'name' => 'TransHolding System',
        ],
        [
            'id' => 'yq',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'yq',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'Polet Airlines (Priv)',
        ],
        [
            'id' => 'yr',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'yr',
            'code_icao' => '',
            'country_id' => 'us',
            'name' => 'SENIC AIRLINES',
        ],
        [
            'id' => 'ys',
            'alias' => '',
            'call_sign' => 'REGIONAL EUROPE',
            'code_iata' => 'ys',
            'code_icao' => 'rae',
            'country_id' => 'fr',
            'name' => 'Régional',
        ],
        [
            'id' => 'yt',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'yt',
            'code_icao' => '',
            'country_id' => 'np',
            'name' => 'Yeti Airways',
        ],
        [
            'id' => 'yv',
            'alias' => '',
            'call_sign' => 'AIR SHUTTLE',
            'code_iata' => 'yv',
            'code_icao' => 'ash',
            'country_id' => 'us',
            'name' => 'Mesa Airlines',
        ],
        [
            'id' => 'yw',
            'alias' => '',
            'call_sign' => 'AIR NOSTRUM',
            'code_iata' => 'yw',
            'code_icao' => 'ane',
            'country_id' => 'es',
            'name' => 'Air Nostrum',
        ],
        [
            'id' => 'yx',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'yx',
            'code_icao' => 'mep',
            'country_id' => 'us',
            'name' => 'Midwest Airlines',
        ],
        [
            'id' => 'yy',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'yy',
            'code_icao' => 'vwa',
            'country_id' => 'de',
            'name' => 'Virginwings',
        ],
        [
            'id' => 'yz',
            'alias' => 'Russian. Yours Air Lines',
            'call_sign' => 'Moscow frog',
            'code_iata' => 'yz',
            'code_icao' => 'yzz',
            'country_id' => 'ru',
            'name' => 'LSM AIRLINES',
        ],
        [
            'id' => 'z2',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'z2',
            'code_icao' => '',
            'country_id' => 'ph',
            'name' => 'Zest Air',
        ],
        [
            'id' => 'z3',
            'alias' => '',
            'call_sign' => 'AVAVIA',
            'code_iata' => 'z3',
            'code_icao' => 'smj',
            'country_id' => 'zw',
            'name' => 'Avient Aviation',
        ],
        [
            'id' => 'z4',
            'alias' => '',
            'call_sign' => 'ZOOM',
            'code_iata' => 'z4',
            'code_icao' => 'oom',
            'country_id' => 'ca',
            'name' => 'Zoom Airlines',
        ],
        [
            'id' => 'z6',
            'alias' => 'ZABAIKAL',
            'call_sign' => 'BAIKAL',
            'code_iata' => 'z6',
            'code_icao' => 'ztt',
            'country_id' => 'ru',
            'name' => 'ZABAIKAL AIRLINES',
        ],
        [
            'id' => 'z7',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'z7',
            'code_icao' => '',
            'country_id' => 'bb',
            'name' => 'REDjet',
        ],
        [
            'id' => 'z8',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'z8',
            'code_icao' => 'azn',
            'country_id' => '',
            'name' => 'Amaszonas',
        ],
        [
            'id' => 'z9',
            'alias' => 'Flightlink',
            'call_sign' => '',
            'code_iata' => 'z9',
            'code_icao' => '',
            'country_id' => '',
            'name' => 'Flightlink Tanzania',
        ],
        [
            'id' => 'za',
            'alias' => '',
            'call_sign' => 'ASTAIR',
            'code_iata' => 'za',
            'code_icao' => 'suw',
            'country_id' => 'ru',
            'name' => 'Interavia Airlines',
        ],
        [
            'id' => 'za',
            'alias' => '',
            'call_sign' => 'CYCLONE',
            'code_iata' => 'za',
            'code_icao' => 'cyd',
            'country_id' => 'us',
            'name' => 'Access Air',
        ],
        [
            'id' => 'zb',
            'alias' => '',
            'call_sign' => 'MONARCH',
            'code_iata' => 'zb',
            'code_icao' => 'mon',
            'country_id' => 'gb',
            'name' => 'Monarch Airlines',
        ],
        [
            'id' => 'zb',
            'alias' => '',
            'call_sign' => 'BOURBON',
            'code_iata' => 'zb',
            'code_icao' => 'bub',
            'country_id' => 're',
            'name' => 'Air Bourbon',
        ],
        [
            'id' => 'zc',
            'alias' => '',
            'call_sign' => 'KORONGO',
            'code_iata' => 'zc',
            'code_icao' => 'kgo',
            'country_id' => '',
            'name' => 'Korongo Airlines',
        ],
        [
            'id' => 'ze',
            'alias' => '',
            'call_sign' => 'Eastar',
            'code_iata' => 'ze',
            'code_icao' => 'esr',
            'country_id' => '',
            'name' => 'Eastar Jet',
        ],
        [
            'id' => 'zf',
            'alias' => '',
            'call_sign' => 'ATHENSAIR',
            'code_iata' => 'zf',
            'code_icao' => '',
            'country_id' => 'gr',
            'name' => 'Athens Airways',
        ],
        [
            'id' => 'zg',
            'alias' => '',
            'call_sign' => 'JACKPOT',
            'code_iata' => 'zg',
            'code_icao' => 'vvm',
            'country_id' => 'mo',
            'name' => 'Viva Macau',
        ],
        [
            'id' => 'zh',
            'alias' => '',
            'call_sign' => 'SHENZHEN AIR',
            'code_iata' => 'zh',
            'code_icao' => 'csz',
            'country_id' => 'cn',
            'name' => 'Shenzhen Airlines',
        ],
        [
            'id' => 'zi',
            'alias' => '',
            'call_sign' => 'AIGLE AZUR',
            'code_iata' => 'zi',
            'code_icao' => 'aaf',
            'country_id' => 'fr',
            'name' => 'Aigle Azur',
        ],
        [
            'id' => 'zj',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'zj',
            'code_icao' => '',
            'country_id' => 'zm',
            'name' => 'Zambezi Airlines (ZMA)',
        ],
        [
            'id' => 'zk',
            'alias' => '',
            'call_sign' => 'LAKES AIR',
            'code_iata' => 'zk',
            'code_icao' => 'gla',
            'country_id' => 'us',
            'name' => 'Great Lakes Airlines',
        ],
        [
            'id' => 'zl',
            'alias' => '',
            'call_sign' => 'REX',
            'code_iata' => 'zl',
            'code_icao' => 'rxa',
            'country_id' => 'au',
            'name' => 'Regional Express',
        ],
        [
            'id' => 'zm',
            'alias' => 'Apache',
            'call_sign' => 'APACHE',
            'code_iata' => 'zm',
            'code_icao' => 'iwa',
            'country_id' => 'us',
            'name' => 'Apache Air',
        ],
        [
            'id' => 'zn',
            'alias' => 'Zenith',
            'call_sign' => 'ZENITH',
            'code_iata' => 'zn',
            'code_icao' => 'zna',
            'country_id' => 'th',
            'name' => 'Zenith International Airline',
        ],
        [
            'id' => 'zp',
            'alias' => 'Baikal Airlines',
            'call_sign' => 'Lakeair',
            'code_iata' => 'zp',
            'code_icao' => 'zzz',
            'country_id' => 'ru',
            'name' => 'Zabaykalskii Airlines',
        ],
        [
            'id' => 'zq',
            'alias' => '',
            'call_sign' => 'LOCAIR',
            'code_iata' => 'zq',
            'code_icao' => 'loc',
            'country_id' => 'us',
            'name' => 'Locair',
        ],
        [
            'id' => 'zs',
            'alias' => '',
            'call_sign' => 'NAJIM',
            'code_iata' => 'zs',
            'code_icao' => 'smy',
            'country_id' => 'sa',
            'name' => 'Sama Airlines',
        ],
        [
            'id' => 'zw',
            'alias' => '',
            'call_sign' => 'AIR WISCONSIN',
            'code_iata' => 'zw',
            'code_icao' => 'awi',
            'country_id' => 'us',
            'name' => 'Air Wisconsin',
        ],
        [
            'id' => 'zx',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'zx',
            'code_icao' => 'zxy',
            'country_id' => 'jp',
            'name' => 'Japan Regio',
        ],
        [
            'id' => 'zy',
            'alias' => '',
            'call_sign' => 'ADA AIR',
            'code_iata' => 'zy',
            'code_icao' => 'ade',
            'country_id' => 'al',
            'name' => 'Ada Air',
        ],
        [
            'id' => 'zz',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'zz',
            'code_icao' => '',
            'country_id' => 'be',
            'name' => 'Zz',
        ],
        [
            'id' => 'ми',
            'alias' => '',
            'call_sign' => 'Air Minvody',
            'code_iata' => 'ми',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'KMV',
        ],
        [
            'id' => 'яп',
            'alias' => '',
            'call_sign' => '',
            'code_iata' => 'яп',
            'code_icao' => '',
            'country_id' => 'ru',
            'name' => 'Polar Airlines',
        ],
    ];

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function continent()
    {
        return $this->hasOneThrough(Continent::class, Country::class);
    }
}
