<?php

namespace App\Models;

use App\Enum\SalesSupportPurposeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\SalesSupportAction.
 *
 * @property int $id
 * @property int $company_id
 * @property string $action
 * @property SalesSupportPurposeEnum $purpose
 * @property \Illuminate\Support\Carbon $date
 * @property int|null $done_by
 * @property int|null $duration
 * @property string|null $comments
 * @property string|null $next_step_action
 * @property string|null $next_step_purpose
 * @property int|null $next_step_done_by
 * @property \Illuminate\Support\Carbon|null $next_step_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Company $company
 * @property-read \App\Models\AdminUser|null $maker
 * @property-read \App\Models\AdminUser|null $makerWithTrashed
 * @property-read \App\Models\AdminUser|null $nextStepMaker
 * @property-read \App\Models\AdminUser|null $nextStepMakerWithTrashed
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\CompanyTag> $tags
 * @property-read int|null $tags_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction query()
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereDoneBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereNextStepAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereNextStepDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereNextStepDoneBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereNextStepPurpose($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction wherePurpose($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|SalesSupportAction withoutTrashed()
 *
 * @mixin \Eloquent
 */
class SalesSupportAction extends Model
{
    use SoftDeletes;

    public $timestamps = true;

    protected $casts = [
        'date' => 'date',
        'purpose' => SalesSupportPurposeEnum::class,
        'next_step_date' => 'date',
    ];

    protected $guarded = ['id'];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function maker(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'done_by');
    }

    public function makerWithTrashed(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'done_by')->withTrashed();
    }

    public function nextStepMaker(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'next_step_done_by');
    }

    public function nextStepMakerWithTrashed(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'next_step_done_by')->withTrashed();
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(CompanyTag::class, 'company_tag_sales_support_action');
    }
}
