<?php

namespace App\Models;

use App\Models\User as RNUser;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\AlertsSnoozeTime.
 *
 * @property int $id
 * @property int $booking_alert_id
 * @property int $user_id
 * @property Carbon $snoozed_until
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read RNUser $user
 *
 * @method static Builder|AlertsSnoozeTime newModelQuery()
 * @method static Builder|AlertsSnoozeTime newQuery()
 * @method static Builder|AlertsSnoozeTime query()
 * @method static Builder|AlertsSnoozeTime whereBookingAlertId($value)
 * @method static Builder|AlertsSnoozeTime whereCreatedAt($value)
 * @method static Builder|AlertsSnoozeTime whereId($value)
 * @method static Builder|AlertsSnoozeTime whereSnoozedUntil($value)
 * @method static Builder|AlertsSnoozeTime whereUpdatedAt($value)
 * @method static Builder|AlertsSnoozeTime whereUserId($value)
 *
 * @mixin Eloquent
 */
class AlertsSnoozeTime extends Model
{
    public $timestamps = true;

    protected $table = 'booking_alerts_snooze_time';

    protected $fillable = [
        'booking_alert_id',
        'user_id',
        'snoozed_until',
    ];

    protected $casts = [
        'snoozed_until' => 'date',
    ];

    protected $guarded = [];

    public function delete(): ?bool
    {
        nLog("DELETING SNOOZED ALERT for user {$this->user_id} and booking alert {$this->booking_alert_id}", $this->user->current_team_id);

        return parent::delete();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(RNUser::class);
    }
}
