<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ChannelManager.
 *
 * @property int $id
 * @property string|null $name
 *
 * @method static Builder|ChannelManager newModelQuery()
 * @method static Builder|ChannelManager newQuery()
 * @method static Builder|ChannelManager query()
 * @method static Builder|ChannelManager whereId($value)
 * @method static Builder|ChannelManager whereName($value)
 *
 * @mixin Eloquent
 */
class ChannelManager extends Model
{
    use Sushi;

    protected $rows = [
        [
            'id' => 1,
            'name' => 'Smily',
        ],
        [
            'id' => 2,
            'name' => 'Lodgify',
        ],
        [
            'id' => 3,
            'name' => 'Smoobu',
        ],
        [
            'id' => 4,
            'name' => 'Rentals United',
        ],
        [
            'id' => 5,
            'name' => 'Avirato',
        ],
        [
            'id' => 6,
            'name' => 'Barefoot',
        ],
        [
            'id' => 7,
            'name' => 'BedLoop',
        ],
        [
            'id' => 8,
            'name' => 'Beds 24',
        ],
        [
            'id' => 9,
            'name' => 'BeeRent',
        ],
        [
            'id' => 10,
            'name' => 'Bookfull',
        ],
        [
            'id' => 11,
            'name' => 'Booking Automation',
        ],
        [
            'id' => 12,
            'name' => 'Booking ninjas',
        ],
        [
            'id' => 13,
            'name' => '365 Villas',
        ],
        [
            'id' => 14,
            'name' => 'BookiPro',
        ],
        [
            'id' => 15,
            'name' => 'Bookster',
        ],
        [
            'id' => 16,
            'name' => 'Brichtside Rental Management',
        ],
        [
            'id' => 17,
            'name' => 'Ciirus',
        ],
        [
            'id' => 18,
            'name' => 'Cloudbeds',
        ],
        [
            'id' => 19,
            'name' => 'Direct',
        ],
        [
            'id' => 20,
            'name' => 'Elina',
        ],
        [
            'id' => 21,
            'name' => 'Escapia',
        ],
        [
            'id' => 22,
            'name' => 'Eviivo',
        ],
        [
            'id' => 23,
            'name' => 'Fantastic Stay',
        ],
        [
            'id' => 24,
            'name' => 'Futurestay',
        ],
        [
            'id' => 25,
            'name' => 'Genkan',
        ],
        [
            'id' => 26,
            'name' => 'Guesty',
        ],
        [
            'id' => 27,
            'name' => 'Hirum',
        ],
        [
            'id' => 28,
            'name' => 'Holigest',
        ],
        [
            'id' => 29,
            'name' => 'Host Platform',
        ],
        [
            'id' => 30,
            'name' => 'HostAway',
        ],
        [
            'id' => 31,
            'name' => 'Hostfully',
        ],
        [
            'id' => 32,
            'name' => 'Hostify',
        ],
        [
            'id' => 33,
            'name' => 'Icnea',
        ],
        [
            'id' => 34,
            'name' => 'iGMS',
        ],
        [
            'id' => 35,
            'name' => 'iPRO',
        ],
        [
            'id' => 36,
            'name' => 'Janiis',
        ],
        [
            'id' => 37,
            'name' => 'Kigo',
        ],
        [
            'id' => 38,
            'name' => 'Klick.villas',
        ],
        [
            'id' => 39,
            'name' => 'Kroos booking',
        ],
        [
            'id' => 40,
            'name' => 'LiveRez',
        ],
        [
            'id' => 41,
            'name' => 'Lodgix.com',
        ],
        [
            'id' => 42,
            'name' => 'Maxxton',
        ],
        [
            'id' => 43,
            'name' => 'MyRent',
        ],
        [
            'id' => 44,
            'name' => 'MyVR',
        ],
        [
            'id' => 45,
            'name' => 'net2rent',
        ],
        [
            'id' => 46,
            'name' => 'Nightsbridge',
        ],
        [
            'id' => 47,
            'name' => 'Nuevah',
        ],
        [
            'id' => 48,
            'name' => 'Octopus',
        ],
        [
            'id' => 49,
            'name' => 'Octorate',
        ],
        [
            'id' => 50,
            'name' => 'OneMineral',
        ],
        [
            'id' => 51,
            'name' => 'OwnerRez',
        ],
        [
            'id' => 52,
            'name' => 'Rentability',
        ],
        [
            'id' => 53,
            'name' => 'Rentivo',
        ],
        [
            'id' => 54,
            'name' => 'Res Nexus',
        ],
        [
            'id' => 55,
            'name' => 'Res: Harmonics',
        ],
        [
            'id' => 56,
            'name' => 'ResBook',
        ],
        [
            'id' => 57,
            'name' => 'Reservation Key',
        ],
        [
            'id' => 58,
            'name' => 'Resort Data Processing',
        ],
        [
            'id' => 59,
            'name' => 'RMS9+',
        ],
        [
            'id' => 60,
            'name' => 'RPM PRO',
        ],
        [
            'id' => 61,
            'name' => 'Sabee app',
        ],
        [
            'id' => 62,
            'name' => 'AvaiBook',
        ],
        [
            'id' => 63,
            'name' => 'Streamline',
        ],
        [
            'id' => 64,
            'name' => 'Suitech',
        ],
        [
            'id' => 65,
            'name' => 'Supercontrol',
        ],
        [
            'id' => 66,
            'name' => 'Think Reservations',
        ],
        [
            'id' => 67,
            'name' => 'Tokeet',
        ],
        [
            'id' => 68,
            'name' => 'Track',
        ],
        [
            'id' => 69,
            'name' => 'Uplisting',
        ],
        [
            'id' => 70,
            'name' => 'Virtual Resort Management',
        ],
        [
            'id' => 71,
            'name' => 'VR Bookings',
        ],
        [
            'id' => 72,
            'name' => 'Vreasy',
        ],
        [
            'id' => 73,
            'name' => 'Webrezpro',
        ],
        [
            'id' => 74,
            'name' => 'Your Porter App',
        ],
        [
            'id' => 75,
            'name' => 'Your Rentals',
        ],
        [
            'id' => 76,
            'name' => 'Zeevou',
        ],
        [
            'id' => 77,
            'name' => 'AnyTime booking',
        ],
        [
            'id' => 78,
            'name' => 'Avantio',
        ],
        [
            'id' => 79,
            'name' => 'Rental Ninja iCal',
        ],
        [
            'id' => 80,
            'name' => 'Rental Ninja CM',
        ],
        [
            'id' => 81,
            'name' => 'Superhote',
        ],
        [
            'id' => 82,
            'name' => 'Rental Ready',
        ],
        [
            'id' => 83,
            'name' => 'Resly',
        ],
        [
            'id' => 84,
            'name' => 'Amenitiz',
        ],
    ];
}
