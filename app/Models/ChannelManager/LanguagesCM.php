<?php

namespace App\Models\ChannelManager;

use App\Models\Sushi;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ChannelManager\LanguagesCM.
 *
 * @property int $id
 * @property string|null $code
 * @property string|null $name
 *
 * @method static \Illuminate\Database\Eloquent\Builder|LanguagesCM newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LanguagesCM newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LanguagesCM query()
 * @method static \Illuminate\Database\Eloquent\Builder|LanguagesCM whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LanguagesCM whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LanguagesCM whereName($value)
 *
 * @mixin \Eloquent
 */
class LanguagesCM extends Model
{
    use Sushi;

    protected array $rows = [
        ['id' => 1, 'code' => 'en', 'name' => 'English'],
        ['id' => 2, 'code' => 'de', 'name' => 'German'],
        ['id' => 3, 'code' => 'pl', 'name' => 'Polish'],
        ['id' => 4, 'code' => 'fr', 'name' => 'French'],
        ['id' => 5, 'code' => 'es', 'name' => 'Spanish'],
        ['id' => 6, 'code' => 'it', 'name' => 'Italian'],
        ['id' => 7, 'code' => 'sv', 'name' => 'Swedish'],
        ['id' => 8, 'code' => 'da', 'name' => 'Danish'],
        ['id' => 9, 'code' => 'pt', 'name' => 'Portuguese'],
        ['id' => 10, 'code' => 'cs', 'name' => 'Czech'],
        ['id' => 11, 'code' => 'nl', 'name' => 'Dutch'],
        ['id' => 12, 'code' => 'fi', 'name' => 'Finnish'],
        ['id' => 13, 'code' => 'ru', 'name' => 'Russian'],
        ['id' => 14, 'code' => 'lv', 'name' => 'Latvian'],
        ['id' => 15, 'code' => 'lt', 'name' => 'Lithuanian'],
        ['id' => 16, 'code' => 'ee', 'name' => 'Estonian'],
        ['id' => 17, 'code' => 'sk', 'name' => 'Slovak'],
        ['id' => 18, 'code' => 'hu', 'name' => 'Hungarian'],
        ['id' => 19, 'code' => 'jp', 'name' => 'Japanese'],
        ['id' => 22, 'code' => 'bg', 'name' => 'Bulgarian'],
        ['id' => 23, 'code' => 'tk', 'name' => 'Turkish'],
        ['id' => 24, 'code' => 'gr', 'name' => 'Greek'],
        ['id' => 25, 'code' => 'ro', 'name' => 'Romanian'],
        ['id' => 26, 'code' => 'zh', 'name' => 'Chinese'],
        ['id' => 27, 'code' => 'ca', 'name' => 'Catalan'],
        ['id' => 28, 'code' => 'he', 'name' => 'Hebrew'],
        ['id' => 29, 'code' => 'hr', 'name' => 'Croatian'],
        ['id' => 30, 'code' => 'id', 'name' => 'Indonesian'],
        ['id' => 31, 'code' => 'is', 'name' => 'Icelandic'],
        ['id' => 32, 'code' => 'ko', 'name' => 'Korean'],
        ['id' => 33, 'code' => 'ms', 'name' => 'Malay'],
        ['id' => 34, 'code' => 'no', 'name' => 'Norwegian'],
        ['id' => 35, 'code' => 'th', 'name' => 'Thai'],
        ['id' => 36, 'code' => 'tl', 'name' => 'Tagalog'],
    ];
}
