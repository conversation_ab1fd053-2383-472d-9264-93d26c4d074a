<?php

namespace App\Models\ChannelManager;

use App\Enum\ChangeOverEnum;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Database\Eloquent\Castable;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use RuntimeException;
use Spatie\LaravelData\Casts\Cast;
use Spatie\LaravelData\Support\DataProperty;
use Spatie\LaravelData\Transformers\Transformer;

class Changeovers implements Castable, Transformer, Cast
{
    public function __construct(
        public ChangeOverEnum $monday = ChangeOverEnum::both,
        public ChangeOverEnum $tuesday = ChangeOverEnum::both,
        public ChangeOverEnum $wednesday = ChangeOverEnum::both,
        public ChangeOverEnum $thursday = ChangeOverEnum::both,
        public ChangeOverEnum $friday = ChangeOverEnum::both,
        public ChangeOverEnum $saturday = ChangeOverEnum::both,
        public ChangeOverEnum $sunday = ChangeOverEnum::both,
    ) {
    }

    public function on(Carbon|int $day): ChangeOverEnum
    {
        if ($day instanceof Carbon) {
            $day = $day->dayOfWeek;
        }

        return match ($day) {
            0 => $this->sunday,
            1 => $this->monday,
            2 => $this->tuesday,
            3 => $this->wednesday,
            4 => $this->thursday,
            5 => $this->friday,
            6 => $this->saturday,
        };
    }

    /**
     * Get the caster class to use when casting from / to this cast target.
     */
    public static function castUsing(array $arguments): CastsAttributes
    {
        return new class implements CastsAttributes
        {
            public function get(Model $model, string $key, mixed $value, array $attributes): Changeovers
            {
                $data = json_decode($value);
                $default = ChangeOverEnum::both->value;

                return new Changeovers(
                    monday: ChangeOverEnum::tryFrom(data_get($data, CarbonInterface::MONDAY, $default)),
                    tuesday: ChangeOverEnum::tryFrom(data_get($data, CarbonInterface::TUESDAY, $default)),
                    wednesday: ChangeOverEnum::tryFrom(data_get($data, CarbonInterface::WEDNESDAY, $default)),
                    thursday: ChangeOverEnum::tryFrom(data_get($data, CarbonInterface::THURSDAY, $default)),
                    friday: ChangeOverEnum::tryFrom(data_get($data, CarbonInterface::FRIDAY, $default)),
                    saturday: ChangeOverEnum::tryFrom(data_get($data, CarbonInterface::SATURDAY, $default)),
                    sunday: ChangeOverEnum::tryFrom(data_get($data, CarbonInterface::SUNDAY, $default)),
                );
            }

            public function set(Model $model, string $key, mixed $value, array $attributes): array
            {
                if ($value instanceof Changeovers) {
                    $data = json_encode($value->toArray());
                } else {
                    throw new RuntimeException('Invalid $value in Changeovers::set');
                }

                return [$key => $data];
            }
        };
    }

    public function transform(DataProperty $property, mixed $value): mixed
    {
        if ($value instanceof Changeovers) {
            return [
                'monday' => $value->monday->value,
                'tuesday' => $value->tuesday->value,
                'wednesday' => $value->wednesday->value,
                'thursday' => $value->thursday->value,
                'friday' => $value->friday->value,
                'saturday' => $value->saturday->value,
                'sunday' => $value->sunday->value,
            ];
        }
        throw new RuntimeException('Invalid value');
    }

    public function cast(DataProperty $property, mixed $value, array $context): Changeovers
    {
        if ($value instanceof Changeovers) {
            return $value;
        }
        $default = ChangeOverEnum::both->value;

        return new Changeovers(
            monday: ChangeOverEnum::tryFrom(data_get($value, 'monday', $default)),
            tuesday: ChangeOverEnum::tryFrom(data_get($value, 'tuesday', $default)),
            wednesday: ChangeOverEnum::tryFrom(data_get($value, 'wednesday', $default)),
            thursday: ChangeOverEnum::tryFrom(data_get($value, 'thursday', $default)),
            friday: ChangeOverEnum::tryFrom(data_get($value, 'friday', $default)),
            saturday: ChangeOverEnum::tryFrom(data_get($value, 'saturday', $default)),
            sunday: ChangeOverEnum::tryFrom(data_get($value, 'sunday', $default)),
        );
    }

    public function toArray(): ?array
    {
        $data = [];
        if ($this->monday != ChangeOverEnum::both) {
            $data[CarbonInterface::MONDAY] = $this->monday->value;
        }
        if ($this->tuesday != ChangeOverEnum::both) {
            $data[CarbonInterface::TUESDAY] = $this->tuesday->value;
        }
        if ($this->wednesday != ChangeOverEnum::both) {
            $data[CarbonInterface::WEDNESDAY] = $this->wednesday->value;
        }
        if ($this->thursday != ChangeOverEnum::both) {
            $data[CarbonInterface::THURSDAY] = $this->thursday->value;
        }
        if ($this->friday != ChangeOverEnum::both) {
            $data[CarbonInterface::FRIDAY] = $this->friday->value;
        }
        if ($this->saturday != ChangeOverEnum::both) {
            $data[CarbonInterface::SATURDAY] = $this->saturday->value;
        }
        if ($this->sunday != ChangeOverEnum::both) {
            $data[CarbonInterface::SUNDAY] = $this->sunday->value;
        }

        return empty($data) ? null : $data;
    }
}
