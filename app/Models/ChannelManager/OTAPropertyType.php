<?php

namespace App\Models\ChannelManager;

use App\Models\Sushi;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ChannelManager\OTAPropertyType.
 *
 * @property int $id
 * @property string|null $name
 * @property int|null $code
 *
 * @method static \Illuminate\Database\Eloquent\Builder|OTAPropertyType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OTAPropertyType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OTAPropertyType query()
 * @method static \Illuminate\Database\Eloquent\Builder|OTAPropertyType whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OTAPropertyType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OTAPropertyType whereName($value)
 *
 * @mixin \Eloquent
 */
class OTAPropertyType extends Model
{
    use Sushi;

    protected array $rows = [
        ['id' => 1, 'name' => 'All suite', 'code' => 1],
        ['id' => 2, 'name' => 'All-Inclusive resort', 'code' => 2],
        ['id' => 3, 'name' => 'Apartment', 'code' => 3],
        ['id' => 4, 'name' => 'Bed and breakfast', 'code' => 4],
        ['id' => 7, 'name' => 'Chalet', 'code' => 7],
        ['id' => 16, 'name' => 'Guest house', 'code' => 16],
        ['id' => 20, 'name' => 'Hotel', 'code' => 20],
        ['id' => 22, 'name' => 'Lodge', 'code' => 22],
        ['id' => 30, 'name' => 'Resort', 'code' => 30],
        ['id' => 35, 'name' => 'Villa', 'code' => 35],
        ['id' => 37, 'name' => 'Castle', 'code' => 37],
        ['id' => 63, 'name' => 'Aparthotel', 'code' => 3],
        ['id' => 64, 'name' => 'Boat', 'code' => 31],
        ['id' => 65, 'name' => 'Cottage', 'code' => 5],
        ['id' => 66, 'name' => 'Camping', 'code' => 25],
        ['id' => 67, 'name' => 'House', 'code' => 34],
        ['id' => 68, 'name' => 'Private room in apartment', 'code' => 68],
        ['id' => 69, 'name' => 'Shared room', 'code' => 69],
    ];
}
