<?php

namespace App\Models;

use App\Enum\FeeKindEnum;
use App\Enum\FeeRateKindEnum;
use App\Exceptions\NinjaNotImplementedException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Fee.
 *
 * @property int $id
 * @property string|null $external_id
 * @property int|null $provider_id
 * @property int $team_id
 * @property string $name
 * @property FeeKindEnum $kind
 * @property float $rate
 * @property FeeRateKindEnum $rate_kind
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Team $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Fee newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Fee newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Fee onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Fee query()
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereKind($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereRateKind($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fee withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Fee withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Fee extends Model
{
    use SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'kind' => FeeKindEnum::class,
        'rate_kind' => FeeRateKindEnum::class,
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    // If fee is updated to decimal, we will not need to divide the rate by 100.
    public function getRateInCents(): float
    {
        return ninjaIntval($this->rate * 100);
    }

    /**
     * Shortcut to calculate fixed fees for a booking. Mainly used in upsells.
     * Returns [int FeePrice, int Times, int FreePricePerUnit].
     * */
    public function calculateNewBookingFeeValues(Booking $booking, ?int $timesBooked = null): array
    {
        return $this->calculateAmountInCentsAndTimes($booking->nights, $booking->adults + $booking->children, timesBooked: $timesBooked);
    }

    /**
     * Length is in days.
     * Guests is the total number of guests, including adults and childs.
     * Price is in cents.
     *
     * Returns [int FeePrice, int Times, int FreePricePerUnit]
     */
    public function calculateAmountInCentsAndTimes(int $length, int $guests, ?int $price = null, ?int $timesBooked = null): array
    {
        if ($this->rate_kind->isPercentage() && is_null($price)) {
            throw new NinjaNotImplementedException('Percentage rate fees require a price to calculate the fee.');
        }

        $unitFeePriceInCents = $this->getUnitFeePrice($price);
        $times = $this->getTimes($length, $guests, $timesBooked);

        return [$unitFeePriceInCents * $times, $times, $unitFeePriceInCents];
    }

    private function getUnitFeePrice(?int $price): int
    {
        if ($this->rate_kind->isPercentage() && is_null($price)) {
            throw new NinjaNotImplementedException('Percentage rate fees require a price to calculate the fee.');
        }

        if ($this->rate_kind->isPercentage()) {
            return ninjaIntval($this->rate * $price / 100);
        } else {
            return ninjaIntval($this->rate * 100);
        }
    }

    public function getTimes(int $length, int $guests, ?int $timesBooked = null): int
    {
        if ($timesBooked) {
            return $timesBooked;
        }

        // Fee is in decimal (not cents) or in percentage (100 in 100%).
        return match ($this->rate_kind) {
            FeeRateKindEnum::fixed => 1,
            FeeRateKindEnum::fixedPerPerson, FeeRateKindEnum::percentagePerPerson => $guests,
            FeeRateKindEnum::fixedPerNight => $length,
            FeeRateKindEnum::fixedPerPersonPerNight => $length * $guests,
            // Other percentage fees are applied once.
            default => 1
        };
    }
}
