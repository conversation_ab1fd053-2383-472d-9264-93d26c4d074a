<?php

namespace App\Models;

use App\Enum\BillableAddonTypeEnum;
use <PERSON><PERSON><PERSON>z\Compoships\Compoships;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\BillableAddonLog.
 *
 * @property int $id
 * @property int $team_id
 * @property BillableAddonTypeEnum $type
 * @property int $quantity
 * @property int|null $rental_id
 * @property int|null $booking_id
 * @property int|null $user_id
 * @property string|null $extra
 * @property float|null $cost
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Booking|null $booking
 * @property-read \App\Models\Rental|null $rental
 * @property-read \App\Models\Team $team
 * @property-read \App\Models\User|null $user
 *
 * @method static Builder|BillableAddonLog newModelQuery()
 * @method static Builder|BillableAddonLog newQuery()
 * @method static Builder|BillableAddonLog query()
 * @method static Builder|BillableAddonLog whereBookingId($value)
 * @method static Builder|BillableAddonLog whereCost($value)
 * @method static Builder|BillableAddonLog whereCreatedAt($value)
 * @method static Builder|BillableAddonLog whereExtra($value)
 * @method static Builder|BillableAddonLog whereId($value)
 * @method static Builder|BillableAddonLog whereQuantity($value)
 * @method static Builder|BillableAddonLog whereRentalId($value)
 * @method static Builder|BillableAddonLog whereTeamId($value)
 * @method static Builder|BillableAddonLog whereType($value)
 * @method static Builder|BillableAddonLog whereUpdatedAt($value)
 * @method static Builder|BillableAddonLog whereUserId($value)
 *
 * @mixin Eloquent
 */
class BillableAddonLog extends Model
{
    use Compoships;

    protected $fillable = [
        'team_id',
        'type',
        'quantity',
        'rental_id',
        'booking_id',
        'user_id',
        'extra',
        'cost',
    ];

    protected $casts = [
        'type' => BillableAddonTypeEnum::class,
    ];

    protected $attributes = [
        'quantity' => 0,
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class, ['booking_id', 'team_id'], ['id', 'team_id'])->withoutGlobalScopes();
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id']);
    }
}
