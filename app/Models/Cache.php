<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Cache.
 *
 * @property int $key
 * @property string $value
 * @property int $expiration
 *
 * @method static Builder|Cache newModelQuery()
 * @method static Builder|Cache newQuery()
 * @method static Builder|Cache query()
 * @method static Builder|Cache whereExpiration($value)
 * @method static Builder|Cache whereKey($value)
 * @method static Builder|Cache whereValue($value)
 *
 * @mixin Eloquent
 */
class Cache extends Model
{
    protected $table = 'cache';

    protected $primaryKey = 'key';

    public $timestamps = false;

    protected $fillable = [
        'value',
        'expiration',
    ];

    protected $guarded = [];
}
