<?php

namespace App\Models;

use App\DTO\Accounting\AdvancedAccountingRuleDto;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

/**
 * App\Models\SourceCommissionStrategy.
 *
 * @property int $id
 * @property int $team_id
 * @property bool $enabled
 * @property int $advanced_settings_id
 * @property int $source_id
 * @property bool $calculate_from_final_price If true, we calculate commission from rental price. Otherwise from booking breakdown
 * @property array|null $final_price_rules
 * @property array|null $rental_price_rules
 * @property array|null $fees_rules
 * @property array|null $taxes_rules
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\AccountingAdvancedSettings $accountingAdvancedSettings
 * @property-read \App\Models\Source $source
 * @property-read \App\Models\Team $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy query()
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereAdvancedSettingsId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereCalculateFromFinalPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereFeesRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereFinalPriceRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereRentalPriceRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereTaxesRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SourceCommissionStrategy whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class SourceCommissionStrategy extends NinjaProviderModel
{
    protected $table = 'source_commission_strategies';

    protected $fillable = [
        'team_id',
        'source_id',
        'calculate_from_final_price',
        'final_price_rules',
        'rental_price_rules',
        'fees_rules',
        'taxes_rules',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'calculate_from_final_price' => 'boolean',
        'final_price_rules' => 'array',
        'rental_price_rules' => 'array',
        'fees_rules' => 'array',
        'taxes_rules' => 'array',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function source(): BelongsTo
    {
        return $this->belongsTo(Source::class, ['source_id', 'team_id'], ['id', 'team_id']);
    }

    public function accountingAdvancedSettings(): BelongsTo
    {
        return $this->belongsTo(AccountingAdvancedSettings::class, 'advanced_settings_id', 'id');
    }

    // Rules parsers
    public function getFinalPriceRulesDataCollection(): Collection
    {
        return collect($this->final_price_rules)->recursive()->map(
            fn ($item) => AdvancedAccountingRuleDto::from(['value' => $item['value'], 'rentals' => $item['rentals']])
        );
    }

    public function getRentalPriceRulesDataCollection(): Collection
    {
        return collect($this->rental_price_rules)->recursive()->map(
            fn ($item) => AdvancedAccountingRuleDto::from(['value' => $item['value'], 'rentals' => $item['rentals']])
        );
    }

    public function getTaxesPriceRulesDataCollection(): Collection
    {
        return collect($this->taxes_rules)->recursive()->map(
            fn ($item) => new AdvancedAccountingRuleDto($item['value'], $item['rentals'], $item['string'], $item['contains'])
        );
    }

    public function getFeesPriceRulesDataCollection(): Collection
    {
        return collect($this->fees_rules)->recursive()->map(
            fn ($item) => new AdvancedAccountingRuleDto($item['value'], $item['rentals'], $item['string'], $item['contains'])
        );
    }
}
