<?php

namespace App\Query;

use App\Models\ProviderAccount;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class RentalQueryBuilder.
 *
 * @mixin ProviderAccount
 *
 * @extends Builder<Rental>
 */
class ProviderAccountQuery extends Builder
{
    public function onTeamId(int $teamId): self
    {
        return $this->where('team_id', '=', $teamId);
    }

    public function onProvider(int $providerId): self
    {
        return $this->where('provider_id', '=', $providerId);
    }

    public function whereAccountId(string $accountId): self
    {
        return $this->where('account_id', '=', $accountId);
    }
}
