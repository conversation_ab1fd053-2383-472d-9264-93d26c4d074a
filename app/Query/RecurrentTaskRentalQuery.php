<?php

namespace App\Query;

use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;

class RecurrentTaskRentalQuery extends Builder
{
    public function onTeam(Team|int $team): self
    {
        if ($team instanceof Team) {
            $team = $team->id;
        }

        return $this->where('team_id', '=', $team);
    }

    public function whereRentalIdNotIn(array $rentals): self
    {
        return $this->whereNotIn('rental_id', $rentals);
    }

    public function whereRentalIdIn(array $rentals): self
    {
        return $this->whereIn('rental_id', $rentals);
    }
}
