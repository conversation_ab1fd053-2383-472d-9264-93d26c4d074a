<?php

namespace App\Query;

use App\Models\Booking;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class PreCheckInFormQuery.
 *
 * @mixin \App\Models\PreCheckInForm
 *
 * @extends Builder<PreCheckInForm>
 */
class PreCheckInFormQuery extends Builder
{
    public function onTeam(Team|int $team): self
    {
        if (is_int($team)) {
            return $this->where('team_id', '=', $team);
        }

        return $this->where('team_id', '=', $team->id);
    }

    public function fromBooking(int|Booking $booking): self
    {
        if (is_int($booking)) {
            return $this->where('booking_id', $booking);
        }

        return $this->where('booking_id', $booking->id);
    }

    public function whereId(int|array $formIds): self
    {
        if (is_int($formIds)) {
            return $this->where('id', '=', $formIds);
        }

        return $this->whereIn('id', $formIds);
    }

    public function whereCompleted(): self
    {
        return $this->where('completed', true);
    }
}
