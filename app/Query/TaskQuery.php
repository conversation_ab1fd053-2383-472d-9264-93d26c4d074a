<?php

namespace App\Query;

use App\Models\Booking;
use App\Models\RecurrentTask;
use App\Models\Rental;
use App\Models\ScheduledTask;
use App\Models\Task;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * Class TaskQueryBuilder.
 *
 * @extends Builder<Task>
 *
 * @mixin Task
 */
class TaskQuery extends Builder
{
    public function assignedToUser(User|int $user)
    {
        return $this->where('assignee_id', '=', $this->modelId($user));
    }

    private function modelId(Model|int $model): int
    {
        return $model instanceof Model ? $model->id : $model;
    }

    public function filterByUsersAndRoles(?array $users, ?array $roles): self
    {
        // It is an array, and it is not null.
        return $this->when(is_array($users) || is_array($roles), function (self $query) use ($users, $roles) {
            return $query->when(
                value: empty($users),
                callback: fn (self $query) => $query
                    ->whereNull('assignee_id')
                    ->whereNotNull('role'),
                default: fn (self $query) => $query->assignedToUsersOrRoles($users, $roles)
            );
        });
    }

    public function assignedToUsersOrRoles(?array $users, ?array $roles): self
    {
        if (! empty($users) && ! empty($roles)) {
            return $this
                ->where(fn (TaskQuery $query) => $query
                    ->whereIn('assignee_id', $users)
                    ->orWhere(fn ($query) => $query
                        ->whereIn('role', $roles)
                        ->whereNull('assignee_id')
                    )
                );
        }

        return $this;
    }

    public function onTeam(Team|int $team): self
    {
        return $this->where('team_id', '=', $this->modelId($team));
    }

    public function whereRental(int|Rental $rental): self
    {
        return $this->where('rental_id', '=', $this->modelId($rental));
    }

    public function whereBooking(int|Booking $booking): self
    {
        return $this->where('booking_id', '=', $this->modelId($booking));
    }

    public function whereScheduledTask(int|ScheduledTask $task): self
    {
        return $this->where('scheduled_task_id', '=', $this->modelId($task));
    }

    public function whereRecurrentTask(int|RecurrentTask $task): self
    {
        return $this->where('recurrent_task_id', '=', $this->modelId($task));
    }

    public function orWhereAdminTask(): self
    {
        return $this->orWhereNull('rental_id');
    }

    public function whereAdminTask(): self
    {
        return $this->whereNull('rental_id');
    }

    public function supervisedBy(User|int $user): self
    {
        return $this->where('supervisor_id', '=', $this->modelId($user));
    }

    public function assignedTo(User|int $user): self
    {
        return $this->where('assignee_id', '=', $this->modelId($user));
    }

    public function whereIncomplete(): self
    {
        return $this->whereNull('completed_at');
    }

    public function whereCompleted()
    {
        return $this->whereNotNull('completed_at');
    }

    public function onRentals(int|array $rentals): self
    {
        if (is_int($rentals)) {
            return $this->where('rental_id', '=', $rentals);
        }

        return $this->whereIn('rental_id', $rentals);
    }

    public function onRentalsOrAdmin(int|array $rentals, bool $includeAdmin): self
    {
        return $this->where(
            fn (self $q) => $q->onRentals($rentals)->when($includeAdmin,
                fn (self $q) => $q->orWhereAdminTask()
            ));
    }

    public function whereSupervised(): self
    {
        return $this->whereNotNull('supervised_at');
    }

    public function whereNotSupervised(): self
    {
        return $this->whereNull('supervised_at');
    }

    public function whereBookingIsNull(): self
    {
        return $this->whereNull('booking_id');
    }

    public function whereBookingIsNotNull(): self
    {
        return $this->whereNotNull('booking_id');
    }

    public function whereBookingDoesNotExistOnRental(): self
    {
        return $this->whereDoesntHave('booking', fn (BookingQuery $builder) => $builder
            ->whereColumn('tasks.rental_id', 'booking.rental_id')
        );
    }

    public function whereScheduledTaskIsNotNull(): self
    {
        return $this->whereNotNull('scheduled_task_id');
    }

    public function filterByScheduledTasksOrJobTitle(?string $job_title = null, ?array $scheduled_ids = []): self
    {
        if (empty($job_title)) {
            $job_title = null;
        }
        if (empty($scheduled_ids)) {
            $scheduled_ids = null;
        }

        return $this
            ->when(
                value: $job_title && $scheduled_ids,
                callback: fn (TaskQuery $query) => $query->filterByScheduledTasksAndJobTitle($job_title, $scheduled_ids)
            )
            ->when(
                value: $job_title && ! $scheduled_ids,
                callback: fn (TaskQuery $query) => $query->filterByJobTitle($job_title)
            )
            ->when(
                value: $scheduled_ids && ! $job_title,
                callback: fn (TaskQuery $query) => $query->filterByScheduledTasks($scheduled_ids)
            );
    }

    public function filterByScheduledTasksAndJobTitle(?string $job_title = null, ?array $scheduled_ids = []): self
    {
        if (! is_null($job_title) && ! empty($scheduled_ids)) {
            return $this->where(function (TaskQuery $query) use ($job_title, $scheduled_ids) {
                return $query
                    ->whereIn('scheduled_task_id', $scheduled_ids)
                    ->orWhere('title', 'LIKE', '%'.$job_title.'%');
            });
        }

        return $this;
    }

    public function filterByJobTitle(?string $job_title = null): TaskQuery
    {
        if (! is_null($job_title)) {
            return $this->where('title', 'LIKE', '%'.$job_title.'%');
        }

        return $this;
    }

    public function filterByScheduledTasks(?array $scheduled_ids = []): self
    {
        if (! empty($scheduled_ids)) {
            return $this->whereIn('scheduled_task_id', $scheduled_ids);
        }

        return $this;
    }

    public function startingAfter(?Carbon $date)
    {
        if (! is_null($date)) {
            return $this->whereDate('start_from', '>', $date);
        }

        return $this;
    }

    public function startingOnOrAfter(?Carbon $date)
    {
        if (! is_null($date)) {
            return $this->whereDate('start_from', '>=', $date);
        }

        return $this;
    }

    public function startingOnOrBefore(?Carbon $date)
    {
        if (! is_null($date)) {
            return $this->whereDate('start_from', '<=', $date);
        }

        return $this;
    }

    public function finishingBefore(?Carbon $date)
    {
        if (! is_null($date)) {
            return $this->whereDate('finish_before', '<', $date);
        }

        return $this;
    }

    public function finishingOnOrBefore(?Carbon $date)
    {
        if (! is_null($date)) {
            return $this->whereDate('finish_before', '<=', $date);
        }

        return $this;
    }

    public function finishingOnOrAfter(?Carbon $date)
    {
        if (! is_null($date)) {
            return $this->whereDate('finish_before', '>=', $date);
        }

        return $this;
    }

    /**
     * This is a homemade soft delete (substituting the ->delete() method of laravel) to be able to specify the reason why it was deleted in the same query.
     * This also deletes models in a very efficient way, with only one query.
     * WARNING: this won't soft delete any child models, but right now, none can be soft deleted.
     */
    public function softDeleteWithDeletedBy(string $deleted_by): int
    {
        return $this->update([
            'deleted_at' => now(),
            'deleted_by' => $deleted_by,
        ]);
    }
}
