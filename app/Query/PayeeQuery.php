<?php

namespace App\Query;

use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;

class PayeeQuery extends Builder
{
    public function onTeam(Team|int $team): self
    {
        if (is_int($team)) {
            return $this->where('team_id', $team);
        }

        return $this->where('team_id', '=', $team->id);
    }

    public function whereId(int|array $payeeIds): self
    {
        if (is_int($payeeIds)) {
            return $this->where('id', '=', $payeeIds);
        }

        return $this->whereIn('id', $payeeIds);
    }

    public function whereEmailValid(): self
    {
        return $this->where('email', '<>', '')
            ->whereNotNull('email');
    }

    /**
     * This is only applicable for json columns, where you wanna check the column is not null and doesn't contain an empty array.
     */
    public function whereNotNullOrEmpty(string $column): self
    {
        return $this->whereNotNull($column)
            ->whereJsonLength($column, '>', 0);
    }
}
