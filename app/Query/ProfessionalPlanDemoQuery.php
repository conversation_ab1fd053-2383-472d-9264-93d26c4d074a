<?php

namespace App\Query;

use App\Models\ProfessionalPlanDemo;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;

/**
 * @extends Builder<ProfessionalPlanDemo>
 *
 * @mixin ProfessionalPlanDemo
 */
class ProfessionalPlanDemoQuery extends Builder
{
    public function onTeam(Team|int $team): self
    {
        if (is_int($team)) {
            return $this->where('team_id', $team);
        }

        return $this->where('team_id', '=', $team->id);
    }

    public function whereValidDemoTrial(): self
    {
        return $this->whereRaw('trial_ends_at > CURRENT_TIMESTAMP()');
    }
}
