<?php

namespace App\Query;

use App\DataProviders\Providers\NinjaProvider;
use App\DataProviders\Providers\NoProvider;
use App\Domains\Automations\Enums\BookingPaidStatusOptions;
use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Enum\BookingStatusEnum;
use App\Models\Booking;
use App\Models\Message;
use App\Models\Rental;
use App\Models\Team;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

/**
 * Class BookingQueryBuilder.
 *
 * @mixin Booking
 *
 * @extends Builder<Booking>
 */
class BookingQuery extends Builder
{
    public function findExternalReference(int|Team $team, int|NinjaProvider $provider, int|string $externalId): ?Booking
    {
        if ($provider instanceof NinjaProvider) {
            $provider = $provider->id();
        }

        return $this->onTeam($team)
            ->whereProviderId($provider)
            ->whereExternalId($externalId)
            ->first();
    }

    public function whereId(int|array|Collection $bookingIds): self
    {
        if (is_int($bookingIds)) {
            return $this->where('id', '=', $bookingIds);
        }

        return $this->whereIn('id', $bookingIds);
    }

    /**
     * Use this method to load multiple bookings at once. For instance, it may be useful
     * in situations where you need to pass a collection of bookings to a Job, as serialization
     * will not work because Laravel only retreives models by id and not team_id.
     *
     * @param  Collection<array>  $bookingsData
     *                                           We expect $bookingsData to be a Collection containing bookings in the form of [id, team_id]
     */
    public function loadCollection(Collection $bookingsData)
    {
        return $this->where(function (BookingQuery $query) use ($bookingsData) {
            foreach ($bookingsData as $data) {
                $query->orWhere(function (BookingQuery $query) use ($data) {
                    $query->where('id', $data['id'])
                        ->where('team_id', $data['team_id']);
                });
            }
        });
    }

    public function whereExternalId(int|string|array|Collection $bookingIds): self
    {
        if (is_int($bookingIds) || is_string($bookingIds)) {
            return $this->where('external_id', '=', $bookingIds);
        }

        return $this->whereIn('external_id', $bookingIds);
    }

    public function whereExternalIdStartsWith(int|string|array|Collection $bookingIds): self
    {
        if (is_int($bookingIds) || is_string($bookingIds)) {
            return $this->where('external_id', 'like', "$bookingIds%");
        }
        $regex = collect($bookingIds)->map(fn (string $eid) => "^$eid")->implode('|');

        return $this->whereRaw("external_id REGEXP '$regex'");
    }

    public function onRentals(int|array|Rental $rentalInput): self
    {
        if ($rentalInput instanceof Rental) {
            return $this->where('rental_id', '=', $rentalInput->id);
        }
        if (is_int($rentalInput)) {
            return $this->where('rental_id', '=', $rentalInput);
        }

        return $this->whereIn('rental_id', $rentalInput);
    }

    public function whereRentalNotDeleted(): self
    {
        return $this->whereHas('rental', fn (Builder $query) => $query->whereNull('deleted_at'));
    }

    public function whereStatusBooked(): self
    {
        return $this->where('status', '=', BookingStatusEnum::BOOKED);
    }

    public function whereCurrency(string $currency): self
    {
        return $this->where('currency', $currency);
    }

    /** WARNING! Most likelly this method is not used because we now use an scope for the booking model which prevents quering for canceled bookings */
    public function whereNotCancelled(): self
    {
        return $this->where(fn (BookingQuery $query) => $query
            ->where('canceled_at', '=', 0)
            ->where('status', '<>', BookingStatusEnum::CANCELED)
        );
    }

    public function whereCancelled(): self
    {
        return $this->withoutGlobalScopes()
            ->where(fn (BookingQuery $query) => $query
                ->where('canceled_at', '<>', 0)
                ->where('status', '=', BookingStatusEnum::CANCELED)
            );
    }

    public function whereNotUnavailable(): self
    {
        return $this->where(fn (BookingQuery $query) => $query
            ->where('status', '<>', BookingStatusEnum::UNAVAILABLE)
        );
    }

    public function whereTentative(): self
    {
        return $this->where('status', BookingStatusEnum::TENTATIVE);
    }

    public function whereNotTentative(): self
    {
        return $this->where('status', '<>', BookingStatusEnum::TENTATIVE);
    }

    public function whereLead(): self
    {
        return $this->withoutGlobalScopes()
            ->where('status', BookingStatusEnum::LEAD);
    }

    public function whereNotLead(): self
    {
        return $this->where('status', '<>', BookingStatusEnum::LEAD);
    }

    public function withCancellations(): self
    {
        $status = BookingStatusEnum::BLOCKING_STATES;
        $status[] = BookingStatusEnum::CANCELED;

        return $this->withoutGlobalScopes()
            ->whereIn('status', $status);
    }

    public function whereBookingRequest(): self
    {
        return $this->withoutGlobalScopes()
            ->where('status', BookingStatusEnum::REQUEST);
    }

    public function whereBookingLead(): self
    {
        return $this->withoutGlobalScopes()
            ->where('status', BookingStatusEnum::LEAD);
    }

    public function whereBookingTentative(): self
    {
        return $this
            ->where('status', BookingStatusEnum::TENTATIVE);
    }

    public function whereBookingRejected(): self
    {
        return $this->withoutGlobalScopes()
            ->where('status', BookingStatusEnum::REJECTED);
    }

    public function whereBookingArchived(): self
    {
        return $this->withoutGlobalScopes()
            ->where('status', BookingStatusEnum::ARCHIVED);
    }

    public function whereWebsiteAttempts(): self
    {
        return $this->withoutGlobalScopes()
            ->where('status', BookingStatusEnum::WEBSITE);
    }

    public function whereNotBookingRequest(): self
    {
        return $this->where('status', '<>', BookingStatusEnum::REQUEST);
    }

    public function onlyBookedOrUnavailableStatus(): self
    {
        return $this->whereIn('status', [BookingStatusEnum::BOOKED, BookingStatusEnum::UNAVAILABLE]);
    }

    public function whereRejected(): self
    {
        return $this->withoutGlobalScopes()
                    ->where('status', BookingStatusEnum::REJECTED);
    }

    /** Note: this shouldn't be used normally as query scope removes rejected bookings by default*/
    public function whereNotRejected(): self
    {
        return $this->where('status', '<>', BookingStatusEnum::REJECTED);
    }

    public function withPreviousAndNextBookings(): self
    {
        return $this
            ->withPreviousBooking()
            ->withNextBooking();
    }

    public function withNextBooking(): self
    {
        return $this
            ->addSelect(['next_booking_id' => Booking::query()
                ->from('booking AS next')
                ->select('next.id')
                ->whereColumn('next.team_id', 'booking.team_id')
                ->whereColumn('next.rental_id', 'booking.rental_id')
                ->whereRaw('DATE(FROM_UNIXTIME(`next`.start_at)) >= DATE(FROM_UNIXTIME(booking.end_at))')
                ->orderBy('next.start_at')
                ->take(1), ])
            ->with(['nextBooking' => fn ($booking) => $booking
                ->withRental(),
            ]);
    }

    public function withPreviousBooking(): self
    {
        return $this->addSelect(['previous_booking_id' => Booking::query()
            ->from('booking as previous')
            ->select('previous.id')
            ->whereColumn('previous.team_id', 'booking.team_id')
            ->whereColumn('previous.rental_id', 'booking.rental_id')
            ->whereRaw('DATE(FROM_UNIXTIME(`previous`.end_at)) <= DATE(FROM_UNIXTIME(booking.start_at))')
            ->orderBy('previous.end_at', 'desc')
            ->take(1), ])
            ->with(['previousBooking' => fn ($booking) => $booking
                ->withRental(),
            ]);
    }

    public function wherePreCheckInStatus(bool $status): self
    {
        return $this->whereRelation('preCheckInForm', 'completed', '=', $status);
    }

    public function whereSourceId(int|array $source): self
    {
        if (is_int($source)) {
            return $this->where('source_id', '=', $source);
        }

        return $this->whereIn('source_id', $source);
    }

    public function withTeamCheckIntTime(): self
    {
        Booking::toBase();

        return $this->addSelect([
            'team_check_in_time' => Team::query()
                ->select('provider_id')
                ->whereColumn('id', '=', 'booking.team_id')
                ->take(1),
        ]);
    }

    public function withRental(): self
    {
        return $this->with('rental');
    }

    public function withSource(): self
    {
        return $this->with('source');
    }

    public function withTasks(): self
    {
        return $this->with('tasks.items');
    }

    public function withTags(): self
    {
        return $this->with(['tagsPivot', 'tagsPivot.tag']);
    }

    public function whereTagsIn(?array $tags, bool $in = true): self
    {
        if (empty($tags)) {
            return $this;
        }
        $whereIn = $in ? 'whereIn' : 'whereNotIn';

        return $this->whereHas('tagsPivot', fn ($query) => $query->$whereIn('booking_tag_id', $tags));
    }

    public function wherePaymentThreshold(BookingPaidStatusOptions $threshold, bool $in = true): self
    {
        $op = $in ? '>=' : '<';

        return $this
            ->when($threshold == BookingPaidStatusOptions::FULLY_PAID,
                fn (Builder $query) => $query->whereRaw("booking.paid_amount $op booking.final_price + booking.damage_deposit - 0.1")
            )->when($threshold == BookingPaidStatusOptions::BOOKING_PAID,
                fn (Builder $query) => $query->whereRaw("booking.paid_amount $op booking.final_price - 0.1")
            )->when($threshold == BookingPaidStatusOptions::RENTAL_PRICE_PAID,
                fn (Builder $query) => $query->whereRaw("booking.paid_amount $op booking.rental_price - 0.1")
            )->when($threshold == BookingPaidStatusOptions::DOWNPAYMENT_PAID,
                fn (Builder $query) => $query->whereRaw("booking.paid_amount $op booking.downpayment - 0.1")
            );
    }

    public function withClient(): self
    {
        return $this->with('client');
    }

    public function withBookingTaxes(): self
    {
        return $this->with('bookingTaxes');
    }

    public function withBookingFees(bool $taxablesAlso = false): self
    {
        if ($taxablesAlso) {
            return $this->with('bookingFees.tax');
        }

        return $this->with('bookingFees');
    }

    public function withComments(): self
    {
        return $this->with('comments');
    }

    public function withBookingPayments(bool $withPayments = false, bool $withPictures = false, bool $withPicturesAuthor = false): self
    {
        if (! $withPayments) {
            return $this->with('bookingPayments');
        } else {
            if ($withPictures) {
                if ($withPicturesAuthor) {
                    return $this->with('bookingPayments.pictures.author');
                } else {
                    return $this->with('bookingPayments.pictures');
                }
            } else {
                return $this->with('bookingPayments');
            }
        }
    }

    public function filledOn(Carbon $date): self
    {
        return $this->filledBetween(
            start: $date->copy()->startOfDay(),
            end: $date->copy()->endOfDay()
        );
    }

    // To query for any period
    public function filledBetween(Carbon $start, Carbon $end): self
    {
        return $this
            ->where('end_at', '>=', $start->getTimestamp())
            ->where('start_at', '<=', $end->getTimestamp());
    }

    // When you just want to query for a single day
    public function checkInOn(Carbon $date): self
    {
        return $this->checkInWithin(
            start: $date->copy()->startOfDay(),
            end: $date->copy()->endOfDay()
        );
    }

    public function checkInWithin(Carbon $start, Carbon $end): self
    {
        return $this
            ->where('booking.start_at', '>=', $start->startOfDay()->getTimestamp())
            ->where('booking.start_at', '<=', $end->endOfDay()->getTimestamp());
    }

    /** This method searches for checkins today taking into account the rental's timezone.
     * WARNING: this method makes a join with the rentals table, thus, you better remove any undesired column added to the join in your query calling
     * this method.
     */
    public function whereCheckinToday()
    {
        return $this->addSelect(['rental_timezone' => Rental::query()
                ->selectRaw("COALESCE(timezone, 'UTC')") // In case timezone is null
                ->whereColumn('id', 'booking.rental_id')
                ->whereColumn('team_id', 'booking.team_id')
                ->take(1),
        ])
            // For the sake of efficiency, let's profit from start_at index and easily get a subset of bookings around today and then fine tune with a raw query:
            ->whereBetween('start_at', [now()->subDay()->startOfDay()->timestamp, now()->addDay()->endOfDay()->timestamp]) // subset
            ->havingRaw("date(CONVERT_TZ(NOW(), 'UTC', rental_timezone)) = date(FROM_UNIXTIME(start_at))");
    }

    public function checkOutOn(Carbon $date): self
    {
        return $this->checkOutWithin(
            start: $date->copy()->startOfDay(),
            end: $date->copy()->endOfDay()
        );
    }

    public function checkOutWithin(Carbon $start, Carbon $end): self
    {
        return $this
            ->where('booking.end_at', '>=', $start->startOfDay()->getTimestamp())
            ->where('booking.end_at', '<=', $end->endOfDay()->getTimestamp());
    }

    public function future(?Carbon $from = null): self
    {
        $from ??= Carbon::today();

        return $this->where('booking.end_at', '>=', $from->getTimestamp());
    }

    public function futureCheckin(): self
    {
        return $this->where('booking.start_at', '>=', Carbon::today()->getTimestamp());
    }

    public function passed(?Carbon $from = null): self
    {
        $from ??= Carbon::today();

        return $this->where('booking.end_at', '<=', $from->getTimestamp());
    }

    public function recent(?Carbon $asDate = null): self
    {
        $asDate ??= Carbon::today();

        return $this->where('booking.created_at', '<=', $asDate->getTimestamp());
    }

    public function onActiveRentalsOnly(): self
    {
        return $this->whereRelation('rental', 'deleted_at', '=', null);
    }

    public function onActiveRentalsOfTeam(int $teamId): self
    {
        $activeRentals = Rental::query()
            ->onTeam($teamId)
            ->pluck('id')
            ->toArray();

        return $this->whereIn('rental_id', $activeRentals);
    }

    public function onTeam(Team|int $team): self
    {
        if ($team instanceof Team) {
            return $this->where('team_id', '=', $team->id);
        }

        return $this->where('team_id', '=', $team);
    }

    public function onProvider(int $providerId): self
    {
        return $this->where('provider_id', '=', $providerId);
    }

    public function whereNotProvider(): self
    {
        return $this->where(fn (BookingQuery $q) => $q
            ->where('provider_id', '=', NoProvider::ID)
            ->orWhereNull('provider_id')
        );
    }

    public function whereHasProvider(): self
    {
        return $this->where('provider_id', '>', NoProvider::ID);
    }

    /** Caution using this. It may return multiple bookings as the channel ID may be repeated. Filter them afterwards */
    public function whereReferenceOrChannelId(string $reference): self
    {
        return $this->where(fn (BookingQuery $query) => $query
                ->where('channel_id', $reference)
                ->orWhere('reference', $reference)
        );
    }

    public function whereReference(string $reference): self
    {
        return $this->where('reference', $reference);
    }

    public function withBookingUser(int $userId): self
    {
        return $this->with(['bookingUsers' => fn ($query) => $query->where('user_id', $userId)]);
    }

    public function withLastMessage(): self
    {
        return $this->addSelect(['last_message_id' => Message::query()->select('id')
         ->whereColumn('team_id', 'booking.team_id')
         ->whereColumn('booking_id', 'booking.id')
         ->where('guest_message', true)
         ->latest('sent_at')
         ->take(1),
        ])->with('lastMessage.booking.client'); // Although redundant, we need this to be able to access Client in ConversationMessageResource
    }

    /** This method is used to perform booking queries willing to add the last guest's message date */
    public static function getLastGuestMessageDateSubQuery(): Builder
    {
        return Message::query()->select('sent_at')
                     ->whereColumn('team_id', 'booking.team_id')
                     ->whereColumn('booking_id', 'booking.id')
                     ->where('guest_message', true)
                     ->latest('sent_at')
                     ->take(1);
    }

    public function whereHasRegistrationAuthority(GuestsRegistrationAuthorityEnum $authority): self
    {
        return $this->whereHas('rental.guestRegistrationAuthorities', function (Builder $q) use ($authority) {
            $q->where('authority', $authority)
                ->where(function (Builder $q) {
                    $q->whereJsonContains('sources', DB::raw('CAST(booking.source_id AS JSON)'))
                        ->orWhereNull('sources')
                        ->orWhereJsonLength('sources', 0);
                });
        });
    }
}
