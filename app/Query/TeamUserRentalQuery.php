<?php

namespace App\Query;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

/**
 * Class RentalQueryBuilder.
 *
 * @mixin \App\Models\TeamUserRental
 *
 * @extends Builder<Rental>
 */
class TeamUserRentalQuery extends Builder
{
    // WARNING: do not use this method alone. You should always identify rows by user_id as may be different rentals with the same id
    public function onRental(int|array|Collection $rentalIds): self
    {
        if (is_int($rentalIds)) {
            return $this->where('rental_id', $rentalIds);
        }

        return $this->whereIn('rental_id', $rentalIds);
    }
}
