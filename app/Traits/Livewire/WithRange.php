<?php

namespace App\Traits\Livewire;

trait WithRange
{
    /** @return void|string[]  */
    public function getRange(string $range)
    {
        $pattern = '/(\d{4}-\d{2}-\d{2})/';

        preg_match_all($pattern, $range, $matches);

        if (count($matches[0]) !== 2) {
            return null;
        }

        $startDateTime = $matches[0][0];
        $endDateTime = $matches[0][1];

        return [
            'start' => $startDateTime,
            'end' => $endDateTime,
        ];
    }
}
