<?php

namespace App\Traits\PaymentProcessor;

use App\DTO\Team\PlanData;
use App\DTO\Team\PriceData;
use App\DTO\Team\ProductData;
use App\DTO\Team\SubscriptionData;
use App\Enum\PricingModelEnum;
use App\Enum\SubscriptionBillingCycleEnum;
use App\Enum\TeamStatusEnum;
use App\Exceptions\NinjaAddContextException;
use App\Models\ProfessionalPlanDemo;
use App\Models\Rental;
use App\Models\RentalNinjaTeam;
use App\Models\Team;
use App\Models\TeamSubscription;
use App\Models\TeamSubscriptionItem;
use App\Notifications\Internal\RentalNinjaTeamMailNotification;
use Exception;
use Lara<PERSON>\Cashier\Billable;
use Lara<PERSON>\Cashier\SubscriptionBuilder;
use Spatie\LaravelData\DataCollection;

/**
 * This trait is used to manage subscription topics in RN. Use it to add any method you design. Additionally,
 * it extends NinjaBillable, please place here any method you with to override from there.
 *
 * @mixin Team
 */
trait NinjaBillableManager
{
    const CASHIER_DEFAULT_SUBS_TYPE = 'default'; // This is the type used by cashier used to identify which is the main type of subscription.

    use Billable {
        Billable::subscribed as subscribedToStripe;
        Billable::onGenericTrial as originalOnGenericTrial;
        Billable::newSubscription as originalNewSubscription;
    }

    /** NINJA METHODS: to check team's status about subscriptions */
    /** This method basically checks if the team has access of any sort */
    public function isSubscribed(): bool
    {
        try {
            return $this->subscribed() || $this->onGenericTrial();
        } catch (Exception $exception) {
            report($exception);

            return false;
        }
    }

    public function canUseGuestsApp(): bool
    {
        return $this->isProfessional();
    }

    public function canUseAccounting(): bool
    {
        return $this->isProfessional();
    }

    public function shouldShowSubscriptionForm(): bool
    {
        $config = $this->config();

        return $config->billedThroughRentalNinja() || (! $this->isSubscribed() && $config->canSwitchToDefaultFlavor());
    }

    public function isProfessional(): bool
    {
        try {
            if ($this->onGenericTrial()) {
                return true;
            } elseif (! $this->config()->billedThroughRentalNinja() && $this->subscribed()) {
                return true;
            } elseif ($demo = ProfessionalPlanDemo::query()->onTeam($this->id)->whereValidDemoTrial()->first()) {
                if ($demo->trial_ends_at && now()->lt($demo->trial_ends_at)) {
                    return true;
                }
            }

            return $this->hasProfessionalSubscription();
        } catch (Exception $exception) {
            report($exception);

            return false;
        }
    }

    public function hasProfessionalSubscription(): bool
    {
        return $this->current_rn_plan?->isProfessional() ?? false;
    }

    public function hasStarterSubscription(): bool
    {
        return $this->current_rn_plan?->isStarter() ?? false;
    }

    public function isYearlySubscription(): bool
    {
        return $this->subscribedToStripe() && $this->subscription()->billing_cycle === SubscriptionBillingCycleEnum::YEAR;
    }

    /** This method uses information from the subscription tables to get the plan, no stripe connection.
     * It returns a full PlanData object. For more performant operations, getRnPlanFromTeamsTable().
     */
    public function getRnPlanFromSubscription(): ?PlanData
    {
        if (! $this->subscribedToStripe()) { // Checks if the subscription is active (takes into account incomplete and Past Due Cashier settings)
            return null;
        }

        $subscription = $this->subscription();
        $stripeProductIds = $subscription->items->pluck('stripe_product');

        try {
            return PlanData::fromProducts(ProductData::fromStripe($stripeProductIds));
        } catch (Exception $e) {
            report(new NinjaAddContextException(
                previous: $e,
                team: $this,
                context: ['stripeProductIds' => $stripeProductIds],
            ));
            throw $e;
        }
    }

    /** This is an easy access to the Team's Plan maintained through the code.
     * This table column is also used to be able to, upon stripe webhook processed, be able to know if the team was previously on another plan
     * (laravel cashier will not change this field).
     */
    public function getRnPlanFromTeamsTable(): ?PlanData
    {
        return $this->current_rn_plan ? PlanData::findPlan($this->current_rn_plan) : null;
    }

    /** The above two methods return the generic plan of the team, without knowing if it is yearly or monthly, if has connected rentals or not.
     * This method returns such information, which is about the specific subscription of the team: rentals, yearly or not,...
     */
    public function getSubscriptionData(): ?SubscriptionData
    {
        return $this->subscribedToStripe() ? SubscriptionData::get($this) : null;
    }

    /** CASHIER EXTENDING METHODS: methods which extend the default of cashier or new methods in case there is no such functionality in cashier */
    public function subscribed($name = 'default', $price = null): bool
    {
        if (! $this->mainProvider()->enabled()) {
            return false;
        }

        if (! $this->config()->billedThroughRentalNinja()) {
            return $this->status !== TeamStatusEnum::disabled; // In case we have trialing status teams
        }

        return $this->subscribedToStripe($name, $price);
    }

    public function onGenericTrial(): bool
    {
        if (! $this->mainProvider()->enabled()) {
            return false;
        }

        if ($this->status === TeamStatusEnum::trialing) {
            return true;
        }

        return $this->originalOnGenericTrial();
    }

    public function isPartnerTestAccount(): bool
    {
        return in_array($this->id, config('ninja.testing_team_ids'));
    }

    /**
     * @param  DataCollection<PriceData>  $prices
     */
    public function newSubscription(string $name, DataCollection $prices, int $rentals): SubscriptionBuilder
    {
        $subscription = $this->originalNewSubscription($name, $prices->toCollection()->pluck(isProduction() ? 'production' : 'test')->toArray());
        // Error always on new subscription.
        $subscription->errorIfPaymentFails();

        // If only one price is yearly, it must all be a yearly subscription thus we should not prorate
        $prices->first()->yearly ? $subscription->alwaysInvoice() : $subscription->prorate();

        $prices->each(function (PriceData $price) use (&$subscription, $rentals) {
            $stripePrice = $price->getStripePrice();
            if ($price->metered) {
                $subscription->meteredPrice($stripePrice);
            } else {
                $subscription->quantity($rentals, $stripePrice);
            }
        });

        return $subscription;
    }

    /*
    // In case you wish to add a second subscription to our customers, re-use this code (otherwise delete)
    public function newBillableAddonsSubscription(string $priceId): void
    {
        $defaultSubscription = $this->subscription()->asStripeSubscription();
        $subName = config('ninja-stripe.billable_addons.subscription_name');
        $this->originalNewSubscription($subName)
             ->meteredPrice($priceId)
             ->anchorBillingCycleOn($defaultSubscription->current_period_end)
            ->withMetadata([ // For consistency with that cashier does with standard subscriptions
                'name' => $subName,
                'type' => $subName,
            ])
             ->create(subscriptionOptions: [
                 'billing_thresholds' => [
                     'amount_gte' => 50000,
                     'reset_billing_cycle_anchor' => false, //This is in fact the default of stripe
                 ],
             ]);
    }*/

    /**
     * Updates the number of total seats of the team, thus prices who's count is related to the total rentals of the team.
     * WARNING! It assumes $team->rentals is already updated.
     *
     * @throws Exception This may throw an exception if the payment fails (if payment to be done)
     */
    public function updateTotalSeats(?PlanData $plan = null): void
    {
        if (! $this->relationLoaded('subscriptions')) {
            $this->load('subscriptions.items');
        }

        /** @var TeamSubscription $subscription */
        if (! $subscription = $this->subscription()) {
            return;
        }

        // If no plan is passed, get it
        if (! $plan) {
            $plan = $this->getRnPlanFromTeamsTable();
        }

        // Get a collection of products to update: the main product of the subscription, and any other product dependant on total rental count
        $products = ProductData::collection([
            $plan->getTotalCountProduct(), // Main product
            ProductData::findProduct('customer_white_label'),
        ]);

        if ($products->count() === 0) {
            report(new Exception("WARNING! We have a Subscribed team without Products! Team id: $this->id"));
        }

        foreach ($products as $product) {
            $subscriptionItem = $subscription->items
                ->where(fn (TeamSubscriptionItem $item) => $item->stripe_product === $product->production || $item->stripe_product === $product->test)
                ->first();

            if ($subscriptionItem) {
                $this->updateSeats($subscription, $subscriptionItem, $this->rentals);
            }
        }
    }

    /**
     * Updates the seats of the channel manager related prices only.
     *
     * @throws Exception This may throw an exception if the payment fails (if payment to be done)
     */
    public function updateChannelManagerSeats(?PlanData $plan = null): void
    {
        if (! $this->relationLoaded('subscriptions')) {
            $this->load('subscriptions.items');
        }

        if (! $subscription = $this->subscription()) {
            return;
        }

        // If no plan is passed, get it
        if (! $plan) {
            $plan = $this->getRnPlanFromTeamsTable();
        }

        if (! $plan) {
            return;
        }

        $billableProduct = $plan->getChannelManagerCountProduct();
        // For the plans without having to look at the connected rentals...
        if ($billableProduct === null) {
            return;
        }

        $newCount = Rental::query()
            ->onTeam($this)
            ->withUncompleted()
            ->wherePublished()
            ->count();

        $this->syncProductSeats($subscription, $billableProduct, $newCount);
    }

    public function updatePricingSeats(): void
    {
        $this->load('subscriptions.items');

        if (! $subscription = $this->subscription()) {
            $this->reportIfNotDemoTeam("Team $this->id has no subscription and is using Price Automation");

            return;
        }

        $billableProduct = ProductData::findProduct('smart-pricing');

        $newCount = Rental::query()
            ->onTeam($this)
            ->withUncompleted()
            ->where('pricing_model', '=', PricingModelEnum::smartPricing)
            ->count();

        $this->syncProductSeats($subscription, $billableProduct, $newCount);
    }

    public function updateHomeAutomationDevicesSeats(): void
    {
        if (! $this->relationLoaded('subscriptions')) {
            $this->load('subscriptions.items');
        }

        if (! $subscription = $this->subscription()) {
            $this->reportIfNotDemoTeam("Team $this->id has no subscription and is using Home Automation");

            return;
        }

        $groupedCount = $this->billableHomeAutomationDevices()
            ->select('provider')
            ->selectRaw('count(*) as count')
            ->groupBy('provider')
            ->get();

        foreach ($groupedCount as $deviceModel) {
            $billableProduct = $deviceModel->provider->getBillableProduct();
            $this->syncProductSeats($subscription, $billableProduct, $deviceModel->count);
        }
    }

    private function syncProductSeats(TeamSubscription $subscription, ProductData $billableProduct, int $newCount): void
    {
        /** @var TeamSubscriptionItem $subscriptionItem */
        $subscriptionItem = $subscription->items
            ->firstWhere(fn (TeamSubscriptionItem $item) => $item->stripe_product === $billableProduct->production || $item->stripe_product === $billableProduct->test);

        if ($subscriptionItem) {
            if ($newCount === 0) {
                $subscription->noProrate()->removePrice($subscriptionItem->stripe_price);
            } else {
                $this->updateSeats($subscription, $subscriptionItem, $newCount);
            }
        } elseif (! $subscriptionItem && $newCount > 0) { // Create new price
            $isYearlySubscription = $this->isYearlySubscription();
            $priceId = $billableProduct->getDefaultPriceId($isYearlySubscription);
            $stripePrice = PriceData::find($priceId)->getStripePrice();

            if ($isYearlySubscription) {
                $subscription->addPriceAndInvoice($stripePrice, $newCount);
            } else {
                $subscription->prorate()->addPrice($stripePrice, $newCount);
            }
        }
    }

    // Private method to manage the seats of a subscription item
    private function updateSeats(TeamSubscription $subscription, TeamSubscriptionItem $item, int $newQuantity): void
    {
        // Increment
        if ($newQuantity > $item->quantity) {
            if ($this->isYearlySubscription()) {
                $subscription->incrementAndInvoice($newQuantity - $item->quantity, $item->stripe_price);
            } else {
                $subscription->prorate()->incrementQuantity($newQuantity - $item->quantity, $item->stripe_price);
            }
        } // Decrement
        elseif ($newQuantity < $item->quantity) {
            $subscription->noProrate()->decrementQuantity($item->quantity - $newQuantity, $item->stripe_price);
        }
        // Note: we do nothing if the quantity is the same
    }

    private function reportIfNotDemoTeam(string $message): void
    {
        // In case we have a team without subscription, warn us as we are not billing this team
        if (! in_array($this->id, config('ninja.testing_team_ids')) && isProduction()) {
            $rentalNinja = RentalNinjaTeam::getInstance();
            $rentalNinja->email = RentalNinjaTeam::TECH_EMAIL;
            $rentalNinja->notify(new RentalNinjaTeamMailNotification(
                'WARNING! Team using a paid feature without a RN subscription',
                $message,
            ));
        }
    }
}
