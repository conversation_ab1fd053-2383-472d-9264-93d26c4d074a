<?php

namespace App\Traits;

use App\Exports\MultiSheetExcelExporter;
use App\Models\Team;
use App\Support\SaveExcel;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Excel;

trait ExportsExcel
{
    /**
     * @throws \Exception
     */
    protected function export(Team $team, string $fileTitle, string $directory, MultiSheetExcelExporter $exporter): void
    {
        $name = Str::slug($fileTitle);
        $saver = new SaveExcel(SaveExcel::FULL_PATH);
        $saver->handle($team, $exporter, $name, Excel::XLS, $directory);
    }
}
