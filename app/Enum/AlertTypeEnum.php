<?php

namespace App\Enum;

use App\Models\TeamUsersPermission;
use Illuminate\Support\Collection;

enum AlertTypeEnum: int
{
    case DAMAGE_DEPOSIT = 2;
    case MISSING_PAYMENT = 3;
    case ALREADY_LEFT = 4;
    case MISSING_AUTHORISATIONS = 5;
    case DEVICE_LOW_BATTERY = 6;

    public function getName(?string $locale = null): string
    {
        return match ($this) {
            self::DAMAGE_DEPOSIT => __('messages.alert.damage_deposit', locale: $locale),
            self::MISSING_PAYMENT => __('messages.alert.missing_payment', locale: $locale),
            self::ALREADY_LEFT => __('messages.alert.already_left', locale: $locale),
            self::MISSING_AUTHORISATIONS => __('messages.alert.missing_authorisations', locale: $locale),
            self::DEVICE_LOW_BATTERY => __('messages.alert.device_low_battery', locale: $locale),
        };
    }

    public function getEmoji(): string
    {
        return match ($this) {
            self::DAMAGE_DEPOSIT => "\u{1F511}", // 🔑
            self::MISSING_PAYMENT => "\u{1F4B8}", // 💸
            self::ALREADY_LEFT => "\u{1F631}", // 😱
            self::MISSING_AUTHORISATIONS => "\u{1F512}", // 🔒
            self::DEVICE_LOW_BATTERY => "\u{1F50B}", // 🔋
        };
    }

    public function getDescription(?string $locale = null): string
    {
        return match ($this) {
            self::DAMAGE_DEPOSIT => __('messages.alert.damage_deposit.description', locale: $locale),
            self::MISSING_PAYMENT => __('messages.alert.missing_payment.description', locale: $locale),
            self::ALREADY_LEFT => __('messages.alert.already_left.description', locale: $locale),
            self::MISSING_AUTHORISATIONS => __('messages.alert.missing_authorisations.description', locale: $locale),
            self::DEVICE_LOW_BATTERY => __('messages.alert.device_low_battery.description', locale: $locale),
        };
    }

    public function getDetails(string $value, ?string $locale = null): string
    {
        return match ($this) {
            self::DAMAGE_DEPOSIT => __('messages.alert.damage_deposit.value', ['value' => $value], locale: $locale),
            self::MISSING_PAYMENT => __('messages.alert.missing_payment.value', ['value' => $value], locale: $locale),
            self::ALREADY_LEFT => __('messages.alert.already_left.value', ['value' => $value], locale: $locale),
            self::MISSING_AUTHORISATIONS => __('messages.alert.missing_authorisations.value', ['name' => $value], locale: $locale),
            self::DEVICE_LOW_BATTERY => __('messages.alert.device_low_battery.value', ['name' => $value], locale: $locale),
        };
    }

    public function getPermissionLevel(): string
    {
        return match ($this) {
            self::DAMAGE_DEPOSIT => 'alert_missing_damage_deposit',
            self::MISSING_PAYMENT => 'alert_missing_rental_payment',
            self::ALREADY_LEFT => 'alert_already_left',
            self::MISSING_AUTHORISATIONS, self::DEVICE_LOW_BATTERY => 'use_home_automation_devices',
        };
    }

    public static function getAlertTypesAsPerPermissions(TeamUsersPermission $permissions): Collection
    {
        return collect()
            ->when($permissions->alert_already_left > 0, fn ($c) => $c->push(self::ALREADY_LEFT))
            ->when($permissions->alert_missing_damage_deposit > 0, fn ($c) => $c->push(self::DAMAGE_DEPOSIT))
            ->when($permissions->alert_missing_rental_payment > 0, fn ($c) => $c->push(self::MISSING_PAYMENT))
            ->when($permissions->use_home_automation_devices > 0, fn ($c) => $c->push(self::MISSING_AUTHORISATIONS)->push(self::DEVICE_LOW_BATTERY));
    }
}
