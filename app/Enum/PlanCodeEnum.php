<?php

namespace App\Enum;

enum PlanCodeEnum: string
{
    case growth = 'growth-plan';
    case proConnected = 'pro-connected-plan';
    case pro = 'pro-plan';

    // OLD PLANS:
    case professionalYearly = 'professional-yearly';
    case professionalMonthly = 'professional-monthly';

    public function isChannelManagerPlan(): bool
    {
        return $this === self::growth || $this === self::proConnected;
    }

    public function planCanAddPublishedRentalsProduct(): bool
    {
        return $this === self::pro || $this === self::professionalYearly || $this === self::professionalMonthly;
    }

    public function publicCode(): string
    {
        return match ($this) {
            self::professionalYearly, self::professionalMonthly => 'professional',
            default => $this->value,
        };
    }

    public function isProfessional(): bool
    {
        return true; //Currently all available plans are professional.
    }

    public function isStarter(): bool
    {
        return false; //Currently all available plans are professional.
    }

    public static function professionalPlans(): array
    {
        return self::cases(); // returns all values of this enum as an array containing all enums
    }

    public static function starterPlans(): array
    {
        return [];
    }
}
