<?php

namespace App\Enum;

enum SubscriptionTypeEnum: int
{
    case professionalMonthly = 1;
    case professionalYearly = 2;
    case professionalMonthlyWithCm = 3;
    case professionalYearlyWithCm = 4;
    case proMonthly = 5;
    case proYearly = 6;
    case proMonthlyWithCm = 7;
    case proYearlyWithCm = 8;
    case proConnectedMonthly = 9;
    case proConnectedYearly = 10;
    case growth = 11;

    public function getSubscriptionString(): string
    {
        return match ($this) {
            self::professionalMonthly => 'Old Professional Monthly',
            self::professionalYearly => 'Old Professional Yearly',
            self::professionalMonthlyWithCm => 'Old Professional Monthly + CM',
            self::professionalYearlyWithCm => 'Old Professional Yearly + CM',
            self::proMonthly => 'Pro Monthly',
            self::proYearly => 'Pro Yearly',
            self::proMonthlyWithCm => 'Pro Monthly + CM',
            self::proYearlyWithCm => 'Pro Yearly + CM',
            self::proConnectedMonthly => 'Pro Connected Monthly',
            self::proConnectedYearly => 'Pro Connected Yearly',
            self::growth => 'Growth',
        };
    }
}
