<?php

namespace App\Listeners;

use App\Actions\Users\NotificationTokens\GetUserNotificationTokensForRentalAndRolesAction;
use App\Actions\Users\ShouldNotifyUserAction;
use App\Enum\TeamRolesEnum;
use App\Enum\UserSettingsEnum;
use App\Events\Booking\CheckInOutTimeModifiedEvent;
use App\Models\Booking;
use App\Models\Rental;
use App\Models\TeamSettings;
use App\Models\UserNotificationToken;
use App\Notifications\CheckInOutModifiedNotification;
use App\Notifications\NinjaFcmNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Carbon;

class CheckInOutTimeModifiedListener implements ShouldQueue
{
    public $delay = 60;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CheckInOutTimeModifiedEvent $event): bool
    {
        if ($event->bookingId == null || $event->team == null) {
            return false;
        }

        if (! $event->team->subscribed() && ! $event->team->onGenericTrial()) {
            return false;
        }

        $booking = Booking::getBookingModel($event->team, $event->bookingId);
        $rental = Rental::query()
            ->where('team_id', '=', $event->team->id)
            ->where('id', '=', $booking->rental_id)
            ->first();

        if ($rental == null) {
            return false;
        }

        $roles = TeamRolesEnum::agencyRoles();
        $tokens = GetUserNotificationTokensForRentalAndRolesAction::run($rental, $roles, [$event->issuer->id]);

        foreach ($tokens as $user_and_token) {
            $token = UserNotificationToken::whereToken($user_and_token['token'])
                ->whereUserId($user_and_token['user_id'])
                ->with('user')
                ->first();

            if (! ShouldNotifyUserAction::run($token->user, UserSettingsEnum::CHECK_IN_TIME_MODIFIED_NOTIFICATION)) {
                continue;
            }
            pnLog("[CheckInOutTimeModifiedListener] Sending notification to user {$token->user->id} for booking {$booking->id}", $event->team);

            $locale = substr($token->user->locale, 0, 2);
            Carbon::setLocale($locale);
            $from = Carbon::createFromTimestampUTC($booking->start_at)
                ->toDateString();
            $to = Carbon::createFromTimestampUTC($booking->end_at)
                ->toDateString();

            $at = '';

            if ($booking->expected_checkin_time != null && strlen($booking->expected_checkin_time) > 0) {
                $at .= 'Check In '.$booking->getCheckInTime().' ';
            }

            if ($booking->expected_checkout_time != null && strlen($booking->expected_checkout_time) > 0) {
                $at .= 'Check Out '.$booking->getCheckOutTime();
            }

            $title = __('messages.check_time_modified.title', ['rental' => $rental->name], $locale);
            $body = __('messages.check_time_modified.body', [
                'from' => $from,
                'to' => $to,
                'time' => $at,
            ], $locale);
            $data = [
                'booking' => $booking->id,
                'rental' => $booking->rental_id,
                'rental_name' => $rental->name,
            ];
            $token->notify(new NinjaFcmNotification($token, $title, $body, UserNotificationToken::TYPE_CHECK_IN_TIME_MODIFIED,
                $data));

            pnLog("[CheckInOutTimeModifiedListener] Sent notification to user {$token->user->id} for booking {$booking->id}", $event->team);
        }

        if ($event->team->canReceiveSlackNotifications(TeamSettings::CHECK_IN)) {
            $event->team->notify((new CheckInOutModifiedNotification($booking, $rental, $event->issuer->id))->locale($event->team->getTeamLocale()));
        }

        return true;
    }
}
