<?php

namespace App\Listeners;

use App\Events\TaskAssignedEvent;
use App\Jobs\TaskAssignedJob;

class TaskAssignedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TaskAssignedEvent $event): void
    {
        dispatch(new TaskAssignedJob($event->task, $event->requester))
            ->delay(2);
    }
}
