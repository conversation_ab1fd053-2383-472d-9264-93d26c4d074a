<?php

namespace App\Listeners\Teams\Subscription;

use App\Actions\Sync\SyncTeamForceAction;
use App\DataProviders\Providers\NoProvider;
use App\Events\TeamSubscribedEvent;

class ForceSyncAfterSubscriptionListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TeamSubscribedEvent $event): void
    {
        $team = $event->team;

        nLog("ForceSyncAfterSubscription Listener Fired for Team $team->name", $team);
        SyncTeamForceAction::dispatchIf($team->provider_id != NoProvider::ID, $team, $team->provider_id)->onQueue('priority-sync');
    }
}
