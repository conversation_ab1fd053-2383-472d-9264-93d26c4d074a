<?php

namespace App\Listeners\Teams\Subscription;

use App\Events\RentalCreatedEvent;
use App\Events\RentalDeletedEvent;
use App\Events\RentalRestoredEvent;
use App\Events\SubscriptionUpdatedEvent;
use App\Models\Rental;
use App\Models\RentalNinjaTeam;
use App\Notifications\Internal\RentalCreatedNotification;
use App\Notifications\Internal\RentalDeletedNotification;
use App\Notifications\Internal\RentalRestoredNotification;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;

class NotifyRentalCountChangedListener implements ShouldQueue
{
    /**
     * The time (seconds) before the job should be processed.
     *
     * @var int
     */
    public $delay = 60;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    public function handle(RentalCreatedEvent|RentalRestoredEvent|RentalDeletedEvent $event): void
    {
        // Update the rental count.
        $team = $event->team;
        $current_count = $team->rentals;
        $new_count = Rental::query()
            ->withUncompleted() // Non completed rentals are paid as well
            ->onTeam($team->id)
            ->count();

        if ($current_count != $new_count) {
            $team->rentals = $new_count;
            $team->save();

            if ($team->subscribedToStripe()) {
                try {
                    $team->updateTotalSeats();
                } catch (Exception $e) {
                    // Do nothing: update seats may throw an exception if payment fails, but we don't cancel the subscription and we show a warning in the UI
                }
            } else {
                // If it's billed through Rental Ninja, this event will be triggered upon stripe webhook received
                event(new SubscriptionUpdatedEvent($team)); // This will trigger an intercom sync
            }

            if ($team->shouldNotify()) {
                try {
                    if ($event instanceof RentalCreatedEvent) {
                        RentalNinjaTeam::getInstance()
                            ->notifyNow(new RentalCreatedNotification($event));
                    } elseif ($event instanceof RentalRestoredEvent) {
                        RentalNinjaTeam::getInstance()
                            ->notifyNow(new RentalRestoredNotification($event));
                    } elseif ($event instanceof RentalDeletedEvent) {
                        RentalNinjaTeam::getInstance()
                            ->notifyNow(new RentalDeletedNotification($event));
                    }
                } catch (Exception $e) {
                    pnLog("[NotifyRentalCountChangedListener] Error sending slack message for rental count changed: {$e->getMessage()}", $team);
                    nLogException('Error sending slack message for rental count changed', $e, $team->id);
                }
            }
        }
    }
}
