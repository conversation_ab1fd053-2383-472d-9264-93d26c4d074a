<?php

namespace App\Listeners;

use App\Actions\Users\NotificationTokens\GetUserNotificationTokensForRentalAndPermissionAction;
use App\Actions\Users\ShouldNotifyUserAction;
use App\Enum\UserSettingsEnum;
use App\Events\CommentCreatedEvent;
use App\Models\Booking;
use App\Models\BookingComment;
use App\Models\Rental;
use App\Models\TeamSettings;
use App\Models\User;
use App\Models\UserNotificationToken;
use App\Notifications\CommentCreatedNotification;
use App\Notifications\NinjaFcmNotification;
use Illuminate\Support\Carbon;
use Psr\Log\LogLevel;

class CommentCreatedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CommentCreatedEvent $event): bool
    {
        nLog('Notifying a new comment', $event->team, LogLevel::NOTICE, ['commentId' => $event->commentId, 'env' => app()->environment()]);

        if ($event->commentId == null || $event->team == null || $event->bookingId == null) {
            return false;
        }

        $team = $event->team;

        if (! $team->subscribed() && ! $team->onGenericTrial()) {
            return false;
        }

        $booking = Booking::getBookingModel($team, $event->bookingId);
        $rental = Rental::getRentalModel($booking->team_id, $booking->rental_id);
        $comment = BookingComment::query()
            ->where('team_id', $team->id)
            ->where('booking_id', $booking->id)
            ->where('id', $event->commentId)
            ->first();

        if ($rental == null) {
            return false;
        }

        $content = $this->notificationContent($comment->content);
        $author = $comment->authorUser;
        $tokens = GetUserNotificationTokensForRentalAndPermissionAction::run(
            team: $team,
            rental: $rental,
            permission: 'view_booking_comments',
            except: [$event->issuer]
        );

        foreach ($tokens as $user_and_token) {
            $token = UserNotificationToken::query()
                ->whereToken($user_and_token['token'])
                ->whereUserId($user_and_token['user_id'])
                ->with('user')
                ->first();

            $isMentioned = collect($comment->mentions)->contains($token->user->id);
            $notificationEnabled = ShouldNotifyUserAction::run(
                user: $token->user,
                type: UserSettingsEnum::COMMENT_CREATED_OR_MODIFIED
            );
            // We will not notify the user only in case he is not mentioned and has the notification of comment created disabled. All other cases must proceed.
            if (! $notificationEnabled && ! $isMentioned) {
                continue;
            }

            $token->notify(new NinjaFcmNotification(
                notifiable: $token,
                title: $this->notificationTitle(
                    token: $token,
                    booking: $booking,
                    rental: $rental,
                    author: $author,
                    mentioned: $isMentioned
                ),
                body: $content,
                type: UserNotificationToken::TYPE_COMMENT_CREATED,
                data: [
                    'booking' => $booking->id,
                    'rental' => $booking->rental_id,
                    'rental_name' => $rental->name,
                ]
            ));
        }

        if ($team->canReceiveSlackNotifications(TeamSettings::COMMENT)) {
            $team->notify((new CommentCreatedNotification($booking, $rental, $content, $event->issuer))->locale($team->getTeamLocale()));
        }

        return true;
    }

    private function notificationContent(string $content)
    {
        return preg_replace('/@\[__(\d+)__]\(__(.*?)__\)/', '@$2', $content);
    }

    private function notificationTitle(UserNotificationToken $token, Booking $booking, Rental $rental, ?User $author, bool $mentioned = false): string
    {
        $locale = substr($token->user->locale, 0, 2);
        $from = Carbon::createFromTimestampUTC($booking->start_at)->locale($locale)->isoformat('D MMMM YYYY');
        $to = Carbon::createFromTimestampUTC($booking->end_at)->locale($locale)->isoformat('D MMMM YYYY');

        if (is_null($author)) {
            $title = __('messages.new_comment_created.title', [
                'rental' => $rental->name,
                'from' => $from,
                'to' => $to,
            ], $locale);
        } elseif ($mentioned) {
            $title = __('messages.mentioned_in_comment.title', [
                'user' => $token->user->name,
                'author' => $author?->name ?? '',
                'rental' => $rental->name,
                'from' => $from,
                'to' => $to,
            ], $locale);
        } else {
            $title = __('messages.author_in_comment.title', [
                'author' => $author?->name ?? '',
                'rental' => $rental->name,
                'from' => $from,
                'to' => $to,
            ], $locale);
        }

        return $title;
    }
}
