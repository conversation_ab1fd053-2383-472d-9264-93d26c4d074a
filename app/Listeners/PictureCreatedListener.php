<?php

namespace App\Listeners;

use App\Actions\Users\NotificationTokens\GetUserNotificationTokensForRentalAndPermissionAction;
use App\Actions\Users\ShouldNotifyUserAction;
use App\Enum\UserSettingsEnum;
use App\Events\PictureCreatedEvent;
use App\Models\Team;
use App\Models\TeamSettings;
use App\Models\UserNotificationToken;
use App\Notifications\NinjaFcmNotification;
use App\Notifications\PictureCreatedNotification;

class PictureCreatedListener
{
    const RENTAL_PICTURE = 0;

    const BOOKING_PICTURE = 1;

    const PAYMENT_PICTURE = 2;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PictureCreatedEvent $event): bool
    {
        if ($event->picture == null || $event->picture->team_id == null) {
            return false;
        }

        $team = Team::query()
            ->whereId($event->picture->team_id)
            ->first();

        if ($team == null || ! $team->isSubscribed()) {
            return false;
        }

        $picture = $event->picture;

        // Get the rental for the picture
        $rental = $picture->rental;

        // Who added the picture
        $author_id = $picture->author_id;
        $author = $picture->author->name;

        // Add a new permission.
        $permission = $this->getPermission($event->type);

        // Get the list of tokens to send the notification to.
        // We don't want to send the notification to the author of the payment though.
        $tokens = GetUserNotificationTokensForRentalAndPermissionAction::run($team, $rental, $permission, [$author_id]);

        foreach ($tokens as $user_and_token) {
            $token = UserNotificationToken::query()
                ->whereToken($user_and_token['token'])
                ->whereUserId($user_and_token['user_id'])
                ->with('user')
                ->first();

            if (! ShouldNotifyUserAction::run($token->user, UserSettingsEnum::PICTURE_ADDED_NOTIFICATION)) {
                continue;
            }

            $locale = substr($token->user->locale, 0, 2);
            $title = __('messages.picture_created.title', [], $locale);
            $author = ucwords($author);
            $body = __('messages.picture_created.body', ['author' => $author], $locale);
            $data = [
                'payment' => $picture->payment_id,
                'booking' => $picture->booking_id,
                'rental' => $picture->rental_id,
                'rental_name' => $picture->rental_name,
                'picture' => $picture->id,
            ];

            $token->notify(new NinjaFcmNotification($token, $title, $body, UserNotificationToken::TYPE_PICTURE_CREATED, $data));
        }

        if ($team->canReceiveSlackNotifications(TeamSettings::PICTURE)) {
            $team->notify((new PictureCreatedNotification($rental, $picture, $author))->locale($team->getTeamLocale()));
        }

        return true;
    }

    private function getPermission($type)
    {
        return match ($type) {
            self::PAYMENT_PICTURE => 'see_payments_tab',
            self::BOOKING_PICTURE => 'view_booking_pictures',
            self::RENTAL_PICTURE => 'view_rental_pictures',
            default => 'see_album',
        };
    }
}
