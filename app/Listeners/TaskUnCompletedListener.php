<?php

namespace App\Listeners;

use App\Events\TaskUnCompletedEvent;
use App\Jobs\TaskUnCompletedJob;

class TaskUnCompletedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TaskUnCompletedEvent $event): void
    {
        dispatch(new TaskUnCompletedJob($event->task, $event->requester))
            ->delay(2);
    }
}
