<?php

namespace App\Listeners;

use App\Models\UserNotificationToken;
use Exception;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Notifications\Events\NotificationFailed;
use Kreait\Firebase\Exception\Messaging\NotFound;
use NotificationChannels\Fcm\FcmChannel;

class NotificationFailedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     *
     * @throws Exception
     */
    public function handle(NotificationFailed $event): void
    {
        if ($event->channel == FcmChannel::class
            && $this->isNotFoundException($event)
            && $event->notifiable instanceof UserNotificationToken
        ) {
            $event->notifiable->delete();
        }
    }

    private function isNotFoundException(NotificationFailed $event): bool
    {
        $data = data_get($event->data, 'report')->error();
        $code = method_exists($data, 'getCode') ? $data->getCode() : 500;
        if ($code == 404) {
            return true;
        }

        return $data instanceof NotFound || $data instanceof ClientException;
    }
}
