<?php

namespace App\Listeners;

use App\Enum\TeamStatusEnum;
use App\Events\SubscriptionCancelledEvent;
use App\Events\SubscriptionUpdatedEvent;
use App\Events\TeamSubscribedEvent;
use App\Models\TeamSubscription;
use App\Support\NinjaCashier;

class UpdateTeamActiveSubscription
{
    /**
     * Handle the event.
     */
    public function handle(TeamSubscribedEvent|SubscriptionUpdatedEvent|SubscriptionCancelledEvent $event): void
    {
        $team = $event->team;

        if (! $team->config()->billedThroughRentalNinja()) {
            return;
        }

        $currentPlan = $team->getRnPlanFromSubscription()?->planCode; // Should return null if Canceled (and takes into account incomplete and Past Due Cashier settings)
        $needsProrateCheck = $event instanceof SubscriptionUpdatedEvent && $team->current_rn_plan !== $currentPlan;

        // FIRST (important to be first). We must ensure current_rn_plan is set correctly as we depend on it to know the status of the team elsewhere.
        $team->current_rn_plan = $currentPlan;
        $team->status = ($team->trial_ends_at !== null && $team->trial_ends_at->isFuture()) ? TeamStatusEnum::trialing : ($currentPlan === null ? TeamStatusEnum::disabled : TeamStatusEnum::enabled);
        $team->save(); // Save the team before everything else. It is crucial current_rn_plan is set correctly

        // SECOND. Get the billing cycle of the subscription
        if ($currentPlan !== null) {
            $subscription = $team->subscription();
            if (! empty($subscription->stripe_price)) {
                $priceId = $subscription->stripe_price;
            } else {
                $priceId = $subscription->items->first()->stripe_price;
            }

            $subscription->billing_cycle = NinjaCashier::initialize()->getPriceBillingCycle($priceId); // This is both a getter and a column, it doesn't brake
            $subscription->save();
        }

        // THIRD. Apply the proration changes if needed.
        if ($needsProrateCheck) {
            $team->refresh();
            /** @var TeamSubscription $subscription */
            $subscription = $team->subscription();

            info('Updating Subscription Proration Status');
            $team->isYearlySubscription() ? $subscription->noProrate() : $subscription->prorate();
            $subscription->updateStripeSubscription([
                'payment_behavior' => $subscription->paymentBehavior(),
            ]);
        }

        // Update CRM's Companies table. The team_subscription should be already modified (the webhook does it).
        // Update: Pau requested this to not be automatic
        /*if ($event instanceof SubscriptionCancelledEvent) {
            $subscription->fresh();
            if ($subscription->notOnTrial() && ! $subscription->ended()) {
                $team->company()->update(['status' => RentalNinjaTeam::companyStatuses('pre-churned'), 'churned_on' => now()]);
            } elseif ($subscription->notOnTrial() && $subscription->ended()) {
                $team->company()->update(['status' => RentalNinjaTeam::companyStatuses('churned'), 'churned_on' => now()]);
            }
        }*/
    }
}
