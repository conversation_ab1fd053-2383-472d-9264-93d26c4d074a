<?php

namespace App\Policies;

use App\Models\PossibleLead;
use App\Models\RentalNinjaTeam;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PossibleLeadPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, PossibleLead $possibleLead): bool
    {
        return RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, PossibleLead $possibleLead): bool
    {
        return RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, PossibleLead $possibleLead): bool
    {
        return RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, PossibleLead $possibleLead): bool
    {
        return RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, PossibleLead $possibleLead): bool
    {
        return RentalNinjaTeam::emailHasNovaAccess($user->email);
    }
}
