<?php

namespace App\Policies;

use App\Models\PaymentGatewayAccount;
use App\Models\RentalNinjaTeam;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PaymentGatewayAccountPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, PaymentGatewayAccount $paymentGatewayAccount): bool
    {
        return $user->current_team_id == $paymentGatewayAccount->team_id || RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user): bool
    {
        return false;
    }
}
