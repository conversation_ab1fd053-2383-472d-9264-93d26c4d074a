<?php

namespace App\Policies;

use App\Models\RentalNinjaTeam;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    public function view(User $user, User $model): bool
    {
        return $user->id == $model->id || RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    public function update(User $user, User $model): bool
    {
        return $user->id == $model->id || RentalNinjaTeam::emailHasNovaAccess($user->email);
    }

    /**
     * Determine whether the user can upload files.
     *
     * @return mixed
     */
    public function uploadFiles(User $user)
    {
        return true;
    }
}
