<?php

namespace App\Console\Commands;

use App\Models\Team;
use App\Support\NinjaCashier;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class CreateDiscountCouponCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ninja:create-coupon';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a discount coupon in Stripe';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $couponName = $this->anticipate('What is the name of the company', Team::select('name')
            ->pluck('name')
            ->toArray());
        $months = $this->ask('How many months will the discount last?', '6');
        $discount = $this->ask('What is the discount to apply in %?', '50');
        $maxRdemptions = $this->ask('Max number of times the coupon can be used', '1');
        $redeemByDays = $this->ask('Expire coupon in how many days?', '7');
        $carbon = now()->addDays($redeemByDays);
        $date = $carbon->timestamp;

        $rand = rand(1, 1000);
        $slug = strtoupper(Str::slug($couponName, '_')).'_'.$rand;

        NinjaCashier::initialize()->createCoupon([
            'id' => $slug,
            'duration' => 'repeating',
            'duration_in_months' => intval($months),
            'percent_off' => intval($discount),
            'max_redemptions' => intval($maxRdemptions),
            'redeem_by' => $date,
        ]);

        $parsed = $carbon->format('l jS F Y');

        $this->info("Coupon with id $slug created. $discount% off for $months months. Please redeem before $carbon and $maxRdemptions max redemptions.");

        $this->info("We're so confident you'll love the new Rental Ninja that we'll give you $discount% off for the next $months months if you sign up before $parsed. The coupon name to use is $slug");
    }
}
