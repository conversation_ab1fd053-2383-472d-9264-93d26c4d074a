<?php

namespace App\Console\Commands;

use App\Actions\Support\Firebase\GetFirebaseValidTokensAction;
use App\Models\UserNotificationToken;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class NotificationDeviceCleanupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ninja:notification-device-cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleans up firebase tokens not used within the last 60 days';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @throws Exception
     */
    public function handle(): int
    {
        $time = Carbon::now()
            ->subDays(60); //After 60 days, firebase considers tokens expired

        $tokens = UserNotificationToken::query()
            ->where('updated_at', '<=', $time)
            ->get();

        if ($tokens->isEmpty()) {
            return self::SUCCESS;
        }

        // Check there is no still valid tokens in the list of the ones to remove
        $validTokens = GetFirebaseValidTokensAction::run($tokens);

        // Touch tokens which are still valid so we don't fetch them again tomorrow trying to delete them
        if (! empty($validTokens)) {
            $tokens->whereIn('token', $validTokens)
                ->each(fn (UserNotificationToken $token) => $token->touch());
        }

        $tokensToDelete = collect($tokens->pluck('token'))->diff($validTokens);
        $this->info("Found a total of {$tokensToDelete->count()} Firebase Device Tokens that need to be deleted");

        if ($tokensToDelete->isEmpty()) {
            return self::SUCCESS;
        }

        $deleted = UserNotificationToken::query()
            ->whereIn('token', $tokensToDelete)
            ->delete();

        if (! $deleted) {
            $this->error("Failed to delete {$tokensToDelete->count()} Firebase Device Tokens");

            return self::FAILURE;
        }

        $this->info("Cleanup Complete: Deleted a total of {$tokensToDelete->count()} Firebase Device Tokens.");

        return self::SUCCESS;
    }
}
