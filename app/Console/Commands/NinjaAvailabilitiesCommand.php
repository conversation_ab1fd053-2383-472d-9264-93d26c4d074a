<?php

namespace App\Console\Commands;

use App\Actions\Rentals\Availability\UpdateTeamAvailabilityMapAction;
use App\Models\Team;
use Illuminate\Console\Command;

class NinjaAvailabilitiesCommand extends Command
{
    protected $signature = 'ninja:availabilities';

    protected $description = 'Update all Availabilities';

    public function handle(): void
    {
        Team::activeTeams()->each(
            fn (Team $team) => UpdateTeamAvailabilityMapAction::dispatch($team)
        );
    }
}
