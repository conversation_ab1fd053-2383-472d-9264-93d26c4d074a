<?php

namespace App\Console\Commands;

use App\Actions\Segment\SyncTeamToSegmentAction;
use App\Models\Team;
use App\Query\TeamQuery;
use Exception;
use Illuminate\Console\Command;

class SegmentSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ninja:segment {--backup} {--all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates Data on Segment';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        if ($this->option('backup')) {
            // In force syncs, we also update to Segment. Just sync the teams which hadn't forced sync recently.
            // We don't want to sync too many teams together to reach rate limits of both segment or intercom
            $teams = Team::query()
                ->activeTeams()
                ->whereForcedAtBeforeThan(now()->subDays(4)) //Each team should be force sync every about 3 days
                ->get();
        } else {
            // Sync active teams. In case of --all option, sync all teams, including non active.
            $teams = Team::query()
                ->when(! $this->option('all'), fn (TeamQuery $query) => $query->activeTeams())
                // TODO: would be great an option to sync teams where the subscription was modified in the last X days or 1 year: as a backup when the command hasn't work for some time
                // However, for now (Apr 2024) it is not essential as for the amount of teams we have, synching all teams works well, Segment manages the queue great and we send things in batches
                // (so we end-up sending some thousands of records only)
                ->get();
        }

        foreach ($teams as $team) {
            if (! $team->mainProvider()->enabled() || ! $team->config()->useSegment()) {
                continue;
            }
            SyncTeamToSegmentAction::dispatch($team)->delay(rand(1, 11 * 60));
            // Avoid Intercom rate limit: Let's randomly delay jobs instead of chaining them. If we do so, if one fails, the rest won't continue
        }

        $this->info('[Segment Command] Finished.');
    }
}
