<?php

namespace App\Console\Commands;

use App\Actions\Providers\Generic\GetProviderEndpointsAction;
use App\Actions\Rentals\Availability\UpdateTeamAvailabilityMapAction;
use App\DataProviders\ApiConnectors\ProviderConnector;
use App\DataProviders\ProviderConstants;
use App\DataProviders\Providers\NinjaProvider;
use App\DataProviders\Providers\RentalsUnited;
use App\DataProviders\Providers\RentalsUnitedPms;
use App\DataProviders\ProviderSync;
use App\Jobs\SyncEndpointJob;
use App\Models\Booking;
use App\Models\Rental;
use App\Models\Team;
use App\Models\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class NinjaSyncLocallyCommand extends Command
{
    public bool $force = true;

    public bool $sync = false;

    protected $signature = 'ninja:sync-locally';

    protected $description = 'Sync things from the Providers we currently hold.';

    const EVERYTHING = 'Everything';

    // TODO:
    // Delete cache key if mails
    // "rental_ninja_local_database_rental_ninja_local_cache_:laravel_unique_job:App\\Jobs\\SyncEndpointJobSYNC-1-users-1-0"
    public function handle(): int
    {
        $team = $this->anticipate('What is the name of the company', Team::query()
            ->activeTeams()
            ->select('name')
            ->pluck('name')
            ->toArray());

        $team = Team::query()->activeTeams()->whereName($team)->firstOrFail();

        $providerOptions = $team->getAllProviders();
        $options = $providerOptions->map(fn (NinjaProvider $p) => $p->fullName());
        $providerName = $this->choice('Select provider to sync', $options->toArray(), $options->first());
        /** @var NinjaProvider $provider */
        $provider = $providerOptions->filter(fn (NinjaProvider $p) => $p->fullName() == $providerName)->first();
        $providerId = $provider->id();
        ProviderSync::setEndSync($team->id, $providerId);

        $shouldContinue = $this->checkAndRetrieveTokens($team, $providerId);
        if (! $shouldContinue) {
            return self::FAILURE;
        }

        if (! $team->canSync($providerId)) {
            $this->error("Team cannot sync (p $providerId)");

            return self::FAILURE;
        }

        $this->force = $this->confirm('Will this be a force Sync?', 'yes');

        if (! $this->confirm('Do you want to sync through the queue handler? If not, it will be launched locally')) {
            $this->sync = true;
        }

        $endpointsList = GetProviderEndpointsAction::run($providerId, ProviderConstants::TYPE_FULL);
        $endpointsList = array_merge([self::EVERYTHING], $endpointsList, [ProviderConstants::ENDPOINT_AVAILABILITIES]);
        $endpointsList = array_merge($endpointsList, [ProviderConstants::ENDPOINT_SOURCES]);

        $endpoint = $this->choice('What do you want to sync?', $endpointsList, self::EVERYTHING);

        $this->clearSyncEndpointJobUniqueKey($team, $endpoint);

        nLog('Starting a new log');

        match ($endpoint) {
            self::EVERYTHING => $this->everything($team, $providerId),
            ProviderConstants::ENDPOINT_ACCOUNTS => $this->account($team, $providerId),
            ProviderConstants::ENDPOINT_RENTALS => $this->rentals($team, $providerId),
            ProviderConstants::ENDPOINT_DELETED_PROPS => $this->deleted($team, $providerId),
            ProviderConstants::ENDPOINT_PHOTOS => $this->photos($team, $providerId),
            ProviderConstants::ENDPOINT_CLIENTS => $this->clients($team, $providerId),
            ProviderConstants::ENDPOINT_AVAILABILITIES => $this->availabilities($team, $providerId),
            ProviderConstants::ENDPOINT_FEES => $this->fees($team, $providerId),
            ProviderConstants::ENDPOINT_TAXES => $this->taxes($team, $providerId),
            ProviderConstants::ENDPOINT_PAYMENTS => $this->payments($team, $providerId),
            ProviderConstants::ENDPOINT_BOOKING_TAGS => $this->bookingTags($team, $providerId),
            ProviderConstants::ENDPOINT_USERS => $this->users($team, $providerId),
            ProviderConstants::ENDPOINT_SOURCES => $this->sources($team, $providerId),
            ProviderConstants::ENDPOINT_BOOKINGS => $this->bookings($team, $providerId),
            ProviderConstants::ENDPOINT_LEADS => $this->leads($team, $providerId),
            ProviderConstants::ENDPOINT_RATES => $this->rates($team, $providerId),
            default => throw new Exception("Unknown endpoint: {$endpoint}")
        };

        $this->info('Sync Performed');

        return self::SUCCESS;
    }

    private function everything(Team $team, int $provider)
    {
        ProviderSync::create($team, $provider)->setSync($this->sync)->setForce($this->force)->start(all: true);
    }

    private function rentals(Team $team, int $provider)
    {
        $rentalId = $this->ask('Do you want to sync a particular Rental Id. If so, what is the id?', 0);

        if ($rentalId != 0) {
            $rental = Rental::onTeam($team)->find($rentalId);

            ProviderConnector::from($team, $provider)->updateRental($rental?->external_id ?? $rentalId);

            return;
        }

        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_RENTALS, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function deleted(Team $team, int $provider)
    {
    }

    private function availabilities(Team $team, int $provider)
    {
        $rentalId = $this->ask('Do you want to sync the availability for a particular Rental Id. If so, what is the id?', 0);

        if ($rentalId == 0) {
            UpdateTeamAvailabilityMapAction::dispatch($team)->onConnection($this->getQueueConnection());
        } else {
            Rental::getRentalModel($team, $rentalId)->updateAvailability();
        }
    }

    private function bookings(Team $team, int $provider)
    {
        if ($provider != RentalsUnited::ID && $provider != RentalsUnitedPms::ID) {
            $bookingId = $this->ask("Do you want to sync any particular Booking? If so, what is the id? If you don't have the booking in your DB, provide the external_id.", 0);

            if ($bookingId != 0) {
                // We may not have the booking in the database. In this case, we can use provider id.
                $externalId = Booking::onTeam($team)->whereId($bookingId)->first()?->external_id ?? $bookingId;

                ProviderConnector::from($team, $provider)->updateBooking($externalId);

                return;
            }
        }

        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_BOOKINGS, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function leads(Team $team, int $provider)
    {
        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_LEADS, $this->force)
                       ->onConnection($this->getQueueConnection());
    }

    private function account(Team $team, int $provider)
    {
        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_ACCOUNTS, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function photos(Team $team, int $provider)
    {
        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_PHOTOS, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function clients(Team $team, int $provider)
    {
        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_CLIENTS, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function fees(Team $team, int $provider)
    {
        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_FEES, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function taxes(Team $team, int $provider)
    {
        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_TAXES, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function payments(Team $team, int $provider)
    {
        $paymentId = $this->ask('Do you want to sync any particular Payment? If so, what is the id?', 0);

        if ($paymentId != 0) {
            ProviderConnector::from($team, $provider)
                ->syncEndpointAndResolve(ProviderConstants::ENDPOINT_PAYMENTS, $paymentId);

            return;
        }

        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_PAYMENTS, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function bookingTags(Team $team, int $provider)
    {
        $tagId = $this->ask('Do you want to sync any particular Booking Tag? If so, what is the id?', 0);

        if ($tagId != 0) {
            ProviderConnector::from($team, $provider)
                ->syncEndpointAndResolve(ProviderConstants::ENDPOINT_BOOKING_TAGS, $tagId);

            return;
        }

        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_BOOKING_TAGS, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function sources(Team $team, int $provider)
    {
        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_SOURCES, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function rates(Team $team, int $provider)
    {
        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_RATES, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function users(Team $team, int $provider)
    {
        $userId = $this->ask('Do you want to sync any particular User? If so, what is the id?', 0);
        $user = User::find($userId);

        if ($userId != 0) {
            ProviderConnector::from($team, $provider)
                ->syncEndpointAndResolve(ProviderConstants::ENDPOINT_USERS, $user->external_id);

            return;
        }

        SyncEndpointJob::dispatch($team, $provider, ProviderConstants::ENDPOINT_USERS, $this->force)
            ->onConnection($this->getQueueConnection());
    }

    private function getQueueConnection(): string
    {
        return $this->sync ? 'sync' : config('queue.default');
    }

    private function clearSyncEndpointJobUniqueKEy(Team $team, string $endpoint)
    {
        // In a local sync, an error may have occurred in the previous sync. We force the finalization of previous syncs.
        Cache::forget("laravel_unique_job:App\\Jobs\\SyncEndpointJobSYNC-{$team->id}-$endpoint-1-0");
    }

    /**
     * @noinspection PhpPossiblePolymorphicInvocationInspection
     */
    private function checkAndRetrieveTokens(Team $team, int $providerId): bool
    {
        $account = $team->getProviderAccount($providerId);
        $provider = $account->provider();
        if (! $provider->hasOauth()) {
            return true;
        }
        if (Carbon::createFromTimestampUTC($account->oauth_expires_at)->subMinutes(1)->isPast()) {
            $this->info('Oauth tokens are expired.');
            if (empty(config('database.connections.prod_read_only.username'))) {
                $this->info('Please renew it using nova or set prod_read_only params in local env');

                return false;
            }
            if (! $this->confirm('Do you want to renew them from production?', 'yes')) {
                return false;
            }

            /** @noinspection SqlResolve */
            $data = DB::connection('prod_read_only')
                ->table('provider_accounts')
                ->where('id', $account->id)
                ->select(['oauth_access_token', 'oauth_expires_at'])
                ->first();

            if (Carbon::createFromTimestampUTC($data->oauth_expires_at)->subMinutes(5)->isPast()) {
                $this->info('Production token is also expired. Please, renew it using nova action here:');
                $this->info("https://rental-ninja.com/nova/resources/teams/$team->id");

                $continue = $this->confirm('Try again?', 'yes');

                if (! $continue) {
                    return false;
                }

                /** @noinspection SqlResolve */
                $data = DB::connection('prod_read_only')
                    ->table('provider_accounts')
                    ->where('id', $account->id)
                    ->select(['oauth_access_token', 'oauth_expires_at'])
                    ->first();
            }

            $account->oauth_access_token = $data->oauth_access_token;
            $account->oauth_refresh_token = 'fake';
            $account->oauth_expires_at = $data->oauth_expires_at;

            $account->save();

            $this->info('Oauth tokens fetched from production');

            return true;
        }

        return true;
    }
}
