<?php

namespace App\Console\Commands;

use App;
use App\Actions\Sync\SyncTeamAction;
use App\Actions\Sync\SyncTeamForceAction;
use App\Actions\Sync\SyncTeamFullAction;
use App\DataProviders\Providers\NoProvider;
use App\Models\ProviderAccount;
use App\Models\Team;
use App\Query\TeamQuery;
use Illuminate\Console\Command;

/** NOTE: this command has been deprecated in favour of PeriodicNormalSyncAction. However, we keep this command in case we need to perform
 * a particular sync at any time, triggering it manually.
 */
class BackupNinjaSyncCommand extends Command
{
    const SYNC_FORCE = 0;

    const SYNC_FULL = 1;

    const SYNC_NORMAL = 2;

    /**
     * The name and signature of the console command. ninja:sync --force=true.
     *
     * @var string
     */
    protected $signature = 'ninja:sync {--force=false} {--full=false} {--team=0} {--only=0} ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Syncs a teams status. This should be called by the scheduler.';

    private string $msg = '';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $onlyProvider = $this->option('only');

        $type = self::SYNC_NORMAL;

        if ($this->option('force') === 'true') {
            $type = self::SYNC_FORCE;
        } elseif ($this->option('full') === 'true') {
            $type = self::SYNC_FULL;
        }

        if ((new Team())->count() < 1) {
            $this->msg('No teams have been found.');
        }

        if ($this->option('team') != '0') {
            $team = Team::find($this->option('team'));

            foreach ($team->providerAccounts as $account) {
                /** @var ProviderAccount $account */
                switch ($type) {
                    case self::SYNC_FORCE:
                        pnLog("Issued FORCE Sync for only one team: {$team->name}", $team);
                        SyncTeamForceAction::dispatch($team, $account->provider_id)->onQueue('priority-sync');
                        break;
                    case self::SYNC_FULL:
                        pnLog("Issued FULL Sync for only one team: {$team->name}", $team);
                        SyncTeamFullAction::dispatch($team, $account->provider_id)->onQueue('priority-sync');
                        break;
                    default:
                        pnLog("Issued Sync for only one team: {$team->name}", $team);
                        SyncTeamAction::dispatch($team, $account->provider_id)->onQueue('priority-sync');
                        break;
                }
            }
        } else {
            $teams = Team::query()
                ->activeTeams()
                ->when(
                    value: $onlyProvider != 0,
                    callback: fn (TeamQuery $query) => $query->whereRelation('providerAccounts', 'provider_id', '=', $onlyProvider))
                ->with('providerAccounts')
                ->get();

            foreach ($teams as $team) {
                $externalProviders = $team->providerAccounts->where('provider_id', '!=', NoProvider::ID);
                foreach ($externalProviders as $account) {
                    /** @var ProviderAccount $account */
                    switch ($type) {
                        case self::SYNC_FORCE:
                            SyncTeamForceAction::dispatch($team, $account->provider_id)->onQueue('cron-sync');
                            break;
                        case self::SYNC_FULL:
                            SyncTeamFullAction::dispatch($team, $account->provider_id)->onQueue('cron-sync');
                            break;
                        default:
                            SyncTeamAction::dispatch($team, $account->provider_id)->onQueue('cron-sync');
                            break;
                    }
                }
            }
        }
    }

    /**
     * Adds a new string to an existing string.
     */
    public function msg($message): string
    {
        if (App::runningInConsole()) {
            $this->info($message);
        } else {
            $this->msg .= "$message\n";
        }

        return $this->msg;
    }
}
