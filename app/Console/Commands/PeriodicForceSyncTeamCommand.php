<?php

namespace App\Console\Commands;

use App\Actions\Sync\SyncTeamForceAction;
use App\DataProviders\Providers\NoProvider;
use App\Models\ProviderAccount;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class PeriodicForceSyncTeamCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ninja:percent-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Every 20 min it will pick a % of teams and force sync them.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        /** @var Collection $subscribed */
        $subscribed = Team::query()
            ->activeTeams()
            ->whereRelation('providerAccounts', 'provider_id', '!=', NoProvider::ID)
            ->get();

        // Sync only a % of the team rentals
        $percent = config('ninja-sync.force_sync_percent_every_twenty_minutes');
        $teamsToGet = intval(ceil($subscribed->count() * $percent / 100));
        $teams = $subscribed
            ->filter(fn (Team $team) => $team->synced_at != null && $team->canSync())
            ->map(function (Team $team) {
                // Make sure sorting works. If a team has never been force synced it should
                // have a force sync date (now persistent) equal to its creation date.
                if ($team->force_synced_at == null) {
                    $team->force_synced_at = $team->created_at;
                }

                return $team;
            })
            ->sortBy('force_synced_at')
            ->take($teamsToGet);
        pnLog("FORCE SYNC $percent% OF TEAMS ONCE AN HOUR. That's a total of {$teams->count()} teams.");

        /** @var Team $team */
        foreach ($teams as $team) {
            $name = strtoupper($team->name);
            $this->info("FORCE SYNC ON TEAM $name");

            pnLog('[PeriodicForceSyncTeamCommand] Syncing team', $team);

            $team->providerAccounts
                ->filter(fn (ProviderAccount $p) => $p->provider_id != NoProvider::ID)
                ->each(fn (ProviderAccount $p) => SyncTeamForceAction::dispatch($team, $p->provider_id)->onQueue('cron-sync')
                );
        }

        return 0;
    }
}
