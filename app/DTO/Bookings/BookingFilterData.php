<?php

namespace App\DTO\Bookings;

use App\Actions\Rentals\GetRentalsFromFilterAction;
use App\Enum\BookingStatusEnum;
use App\Models\User;
use Illuminate\Support\Carbon;
use Spatie\LaravelData\Data;

class BookingFilterData extends Data
{
    public function __construct(
        public ?Carbon $from = null,
        public ?Carbon $to = null,
        public ?Carbon $today = null,
        public ?array $bookingSource = [],
        public ?array $bookingTags = [],
        public ?array $rentals = [],

        public ?array $statuses = null,

        public bool $hideCanceled = true,
        public bool $hideUnavailable = true,

        public ?bool $centralizedInbox = null,
        public ?User $user = null,
    ) {
        if (! $from) {
            $this->from = Carbon::today('UTC');
        }
        if (! $to) {
            $this->to = Carbon::today('UTC')->addDays(365);
        }
        if (! $today) {
            $this->today = Carbon::today('UTC');
        }
        // ShitTimezones. I don't know what this is, but it was there.
        $this->today = $this->today->startOfDay()->shiftTimezone('+00:00');
        $this->from = $this->from->startOfDay()->shiftTimezone('+00:00');
        $this->to = $this->to->startOfDay()->shiftTimezone('+00:00');
        $this->rentals = GetRentalsFromFilterAction::run($this->user, $this->rentals);
        $this->statuses = ! is_null($this->statuses) ? array_map(fn ($status) => BookingStatusEnum::from($status), $this->statuses) : null;

        $this->user = request()->user();
        $team = $this->user->team;
        $this->centralizedInbox = $team->hasCentralizedInbox() && $this->user->permissionFor('can_access_inbox');
    }
}
