<?php

namespace App\DTO\Bookings;

use App\Http\Resources\ConversationMessageResource;
use App\Http\Resources\ProviderEventResource;
use App\Models\BookingComment;
use App\Models\Message;
use App\Models\ProviderEvent;
use Illuminate\Support\Carbon;
use Spatie\LaravelData\Data;

/**
 * @mixin Message|BookingComment
 */
class ForumData extends Data
{
    public function __construct(
        public string $type,
        public Carbon $date,
        public Message|BookingComment|null $conversation = null,
        public ?ProviderEvent $timeline = null,
    ) {
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => $this->type,
            'date' => $this->date,
            'conversation' => $this->conversation ? new ConversationMessageResource($this->conversation) : null,
            'timeline' => $this->timeline ? new ProviderEventResource($this->timeline) : null,
        ];
    }

    public static function fromModel(Message|BookingComment|ProviderEvent $model): ForumData
    {
        $date = match (get_class($model)) {
            BookingComment::class => Carbon::createFromTimestamp($model->created_at),
            Message::class => $model->sent_at,
            ProviderEvent::class => $model->created_at,
        };

        if ($model instanceof ProviderEvent) {
            $type = 'timeline';
            $timeline = $model;
            $conversation = null;
        } else {
            $type = 'conversation';
            $timeline = null;
            $conversation = $model;
        }

        return new self(
            type: $type,
            date: $date,
            conversation: $conversation,
            timeline: $timeline,
        );
    }
}
