<?php

namespace App\DTO\Bookings;

use App\Enum\FeeKindEnum;
use App\Enum\FeeRateKindEnum;
use App\Models\BookingFee;
use App\Models\Fee;
use App\Rules\ExistsInTeamRule;
use App\Transformers\TimestampTransformer;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rules\Enum;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;

#[MapName(SnakeCaseMapper::class)]
class BookingFeeData extends Data
{
    public function __construct(
        public int|null|Optional $id,
        public int $feeId,
        public string $feeName,
        public float $price,

        public int|Optional $teamId,
        public int|Optional $bookingId,
        #[WithTransformer(TimestampTransformer::class)]
        public Carbon|Optional $createdAt,
        #[WithTransformer(TimestampTransformer::class)]
        public Carbon|Optional $updatedAt,

        public bool $required = true,
        public int $timesBooked = 1,

        public bool $internal = true,

        public ?FeeKindEnum $kind = FeeKindEnum::other,
        public ?FeeRateKindEnum $rateKind = FeeRateKindEnum::fixed,
    ) {
    }

    public static function rules(): array
    {
        return [
            'fee_id' => ['required', new ExistsInTeamRule(Fee::class)],
            'price' => ['required', 'numeric'],
            'fee_name' => ['required', 'string'],

            'id' => ['nullable', new ExistsInTeamRule(BookingFee::class)],
            'required' => ['nullable', 'boolean'],
            'times_booked' => ['nullable', 'integer'],
            'kind' => [new Enum(FeeKindEnum::class)],
            'rate_kind' => [new Enum(FeeRateKindEnum::class)],
        ];
    }

    public function feeTotalPrice(): float
    {
        return $this->timesBooked * $this->price;
    }

    public function centsFeeTotalPrice(): int
    {
        return ninjaIntval($this->feeTotalPrice() * 100);
    }
}
