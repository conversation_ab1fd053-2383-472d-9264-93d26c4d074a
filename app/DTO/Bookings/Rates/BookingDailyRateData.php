<?php

namespace App\DTO\Bookings\Rates;

use App\Enum\PricingModelEnum;
use App\Models\Rental;
use App\Models\RentalDailyDetails;
use Illuminate\Support\Carbon;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Transformers\DateTimeInterfaceTransformer;

#[MapName(SnakeCaseMapper::class)]
class BookingDailyRateData extends Data
{
    public function __construct(
        #[WithTransformer(DateTimeInterfaceTransformer::class, format: 'Y-m-d')]
        public Carbon $date,
        // Rental basic info
        public int $rentalSleeps,
        // Daily info
        public ?bool $available,

        public ?PricingModelEnum $strategy,

        public ?int $basePrice, // Before markup
        public ?int $extraGuestPrice,
        public ?int $extraGuestCount,
        public ?int $extraGuestValue,
        public ?int $initialPrice,
        public ?int $priceBeforeMarkup,
        public ?int $priceWithMarkup,

        // Discounts
        public ?int $longStayId,
        public ?string $longStayName,
        public ?int $longStayValue,
        // Last minute
        public ?int $lastMinuteId,
        public ?string $lastMinuteName,
        public ?int $lastMinuteValue,
    ) {
    }

    /*
    * $markup: defined over 100, so 10% markup is 10.
    */
    public static function create(Rental $rental, RentalDailyDetails $dailyInfo, int $bookingLength, Carbon $asDate, int $numGuests, float $markup): BookingDailyRateData
    {
        $date = $dailyInfo->date;

        $basePrice = $dailyInfo->price_in_cents;
        $extraGuests = max($numGuests - $rental->sleeps, 0);
        $extraAmountPerGuest = $dailyInfo->extra_guest_in_cents ?? 0;
        $extraGuestAmount = $extraGuests * $extraAmountPerGuest;
        $initialPrice = $basePrice + $extraGuestAmount;

        // Last minute and long stay discounts
        $lastMinute = $rental->getLastMinuteDiscountForDay($date);
        $lastMinuteValue = $lastMinute?->getLastMinuteDiscount($date, $asDate) ?? 0;
        $lastMinuteValue = intval($initialPrice * $lastMinuteValue / 10000);

        $longStay = $rental->getLongStayDiscountForDay($date);
        $longStayValue = $longStay?->getLongStayDiscount($date, $bookingLength) ?? 0;
        $longStayValue = intval($initialPrice * $longStayValue / 10000);

        $priceBeforeMarkup = $initialPrice - $longStayValue - $lastMinuteValue;
        $priceWithMarkup = $priceBeforeMarkup * (1 + $markup / 100);

        return new self(
            date: $date,
            rentalSleeps: $rental->sleeps,
            available: $dailyInfo->available,
            strategy: $dailyInfo->strategy,
            basePrice: $dailyInfo->price_in_cents,
            extraGuestPrice: $dailyInfo->extra_guest_in_cents ?? 0,
            extraGuestCount: $extraGuests,
            extraGuestValue: $extraGuestAmount,
            initialPrice: $initialPrice,
            priceBeforeMarkup: $priceBeforeMarkup,
            priceWithMarkup: $priceWithMarkup,
            longStayId: $longStay?->id,
            longStayName: $longStay?->name,
            longStayValue: $longStayValue,
            lastMinuteId: $lastMinute?->id,
            lastMinuteName: $lastMinute?->name,
            lastMinuteValue: $lastMinuteValue,
        );
    }
}
