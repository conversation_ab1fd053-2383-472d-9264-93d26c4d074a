<?php

namespace App\DTO\Providers\RentalsUnited;

use App\DTO\Providers\ProviderApiResourceDtoInterface;
use App\Enum\BookingStatusEnum;
use App\Models\Booking;
use App\Models\BookingFee;
use App\Models\BookingTax;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Spatie\LaravelData\Data;

/**
 * This DTO is to be filled with the booking data received from Rentals United's bookings endpoint.
 */
class RentalsUnitedApiBookingDto extends Data implements ProviderApiResourceDtoInterface
{
    public function __construct(
        public int $ReservationID,
        public ?string $ReferenceID,
        public string $CreatedDate,
        public string $LastMod,
        public array $StayInfos,
        public array $CustomerInfo,
        public array $GuestDetailsInfo,
        public string $Creator,
        public ?string $Comments,
        public int $ReservationStatusID, // TODO: In the response of the bookings endpoint, name is StatusID. We will need to map this correctly
        public bool $IsArchived,
        public string $attr_Currency = '',
    ) {
    }

    public static function createFromModel(Model $model): self
    {
        /* @var Booking $model */
        $updated_at = Carbon::createFromTimestampUTC($model->updated_at);
        $check_in_time = $model->getCarbonCheckInTime();

        $client = $model->client;
        $customer_info = [
            'Name' => $client->firstname,
            'SurName' => $client->lastname,
            'Email' => $client->getPrimaryEmail(booking: $model),
            'Phone' => $client->getPhoneNumbersCollection()->first(),
            'SkypeID' => null,
            'Address' => null,
            'ZipCode' => null,
            //'CountryID' =>,
            //'LanguageID' =>,
        ];
        $guests_details_info = [
            'NumberOfAdults' => $model->adults,
            'NumberOfChildren' => $model->children,
            'NumberOfInfants' => 0,
        ];

        $stay_infos = [ // If needed, this can be extracted to a separated DTO
            'StayInfo' => [[
                'PropertyID' => $model->rental_id,
                'DateFrom' => Carbon::createFromTimestampUTC($model->start_at)->toDateString(),
                'DateTo' => Carbon::createFromTimestampUTC($model->end_at)->toDateString(),
                'ArrivalTime' => $check_in_time->toDateString().' '.$check_in_time->toTimeString(),
                'NumberOfGuests' => $model->adults,
                'Costs' => [
                    'ClientPrice' => $model->final_price,
                    'AlreadyPaid' => $model->paid_amount,
                ],
                'ReservationBreakdown' => [ // If needed, this can be extracted to a separated DTO
                    'ChannelCommission' => $model->commission,
                    'ChannelBreakdown' => [
                        'ChannelRent' => $model->final_rental_price,
                        'ChannelTotalFeesTaxes' => [
                            'ChannelTotalFeeTax' => self::getChannelTotalFeeTax($model),
                        ],
                    ],
                ],
            ]],
        ];

        return new self(
            ReservationID: $model->id,
            ReferenceID: $model->reference,
            CreatedDate: Carbon::createFromTimestampUTC($model->created_at)->toDateString(),
            LastMod: $updated_at->toDateString().' '.$updated_at->toTimeString(),
            StayInfos: $stay_infos,
            CustomerInfo: $customer_info,
            GuestDetailsInfo: $guests_details_info,
            Creator: $model->sourcePublicName,
            Comments: $model->notes,
            ReservationStatusID: self::getReservationStatus($model),
            IsArchived: false,
            attr_Currency: $model->currency,
        );
    }

    private static function getReservationStatus(Booking $booking): int
    {
        return match ($booking->status) {
            BookingStatusEnum::CANCELED => 2,
            BookingStatusEnum::BOOKED => 1,
            default => -1, // Shouldn't be used
        };
        //NOTE: 3 is reservation updated, but is only used for webhooks.
    }

    private static function getChannelTotalFeeTax(Booking $booking): array
    {
        return $booking->bookingFees->map(function (BookingFee $fee) {
            return [
                'attr_Name' => $fee->fee_name,
                'attr_Amount' => $fee->getTotalPrice(),
                'attr_ItemType' => 'Fee',
                'attr_IncludedInChannelTotal' => 'true',
            ];
        })->merge($booking->bookingTaxes->map(function (BookingTax $tax) {
            return [
                'attr_Name' => $tax->name,
                'attr_Amount' => $tax->amount,
                'attr_ItemType' => 'Tax',
                'attr_IncludedInChannelTotal' => 'true',
            ];
        }))->when($booking->damage_deposit > 0, function ($collection) use ($booking) {
            return $collection->push([
                'attr_Name' => 'Damage Deposit',
                'attr_Amount' => $booking->damage_deposit,
                'attr_ItemType' => 'Fee',
                'attr_IncludedInChannelTotal' => 'false',
            ]);
        })->toArray();
    }
}
