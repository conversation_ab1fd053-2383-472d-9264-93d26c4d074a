<?php

namespace App\DTO\Providers\BookingSync;

use App\DTO\Providers\GenericProviderResponseDto;
use App\Models\Booking;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

class BookingSyncResponseDto extends GenericProviderResponseDto
{
    public function __construct(
        public string $resource_name,
        public Data|DataCollection $resource,
        public ?Collection $meta = null,
        public ?Collection $links = null,
    ) {
        if (! $meta) {
            $this->meta = collect([
                'X-Total-Pages' => '1',
                'X-Total-Count' => '1',
                'X-Per-Page' => '100', // Instead of fetching the config value, keep it simple
            ]);
        }
        if (! $links) {
            $this->links = collect();
        }
    }

    public static function createBookingResponse(Booking|Collection $bookings): self
    {
        return new self(
            resource_name: 'bookings',
            resource: self::getResourceDto($bookings, BookingSyncApiBookingDto::class),
        );
    }

    public function toArray(): array
    {
        return [
            'links' => $this->links->toArray(),
            $this->resource_name => $this->resource instanceof DataCollection ? $this->resource->toArray() : [$this->resource->toArray()],
            'meta' => $this->meta->toArray(),
        ];
    }
}
