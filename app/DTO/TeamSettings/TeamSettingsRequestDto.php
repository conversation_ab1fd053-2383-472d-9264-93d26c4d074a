<?php

namespace App\DTO\TeamSettings;

use Spa<PERSON>\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class TeamSettingsRequestDto extends Data
{
    public bool $active;

    public ?string $slackWebhookUrl;

    public bool $alertNotifications = false;

    public bool $bookingCreatedNotifications = false;

    public bool $bookingCanceledNotifications = false;

    public bool $paymentCreatedNotification = false;

    public bool $pictureAddedNotification = false;

    public bool $commentCreatedNotification = false;

    public bool $checkInOutTimeModifiedNotification = false;

    public bool $taskNotification = false;

    public bool $leadCreatedNotification = false;

    public bool $newMessageNotification = false;

    public bool $upsalePurchasedNotification = false;
}
