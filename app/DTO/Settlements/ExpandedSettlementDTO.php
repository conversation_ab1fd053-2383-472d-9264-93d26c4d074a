<?php

namespace App\DTO\Settlements;

use App\Models\Settlement;
use App\Transformers\BookingAccountingResourceTransformer;
use App\Transformers\SlimRentalResourceTransformer;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * This DTO will contain all of the required information that an expanded settlement needs.
 */
class ExpandedSettlementDTO extends Data
{
    public function __construct(
        public int $id,
        public int $team_id,
        public string $currency,
        public string $name,
        public Carbon $start,
        public Carbon $end,
        public array $rentals,
        public ?array $sources,
        public bool $on_check_out,
        public bool $on_net_income,
        public int $author_id,
        public ?int $scheduled_settlement_id,
        public bool $to_review,
        public Carbon $created_at,
        public Carbon $updated_at,
        public ?Carbon $deleted_at,
    ) {
    }

    private bool $is_simple; // This is an internal variable to know if this is a simple or a full version. As it's private, is not returned to API.

    /** SETTLEMENT SIMPLE VERSION ATTRIBUTES (common values with the full version) */
    public string $author;

    #[WithTransformer(SlimRentalResourceTransformer::class)]
    public EloquentCollection $slim_rentals;

    // Payouts values:
    public float $payouts_value; // total value of payouts

    public float $bookings_value;

    public float $fees_value;

    public float $taxes_value;

    public float $others_value;

    public float $expenses_value;

    /** SETTLEMENT FULL VERSION ATTRIBUTES */
    public int $payouts; // number of payouts

    /** @var Collection[string] */
    public Collection $payee_emails;

    #[WithTransformer(BookingAccountingResourceTransformer::class)]
    public EloquentCollection $bookings;

    /** CALCULATED VALUES (once we have all the information needed from the full version, we calculate this) */
    // Booking related values by rental
    #[DataCollectionOf(IncomeByRentalDTO::class)]
    public ?DataCollection $income_by_rental = null; // Must be either null or a "map" for the frontend to work

    /** @var Collection[string] */
    public Collection $sorted_rentals;

    /** @var Collection[string] */
    public Collection $rentals_with_no_bookings;

    /** @var ?array[array] */
    public ?array $bookings_map_by_rental = null; // Must be either null or a "map" for the frontend to work

    // Booking related values
    public float $rentals_income = 0.0; // Rental price total income

    public int $guests = 0;

    public int $nights = 0;

    public float $total_income = 0.0; // May be net or gross income

    public int $total_bookings = 0;

    /** @var ?Collection[float] */
    public ?Collection $commissions = null; // Must be either null or a "map" for the frontend to work

    public float $total_commissions = 0.0;

    // Channel related values
    /** @var ?Collection[string] */
    public ?Collection $included_channel_names = null; // Null if all sources (empty($this->sources))
    #[DataCollectionOf(IncomeByChannelDTO::class)]
    public ?DataCollection $income_by_channel = null; // Must be either null or a "map" for the frontend to work

    public float $total_channels_income = 0; // Be be net income or gross amount
    public float $total_channels_net_income = 0; // TODO: this is to keep the frontend with the old name working. Remove in a while

    public ?float $total_channels_to_pay_back = null; // If this is null, means we couldn't calculate it

    public ?float $un_payed = 0.0;

    // Booking Fees related values
    #[DataCollectionOf(SettlementFeeDTO::class)]
    public ?DataCollection $fees = null; // Must be either null or a "map" for the frontend to work

    public float $total_fees = 0.0;

    public int $fees_times_booked = 0;

    public float $fees_average_price = 0.0;

    // Booking Taxes related values
    #[DataCollectionOf(SettlementTaxDTO::class)]
    public ?DataCollection $taxes = null; // Must be either null or a "map" for the frontend to work

    public float $total_taxes = 0.0;

    public float $taxes_prices = 0.0;

    public float $taxes_income = 0.0;

    // Booking Payments related values
    /** @var ?Collection[float] */
    public ?Collection $payment_methods = null; // Must be either null or a "map" for the frontend to work

    public float $total_payed = 0.0;

    public static function fromSettlementModel(Settlement $settlement): self
    {
        return new self(
            id: $settlement->id,
            team_id: $settlement->team_id,
            currency: $settlement->currency,
            name: $settlement->name,
            start: $settlement->start,
            end: $settlement->end,
            rentals: $settlement->rentals,
            sources: $settlement->sources,
            on_check_out: $settlement->on_check_out,
            on_net_income: $settlement->on_net_income,
            author_id: $settlement->author_id,
            scheduled_settlement_id: $settlement->scheduled_settlement_id,
            to_review: $settlement->to_review,
            created_at: $settlement->created_at,
            updated_at: $settlement->updated_at,
            deleted_at: $settlement->deleted_at,
        );
    }

    public function setIsSimple(bool $is_simple): self
    {
        $this->is_simple = $is_simple;

        return $this;
    }

    public function toArray(): array
    {
        // Filter out the values for the settlement simple version
        if ($this->is_simple) {
            return [
                'id' => $this->id,
                'team_id' => $this->team_id,
                'currency' => $this->currency,
                'name' => $this->name,
                'start' => $this->start,
                'end' => $this->end,
                'rentals' => $this->rentals,
                'sources' => $this->sources,
                'on_check_out' => $this->on_check_out,
                'on_net_income' => $this->on_net_income,
                'author_id' => $this->author_id,
                'scheduled_settlement_id' => $this->scheduled_settlement_id,
                'to_review' => $this->to_review,
                'created_at' => $this->created_at,
                'updated_at' => $this->updated_at,
                'deleted_at' => $this->deleted_at,
                'author' => $this->author,
                'slim_rentals' => $this->slim_rentals,
                'payouts_value' => $this->payouts_value,
                'bookings_value' => $this->bookings_value,
                'fees_value' => $this->fees_value,
                'taxes_value' => $this->taxes_value,
                'others_value' => $this->others_value,
                'expenses_value' => $this->expenses_value,
            ];
        } else {
            // TODO: the following lines are to keep the frontend with the old name working. Remove in a while
            $this->income_by_rental?->each(function (IncomeByRentalDTO $dto) {
                $dto->total = $dto->finalRentalPricesTotal;
                $dto->netIncome = $dto->bookingsIncome;
            });
            $this->income_by_channel?->each(fn (IncomeByChannelDTO $dto) => $dto->channelNetIncome = $dto->channelIncome);
            $this->total_channels_net_income = $this->total_channels_income;

            return parent::toArray();
        }
    }

    /**
     * This method must be used to convert all DTO's values into array. Including resources and nested resources into those ones.
     * This is something that laravel does when returning a response. However, in some cases, we use this DTO without returning it
     * in a response. Therefore, we need to fully convert it to an array, and make the transformations the Resources do for the frontend.
     */
    public function recursiveToArray(): array
    {
        return json_decode(json_encode($this->toArray()), true);
    }
}
