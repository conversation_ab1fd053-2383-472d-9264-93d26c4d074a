<?php

namespace App\DTO\Settlements;

use <PERSON><PERSON>\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class SettlementFeeDTO extends Data
{
    public function __construct(
        public string $name,
        public int $timesBooked = 0,
        public float $price = 0,
    ) {
    }

    public function update(int $timesBooked, float $price): void
    {
        $this->timesBooked += $timesBooked;
        $this->price += $price;
    }
}
