<?php

namespace App\DTO\ChannelManager;

use App\Casts\TranslationArrayFromRequestCaster;
use App\DTO\Bookings\FeeData;
use App\Models\Fee;
use App\Rules\ExistsInTeamRule;
use App\Transformers\TranslationArrayToResponseTransformer;
use Spatie\LaravelData\Attributes\FromRouteParameterProperty;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\Validation\Rule;
use Spatie\LaravelData\Attributes\WithCastable;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;

#[MapName(SnakeCaseMapper::class)]
class RentalFeeData extends Data
{
    public function __construct(
        public int|Optional $id,
        #[Rule('int')]
        #[FromRouteParameterProperty('team', 'id')]
        public mixed $teamId, // It fails when teamId is int
        #[Rule('int')]
        #[FromRouteParameterProperty('teamRental', 'id')]
        public mixed $rentalId, // It fails when rentalId is int
        #[Rule(new ExistsInTeamRule(Fee::class))]
        public int $feeId,
        public bool $required,
        public ?bool $collectAtBookingTime,

        public int|null|Optional $order,

        public FeeData|Optional|null $fee,

        // Upsale parameters
        public bool|Optional|null $isUpsale,
        public bool|Optional|null $upsaleUnpurchasable,
        #[WithCastable(TranslationArrayFromRequestCaster::class)]
        #[WithTransformer(TranslationArrayToResponseTransformer::class)]
        public TranslationArray|Optional|null $name,
        #[WithCastable(TranslationArrayFromRequestCaster::class)]
        #[WithTransformer(TranslationArrayToResponseTransformer::class)]
        public TranslationArray|Optional|null $description,
        public string|Optional|null $iconName,
        public string|Optional|null $iconColor,
        public string|Optional|null $imageUrl,
        public int|Optional|null $minQuantity,
        public int|Optional|null $maxQuantity,
        public int|Optional|null $daysBeforeStart,
    ) {
        if (is_null($this->collectAtBookingTime)) {
            $this->collectAtBookingTime = $this->required;
        }
    }
}
