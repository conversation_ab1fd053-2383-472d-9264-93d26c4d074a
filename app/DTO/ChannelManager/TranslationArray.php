<?php

namespace App\DTO\ChannelManager;

use JsonSerializable;

class TranslationArray implements JsonSerializable
{
    public function __construct(
        public array $array,
    ) {
    }

    public function jsonSerialize(): ?array
    {
        $array = array_filter($this->array);

        return empty($array) ? null : $array;
    }

    public function getLocaledText(string $locale): ?string
    {
        if (! empty($this->array)) {
            $preferred = data_get($this->array, $locale);
            $english = data_get($this->array, 'en');
            $first = reset($this->array);

            return $preferred ?? $english ?? $first ?? null;
        }

        return null;
    }

    public function onlyLocaledText(string $locale): ?string
    {
        if (! empty($this->array)) {
            return data_get($this->array, $locale);
        }

        return null;
    }

    public function count(): int
    {
        if (! empty($this->array)) {
            return count($this->array);
        }

        return 0;
    }

    public function languages(): array
    {
        if (! empty($this->array)) {
            return array_keys($this->array);
        }

        return [];
    }

    public function empty(): bool
    {
        if (empty($this->array) || empty(array_filter($this->array))) {
            return true;
        }

        return false;
    }
}
