<?php

namespace App\DTO\ChannelManager;

use <PERSON><PERSON>\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class AmenityWithCountData extends Data
{
    public function __construct(
        public int $amenityId,
        public int $count = 1,
    ) {
    }

    public static function parseFromRU(mixed $am): AmenityWithCountData
    {
        return new self(
            amenityId: intval(data_get($am, 'data')),
            count: data_get($am, 'attr_Count', 1),
        );
    }
}
