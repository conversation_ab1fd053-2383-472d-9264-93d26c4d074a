<?php

namespace App\DTO\ChannelManager;

use App\Enum\RentalDiscountEnum;
use App\Transformers\DoNotWrapCollectionTransformer;
use Illuminate\Support\Carbon;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\FromRouteParameter;
use Spatie\LaravelData\Attributes\FromRouteParameterProperty;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;

#[MapName(SnakeCaseMapper::class)]
class RentalDiscountData extends Data
{
    public function __construct(
        public int|Optional $id,
        #[FromRouteParameterProperty('team', 'id')]
        public mixed $teamId, // It fails when teamId is int
        public int|Optional $order,
        #[FromRouteParameterProperty('teamRental', 'id')]
        public mixed $rentalId, // It fails when rentalId is int
        #[FromRouteParameter('type')]
        public RentalDiscountEnum $type,
        public string $name,
        public ?Carbon $startFrom,
        public ?Carbon $validUntil,
        #[WithTransformer(DoNotWrapCollectionTransformer::class)]
        #[DataCollectionOf(RentalDiscountsValuesData::class)]
        public DataCollection $discounts,
    ) {
    }
}
