<?php

namespace App\DTO\ChannelManager;

use App\Models\Amenity;
use App\Transformers\DoNotWrapCollectionTransformer;
use Spa<PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;

#[MapName(SnakeCaseMapper::class)]
class CompositionRoomAmenityData extends Data
{
    public function __construct(
        public int $roomId,
        public string|Optional|null $name,
        public int|Optional|null $id,

        #[WithTransformer(DoNotWrapCollectionTransformer::class)]
        #[DataCollectionOf(AmenityWithCountData::class)]
        public DataCollection $amenities,
    ) {
        if (is_null($this->id) || $this->id instanceof Optional) {
            $this->id = self::generateRandomId();
        }
    }

    public static function parseFromRU(array $room): CompositionRoomAmenityData
    {
        $amenityRoomId = intval(data_get($room, 'attr_CompositionRoomID'));
        $name = Amenity::whereExternalId($amenityRoomId)->first()->default_name;
        $id = self::generateRandomId();

        return new CompositionRoomAmenityData(
            $amenityRoomId,
            $name,
            $id,
            AmenityWithCountData::collection(
                collect(data_get($room, 'Amenities.Amenity'))
                    ->filter()
                    ->map(fn (mixed $a) => AmenityWithCountData::parseFromRU($a))
                    ->values()
                    ->toArray()
            ));
    }

    private static function generateRandomId(): int
    {
        return rand(0, 65535);
    }
}
