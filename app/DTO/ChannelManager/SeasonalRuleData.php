<?php

namespace App\DTO\ChannelManager;

use App\Models\ChannelManager\Changeovers;
use Illuminate\Support\Carbon;
use Spatie\LaravelData\Attributes\FromRouteParameterProperty;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Rule;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;

#[MapName(SnakeCaseMapper::class)]
class SeasonalRuleData extends Data
{
    public function __construct(
        public int|Optional $id,
        #[Rule('int')]      // Needed to accept int when present in json
        #[FromRouteParameterProperty('team', 'id')]
        public mixed $teamId, // It fails when teamId is int
        public int|Optional $order,
        #[Rule('int')]      // Needed to accept int when present in json
        #[FromRouteParameterProperty('teamRental', 'id')]
        public mixed $rentalId, // It fails when rentalId is int
        public string $name,
        public ?Carbon $startFrom,
        public ?Carbon $validUntil,
        public ?array $weekdays,
        #[Required]
        public bool $locked,
        public ?string $strategy,
        public ?int $valueInCents,
        #[WithTransformer(Changeovers::class)]
        #[WithCast(Changeovers::class)]
        public Changeovers|Optional $changeover,
        public ?int $extraGuestAmountInCents = 0,
        public int $minStay = 1,
    ) {
        $this->valueInCents = $valueInCents ?? 0; // Frontend does not accept null
    }
}
