<?php

namespace App\DTO\Tasks;

use App\Enum\TeamRolesEnum;
use Illuminate\Support\Carbon;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

class ScheduledTaskDto extends Data
{
    public Optional|int|null $id;
    public int $team_id;
    public int $checklist_id;
    public bool $active;
    public ?string $title;
    public ?string $description;
    public ?TeamRolesEnum $role;
    public ?int $assignee_id;
    public ?int $supervisor_id;
    public string $can_start;
    public string $due_date;
    public int $priority;
    public bool $remove_on_cancellation;
    public bool $create_for_blocks;
    public bool $has_timer = false;
    public int $max_time = 1;
    public Optional|array $rentals;
    public Optional|Carbon|null $created_at;
    public Optional|Carbon|null $updated_at;
    public Optional|Carbon|null $deleted_at;
}
