<?php

namespace App\DTO\Tasks;

use App\Actions\Users\GetUsersFromRequestFilterAction;
use App\Enum\TeamRolesEnum;
use App\Http\Requests\Tasks\TasksForRentalRequest;
use App\Models\Team;
use App\Models\User;
use Auth;
use Illuminate\Support\Carbon;
use Spatie\LaravelData\Data;

class TaskRequestForRentalFiltersData extends Data
{
    public int $year;

    public int $month;

    public Team $team;

    public array $rentals;

    public $providerAccount;

    public array $users;

    public array $roles;

    public array $scheduled_ids;

    public ?string $job_title;

    public bool $excludeEmpty;

    public bool $includeAdminTasks;

    public static function fromRequest(TasksForRentalRequest $request, Team $team, ?int $rentalId = null): self
    {
        $data = $request->validated();
        [$users, $roles] = GetUsersFromRequestFilterAction::run();

        return self::from([
            'year' => $data['year'],
            'month' => $data['month'],
            'team' => $team,
            'rentals' => data_get($data, key: 'rentals', default: [$rentalId]),
            'users' => $users,
            'roles' => $roles,
            'job_title' => $data['job_title'] ?? null,
            'scheduled_ids' => $data['schedule_ids'] ?? [],
            'providerAccount' => $team->external_id,
            'excludeEmpty' => data_get($data, key: 'exclude_empty', default: true),
            'includeAdminTasks' => data_get($data, key: 'include_admin_tasks', default: false),
        ]);
    }

    public function dates(): array
    {
        return once(fn () => $this->start()->daysUntil($this->end())->toArray());
    }

    public function start(): Carbon
    {
        return once(fn () => Carbon::create($this->year, $this->month)->startOfMonth());
    }

    public function end(): Carbon
    {
        return once(fn () => $this->start()->copy()->endOfMonth());
    }

    public function defaultCheckInTime(): string
    {
        return once(fn () => $this->team->defaultArrivalTime());
    }

    public function defaultCheckOutTime(): string
    {
        return once(fn () => $this->team->defaultDepartureTime());
    }

    public function includeAllTasks(): bool
    {
        return once(fn () => $this->role()->isRentalManagerOrAbove());
    }

    public function role(): TeamRolesEnum
    {
        return once(fn () => $this->user()->ninja_role);
    }

    public function user(): User
    {
        return once(fn () => Auth::user());
    }

    public function excludeEmpty(): bool
    {
        return $this->excludeEmpty;
    }
}
