<?php

namespace App\DTO\Accounting;

use App\Flavors\NinjaFlavor;
use App\Models\Team;
use App\Traits\NinjaNotifiable;
use Spa<PERSON>\LaravelData\Attributes\Validation\BooleanType;
use Spatie\LaravelData\Attributes\Validation\IntegerType;
use Spa<PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

class PaymentRecipientDto extends Data
{
    use NinjaNotifiable;

    public function __construct(
        #[IntegerType]
        public int|Optional $team_id,
        #[StringType, Required]
        public string $email,
        #[StringType, Required]
        public string $content,
        #[BooleanType, Required]
        public bool $include_attachments,
        #[BooleanType, Required]
        public bool $send_copy_to_me,
        #[StringType]
        public string $language = 'en',
        public ?Team $team = null,
    ) {
    }

    public function config(): NinjaFlavor
    {
        return $this->team()->config();
    }

    public function team(): Team
    {
        return $this->team ?? Team::query()->whereId($this->team_id)->first();
    }
}
