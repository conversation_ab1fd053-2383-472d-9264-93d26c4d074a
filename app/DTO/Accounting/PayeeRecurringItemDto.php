<?php

namespace App\DTO\Accounting;

use App\Casts\ArrayToCollectionCaster;
use App\Enum\PayeeRecurringItemStrategyTypeEnum;
use App\Enum\PayoutDetailOriginalTypeEnum;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;

class PayeeRecurringItemDto extends Data
{
    public function __construct(
        public PayeeRecurringItemStrategyTypeEnum $type, // Strategy type: add item per booking or per settlement
        public float $value,
        #[MapName(SnakeCaseMapper::class)]
        public PayoutDetailOriginalTypeEnum $conceptType, // Using Enum, although tax is not allowed
        public string $description, // Description added to the payout detail
        #[WithCast(ArrayToCollectionCaster::class)]
        public Optional|Collection $rentals, // For type=settlement and "all rentals", this is not present
    ) {
    }
}
