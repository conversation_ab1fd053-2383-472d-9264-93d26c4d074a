<?php

namespace App\DTO\Team;

use App\Casts\ArrayToCollectionCaster;
use App\Enum\PlanCodeEnum;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Casts\EnumCast;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

/** This Data is intended to be used as the source of information about the generic plan of a team.
 * Each team must have a unique identifier plan, which doesn't change regardless of connected rentals or not.
 * For instance, the Pro plan, if a team has connected rentals, the plan is the same (not switched to Pro Connected). This data object returns
 * information about such plan, but not about the subscription: if it is yearly or not, if it has connected rentals,...
 * To get such information, use the Subscription Data Object.
 */
#[MapName(SnakeCaseMapper::class)]
class PlanData extends Data
{
    public function __construct(
        #[WithCast(EnumCast::class)]
        public PlanCodeEnum $planCode,
        public bool $hasYearly,
        public bool $availableForSale,
        public bool $chm,
        public string $name,
        public string $description,
        #[WithCast(ArrayToCollectionCaster::class)]
        public Collection $products,
        #[WithCast(ArrayToCollectionCaster::class)]
        public Collection $pricing,
    ) {
    }

    public static function getPlans(bool $availableForSaleOnly = false): DataCollection
    {
        $plans = self::collection(config('ninja-stripe.plans'));

        if ($availableForSaleOnly) {
            $plans = $plans->where('availableForSale', true);
        }

        return $plans->values();
    }

    public static function findPlan(PlanCodeEnum $plan): self
    {
        // Look for a specific plan
        return self::getPlans()
                   ->where('planCode', $plan)
                   ->first();
    }

    /** Given a collection of RN products, it returns to which plan corresponds.
     * @param  DataCollection<ProductData>  $products
     */
    public static function fromProducts(DataCollection $products): self
    {
        $productIds = $products->map(fn (ProductData $product) => $product->id)->toCollection()->values();

        return self::getPlans()
                   ->filter(fn (self $plan, $key) => $plan->products->diff($productIds)->isEmpty())
                   ->first();
    }

    /** Given a Plan, returns the product which must be used to bill the total rentals amount */
    public function getTotalCountProduct(): ProductData
    {
        return match ($this->planCode) {
            PlanCodeEnum::growth => ProductData::findProduct('growth-plan-flat-rate'),
            PlanCodeEnum::proConnected => ProductData::findProduct('pro-connected'),
            PlanCodeEnum::pro => ProductData::findProduct('pms'),
            PlanCodeEnum::professionalYearly => ProductData::findProduct('professional-yearly'),
            PlanCodeEnum::professionalMonthly => ProductData::findProduct('professional'),
        };
    }

    public function getChannelManagerCountProduct(): ?ProductData
    {
        return match ($this->planCode) {
            PlanCodeEnum::growth, PlanCodeEnum::proConnected => null,
            PlanCodeEnum::pro, PlanCodeEnum::professionalYearly, PlanCodeEnum::professionalMonthly => ProductData::findProduct('cm'),
        };
    }

    public function planCanAddPublishedRentalsProduct(): bool
    {
        return $this->planCode->planCanAddPublishedRentalsProduct();
    }
}
