<?php

namespace App\DTO\Team;

use App\DataProviders\Providers\NoProvider;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\Validation\Confirmed;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Unique;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;
use Spatie\LaravelData\Support\Validation\ValidationContext;

#[MapName(SnakeCaseMapper::class)]
class RegisterFormData extends Data
{
    public function __construct(
        // Basic
        public ?string $registrationToken,
        #[DataCollectionOf(PlanData::class)]
        public ?DataCollection $plans,
        public ?array $providers,

        // First: Provider selection
        public int $providerId,

        // Second: Provider information
        public ?string $providerAccountId,
        public ?int $flavorId,
        // Provider or manual information
        #[Min(1)]
        public int $rentalCount,

        // Third: Team information
        public string $teamName,

        // Fourth: Owner information
        public string $name,
        #[Unique('users', 'email')]
        public string $email,
        public string $phoneCountry,
        public string $phone,
        #[Confirmed]
        public string $password,
        #[Nullable, StringType, Max(5), Min(5), MapName('user_language')]
        public Optional|string $locale,

        // Fifth: Register information
        public ?string $plan,
        public ?string $coupon,
    ) {
    }

    public static function rules(ValidationContext $context): array
    {
        return [
            'provider_account_id' => new Unique(
                table: 'provider_accounts',
                column: 'account_id',
                where: fn ($q) => $q
                    ->where('provider_id', '=', $context->fullPayload['provider_id'])
                    ->where('provider_id', '!=', NoProvider::ID)
            ),
        ];
    }
}
