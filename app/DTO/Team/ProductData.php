<?php

namespace App\DTO\Team;

use App\Casts\ArrayToCollectionCaster;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;

#[MapName(SnakeCaseMapper::class)]
class ProductData extends Data
{
    public function __construct(
        public string $id,
        public string $production,
        public string $test,
        #[WithCast(ArrayToCollectionCaster::class)]
        public Optional|Collection $yearlyPrices,
        #[WithCast(ArrayToCollectionCaster::class)]
        public Optional|Collection $monthlyPrices,
    ) {
    }

    public static function getAll(): DataCollection
    {
        return self::collection(config('ninja-stripe.products'));
    }

    public static function findProduct(string $id): self
    {
        return self::getAll()
            ->where('id', $id)
            ->first();
    }

    public static function fromStripe(string|Collection $stripeProducts): DataCollection
    {
        if (is_string($stripeProducts)) {
            $stripeProducts = collect([$stripeProducts]);
        }

        return self::getAll()
            ->filter(fn (self $product) => $stripeProducts->contains($product->production) || $stripeProducts->contains($product->test));
    }

    public static function getGrowthCommissionProduct(): self
    {
        return self::findProduct('growth-plan-commission');
    }

    public function getDefaultPriceId(bool $yearly): string
    {
        $prices = $yearly ? $this->yearlyPrices : $this->monthlyPrices;

        return $prices->firstWhere('default', true)->get('id');
    }

    public function getStripeProduct(): string
    {
        return isProduction() ? $this->production : $this->test;
    }
}
