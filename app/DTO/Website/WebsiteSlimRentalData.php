<?php

namespace App\DTO\Website;

use App\DTO\Bookings\Rates\BookingRateData;
use App\Models\Rental;
use App\Models\RentalPicture;
use App\Support\WebsiteLocaleHelper;
use Illuminate\Support\Enumerable;
use Livewire\Wireable;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;

class WebsiteSlimRentalData extends Data implements Wireable
{
    use WireableData;

    public function __construct(
        public int $id,
        public int $teamId,
        public string $rentalName,
        public string $currency,
        public ?int $surface,
        public string $surfaceUnit,
        public int $bathrooms_count,
        public int $bedrooms_count,
        public string $description,
        public string $headline,
        public Enumerable $pictures,
        public ?string $error = null,
        public bool $hasQuote = false,
        public ?float $finalPrice = null,
        public ?float $avgNightPrice = null,
    ) {
    }

    public static function fromArray(array $data): self
    {
        $data['pictures'] = collect($data['pictures']);

        return new self(...$data);
    }

    public static function fromModel(Rental $rental): self
    {
        $locale = WebsiteLocaleHelper::getLocale();

        return new self(
            id: $rental->id,
            teamId: $rental->team_id,
            rentalName: $rental->name,
            currency: $rental->currency ?? 'EUR',
            surface: $rental->surface,
            surfaceUnit: $rental->surface_unit ?? 'm²',
            bathrooms_count: $rental->getNumberOf('bathroom'),
            bedrooms_count: $rental->getNumberOf('bedroom'),
            description: $rental->description?->getLocaledText($locale) ?? 'Please fill description',
            headline: $rental->headline?->getLocaledText($locale) ?? 'Please fill headline',
            pictures: $rental->photos->take(5)->map(fn (RentalPicture $p) => WebsiteRentalPictureData::from($p, $rental)),
        );
    }

    public function setQuote(BookingRateData $quote): void
    {
        $this->hasQuote = true;
        $this->finalPrice = $quote->finalPrice;
        $this->avgNightPrice = $quote->averageFinalPricePerNight;
    }

    public function setError(): void
    {
        $this->error = true;
    }

    public static function fromMultiple(Rental $rental, ?BookingRateData $quote): self
    {
        $locale = WebsiteLocaleHelper::getLocale();

        return new self(
            id: $rental->id,
            teamId: $rental->team_id,
            rentalName: $rental->name,
            currency: $rental->currency ?? 'EUR',
            surface: $rental->surface,
            surfaceUnit: $rental->surface_unit ?? 'm²',
            bathrooms_count: $rental->getNumberOf('bathroom'),
            bedrooms_count: $rental->getNumberOf('bedroom'),
            description: $rental->description?->getLocaledText($locale) ?? 'Please fill description',
            headline: $rental->headline?->getLocaledText($locale) ?? 'Please fill headline',
            pictures: $rental->photos->take(5)->map(fn (RentalPicture $p) => WebsiteRentalPictureData::from($p, $rental)),
            hasQuote: ! is_null($quote),
            finalPrice: $quote?->finalPrice ?? 0,
            avgNightPrice: $quote?->averageFinalPricePerNight ?? 0,
        );
    }
}
