<?php

namespace App\DTO\Website;

use App\Models\Rental;
use App\Models\RentalPicture;
use App\Support\WebsiteLocaleHelper;
use Livewire\Wireable;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;

class WebsiteRentalPictureData extends Data implements Wireable
{
    use WireableData;

    public function __construct(
        public int $id,
        public string $urlSmall,
        public string $urlBig,
        public string $urlGrid,
        public string $urlClean,
        public ?string $description,
        public ?string $type,
        public ?int $width,
        public ?int $height,
    ) {
    }

    public static function fromModel(RentalPicture $model): self
    {
        $imgixParamsSmall = '?ar=auto&compress=auto&quality=97&fm=auto&w=400';
        $imgixParamsBig = '?auto=compress&auto=true';
        $imgixParamsGrid = '?ar=auto&auto=compress&quality=97&fm=auto&w=379&h=256&ar=2:1&fit=crop&crop=entropy';
        $locale = WebsiteLocaleHelper::getLocale();

        return new self(
            id: $model->id,
            urlSmall: $model->url.$imgixParamsSmall,
            urlBig: $model->url.$imgixParamsBig,
            urlGrid: $model->url.$imgixParamsGrid,
            urlClean: $model->url,
            description: $model->description?->getLocaledText($locale),
            type: $model->image_type->name,
            width: $model->width,
            height: $model->height,
        );
    }

    public static function fromMultiple(RentalPicture $model, Rental $rental): self
    {
        $imgixParamsSmall = '?ar=auto&compress=auto&quality=97&fm=auto&w=400';
        $imgixParamsBig = '?ar=auto&quality=97&auto=compress';
        $imgixParamsGrid = '?ar=auto&auto=compress&quality=97&fm=auto&w=379&h=256&ar=2:1&fit=crop&crop=entropy';
        $locale = WebsiteLocaleHelper::getLocale();

        $description = $model->description?->getLocaledText($locale) ?? $rental->headline?->getLocaledText($locale);
        $type = $model->image_type->name ?? $rental->headline?->getLocaledText($locale);

        return new self(
            id: $model->id,
            urlSmall: $model->url.$imgixParamsSmall,
            urlBig: $model->url.$imgixParamsBig,
            urlClean: $model->url,
            urlGrid: $model->url.$imgixParamsGrid,
            description: $description,
            type: $type,
            width: $model->width,
            height: $model->height,
        );
    }
}
