<?php

namespace App\DTO\Statistics;

class KpiDataResource
{
    public $currency;

    public $labels;

    public $values;

    public $total;

    public $compare;

    public $count;

    public string $type = 'line';

    public bool $is_percent = false;

    public bool $is_reversed = false;

    /**
     * Transform the resource into an array.
     */
    public function toArray(): array
    {
        return [
            'currency' => $this->currency,
            'total' => intval($this->total),
            'compare' => intval($this->compare),
            'labels' => array_values($this->labels),
            'values' => array_map('intval', $this->values),
            'type' => $this->type,
            'is_percent' => $this->is_percent,
            'is_reversed' => $this->is_reversed,
        ];
    }
}
