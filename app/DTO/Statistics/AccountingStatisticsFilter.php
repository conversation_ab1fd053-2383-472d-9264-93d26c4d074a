<?php

namespace App\DTO\Statistics;

use App\Models\CarbonPeriod;
use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Carbon;

class AccountingStatisticsFilter
{
    /** @var User */
    public $user;

    /** @var int */
    public $user_id;

    /** @var Team */
    public $team;

    /** @var int */
    public $team_id;

    /** @var array */
    public $rentals;

    /** @var array */
    public $recipients;

    /** @var array */
    public $types;

    public $from;

    public $previousToFrom;

    public $to;

    public $compare;

    /** @var bool */
    public $no_rental;

    public function formatRentals(): void
    {
        if (! is_array($this->rentals)) {
            if (is_int($this->rentals)) {
                $this->rentals = [$this->rentals];
            } else {
                $this->rentals = [];
            }
        }
    }

    public function filteredRentalsForUser(): array
    {
        if (! is_array($this->rentals)) {
            if (! empty($this->rentals)) {
                $this->rentals = [$this->rentals];
            } else {
                $this->rentals = [];
            }
        }

        // OLD: return array_intersect($this->rentals, $this->rentalsForUser());
        // We don't want to filter by the access of the user. The whole accounting section is made regardless of permissions. In addition, we want
        // to avoid the situation where a user hasn't access to a rental and it's already deleted (so can't recover access) but wants to see stats about that rental.
        return $this->rentals;
    }

    public function sub(int $p): CarbonPeriod
    {
        Carbon::setWeekStartsAt(Carbon::SUNDAY);
        Carbon::useMonthsOverflow(false);
        Carbon::useYearsOverflow(false);
        $tr = $this->_sub($p);
        Carbon::resetMonthsOverflow();
        Carbon::useYearsOverflow(true);
        Carbon::setWeekStartsAt(Carbon::MONDAY);

        return $tr;
    }

    private function _sub(int $p): CarbonPeriod
    {
        $period = CarbonPeriod::instance($this->from->copy()
            ->startOfDay(), $this->to->copy()
            ->endOfDay());

        // IS THIS A YEAR.
        $start = $period->start()
            ->startOfYear();
        $end = $period->end()
            ->endOfYear();

        if ($start->eq($period->start()) && $end->eq($period->end())) {
            return CarbonPeriod::instance($period->start()
                ->subYears($p), $period->end()
                ->subYears($p));
        }

        // IS THIS A QUARTER?
        $start = $period->start()
            ->startOfQuarter();
        $end = $period->end()
            ->endOfQuarter();

        if ($start->eq($period->start()) && $end->eq($period->end())) {
            return CarbonPeriod::instance($period->start()
                ->subMonths($p * 3), $period->end()
                ->subMonths($p * 3)
                ->endOfMonth());
        }

        // IS THIS A MONTH?
        $start = $period->start()
            ->startOfMonth();
        $end = $period->end()
            ->endOfMonth();

        if ($start->eq($period->start()) && $end->eq($period->end())) {
            return CarbonPeriod::instance($period->start()
                ->subMonths($p), $period->end()
                ->subMonths($p)
                ->endOfMonth());
        }

        $x = $period->start()
            ->subDays(($p * $period->lengthInDays()) + $p);
        $e = $period->end()
            ->subDays(($p * $period->lengthInDays()) + $p);

        return CarbonPeriod::instance($x, $e);
    }
}
