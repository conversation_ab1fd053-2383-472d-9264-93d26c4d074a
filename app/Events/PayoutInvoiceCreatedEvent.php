<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\PayeeInvoice;
use App\Models\Payout;
use App\Models\ProviderEvent;
use App\Models\Team;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PayoutInvoiceCreatedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public Team $team,
        public Payout $payout,
        public PayeeInvoice $invoice_data)
    {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        $raw = $this->invoice_data;
        $data = [
            'url' => $raw->url,
            'vat' => $raw->vat,
            'lang' => $raw->lang,
            'total' => $raw->total,
            'issuer' => $raw->issuer,
            'currency' => $raw->currency,
            'receiver' => $raw->receiver,
            'vat_type' => $raw->vat_type,
            'vat_amount' => $raw->vat_amount,
            'issued_at' => $raw->issued_at?->toIso8601ZuluString(),
            'payout_id' => $raw->payout_id,
            'sub_total' => $raw->sub_total,
            'invoice_id' => $raw->invoice_id,
            'other_total' => $raw->others_total,
            'settlement_id' => $raw->settlement_id,
        ];

        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::PAYOUT_INVOICE_CREATED,
            'rental_id' => 0,
            'related_model_id' => $this->invoice_data->id,
            'related_model_type' => PayeeInvoice::class,
            'data' => $data,
        ]);
    }
}
