<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\PartialPayout;
use App\Models\Payout;
use App\Models\ProviderEvent;
use App\Models\Settlement;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PartialPayoutCreated implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public Team $team,
        public Settlement $settlement,
        public Payout $payout,
        public PartialPayout $partial,
        public ?User $user = null,
    ) {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::PARTIAL_PAYOUT_CREATED,
            'rental_id' => 0,
            'initiator_id' => $this->user?->id,
            'related_model_id' => $this->partial->id,
            'related_model_type' => PartialPayout::class,
            'data' => $this->partial->toArray(),
        ]);
    }
}
