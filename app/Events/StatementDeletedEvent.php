<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\ProviderEvent;
use App\Models\Settlement;
use App\Models\Team;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StatementDeletedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public Team $team,
        public Settlement $statement
    ) {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::STATEMENT_DELETED,
            'rental_id' => 0,
            'related_model_id' => $this->statement->id,
            'related_model_type' => Settlement::class,
            'data' => [
                'name' => $this->statement->name,
                'start' => $this->statement->start->toDateString(),
                'end' => $this->statement->end->toDateString(),
                'currency' => $this->statement->currency,
                'author' => $this->statement->author?->name,
            ],
        ]);
    }
}
