<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\PartialPayout;
use App\Models\Payout;
use App\Models\ProviderEvent;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PartialPayoutDeleted implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public Team $team,
        public Payout $payout,
        public array $partial,
        public ?User $user = null,
    ) {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::PARTIAL_PAYOUT_DELETED,
            'rental_id' => 0,
            'initiator_id' => $this->user->id,
            'related_model_id' => $this->payout->id,
            'related_model_type' => PartialPayout::class,
            'data' => $this->partial,
        ]);
    }
}
