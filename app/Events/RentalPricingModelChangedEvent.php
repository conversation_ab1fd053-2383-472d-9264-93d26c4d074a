<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\ProviderEvent;
use App\Models\Rental;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RentalPricingModelChangedEvent implements CreatesProviderEvents
{
    use Dispatchable;
    use SerializesModels;

    public function __construct(
        public Team $team,
        public int|iterable $rentalId,
        public ?User $initiator = null,
        public array $data = [],
    ) {
        $this->initiator = $initiator ?? request()->user();
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::RENTAL_UPDATED,
            'initiator_id' => $this->initiator?->id,
            'rental_id' => $this->rentalId,
            'related_model_id' => $this->rentalId,
            'related_model_type' => Rental::class,
            'data' => $this->data,
        ]);
    }
}
