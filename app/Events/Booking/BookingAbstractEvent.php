<?php

namespace App\Events\Booking;

use App\Contracts\CreatesProviderEvents;
use App\Models\Booking;
use App\Models\ProviderEvent;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BookingAbstractEvent implements CreatesProviderEvents
{
    use Dispatchable;
    use SerializesModels;

    public string $type;

    public function __construct(
        public Team $team,
        public int $bookingId,
        public ?User $user = null,
        public bool $triggersDownPayment = true,
    ) {
        $this->user = $user ?? request()->user();
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        $booking = Booking::getBookingModel($this->team, $this->bookingId);
        $data = ProviderEvent::bookingData($booking);

        return ProviderEvent::create([
            'team_id' => $booking->team_id,
            'type' => $this->type,
            'rental_id' => $data['rental_id'],
            'booking_id' => $booking->id,
            'initiator_id' => $this->user?->id,
            'related_model_id' => $booking->id,
            'related_model_type' => Booking::class,
            'data' => $data,
        ]);
    }
}
