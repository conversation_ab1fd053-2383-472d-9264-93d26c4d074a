<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\ProviderEvent;
use App\Models\Settlement;
use App\Models\Team;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StatementCreatedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public Team $team,
        public Settlement $settlement
    ) {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::STATEMENT_CREATED,
            'rental_id' => 0,
            'related_model_id' => $this->settlement->id,
            'related_model_type' => Settlement::class,
            'data' => [
                'name' => $this->settlement->name,
                'start' => $this->settlement->start->toDateString(),
                'end' => $this->settlement->end->toDateString(),
                'currency' => $this->settlement->currency,
                'author' => $this->settlement->author?->name,
            ],
        ]);
    }
}
