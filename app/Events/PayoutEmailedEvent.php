<?php

namespace App\Events;

use App\Models\Payout;
use App\Models\Settlement;
use App\Models\Team;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PayoutEmailedEvent
{
    use Dispatchable, SerializesModels;

    public Team $team;

    public Settlement $settlement;

    public Payout $payout;

    /**
     * Create a new event instance.
     */
    public function __construct(Team $team, Settlement $settlement, Payout $payout)
    {
        $this->team = $team;
        $this->settlement = $settlement;
        $this->payout = $payout;
    }
}
