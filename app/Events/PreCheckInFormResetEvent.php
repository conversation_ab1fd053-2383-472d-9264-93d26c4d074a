<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\PreCheckInForm;
use App\Models\ProviderEvent;
use App\Models\Team;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PreCheckInFormResetEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public PreCheckInForm $form,
        public int $bookingId,
        public int $clientId,
        public Team $team)
    {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::PRE_CHECK_IN_FORM_RESET,
            'rental_id' => $this->form->booking->rental_id,
            'booking_id' => $this->form->booking->id,
            'related_model_id' => $this->form->id,
            'related_model_type' => PreCheckInForm::class,
            'data' => [],
        ]);
    }
}
