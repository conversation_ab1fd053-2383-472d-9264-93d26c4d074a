<?php

namespace App\Events;

use App\Models\Task;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TaskRejectedEvent
{
    use Dispatchable, SerializesModels;

    /** @var \App\Models\Task */
    public $task;

    /** @var \App\Models\User */
    public $requester;

    /**
     * Create a new event instance.
     */
    public function __construct(Task $task, ?User $requester = null)
    {
        $this->task = $task;
        $this->requester = $requester;
    }
}
