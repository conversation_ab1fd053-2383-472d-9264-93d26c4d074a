<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\ProviderEvent;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PreCheckInEnabledEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(public Team $team, public ?int $rentalId = null)
    {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::PRE_CHECK_IN_ENABLED,
            'rental_id' => $this->rentalId ?? 0,
            'related_model_id' => is_null($this->rentalId) ? $this->team->id : $this->rentalId,
            'related_model_type' => is_null($this->rentalId) ? Team::class : Rental::class,
            'data' => [],
        ]);
    }
}
