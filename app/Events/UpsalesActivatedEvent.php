<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\ProviderEvent;
use App\Models\Rental;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UpsalesActivatedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(public Team $team, public int $rentalId, public User $user)
    {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::UPSALES_ACTIVATED,
            'rental_id' => $this->rentalId,
            'initiator_id' => $this->user->id,
            'related_model_id' => $this->rentalId,
            'related_model_type' => Rental::class,
            'data' => [],
        ]);
    }
}
