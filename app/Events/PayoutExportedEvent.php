<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\Payout;
use App\Models\ProviderEvent;
use App\Models\Team;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PayoutExportedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public Team $team,
        public Payout $payout)
    {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::PAYOUT_EXPORTED,
            'rental_id' => 0,
            'related_model_id' => $this->payout->id,
            'related_model_type' => Payout::class,
            'data' => $this->payout->toArray(),
        ]);
    }
}
