<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\ProviderEvent;
use App\Models\Rental;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class GuestAppDeactivatedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(public Team $team, public ?int $rentalId = null, public ?User $user = null)
    {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->team->id,
            'type' => ProviderEvent::GUEST_APP_DEACTIVATED,
            'rental_id' => $this->rentalId ?? 0,
            'initiator_id' => $this->user?->id,
            'related_model_id' => is_null($this->rentalId) ? $this->team->id : $this->rentalId,
            'related_model_type' => is_null($this->rentalId) ? Team::class : Rental::class,
            'data' => [],
        ]);
    }
}
