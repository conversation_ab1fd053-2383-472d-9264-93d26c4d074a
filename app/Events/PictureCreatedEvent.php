<?php

namespace App\Events;

use App\Contracts\CreatesProviderEvents;
use App\Models\NinjaPicture;
use App\Models\ProviderEvent;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PictureCreatedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public NinjaPicture $picture,
        public int $type)
    {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        $data = [
            'author' => $this->picture->author?->name,
            'description' => $this->picture->description,
            'payment_id' => $this->picture->payment_id,
            'rental_name' => $this->picture->rental_name,
            'full_size_url' => $this->picture->full_size_url,
            'medium_size_url' => $this->picture->medium_size_url,
            'thumbnail_size_url' => $this->picture->thumbnail_size_url,
        ];

        return ProviderEvent::create([
            'team_id' => $this->picture->team_id,
            'type' => ProviderEvent::PICTURE_CREATED,
            'data' => $data,
            'initiator_id' => $this->picture->author_id,
            'rental_id' => $this->picture->rental_id,
            'booking_id' => $this->picture->booking_id,
            'related_model_id' => $this->picture->id,
            'related_model_type' => NinjaPicture::class,
        ]);
    }

    public static function updateProviderEvent(ProviderEvent $event): void
    {
        if ($event->type != ProviderEvent::PICTURE_CREATED) {
            return;
        }

        $data = $event->data;
        $event->initiator_id = data_get($data, 'author_id');
        $event->data = [
            'author' => data_get($data, 'author'),
            'description' => data_get($data, 'description'),
            'payment_id' => data_get($data, 'payment_id'),
            'rental_name' => data_get($data, 'rental_name'),
            'medium_size_url' => data_get($data, 'medium_size_url'),
            'thumbnail_size_url' => data_get($data, 'thumbnail_size_url'),
            'full_size_url' => data_get($data, 'full_size_url'),
        ];
        $event->save();
    }
}
