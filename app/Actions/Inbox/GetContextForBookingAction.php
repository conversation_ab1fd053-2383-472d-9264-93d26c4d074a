<?php

namespace App\Actions\Inbox;

use App\Actions\Auth\VerifyUserHasAccessToBookingAction;
use App\Http\Resources\BookingContextResource;
use App\Models\Booking;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetContextForBookingAction
{
    use AsAction;

    public function asController(Team $team, Booking $booking, ActionRequest $request): BookingContextResource
    {
        VerifyUserHasAccessToBookingAction::run($booking);

        return $this->handle($booking);
    }

    public function handle(Booking $booking): BookingContextResource
    {
        $booking->load([
            'client',
            'source',
            'preCheckInForm',
            'bookingFees',
            'bookingTaxes',
            'rental.rentalFees',
            'rental.rentalFees.fee',
            'comments',
            'messages',
        ]);

        return new BookingContextResource($booking);
    }
}
