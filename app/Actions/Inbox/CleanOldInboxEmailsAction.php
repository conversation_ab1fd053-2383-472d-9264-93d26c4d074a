<?php

namespace App\Actions\Inbox;

use App\Actions\Storage\GetStorageDirectoryAction;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Lorisleiva\Actions\Concerns\AsAction;

class CleanOldInboxEmailsAction
{
    use AsAction;

    const SIGNATURE = 'ninja:clean-inbox-emails';

    public string $commandSignature = self::SIGNATURE;

    public function asCommand(Command $command): int
    {
        $this->handle();

        return $command::SUCCESS;
    }

    public function handle(): void
    {
        $baseDir = GetStorageDirectoryAction::run(config('ninja.inbox_path'));

        foreach (Storage::disk('s3')->allFiles($baseDir.config('ninja.inbox_to_delete_sub_path')) as $toDelete) {
            $lastModified = Carbon::createFromTimestampUTC(Storage::disk('s3')->lastModified($toDelete));
            if ($lastModified->diffInDays(now(), true) > (30 * 3)) {
                Storage::disk('s3')->delete($toDelete);
            }
        }
    }
}
