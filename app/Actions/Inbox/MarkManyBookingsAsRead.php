<?php

namespace App\Actions\Inbox;

use App\Models\Booking;
use App\Models\Team;
use App\Models\User;
use Auth;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class MarkManyBookingsAsRead
{
    use AsAction;

    public function rules(): array
    {
        return [
            'ids' => 'array|required',
        ];
    }

    public function asController(Team $team, ActionRequest $request)
    {
        $this->handle($team, $request->input('ids'), Auth::user());

        return response()->json();
    }

    public function handle(Team $team, array $bookingIds, User $user): bool
    {
        Booking::query()
            ->withoutGlobalScopes()
            ->whereId($bookingIds)
            ->onTeam($team)
            ->with('bookingUsers')
            ->get()
            ->each(fn (Booking $booking) => MarkBookingAsReadOrArchiveAction::run($booking, true, $user));

        return true;
    }
}
