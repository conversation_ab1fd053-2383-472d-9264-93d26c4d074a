<?php

namespace App\Actions\BookingTags;

use App\DataProviders\ApiConnectors\Interfaces\BookingTagsApi;
use App\Http\Resources\BookingTagResource;
use App\Models\BookingTag;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateBookingTagAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'name' => 'required',
        ];
    }

    public function asController(Team $team, ActionRequest $request): BookingTagResource
    {
        $request->validated();
        $bookingTag = $this->handle(
            team: $team,
            name: $request->get('name'),
            color: $request->get('color'),
            logo: $request->get('logo')
        );

        return new BookingTagResource($bookingTag);
    }

    public function handle(Team $team, string|array $name, ?string $color, ?string $logo): BookingTag
    {
        // Booking tags are not rental related. We cannot sync tags among providers.
        $connector = $team->getMainProviderConnector();
        if ($connector instanceof BookingTagsApi) {
            $id = $connector->createBookingTag($name, $color, $logo);
        } else {
            $id = (BookingTag::query()
                    ->withTrashed()
                    ->whereTeamId($team->id)
                    ->orderBy('id', 'desc')
                    ->first()?->id ?? 0) + 1;
        }

        BookingTag::create([
            'id' => $id,
            'team_id' => $team->id,
            'name' => $name,
            'color' => $color,
            'logo' => $logo,
            'created_at' => now()->timestamp,
            'updated_at' => now()->timestamp,
        ]);

        return BookingTag::find($id);
    }
}
