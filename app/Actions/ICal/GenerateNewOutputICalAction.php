<?php

namespace App\Actions\ICal;

use App\Http\Resources\ICalOutputResource;
use App\Models\ICalOutput;
use App\Models\Rental;
use App\Models\Source;
use App\Models\Team;
use App\Rules\ExistsInTeamRule;
use Illuminate\Support\Str;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateNewOutputICalAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'name' => ['required', 'string'],
            'source_id' => ['nullable', new ExistsInTeamRule(model:Source::class, except: 0)],
        ];
    }

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): ICalOutputResource
    {
        $name = $request->input('name');
        $sourceId = $request->input('source_id');

        $ical = $this->handle($teamRental, $name, $sourceId);

        return new ICalOutputResource($ical);
    }

    public function handle(Rental $rental, string $name, ?int $sourceId): ICalOutput
    {
        $uuid = Str::uuid();

        return ICalOutput::create([
            'team_id' => $rental->team_id,
            'rental_id' => $rental->id,
            'name' => $name,
            'passcode' => $uuid,
            'source_id' => $sourceId,
        ]);
    }
}
