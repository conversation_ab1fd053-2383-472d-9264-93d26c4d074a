<?php

namespace App\Actions\ICal;

use App\Models\Booking;
use App\Models\ICalInput;
use App\Models\ICalOutput;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class RemoveICalAction
{
    use AsAction;

    public function asController(ActionRequest $request, Team $team, Rental $teamRental, ?ICalInput $iCalInput = null, ?ICalOutput $iCalOutput = null): ?bool
    {
        abort_if(is_null($iCalInput) && is_null($iCalOutput), 404, 'No ical found');
        $ical = $iCalInput ?: $iCalOutput;
        $delete = $request->boolean('delete');

        return $this->handle($ical, $delete);
    }

    public function handle(ICalInput|ICalOutput $iCal, bool $delete = false): ?bool
    {
        if ($iCal instanceof ICalInput && $delete) {
            $this->deleteFutureBookings($iCal);
        }

        return $iCal->delete();
    }

    private function deleteFutureBookings(ICalInput $iCal): void
    {
        $iCal->bookings()
            ->where('start_at', '>', now()->endOfDay()->timestamp)
            ->each(function (Booking $b) {
                $b->alerts()->delete();
                $b->bookingFees()->delete();
                $b->bookingTaxes()->delete();
                $b->comments()->delete();
                $b->preCheckInForm?->passports()->delete();
                $b->preCheckInForm()->delete();
                $b->tasksWithTrashed()->forceDelete();
                $b->bookingPayments()->delete();
                $b->delete();
            });
    }
}
