<?php

namespace App\Actions\ICal;

use App\Actions\Bookings\GenerateBookingReferenceAction;
use App\Actions\ICal\ICalParser\ICalParser;
use App\Actions\Tasks\Cleanup\CleanupCancelledBookingTasksAction;
use App\Actions\Tasks\ScheduledTask\HandleTasksForBookingAction;
use App\DataProviders\Providers\NoProvider;
use App\DTO\Bookings\ICalBookingData;
use App\Enum\BookingStatusEnum;
use App\Events\Booking\BookingCancelledEvent;
use App\Events\Booking\BookingConfirmedEvent;
use App\Http\Resources\ICalInputResource;
use App\Models\Booking;
use App\Models\ICalInput;
use App\Models\Rental;
use App\Models\Team;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class SyncRentalICalAction
{
    use AsAction;

    public string $jobQueue = 'cron-sync';

    public string $commandSignature = 'ninja-dev:sync-rental-ical {ical} {--force}';

    public int $jobMaxExceptions = 1;

    public function asJob(ICalInput $iCal, bool $force = false): void
    {
        // Check if rental is still active.
        if (is_null($iCal->rental)) {
            return;
        }
        $this->handle($iCal->team, $iCal->rental, $iCal, $force);
    }

    public function asCommand(Command $command): int
    {
        $inputIcalId = $command->argument('ical');
        $force = $command->option('force');

        $ical = ICalInput::query()->findOrFail($inputIcalId);
        $team = $ical->team;
        $rental = $ical->rental;

        if (! $rental) {
            $command->error('Rental is deleted');

            return $command::FAILURE;
        }

        $ical = $this->handle($team, $rental, $ical, $force);

        if ($ical) {
            return $command::SUCCESS;
        }

        return $command::FAILURE;
    }

    public function asController(Team $team, Rental $teamRental, ICalInput $iCalInput): ICalInputResource
    {
        abort_unless($teamRental->canUseInputIcal(), 401, 'Unauthorized');

        // User triggered synchronizations will be force synchronizations, to look for duplicates with existing bookings.
        $iCalInput = $this->handle($team, $teamRental, $iCalInput, true);
        abort_if(is_null($iCalInput), 400, 'iCal cannot be synchronized');

        return new ICalInputResource($iCalInput);
    }

    public function handle(Team $team, Rental $rental, ICalInput $iCal, bool $force = false): ?ICalInput
    {
        if (empty($iCal->url)) {
            return null;
        }
        $parser = ICalParser::getParser($iCal->url);
        $importedBookings = $parser->getBookings();
        $usesUIDs = $parser->usesUIDs();
        if (is_null($importedBookings)) {
            return null;
        }

        $this->syncBookings($rental, $iCal, $importedBookings, $usesUIDs, $force);
        $iCal->last_synced = now();
        $iCal->save();

        $rental->updateAvailability();

        return $iCal;
    }

    private function syncBookings(Rental $rental, ICalInput $iCal, Collection $iCalBookings, bool $usesUIDs, bool $force): void
    {
        $team = $iCal->team;
        $teamId = $iCal->team_id;
        $rentalId = $iCal->rental_id;
        $sourceId = $iCal->source_id;
        $iCalId = $iCal->id;

        if ($force) {
            $this->migrateProviderSourceBookings($teamId, $rentalId, $sourceId, $iCalId, $iCalBookings);
        }

        // Fetch existing bookings.
        $firstBookingEnd = $iCalBookings->first()?->endDay?->startOfDay()->timestamp; // First received booking
        $today = Carbon::today()->startOfDay()->timestamp; // Future bookings
        $bookingsFrom = is_null($firstBookingEnd) || $firstBookingEnd > $today ? $today : $firstBookingEnd;

        $databaseBookings = Booking::query()
            ->onTeam($teamId)
            ->whereRentalId($rentalId)
            ->whereCanceledAt(0)
            ->where('end_at', '>=', $bookingsFrom)
            ->where('rental_ical_id', '=', $iCalId)
            ->where('provider_id', '=', NoProvider::ID)
            ->get();

        /** @var ICalBookingData $iCalBooking */
        foreach ($iCalBookings as $iCalBooking) {
            // Ignore bookings that start and end on the same day.
            if ($iCalBooking->startDay->isSameDay($iCalBooking->endDay)) {
                continue;
            }
            // Some providers include cancelled bookings in the ical response.
            $canceled_at = $iCalBooking->status == BookingStatusEnum::CANCELED ? now()->timestamp : 0;

            // Get the existing booking or get a new one with basic attributes.
            // If the booking is found, it is removed from $databaseBookings. Any bookings not matched will be removed.
            $booking = $this->findBooking($databaseBookings, $iCalBooking, $usesUIDs);

            // Before creating a new booking, check if there are overbooking.
            if (is_null($booking)) {
                $overbookingCount = Booking::query()
                    ->onTeam($teamId)
                    ->whereRentalId($rentalId)
                    ->filledBetween($iCalBooking->startDay, $iCalBooking->endDay)
                    ->count();

                if ($overbookingCount > 3) {
                    $st = $iCalBooking->startDay->toDateString();
                    $end = $iCalBooking->endDay->toDateString();
                    pnLog("[SyncRentalICalAction] Overbooking detected in rental $rentalId ($st-$end)", $team);

                    throw new Exception('Too many overbooking when creating a booking');
                }

                $booking = new Booking([
                    'id' => Booking::getRandomId($teamId),
                    'team_id' => $teamId,
                    'rental_id' => $rentalId,
                    'rental_ical_id' => $iCal->id,
                    'provider_id' => NoProvider::ID,
                    'external_id' => $iCalBooking->externalId,
                ]);
            }

            // If booking is new or future, use received start date. If booking has started, use the earliest start date.
            if (is_null($booking->start_at) || $iCalBooking->startDay->isAfter(now()->endOfDay())) {
                $startAt = $iCalBooking->startDay->timestamp;
            } else {
                $startAt = min($booking->start_at, $iCalBooking->startDay->timestamp);
            }
            $booking->external_id = $iCalBooking->externalId;
            $booking->channel_id = $iCalBooking->channelId;
            $booking->start_at = $startAt;
            $booking->end_at = $iCalBooking->endDay->timestamp;
            $booking->nights = $iCalBooking->endDay->diffInDays($iCalBooking->startDay->startOfDay());
            $booking->status = $iCalBooking->status;
            $booking->created_at = $booking->created_at ?? now()->timestamp;
            $booking->updated_at = now()->timestamp;
            $booking->notes = $iCalBooking->description;
            $booking->currency = $rental->currency;
            $booking->locked = 0;
            $booking->source_id = $sourceId;
            $booking->canceled_at = $canceled_at;
            $booking->reference = $booking->reference ?? GenerateBookingReferenceAction::run();

            $generateTasks = $booking->isDirty(['start_at', 'end_at']);
            $booking->save();

            if ($booking->wasRecentlyCreated) {
                event(new BookingConfirmedEvent($team, $booking->id));
            }

            if ($generateTasks) {
                HandleTasksForBookingAction::run($team, $booking);
            }
        }

        $this->processCanceledBookings($team, $databaseBookings);
    }

    protected function processCanceledBookings(Team $team, Collection $notReceivedBookings): void
    {
        $notReceivedBookings
            // Current bookings are not being deleted, since they can be ignored from the ical in the last day.
            ->where('start_at', '>', now()->startOfDay()->timestamp)
            ->each(function (Booking $b) use ($team) {
                $b->status = BookingStatusEnum::CANCELED;
                $b->canceled_at = now()->timestamp;
                $b->save();

                CleanupCancelledBookingTasksAction::run($team);

                event(new BookingCancelledEvent($team, $b->id));
            });
    }

    private function migrateProviderSourceBookings(int $teamId, int $rentalId, int $sourceId, int $iCalId, Collection $iCalBookings): void
    {
        $bookings = Booking::query()
            ->onTeam($teamId)
            ->whereRentalId($rentalId)
            ->whereSourceId($sourceId)
            ->where('end_at', '>=', Carbon::today()->timestamp)
            ->whereNull('rental_ical_id')
            ->get();

        if ($bookings->isEmpty()) {
            return;
        }

        foreach ($iCalBookings as $iCalBooking) {
            $migratedBooking = null;
            // If ical providers the channel identifier, try to use it.
            if (! is_null($iCalBooking->channelId)) {
                $migratedBooking = $bookings
                    ->where('channel_id', '=', $iCalBooking->channelId)
                    ->where('provider_id', '!=', NoProvider::ID)
                    ->first();
            }
            // Otherwise, search for duplicates using start and end dates.
            else {
                $migratedBooking = $bookings
                    ->where('start_at', '>=', $iCalBooking->startDay->startOfDay()->timestamp)
                    ->where('start_at', '<=', $iCalBooking->startDay->endOfDay()->timestamp)
                    ->where('end_at', '>=', $iCalBooking->endDay->startOfDay()->timestamp)
                    ->where('end_at', '<=', $iCalBooking->endDay->endOfDay()->timestamp)
                    ->first();
            }

            // If we find a booking that matches an imported booking, update its information to migrated it as an ical booking.
            if ($migratedBooking) {
                $migratedBooking->external_id = $iCalBooking->externalId;
                $migratedBooking->team_id = $teamId;
                $migratedBooking->provider_id = NoProvider::ID;
                $migratedBooking->rental_id = $rentalId;
                $migratedBooking->rental_ical_id = $iCalId;
                $migratedBooking->save();
            }
        }
    }

    private function findBooking(\Illuminate\Database\Eloquent\Collection|Collection &$databaseBookings, ICalBookingData $iCalBooking, bool $usesUIDs): ?Booking
    {
        if ($usesUIDs) {
            // Find booking by uuid
            $booking = $databaseBookings->firstWhere('external_id', $iCalBooking->externalId);
        } else {
            $booking = $this->findBookingByDate($iCalBooking, $databaseBookings);
        }
        if (! $booking) {
            $booking = $this->findBookingOverlapSameIcal($iCalBooking, $databaseBookings);
        }
        if ($booking) {
            $databaseBookings = $databaseBookings->where('id', '!=', $booking->id);
        }

        return $booking;
    }

    protected function findBookingByDate(ICalBookingData $iCalBooking, \Illuminate\Database\Eloquent\Collection|Collection $databaseBookings)
    {
        $startDay = $iCalBooking->startDay->copy();
        $endDay = $iCalBooking->endDay->copy();
        // If booking started some days ago, some providers does not send the real start day, and send instead current date.
        if ($iCalBooking->startDay->isBefore(now()->endOfDay()->addHours(4))) {
            $booking = $databaseBookings
                ->where('start_at', '<=', $startDay->endOfDay()->timestamp)
                ->where('end_at', '>=', $endDay->startOfDay()->timestamp)
                ->where('end_at', '<=', $endDay->endOfDay()->timestamp)
                ->first();
        } else {
            $booking = $databaseBookings
                ->where('start_at', '>=', $startDay->startOfDay()->timestamp)
                ->where('start_at', '<=', $startDay->endOfDay()->timestamp)
                ->where('end_at', '>=', $endDay->startOfDay()->timestamp)
                ->where('end_at', '<=', $endDay->endOfDay()->timestamp)
                ->first();
        }

        return $booking;
    }

    private function findBookingOverlapSameIcal(ICalBookingData $iCalBooking, \Illuminate\Database\Eloquent\Collection|Collection &$databaseBookings): ?Booking
    {
        if ($databaseBookings->isEmpty()) {
            return null;
        }

        $startDay = $iCalBooking->startDay->copy();
        $endDay = $iCalBooking->endDay->copy();

        $endOverlap = $databaseBookings
            ->where('status', '=', BookingStatusEnum::BOOKED)
            ->where('end_at', '>=', $startDay->endOfDay()->timestamp)
            ->where('end_at', '<', $endDay->startOfDay()->timestamp);

        $startOverlap = $databaseBookings
            ->where('status', '=', BookingStatusEnum::BOOKED)
            ->where('start_at', '>', $startDay->endOfDay()->timestamp)
            ->where('start_at', '<=', $endDay->startOfDay()->timestamp);

        $overlaps = $endOverlap->merge($startOverlap);

        /** @var Booking $match */
        $match = $overlaps->pop();

        if ($overlaps->isNotEmpty()) {
            $databaseBookings = $databaseBookings->whereNotIn('id', $overlaps->pluck('id'));
            $stringIds = implode(',', $overlaps->pluck('id')->toArray());
            pnLog("[SyncRentalICalAction] Overlapping bookings ($stringIds) found in rental $match->rental_id from {$startDay->toDateString()} to {$endDay->toDateString()}.", $match->team_id);
            $team = $match->team;
            $overlaps->each(function (Booking $b) use ($team) {
                $b->status = BookingStatusEnum::CANCELED;
                $b->canceled_at = now()->timestamp;
                $b->save();

                CleanupCancelledBookingTasksAction::run($team);

                event(new BookingCancelledEvent($team, $b->id));
            });
        }

        return $match;
    }
}
