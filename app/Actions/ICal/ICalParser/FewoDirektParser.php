<?php

namespace App\Actions\ICal\ICalParser;

use App\Enum\BookingStatusEnum;
use Illuminate\Support\Str;
use Illuminate\Support\Stringable;

class FewoDirektParser extends ICalParser
{
    protected function parseStatus(Stringable $bookingData): BookingStatusEnum
    {
        $summary = $bookingData->after('SUMMARY:')->before(PHP_EOL);
        if (Str::of($summary)->contains('Geblockt', true)) {
            return BookingStatusEnum::UNAVAILABLE;
        }

        return BookingStatusEnum::BOOKED;
    }

    protected function parseDescription(Stringable $bookingData): ?string
    {
        return $bookingData->after('SUMMARY:')->after('Belegt - ')->before(PHP_EOL);
    }
}
