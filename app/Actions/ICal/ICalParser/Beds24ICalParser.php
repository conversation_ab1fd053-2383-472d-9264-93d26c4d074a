<?php

namespace App\Actions\ICal\ICalParser;

use App\Enum\BookingStatusEnum;
use Illuminate\Support\Str;
use Illuminate\Support\Stringable;

class Beds24ICalParser extends ICalParser
{
    protected function parseBookingId(Stringable $bookingData): ?string
    {
        return $bookingData->after('beds24.com/control.php?bookid=')->before(PHP_EOL);
    }

    protected function parseStatus(Stringable $bookingData): BookingStatusEnum
    {
        $summary = $bookingData->after('SUMMARY:')->before(PHP_EOL);
        if (Str::of($summary)->contains(['not available', 'unavailable'], true)) {
            return BookingStatusEnum::UNAVAILABLE;
        }
        if (Str::of($summary)->contains(['canceled'], true)) {
            return BookingStatusEnum::CANCELED;
        }

        return BookingStatusEnum::BOOKED;
    }

    protected function parseDescription(Stringable $bookingData): ?string
    {
        return $bookingData->after('URL;VALUE=URI:')->before(PHP_EOL);
    }
}
