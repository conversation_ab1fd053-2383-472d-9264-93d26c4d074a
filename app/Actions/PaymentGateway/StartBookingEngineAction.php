<?php

namespace App\Actions\PaymentGateway;

use App\Actions\Bookings\StoreBookingAction;
use App\Actions\Clients\StoreClientAction;
use App\Enum\BookingStatusEnum;
use App\Exceptions\NinjaBookingException;
use App\Models\Rental;
use App\Models\Team;
use Exception;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class StartBookingEngineAction
{
    use AsAction;

    private const UNAVAILABLE_MESSAGE = 'The rental you selected is no longer available. Try refreshing the page and try again.';
    private const MIN_STAY_MESSAGE = 'The booking does not meet the minimum stay requirement of XXX nights';
    private const MAX_STAY_MESSAGE = 'The booking does not meet the max stay requirement of XXX nights';

    public function handle(Team $team, Rental $rental, Carbon $from, Carbon $to, int $guests, int $sourceId, string $currentUrl, string $locale, float $markup): array
    {
        // Create a request to book. Do not trust any request data (may be faked). Calculate everything here from scratch
        // TODO: we should get the request's timezone to be able to know how many days in advance from check-in for last-minute discount purposes

        try {
            $booking = StoreBookingAction::run($team, $rental, $from, $to, BookingStatusEnum::WEBSITE, $guests, null, sourceId: $sourceId, markup: $markup);
        } catch (NinjaBookingException $exception) {
            if ($exception->type == NinjaBookingException::UNAVAILABLE) {
                $message = self::UNAVAILABLE_MESSAGE;
            } elseif ($exception->type == NinjaBookingException::MAX_STAY) {
                $message = str_replace('XXX', $exception->extra, self::MAX_STAY_MESSAGE);
            } else {
                $message = str_replace('XXX', $exception->extra, self::MIN_STAY_MESSAGE);
            }
            $response['is_error'] = true;
            $response['is_range_error'] = true;
            $response['result'] = $message;

            return $response;
        }

        $isValidBooking = VerifyGatewayCanBeUsedAction::run($team, $booking);
        if (is_string($isValidBooking)) {
            $response['is_error'] = true;
            $response['result'] = $isValidBooking;

            return $response;
        }

        // Create client and give it basic info: an empty client gives visualisation problems in the clients view
        $client = StoreClientAction::run($team, 'Booking Engine Client', notes: __('teams.clients.booking_engine.pending_client', locale: $team->getTeamLocale()), locale: $locale);
        $booking->client_id = $client->id;

        $booking->commission = $booking->calculateBookingEngineCommissionAmount($team->teamSettings);
        $booking->save();

        // Get Stripe Checkout URL
        $currentUrl = strtok($currentUrl, '?'); // Remove query params
        $successUrl = $currentUrl.'?success=true'.'&reference='.$booking->reference.'&value='.$booking->final_rental_price.'&currency='.$booking->currency;
        $cancelUrl = $currentUrl.'?start='.$from->toDateString().'&end='.$to->toDateString().'&cancel=true';

        if (! empty($booking->downpayment)) {
            $amount = $booking->downpayment;
            $isDownpayment = true;
        } else {
            $amount = $booking->final_price;
            $isDownpayment = false;
        }
        $amountInCents = ninjaIntval(round($amount, 2) * 100);
        if ($amountInCents <= 0) {
            report(new Exception('For some reason, we are trying to pass our Booking Engine a negative amount to be charged. This must be an error.'));

            return [
                'is_error' => true,
                'result' => 'Wrong amount. For some reason, the amount we calculated is zero or negative. This must be an error. Contact your host.',
            ];
        }

        $productDescription = $isDownpayment ? $team->stripeConnect()->getDownpaymentProductDescription($booking) : $team->stripeConnect()->getBookingProductDescription($booking);
        $metadata = $team->stripeConnect()->getBookingEngineMetadata($booking);
        $extra = $team->stripeConnect()->getExtraParamsForPreAuthorization();
        $extra['phone_number_collection'] = ['enabled' => true]; // https://docs.stripe.com/payments/checkout/phone-numbers;

        try {
            $response = ['is_error' => false];
            $response['result'] = $team->stripeConnect()->getCheckoutSessionUrl($booking, $amountInCents, $productDescription, $successUrl, $cancelUrl, $metadata, $extra);

            return $response;
        } catch (Exception $e) {
            ReportCheckoutSessionErrorAction::run($e);

            return [
                'is_error' => true,
                'result' => $e->getMessage().'. Contact your Host and inform him about this error message.',
            ];
        }
    }
}
