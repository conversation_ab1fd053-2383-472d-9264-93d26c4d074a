<?php

namespace App\Actions\PaymentGateway;

use App\DTO\PaymentGatewayAccount\PaymentGatewayAccountData;
use App\Models\PaymentGatewayAccount;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\PaginatedDataCollection;

class GetAllPaymentGatewayAccountsAction
{
    use AsAction;

    public function asController(Team $team): PaginatedDataCollection
    {
        return PaymentGatewayAccountData::collection($this->handle($team)->paginate(50));
    }

    public function handle(Team $team): Builder
    {
        return PaymentGatewayAccount::query()
            ->where('team_id', $team->id);
    }
}
