<?php

namespace App\Actions\PaymentGateway;

use App\Actions\Auth\VerifyRequestResourceIsFromSameTeamAction;
use App\Actions\PaymentGateway\Stripe\DeauthorizeStripeConnectAction;
use App\Models\PaymentGatewayAccount;
use App\Models\Team;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\Concerns\AsAction;

class DeletePaymentGatewayAccountAction
{
    use AsAction;

    public function asController(Team $team, PaymentGatewayAccount $paymentGatewayAccount): JsonResponse
    {
        VerifyRequestResourceIsFromSameTeamAction::run($team, $paymentGatewayAccount);
        $this->handle($team, $paymentGatewayAccount);

        return response()->json();
    }

    public function handle(Team $team, PaymentGatewayAccount $paymentGatewayAccount): void
    {
        if ($paymentGatewayAccount->isConnected()) {
            DeauthorizeStripeConnectAction::run($team, $paymentGatewayAccount);
        }

        $paymentGatewayAccount->delete();
    }
}
