<?php

namespace App\Actions\PaymentGateway;

use App\Models\Booking;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class VerifyGatewayCanBeUsedAction
{
    use AsAction;

    /** Returns a string when Gateway can't be used and the string contains the reason */
    public function handle(Team $team, Booking $booking): true|string
    {
        if (! $team->config()->ninjaPaymentGatewayEnabled()) {
            return 'We are sorry, this functionality is not enabled for your host. Contact your host.';
        }

        if ($booking->rental === null || $booking->rental->deleted_at !== null || ! $team->isSubscribed() || ! $team->gatewayActivated($booking->rental_id)) {
            return 'We are sorry, the payment gateway is no longer active for this Rental. Contact your host.';
        }

        if ($booking->isUnavailable() || empty($booking->final_price)) {
            return "We are sorry but the booking you are trying to pay doesn't have any price value. Contact your host.";
            // Otherwise, guest will be charged and a booking payment will be created, which I think could cause problems (not a use case handled)
        }

        $disabledSources = $team->findPaymentGateway($booking->rental_id)->disabled_sources;
        if (! empty($disabledSources) && in_array($booking->source_id, $disabledSources)) {
            return "You can't use the Payment Gateway for the Source of your Booking";
            // The guest should never have the link, so this is not common to be reached. Thus, no need for translation.
        }

        if ($booking->currency === null || strlen($booking->currency) != 3) {
            return 'Booking currency. It must be an ISO 4217 currency code. Contact your host.';
            // This is a requirement because stripe needs that currency format
        }

        return true;
    }
}
