<?php

namespace App\Actions\PaymentGateway;

use App\Actions\Support\Location\GetRequestTimeZoneAction;
use App\Actions\Support\Location\GetTimeZoneFromCoordinatesAction;
use App\Models\Booking;
use App\Models\Rental;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class VerifyGatewayDepositCollectionWindowOpenAction
{
    use AsAction;

    const DD_COLLECTION_START_FROM_CI = 1; // Days

    public function handle(Booking $booking, ?Rental $rental = null): bool
    {
        if (! $rental) {
            $rental = $booking->rental;
        }

        // #Get the timezone
        $timeZone = null;
        if (! empty($rental->lat) && ! empty($rental->lng) && ! empty($rental->country_code)) {
            $timeZone = $rental->timezone;
        }

        if (! $timeZone) {
            $timeZone = GetRequestTimeZoneAction::run();
        }

        if (! $timeZone) {
            $request = request(); // In case we have a request, we assume the guest is within the same TZ as the rental
            if ($request) {
                $timeZone = GetTimeZoneFromCoordinatesAction::run(
                    $request->header('cf-iplatitude'),
                    $request->header('cf-iplongitude'),
                    $request->header('cf-ipcountry')
                );
            }
        }

        if (! $timeZone) { // We may enter here only in case no lat and long, we may have just the country code.
            $timeZone = $rental->timezone;
        }

        // #Use the timezone to find-out if we are in the window
        $checkInTime = $booking->getCarbonCheckInTime();
        $checkOutTime = $booking->getCarbonCheckOutTime();

        // 1- If we have timezone, we allow only the same day
        if ($timeZone != null) {
            $localTime = Carbon::now($timeZone);

            return $localTime->isAfter($checkInTime->copy()->shiftTimezone($timeZone)->startOfDay()) &&
                $localTime->isBefore($checkOutTime->copy()->shiftTimezone($timeZone)->endOfDay());
        }

        // 2- Fallback when no timezone: we allow from the day before
        return now()->isAfter($checkInTime->copy()->subDays(self::DD_COLLECTION_START_FROM_CI))
            && now()->isBefore($checkOutTime->copy()->addDay());
    }
}
