<?php

namespace App\Actions\PaymentGateway\Stripe;

use App\Actions\BillableAddons\BillAddonAction;
use App\Actions\Bookings\Fees\AddBookingFeesAction;
use App\Actions\Bookings\Payment\AddBookingPaymentAction;
use App\Actions\Bookings\Payment\CancelBookingPaymentAction;
use App\Actions\Clients\StoreClientAction;
use App\Actions\Clients\UpdateClientAction;
use App\Actions\Guests\Upsales\GetBookingFeeDataForUpsaleAction;
use App\Actions\Notifications\SendBookingNotificationAction;
use App\Actions\PaymentGateway\CreateReturnDamageDepositTaskAction;
use App\Actions\PaymentGateway\HandleBookingEngineCheckoutSessionAction;
use App\Actions\Support\Currencies\ConvertCurrencyToEuroAction;
use App\Actions\Users\TouchTeamUsersAction;
use App\DTO\Bookings\PaymentData;
use App\Enum\BillableAddonTypeEnum;
use App\Enum\NotificationTypeEnum;
use App\Events\GuestNotificationSentEvent;
use App\Events\UpsalePurchasedEvent;
use App\Exceptions\NinjaAddContextException;
use App\Mail\GuestNotifications\BookingRejectedEmail;
use App\Mail\GuestNotifications\UpsalePurchasedEmail;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Fee;
use App\Models\PaymentGatewayAccount;
use App\Models\Team;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Stripe;
use Stripe\WebhookSignature;
use Symfony\Component\HttpFoundation\Response;

class HandleStripeConnectWebhookAction
{
    use AsAction;

    public function asController(Request $request)
    {
        // Note: Based on Laravel\Cashier\Http\Controllers\WebhookController
        $requestRawContent = $request->getContent();
        $payload = json_decode($requestRawContent, true);

        if ((isProduction() && ! $payload['livemode']) || (isNotProduction() && $payload['livemode'])) {
            // According to this documentation, production can receive webhooks from test: https://stripe.com/docs/connect/webhooks
            return new Response('This webhook is not for this environment', 200); // If you return other than 200, stripe will try to retry it
        }

        // Verify the request is from stripe and un-changed using signature
        if (config('ninja-stripe.connect.webhook.secret')) {
            try {
                WebhookSignature::verifyHeader(
                    $requestRawContent,
                    $request->header('Stripe-Signature'),
                    config('ninja-stripe.connect.webhook.secret'),
                    config('ninja-stripe.connect.webhook.tolerance')
                );
            } catch (SignatureVerificationException $exception) {
                abort(401, 'Unauthorized');
                // This response won't be for stripe, and if it is we must be warned by the logs or the email they sent when multiple webhooks fail
            }
        }

        // Note about responses. Any response to stripe without 2xx code will be retried. So only respond without 2xx when:
        // 1- We need the webhook to be retried
        // 2- This is an error we should fix. Stripe will warn us by email if multiple webhooks fail: https://stripe.com/docs/webhooks#disable
        switch ($payload['type']) {
            case 'account.updated':
                $this->setMaxNetworkRetries();

                return $this->accountUpdated($payload);
            case 'checkout.session.completed':
                $this->setMaxNetworkRetries();

                return $this->checkoutSessionCompleted($payload);
            case 'account.application.deauthorized':
                $this->setMaxNetworkRetries();

                return $this->handleAccountApplicationDeauthorized($payload);
            case 'charge.refunded':
                $this->setMaxNetworkRetries();

                return $this->handleChargeRefunded($payload);
            case 'payment_intent.canceled':
                $this->setMaxNetworkRetries();

                return $this->handlePaymentIntentCanceled($payload);
            case 'charge.captured':
                $this->setMaxNetworkRetries();

                return $this->handleChargeCaptured($payload);
            default:
                return new Response('Webhook not handled', 404);
        }
    }

    protected function accountUpdated(array $payload): Response
    {
        // We could receive this webhook for cases where charges_enabled turns from false to true
        $account = PaymentGatewayAccount::query()
            ->where('gateway_account_id', $payload['account'])
            ->first();

        if (! $account) { // Staging may receive webhooks from tests in local envs
            return new Response('Webhook Handled', 200);
        }

        $account->charges_enabled = $payload['data']['object']['charges_enabled'];
        $account->save();

        TouchTeamUsersAction::run($account->team); // Needed to ensure TeamFullResource is updated (such info is cached in /me endpoint)

        return new Response('Webhook Handled', 200);
    }

    protected function handleAccountApplicationDeauthorized(array $payload): Response
    {
        $account = PaymentGatewayAccount::query()
            ->where('gateway_account_id', $payload['account'])
            ->first();

        if (! $account) { // Staging may receive webhooks from tests in local envs
            return new Response('Webhook Handled', 200);
        }

        $account->gateway_account_id = null;
        $account->charges_enabled = false;
        $account->save();

        TouchTeamUsersAction::run($account->team); // Needed to ensure TeamFullResource is updated (such info is cached in /me endpoint)

        return new Response('Webhook Handled', 200);
    }

    protected function checkoutSessionCompleted(array $payload): Response
    {
        $checkoutSession = $payload['data']['object'];

        try {
            // Somebody may have initiated a checkout session which doesn't come from us.
            if (! array_key_exists('booking_reference', $checkoutSession['metadata'])) {
                return new Response('Webhook Handled', 200);
            }

            $team = PaymentGatewayAccount::query()
                ->where('gateway_account_id', $payload['account'])
                ->with('team')
                ->first()?->team;

            if (! $team) { // Staging may receive webhooks from local env tests while not finding a team here
                return new Response('Webhook Handled', 200);
            }

            $booking = Booking::query()
                ->withoutGlobalScopes()
                ->onTeam($team)
                ->with(['client', 'bookingFees'])
                ->whereReference($checkoutSession['metadata']['booking_reference'])
                ->first();

            if (! $booking) {
                if (isProduction()) { // Staging may receive webhooks made from local envs, from bookings staging doesn't have
                    report(new NinjaAddContextException(
                        team: $team,
                        context: $payload,
                        message: "[Stripe Connect] Soft Warning: Somebody is using the same metadata key to identify a booking in a checkout session, but we couldn't find such booking in our DB. Could be from a deleted rental in RN but still active outside?",
                    ));
                }

                return new Response('Webhook Handled', 200);
                // Weird, but somebody may have be using the same key name booking_reference but we don't manage such booking
            }

            if (strtolower($checkoutSession['currency']) !== strtolower($booking->currency)) {
                throw new Exception("Checkout session completed webhook: currency received doesn't match with booking currency.", 500);
            }

            $checkoutEmail = $this->handleClient($team, $booking, $checkoutSession);

            if ($this->isBookingEngineSession($checkoutSession)) {
                // Take it async and respond stripe with 200 soon. There, we will ensure we block availability and notify the guest about success or failure. Also handle retries correctly.
                HandleBookingEngineCheckoutSessionAction::dispatchIf(! $booking->isBooked(), $team, $booking->id, $checkoutSession);
            } elseif ($this->isUpsalesSession($checkoutSession)) {
                $this->handleUpsalesSession($team, $booking, $checkoutSession, $checkoutEmail);
            } elseif ($this->isNormalPaymentOrDDSession($checkoutSession)) {
                // Save the booking payment and include the reference to the stripe payment
                $this->handleExistingBookingCheckoutSession($team, $booking, $checkoutSession, $checkoutEmail);
            }
        } catch (Exception $exception) {
            report(new NinjaAddContextException(
                previous: $exception,
                team: $team ?? null,
                context: ['webhook_payload' => $payload],
                message: '[Stripe Connect] BIG WARNING! We have an error while processing a checkout session completed webhook!',
            ));

            if ($this->isBookingEngineSession($checkoutSession)) {
                // This case shouldn't be critical, we have not yet charged the guest. Just inform him the booking is REJECTED
                if (isset($team) && isset($booking)) {
                    SendBookingNotificationAction::run(
                        $team,
                        $booking,
                        new BookingRejectedEmail($team, $booking->id),
                        Arr::get($checkoutSession, 'customer_details.email')
                    );
                    // I don't trigger GuestNotificationSentEvent as we will later on remove all booking DB entries, thus no need to create a provider event
                }
            } else { // Do not use else if, ensure all cases are covered
                // Here we should land in case of DD or real payment. This case is more important, we may have not registered the payment
                // TODO: send an email to the guest informing we had a problem. Contact the host to check if your payment has been successfully recorded. Should we also inform the host?
            }

            return new Response('Webhook ERROR. Check Sentry URGENTLY: checkout session completed has thrown an error.', 500);
        }

        return new Response('Webhook Handled', 200);
    }

    private function handleClient(Team $team, Booking $booking, array $checkoutSession): string
    {
        // Update client's email and phone
        $checkoutEmail = Arr::get($checkoutSession, 'customer_details.email'); // should never be null
        $checkoutPhone = Arr::get($checkoutSession, 'customer_details.phone'); // may be null
        $checkoutCustomerName = Arr::get($checkoutSession, 'customer_details.name'); // may be null

        try {
            if (! $booking->client) {
                $client = StoreClientAction::run(
                    team: $team,
                    firstname: $checkoutCustomerName,
                    email: $checkoutEmail,
                    emailLabel: 'Payment email',
                    phone: $checkoutPhone,
                    phoneLabel: 'Payment phone',
                );
                $booking->client_id = $client->id;
                $booking->save();
            } else {
                if (
                    ($checkoutEmail && ! in_array($checkoutEmail, $booking->client->getAllEmails()))
                    || ($checkoutPhone && $booking->client->getPhoneNumbersCollection()->doesntContain($checkoutPhone))
                    || ($checkoutCustomerName && empty($booking->client->firstname))
                ) {
                    UpdateClientAction::run(
                        team: $team,
                        client: $booking->client,
                        // For booking engine, create the booking with a dummy first name
                        firstname: empty($booking->client->firstname) || $this->isBookingEngineSession($checkoutSession) ? $checkoutCustomerName : null,
                        notes: $this->isBookingEngineSession($checkoutSession) ? __('teams.clients.booking_engine.new_client') : null,
                        email: $checkoutEmail,
                        emailLabel: 'Payment email',
                        phone: $checkoutPhone,
                        phoneLabel: 'Payment phone',
                    );
                }
            }
            $booking->load('client');
        } catch (Exception $exception) {
            report($exception); // We want to make sure we continue although the client is not saved correctly
        }

        return $checkoutEmail;
    }

    private function isNormalPaymentOrDDSession(array $checkoutSession): bool
    {
        if (Arr::get($checkoutSession, 'metadata.is_normal_payment') !== 'true') {
            return false;
        }

        return $checkoutSession['payment_status'] == 'paid' || ($checkoutSession['payment_status'] == 'unpaid' && array_key_exists('hold_until', $checkoutSession['metadata']));
    }

    private function isBookingEngineSession(array $checkoutSession): bool
    {
        return Arr::get($checkoutSession, 'metadata.is_booking_engine') === 'true';
    }

    private function isUpsalesSession(array $checkoutSession): bool
    {
        return ! empty(Arr::get($checkoutSession, 'metadata.ninja_upsells'));
    }

    private function handleExistingBookingCheckoutSession(Team $team, Booking $booking, array $checkoutSession, string $checkoutEmail): void
    {
        if ($checkoutSession['metadata']['should_create_task'] === 'true' && $team->isProfessional()) {
            CreateReturnDamageDepositTaskAction::dispatch($team, $booking->id); // Dispatch to avoid errors not making us save the payment
        }

        $holdUntil = null;
        if (array_key_exists('hold_until', $checkoutSession['metadata'])) {
            $holdUntil = Carbon::parse($checkoutSession['metadata']['hold_until']);
            $team->assignTeamLocale();
            /*TODO: If we can do extended authorization, try to get the charge from stripe and check the following fields to ensure the DD
                is returned when expected and not automatically by stripe before the desired date:
                'payment_method_details.card.capture_before' => , //https://stripe.com/docs/api/charges/object#charge_object-payment_method_details-card-capture_before
                'payment_method_details.card.extended_authorization.status' => , //https://stripe.com/docs/api/charges/object#charge_object-payment_method_details-card-extended_authorization-status
                https://stripe.com/docs/payments/extended-authorization*/
        }

        // Save payment to DB. We need this sync, we need to confirm the payment was saved in DB. Dispatch payment to provider
        $paymentIntent = $team->stripeConnect()->getPaymentIntent($booking, $checkoutSession['payment_intent']);
        $stripeCommissionCurrency = $paymentIntent->latest_charge->balance_transaction?->currency;

        $paymentData = PaymentData::from([
            'amountInCents' => $checkoutSession['amount_total'], // Comes already in cents
            'currency' => $booking->currency,
            'kind' => $booking->provider()->paymentGatewayBookingPaymentsKind($team),
            'paidAt' => now(),
            'notes' => $holdUntil != null ? __('messages.payment_gateway.damage_deposit.note') : null,
            'stripePaymentIntentId' => $paymentIntent->id,
            'holdUntil' => $holdUntil,
            'stripeCaptured' => ! ($holdUntil != null),
            'stripeCentsFee' => $paymentIntent->latest_charge->balance_transaction?->fee, //To capture the stripe processing fee
            'stripeFeeCurrency' => $stripeCommissionCurrency != null ? strtoupper($stripeCommissionCurrency) : null, //Connected account currency may be different from booking currency
            'email' => $checkoutEmail,
        ]);

        $exists = BookingPayment::query()->whereStripePaymentIntentId($paymentIntent->id)->exists();
        if ($exists) {
            // We already have this payment in DB, we don't need to save it again
            return;
        }

        $addPaymentResult = AddBookingPaymentAction::run(team: $team, booking: $booking, paymentData: $paymentData);

        if (! $addPaymentResult) {
            throw new NinjaAddContextException( // Will be caught by parent try/catch
                team: $team,
                context: ['booking' => [
                    'id' => $booking->id,
                    'paid_amount' => $booking->paid_amount,
                    'left_to_pay_total' => $booking->getLeftToPayTotal(false),
                    'final_price' => $booking->final_price,
                    'damage_deposit' => $booking->damage_deposit,
                ], 'payment_data' => $paymentData->toArray()],
                message: "We couldn't save the payment to DB"
            );
        }
    }

    private function handleUpsalesSession(Team $team, Booking $booking, array $checkoutSession, string $checkoutEmail): void
    {
        $team->assignTeamLocale(); // For translations

        // Get the BookingFeesData
        $metadataUpsales = collect(json_decode(Arr::get($checkoutSession, 'metadata.ninja_upsells'), true));
        $databaseFees = Fee::query()
            ->where('team_id', $team->id)
            ->whereIn('id', $metadataUpsales->pluck('fee_id'))
            ->get();

        if ($metadataUpsales->count() != $databaseFees->count()) {
            report(new NinjaAddContextException(
                team: $team,
                context: ['booking_id' => $booking->id, 'metadata_upsales' => $metadataUpsales->toArray(), 'database_fees' => $databaseFees->toArray()],
                message: "WARNING! We couldn't find all the Upsells selected by the user in the DB"
            ));
        }

        $bookingFeesData = collect();
        /** @var Fee $fee */
        foreach ($databaseFees as $fee) {
            $metadataUpsale = $metadataUpsales->firstWhere('fee_id', $fee->id);
            $bookingFeesData[] = GetBookingFeeDataForUpsaleAction::run($booking, $fee, $metadataUpsale['times_booked'], $metadataUpsale['price']);
        }

        // Now get the payment info
        $paymentIntent = $team->stripeConnect()->getPaymentIntent($booking, $checkoutSession['payment_intent']);
        $stripeCommissionCurrency = $paymentIntent->latest_charge->balance_transaction?->currency;
        $buyingList = $databaseFees->pluck('name')->implode(', ');
        $totalPaid = $checkoutSession['amount_total'];

        $paymentData = PaymentData::from([
            'amountInCents' => $totalPaid, // Comes already in cents
            'currency' => $booking->currency,
            'kind' => $booking->provider()->paymentGatewayBookingPaymentsKind($team),
            'paidAt' => now(),
            'notes' => __('messages.payment_gateway.charge_description.upsales', ['buyingList' => $buyingList]),
            'stripePaymentIntentId' => $paymentIntent->id,
            'stripeCaptured' => true,
            'stripeCentsFee' => $paymentIntent->latest_charge->balance_transaction?->fee, //To capture the stripe processing fee
            'stripeFeeCurrency' => $stripeCommissionCurrency != null ? strtoupper($stripeCommissionCurrency) : null, //Connected account currency may be different from booking currency
            'email' => $checkoutEmail,
        ]);

        // Create the fees and the payment
        $bookingFees = AddBookingFeesAction::run($team, $booking, $bookingFeesData, $paymentData);
        $method = SendBookingNotificationAction::run($team, $booking, new UpsalePurchasedEmail($team, $booking->id, $bookingFeesData), $checkoutEmail);
        event(new GuestNotificationSentEvent($team, $booking->id, NotificationTypeEnum::UPSELL_PURCHASED, $method));

        event(new UpsalePurchasedEvent($team, $booking->id, $bookingFees));

        // Now we charge our client
        $commission = $team->teamSettings->getUpsalesCommissionPercentage() * $totalPaid / 10000; // In units, NOT cents
        if (strtolower($booking->currency) !== 'eur') {
            try {
                $commission = ConvertCurrencyToEuroAction::run($commission, $booking->currency); // In euros
            } catch (Exception $e) {
                report(new NinjaAddContextException(
                    previous: $e,
                    team: $team,
                    context: [
                        'currency' => $booking->currency,
                        'amount' => $commission,
                        'booking_id' => $booking->id,
                        'time' => now()->toIso8601String(),
                    ],
                    message: 'FAILED WHEN TRYING TO CONVERT CURRENCY FOR UPSELLS COMMISSION. FIX IT AND ADD THE AMOUNT MANUALLY IN THE DB. Check context.'
                ));
            }
        }

        BillAddonAction::run(
            team: $team,
            type: BillableAddonTypeEnum::UPSALE_COMMISSION,
            quantity: ninjaIntval($commission * 100),
            rentalId: $booking->rental_id,
            bookingId: $booking->id,
        );
    }

    /**
     * We can get here after the refund was done from RN or from Stripe.
     * We want to cover the case were the refund was done from Stripe. Thus, just find the PaymentIntent and cancel it if not yet canceled.
     */
    protected function handleChargeRefunded(array $payload): Response
    {
        // Do not check if team is subscribed here. It may be unsubscribed due to impayment and we don't want to loose sync of info. With unsubscribe team nova action, we disable connect.
        $charge = $payload['data']['object'];

        // In case $charge['captured'] == false, this is the return of a DD thus we must not do anything in this webhook. Payment intent canceled webhook will act.
        if (($charge['status'] == 'succeeded' || $charge['status'] == 'successful') && $charge['captured'] && $charge['payment_intent']) {
            /** @var Collection<BookingPayment> $payments */
            $payments = BookingPayment::query()
                ->wherePaymentIntent($charge['payment_intent'])
                ->whereNotCancelled()
                ->with(['booking.team'])
                ->orderBy('created_at')
                ->get()
                ->where('amount_in_cents', '<>', 0); // In case we have with value of 0

            if ($payments->isNotEmpty()) { // WARNING: WE MAY NOT FIND ANY PAYMENTS IN CASE SOMEBODY HAS REFUNDED A PAYMENT WHICH WAS NOT CREATED BY RENTAL NINJA
                /** @var Booking $booking */
                $booking = $payments->first()->booking;
                $booking->team->assignTeamLocale();
                $positivePayment = $payments->firstWhere('amount_in_cents', '>', 0); //There must be 1 at least
                $negativePayments = $payments->where('amount_in_cents', '<', 0);
                if ($negativePayments->count() === 0 && $charge['amount_refunded'] === $positivePayment->amount_in_cents) {
                    $result = CancelBookingPaymentAction::run(
                        $booking->team,
                        $booking,
                        $positivePayment,
                        notifyStripe: false,
                    );
                } else {
                    $amount = ($charge['amount_refunded'] + $negativePayments->sum('amount_in_cents')) * -1;
                    // Add a negative payment
                    $paymentData = PaymentData::from([
                        'amountInCents' => $amount,
                        'currency' => $booking->currency,
                        'kind' => $booking->provider()->paymentGatewayBookingPaymentsKind($booking->team),
                        'paidAt' => now(),
                        'notes' => __('messages.payment_gateway.refund.partial.note'),
                        'stripePaymentIntentId' => $charge['payment_intent'],
                    ]);
                    $result = AddBookingPaymentAction::run($booking->team, $booking, $paymentData);
                }

                if (! $result) {
                    report(new NinjaAddContextException(
                        team: $booking->team,
                        context: ['booking' => [
                            'id' => $booking->id,
                            'paid_amount' => $booking->paid_amount,
                            'left_to_pay_total' => $booking->getLeftToPayTotal(false),
                            'final_price' => $booking->final_price,
                            'damage_deposit' => $booking->damage_deposit,
                        ], 'payment_data' => $paymentData?->toArray()],
                        message: "We couldn't update our Database to cancel/refund this payment!"
                    ));

                    return new Response("We couldn't update our Database to cancel/refund this payment!", 500); // I want to see this in stripe logs
                }
            }
        }

        return new Response('Webhook Handled', 200);
    }

    /**
     * Used when a damage deposit is returned.
     */
    protected function handlePaymentIntentCanceled(array $payload): Response
    {
        $paymentIntent = $payload['data']['object'];
        if ($paymentIntent['status'] == 'canceled') {
            $payment = BookingPayment::query()
                ->wherePaymentIntent($paymentIntent['id'])
                ->whereNotCancelled()
                ->with(['booking.team'])
                ->first();
            if ($payment) { // WARNING: WE MAY NOT FIND ANY PAYMENTS IN CASE SOMEBODY HAS RETURNED A DD WHICH WAS NOT CREATED BY RENTAL NINJA
                $payment->booking->team->assignTeamLocale();
                $result = CancelBookingPaymentAction::run(
                    $payment->booking->team,
                    $payment->booking,
                    $payment,
                    notifyStripe: false,
                    reason: __('messages.payment_gateway.damage_deposit.returned')
                );

                if (! $result) {
                    $booking = $payment->booking;
                    report(new NinjaAddContextException(
                        team: $booking->team,
                        context: ['booking' => [
                            'id' => $booking->id,
                            'paid_amount' => $booking->paid_amount,
                            'left_to_pay_total' => $booking->getLeftToPayTotal(false),
                            'final_price' => $booking->final_price,
                            'damage_deposit' => $booking->damage_deposit,
                        ]],
                        message: "We couldn't update our Database to cancel this payment!"
                    ));

                    return new Response("We couldn't update our Database to cancel this payment!", 500); // I want to see this in stripe logs
                }
            }
        }

        return new Response('Webhook Handled', 200);
    }

    /** We should land here:
     * 1- in the event a previously uncaptured charge is captured: whenever a DD is captured
     * 2- When a booking from the booking engine is confirmed (after preauth, we charge the booking): this case shouldn't be handled
     * 3- When a payment not related to RN was captured from (our customer may use stripe for other purposes).
     */
    protected function handleChargeCaptured(array $payload): Response
    {
        $charge = $payload['data']['object'];
        $payment = BookingPayment::query()
            ->wherePaymentIntent($charge['payment_intent'])
            ->whereIsActiveStripeDamageDeposit()
            ->with(['booking.team'])
            ->first();

        // WARNING: WE MAY NOT FIND ANY PAYMENTS IN CASE SOMEBODY HAS CAPTURED A DD WHICH WAS NOT CREATED BY RENTAL NINJA or when capturing our Booking Engine reservation amounts
        if ($payment && $charge['captured'] && $charge['status'] == 'succeeded') {
            // I see in the payload "previous_attributes" with amount_captured = 0 and captured = false. Can be used if needed
            $booking = $payment->booking;
            $team = $booking->team;
            $team->assignTeamLocale();

            // Convert the DD payment into a normal payment
            $paymentIntent = $team->stripeConnect()->getPaymentIntent($booking, $charge['payment_intent']);
            $payment->stripe_cents_fee = $paymentIntent->latest_charge->balance_transaction?->fee;
            $stripeCommissionCurrency = $paymentIntent->latest_charge->balance_transaction?->currency;
            $payment->stripe_fee_currency = $stripeCommissionCurrency != null ? strtoupper($stripeCommissionCurrency) : null;
            $payment->stripe_captured = true;
            $payment->save(); // We can save this directly because the modified columns belong only to RN. Otherwise we have to sync with provider!

            if ($charge['amount_captured'] < $payment->amount_in_cents) {
                // Add a negative payment with the difference
                $newPaymentData = PaymentData::from([
                    'amountInCents' => ($payment->amount_in_cents - $charge['amount_captured']) * -1,
                    'currency' => $payment->currency,
                    'kind' => $booking->provider()->paymentGatewayBookingPaymentsKind($team),
                    'paidAt' => now(),
                    'notes' => __('messages.payment_gateway.damage_deposit.partially_captured'),
                    'stripePaymentIntentId' => $charge['payment_intent'],
                ]);
                AddBookingPaymentAction::run($team, $booking, $newPaymentData);
            }
        }

        return new Response('Webhook Handled', 200);
    }

    protected function setMaxNetworkRetries($retries = 3)
    {
        Stripe::setMaxNetworkRetries($retries);
    }
}
