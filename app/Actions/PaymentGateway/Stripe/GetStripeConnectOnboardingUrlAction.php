<?php

namespace App\Actions\PaymentGateway\Stripe;

use App\Models\PaymentGatewayAccount;
use App\Models\Team;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\Concerns\AsAction;

class GetStripeConnectOnboardingUrlAction
{
    use AsAction;

    public function asController(Team $team, PaymentGatewayAccount $paymentGatewayAccount): JsonResponse
    {
        return response()->json(['data' => ['url' => $this->handle($team, $paymentGatewayAccount)]]);
    }

    public function handle(Team $team, PaymentGatewayAccount $paymentGatewayAccount): string
    {
        return $team->stripeConnect()->onboardingFormUrl($paymentGatewayAccount);
    }
}
