<?php

namespace App\Actions\PaymentGateway\Stripe;

use App\Actions\Users\TouchTeamUsersAction;
use App\Models\PaymentGatewayAccount;
use App\Models\Team;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;

class ProcessStripeConnectOauthAction
{
    use AsAction;

    private ?string $error = null;

    public function asController(Request $request): RedirectResponse
    {
        // https://stripe.com/docs/connect/oauth-reference
        $paymentGatewayAccount = PaymentGatewayAccount::find($request->input('state'));
        $team = $paymentGatewayAccount->team;

        abort_unless($paymentGatewayAccount !== null && $team !== null, 404, "Couldn't complete Stripe Oauth.");
        // Abrupt stop: Someone is trying to fake us, not sending our previously sent teamId

        if ($request->input('error') == 'access_denied') {
            return redirect($team->config()->rnAppTargetDomain(config('ninja-stripe.connect.frontend-settings-page')));
            // The user didn't complete the form and didn't accept the oauth
        }

        $code = $request->input('code');
        abort_unless(
            $request->input('error') === null && $code !== null,
            500,
            $request->input('error_description') ?? 'We had a problem with the received response from Stripe.'
        ); // This must be our fault

        if ($this->handle($team, $paymentGatewayAccount, $code)) {
            return redirect($team->config()->rnAppTargetDomain(config('ninja-stripe.connect.frontend-settings-page')));
        }

        // Show the resources/views/errors/minimal.blade.php view with error message
        abort(500, $this->error ?? 'We had a problem trying to connect your account to ours. Try again.');
    }

    public function handle(Team $team, PaymentGatewayAccount $paymentGatewayAccount, string $code): bool
    {
        try {
            // Check first if we have other accounts with the same Id. Don't allow this as then webhooks will pick the first account with that ID to find the team.
            $gatewayAccountId = $team->stripeConnect()->completeOauth($code);
            $existing = PaymentGatewayAccount::query()
                ->where('gateway_account_id', $gatewayAccountId)
                ->count();

            if ($existing > 0) {
                // Note: don't deauthorise this account as it is not yet "enabled" in the DB + the user will loose the oauth with the currently existing account
                $this->error = __('teams.settings.payment_gateway.validation.account_connected_twice');

                return false;
            } else {
                $paymentGatewayAccount->gateway_account_id = $gatewayAccountId;
                $paymentGatewayAccount->save();
            }
        } catch (Exception $exception) {
            report($exception);
            $this->error = $exception->getMessage();

            return false;
        }

        // Now let's check if the team has the status to start accepting payments.
        // Let's do this after we correctly save the account id, because if we need to deactivate the account, we need the account id, so we can't loose it
        try {
            $paymentGatewayAccount->charges_enabled = $team->stripeConnect()->checkChargesEnabled($paymentGatewayAccount);
            if ($paymentGatewayAccount->save()) {
                TouchTeamUsersAction::run($team); // Needed to ensure TeamFullResource is updated (such info is cached in /me endpoint)

                return true;
            } else {
                DeauthorizeStripeConnectAction::run($team, $paymentGatewayAccount);
                $this->error = 'We had a problem saving your account to the database. Contact support';

                return false;
            }
        } catch (Exception $exception) {
            report($exception);
            $this->error = $exception->getMessage();
            DeauthorizeStripeConnectAction::run($team, $paymentGatewayAccount);

            return false;
        }
    }
}
