<?php

namespace App\Actions\Rentals\SeasonalPrices;

use App\DTO\ChannelManager\SeasonalRuleData;
use App\Models\Rental;
use App\Models\RentalSeasonalPrice;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsController;

class GetOneSeasonalPriceAction
{
    use AsController;

    public function asController(Team $team, Rental $teamRental, RentalSeasonalPrice $seasonalPrice): SeasonalRuleData
    {
        return SeasonalRuleData::from($seasonalPrice);
    }
}
