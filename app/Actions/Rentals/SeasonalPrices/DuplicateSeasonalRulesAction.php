<?php

namespace App\Actions\Rentals\SeasonalPrices;

use App\Actions\ChannelManager\Pushers\UpdateAvailabilityInChannelManagerAction;
use App\Actions\ChannelManager\Pushers\UpdatePricesInChannelManagerAction;
use App\Actions\Rentals\DailyDetails\Rates\ProcessRentalSeasonalRulesAction;
use App\DTO\ChannelManager\SeasonalRuleData;
use App\Models\Rental;
use App\Models\RentalSeasonalPrice;
use App\Models\Team;
use App\Rules\ExistsInTeamRule;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

class DuplicateSeasonalRulesAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'rental_id' => ['required', 'integer', new ExistsInTeamRule(Rental::class)],
        ];
    }

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): DataCollection
    {
        $sourceRental = Rental::getRentalModel($team, $request->input('rental_id'));
        $this->handle($team, $teamRental, $sourceRental);

        $teamRental->load('seasonalPrices');

        return SeasonalRuleData::collection($teamRental->seasonalPrices);
    }

    public function handle(Team $team, Rental $rental, Rental $sourceRental): void
    {
        $rental->seasonalPrices()->delete();

        $sourceRental->seasonalPrices->each(function (RentalSeasonalPrice $season) use ($rental) {
            $seasonData = SeasonalRuleData::from($season);
            $rental->seasonalPrices()->create($seasonData->all());
        });

        ProcessRentalSeasonalRulesAction::run($team, $rental);

        UpdatePricesInChannelManagerAction::dispatch($team, $rental->id);
        UpdateAvailabilityInChannelManagerAction::dispatch($team, $rental->id);
    }
}
