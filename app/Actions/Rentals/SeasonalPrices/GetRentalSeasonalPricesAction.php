<?php

namespace App\Actions\Rentals\SeasonalPrices;

use App\DTO\ChannelManager\SeasonalRuleData;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Eloquent\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

class GetRentalSeasonalPricesAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental): DataCollection
    {
        $seasonalPrices = $this->handle($teamRental);

        return SeasonalRuleData::collection($seasonalPrices);
    }

    public function handle(Rental $rental): Collection
    {
        return $rental->seasonalPrices;
    }
}
