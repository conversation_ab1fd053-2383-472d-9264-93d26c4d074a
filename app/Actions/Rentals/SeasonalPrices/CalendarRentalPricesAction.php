<?php

namespace App\Actions\Rentals\SeasonalPrices;

use App\Models\Rental;
use App\Models\Team;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CalendarRentalPricesAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): Collection
    {
        $from = Carbon::parse($request->input('from', now()));
        $to = Carbon::parse($request->input('to', now()->year));

        return $this->handle($teamRental, $from, $to);
    }

    public function handle(Rental $rental, Carbon $from, Carbon $to): Collection
    {
        $period = collect($from->toPeriod($to));

        $bookingsMap = $this->getBookingsMap($rental, $from, $to);

        return $period->map(function (Carbon $date) use ($rental, $bookingsMap) {
            $basePrice = $rental->base_rate;

            $season = $rental->getSeasonalPriceForDay($date);
            $longStay = $rental->getLongStayDiscountForDay($date);
            $lastMinute = $rental->getLastMinuteDiscountForDay($date);
            $bookingDay = data_get($bookingsMap, $date->toDateString());

            $initialPrice = $season?->getRate($date, $rental);
            $limitedPrice = $initialPrice;
            // Apply min and max price
            if (! is_null($limitedPrice)) {
                $limitedPrice = max($initialPrice, $rental->min_price);
                if (! empty($rental->max_price)) {
                    $limitedPrice = min($limitedPrice, $rental->max_price);
                }
            }

            $initialPrice = $initialPrice != null ? intval($initialPrice) : null;
            $limitedPrice = $limitedPrice != null ? intval($limitedPrice) : null;
            $basePrice = $basePrice != null ? intval($basePrice) : 0;
            $rentalPrice = $limitedPrice ?? $basePrice;

            return [
                'date' => $date->toDateString(),
                'season_id' => $season?->id,
                'season_value' => $season?->value_in_cents,
                'season_name' => $season?->name,
                'season_locked' => $season?->locked,
                'strategy' => $season?->strategy ?? 'base_price',
                'guests_included' => $rental->sleeps,
                'extra_guests_amount' => $season?->extra_guest_amount_in_cents ?? 0,
                'initial_price' => $initialPrice,
                'limited_price' => $limitedPrice,
                'rental_price' => $rentalPrice,
                'base' => $basePrice,
                'min_price' => $rental->min_price,
                'max_price' => $rental->max_price,
                'currency' => $rental->currency,
                'season_min_stay' => $season?->min_stay,
                'absolute_min_stay' => $rental->min_stay,
                'min_stay' => max($season?->min_stay, $rental->min_stay),
                'available' => is_null($bookingDay) && ! $season?->locked,
                'booking' => $bookingDay,
                'long_stay_id' => $longStay?->id,
                'long_stay_name' => $longStay?->name,
                'last_minute_id' => $lastMinute?->id,
                'last_minute_name' => $lastMinute?->name,
            ];
        });
    }

    protected function getBookingsMap(Rental $rental, Carbon $from, Carbon $to): array
    {
        $bookingsMap = [];
        $bookings = $rental->bookings()->filledBetween($from, $to)->get();
        foreach ($bookings as $booking) {
            $bookingPeriod = collect($booking->getStartAt()->toPeriod($booking->getEndAt()->subDay()->endOfDay())); // CheckOut day is available
            $bP = $bookingPeriod->mapWithKeys(fn (Carbon $date) => [$date->toDateString() => [
                'booking_id' => $booking->id,
                'rental_price' => intval($booking->final_rental_price * 100 / $booking->nights),
                'unavailable' => $booking->isUnavailable(),
            ]])->toArray();
            $bookingsMap = array_merge($bookingsMap, $bP);
        }

        return $bookingsMap;
    }
}
