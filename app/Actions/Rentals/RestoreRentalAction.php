<?php

namespace App\Actions\Rentals;

use App\Actions\Rentals\DailyDetails\Support\FillFutureDailyRentalDetailsAction;
use App\DataProviders\Providers\NoProvider;
use App\Events\RentalRestoredEvent;
use App\Http\Resources\RentalResource;
use App\Models\Booking;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class RestoreRentalAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental): RentalResource
    {
        $rental = $this->handle($team, $teamRental);

        return new RentalResource($rental);
    }

    public function handle(Team $team, Rental $rental): Rental
    {
        $wasDeleted = $rental->trashed();

        $rental->currency = $rental->currency ?? $team->currency ?? 'eur';

        // If the rental was from a provider, we need to migrate it to internal rental.
        if (! $rental->is_rental_ninja) {
            $rental->fill([
                'external_id' => null,
                'provider_id' => NoProvider::ID,
                'deleted_at' => null,
                'updated_at' => now()->timestamp,
                'is_rental_ninja' => true,
            ])->save();

            $rental->photos()->update(['external_id' => null]);
            $rental->rentalFees()->update(['external_id' => null]);
            $rental->bookings()->future()->update(['external_id' => null, 'provider_id' => NoProvider::ID, 'locked' => 'false']);
            $rental->bookings()->future()->get()->each(
                fn (Booking $b) => $b->bookingPayments()->update(['external_id' => null, 'provider_id' => NoProvider::ID])
            );
        } else {
            $rental->restore();
        }

        FillFutureDailyRentalDetailsAction::run(rental: $rental);

        if ($wasDeleted) {
            event(new RentalRestoredEvent($team, $rental->id));
        }

        return $rental;
    }
}
