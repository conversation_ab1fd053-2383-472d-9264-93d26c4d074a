<?php

namespace App\Actions\Rentals;

use App\Enum\ChangeOverEnum;
use App\Models\Rental;
use App\Models\RentalDailyDetails;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetRentalAvailableCheckOutDaysForDateAction
{
    use AsAction;

    public function handle(Rental $rental, Carbon $dateFrom): Collection
    {
        $dateFrom = $dateFrom->copy()->startOfDay();

        /** @var Collection<RentalDailyDetails> $detailsForPeriod */
        $detailsForPeriod = $rental->getDetailsForPeriod($dateFrom, $dateFrom->copy()->addMonths($rental->bookable_months));

        $firstNotAvailableDate = $detailsForPeriod->where('available', false)->first()?->date;

        $dayMinStay = $detailsForPeriod->first()?->min_stay;

        $maxDate = $rental->max_stay ? $dateFrom->copy()->addDays($rental->max_stay) : null;

        $dates = $detailsForPeriod
            ->where('date', '>=', $dateFrom->addDays($dayMinStay))
            ->when($maxDate, fn (Collection $query) => $query->where('date', '<=', $maxDate))
            ->when($firstNotAvailableDate, fn (Collection $query) => $query->where('date', '<=', $firstNotAvailableDate))
            ->whereIn('changeover', ChangeOverEnum::canCheckOutOptions())
            ->map(fn (RentalDailyDetails $dailyDetails) => $dailyDetails->date)
            ->values();

        return $dates;
    }
}
