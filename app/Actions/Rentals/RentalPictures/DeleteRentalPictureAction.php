<?php

namespace App\Actions\Rentals\RentalPictures;

use App\Actions\ChannelManager\Pushers\UpdateRentalInChannelManagerAction;
use App\Actions\Pictures\RemoveFileFromFirebaseStorage;
use App\Models\Rental;
use App\Models\RentalPicture;
use App\Models\Team;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\Concerns\AsAction;

use function response;

class DeleteRentalPictureAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental, RentalPicture $photo): JsonResponse
    {
        $this->handle($team, $teamRental, $photo);

        return response()->json('', 204);
    }

    public function handle(Team $team, Rental $rental, RentalPicture $photo): bool
    {
        UpdateRentalInChannelManagerAction::dispatch($team, $rental->id);
        RemoveFileFromFirebaseStorage::run($photo->url);

        $result = $photo->delete();

        // Reorder remaining pictures
        $rental->photos()->where('order', '>', $photo->order)->decrement('order');

        return $result;
    }
}
