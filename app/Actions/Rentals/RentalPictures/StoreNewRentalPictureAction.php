<?php

namespace App\Actions\Rentals\RentalPictures;

use App\Actions\ChannelManager\Pushers\UpdateRentalInChannelManagerAction;
use App\Actions\Sync\MoveRentalPhotosToPublicS3Action;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\DTO\ChannelManager\RentalPictureData;
use App\Models\Rental;
use App\Models\RentalPicture;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreNewRentalPictureAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental, RentalPictureData $data): RentalPictureData
    {
        $photo = $this->handle($team, $teamRental, $data);

        return RentalPictureData::from($photo);
    }

    public function handle(Team $team, Rental $rental, RentalPictureData $data): RentalPicture
    {
        // New pictures are stored in the last position.
        $data->order = ($rental->photos->last()?->order ?? 0) + 1;

        $picture = $rental->photos()->create($data->all());

        if (! $picture->in_public_storage && $rental->provider_id == ChannelManagerProvider::ID) {
            MoveRentalPhotosToPublicS3Action::dispatch($picture);
        }

        GetRentalPictureResolutionFromImgixAction::dispatch($picture)->delay(10);
        UpdateRentalInChannelManagerAction::dispatch($team, $rental->id);

        return $picture;
    }
}
