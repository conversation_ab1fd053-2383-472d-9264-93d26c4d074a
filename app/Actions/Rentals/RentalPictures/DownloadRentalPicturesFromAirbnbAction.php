<?php

namespace App\Actions\Rentals\RentalPictures;

use App\Actions\Storage\GetStorageDirectoryAction;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\Enum\ImageTypeEnum;
use App\Models\Rental;
use App\Models\Team;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class DownloadRentalPicturesFromAirbnbAction
{
    use AsAction;

    public function asJob(Team $team, array $data): void
    {
        $data = collect($data);

        $ruId = $data->get('ru_id');
        $airbnbId = $data->get('airbnb_id');

        $rental = Rental::onTeam($team)
            ->onProvider(ChannelManagerProvider::ID)
            ->firstWhere('external_id', $ruId) ??
            Rental::onTeam($team)
                ->onProvider(ChannelManagerProvider::ID)
                ->whereHas('channelRentals',
                    fn (Builder $b) => $b
                        ->where('channel', '=', 'Airbnb')
                        ->where('channel_listing_id', '=', $airbnbId)
                )
                ->first();

        if (is_null($rental)) {
            pnLog("[DownloadRentalPicturesFromAirbnbAction] Rental not found. RU: $ruId and Airbnb: $airbnbId", $team);

            return;
        }
        $images = collect($data->get('images'));

        $this->handle($team, $rental, $images);
    }

    public function handle(Team $team, Rental $rental, Collection $images): void
    {
        // Clear photos for rental before downloading new ones.
        $rental->photos->each(fn ($photo) => $photo->delete());

        $images
            ->each(fn (array $picture) => $this->downloadAirbnbPicture($rental, $picture));

        $imageCount = $images->count();
        pnLog("[DownloadRentalPicturesFromAirbnbAction] $imageCount pictures downloaded for rental: $rental->id", $team);

        $rental->refresh();
        $rental->photos->each(fn ($photo) => GetRentalPictureResolutionFromImgixAction::dispatch($photo)->delay(2));
    }

    private function downloadAirbnbPicture(Rental $rental, array $picture): void
    {
//        "src": "https://a0.muscache.com/im/pictures/miso/Hosting-868128084414960880/original/cff5ee57-f6c1-4687-b5e5-6a4156e2f383.jpeg",
//        "alt": "Pool image 1",
//        "section": "Pool",
//        "imageId": "1835483730",
//        "width": 1200,
//        "height": 800,
//        "order": 1

        $path = $this->downloadImage($rental, data_get($picture, 'src'));
        if ($path === false) {
            return;
        }

        $rental->photos()
            ->create([
                'order' => data_get($picture, 'order'),
                'image_type' => $this->mapType($picture),
                'storage_path' => $path,
                'url' => config('ninja.public_imgix_url').'/'.$path,
                'width' => null,
                'height' => null, // This will be updated later by GetRentalPictureResolutionFromImgixAction
                'description' => ['en' => data_get($picture, 'alt')],
            ]);
    }

    private function downloadImage(Rental $rental, string $url): bool|string
    {
        // Fetch the image. If this is not accessible, delete image and return.
        try {
            $rawImage = file_get_contents($url);
        } catch (Exception) {
            nLog("[DownloadRentalPicturesFromAirbnbAction] Picture not available:  $url");

            return false;
        }
        $filename = Str::uuid().'.'.Str::of($url)
                ->afterLast('.');
        $path = GetStorageDirectoryAction::run("public/listings/$rental->team_id/$rental->id/$filename");

        // Try to upload the image. If it fails, throw exception.
        $success = Storage::disk('s3')
            ->put($path, $rawImage);
        if (! $success) {
            throw new Exception('Could not store the image in S3');
        }

        pnLog("[DownloadRentalPicturesFromAirbnbAction] Image downloaded: $url");

        return $path;
    }

    private function mapType(array $picture): ImageTypeEnum
    {
        if (data_get($picture, 'order') == 1) {
            return ImageTypeEnum::mainImage;
        }
        $section = data_get($picture, 'section');

        // Number may be numerated. Remove the last number if present.
        $section = preg_replace('/\s\d+$/', '', $section);
        $imageType = match ($section) {
            'Backyard' => ImageTypeEnum::exteriorGarden,
            'Dining area' => ImageTypeEnum::roomsDiningArea,
            'Full kitchen' => ImageTypeEnum::roomsKitchen,
            'Living room' => ImageTypeEnum::roomsLivingRoom,
            'Bedroom 1' => ImageTypeEnum::roomsBedroom,
            'Full bathroom' => ImageTypeEnum::roomsBathroom,
            'Patio' => ImageTypeEnum::exteriorPatio,
            'Laundry area' => ImageTypeEnum::interiorLaundryRoom,
            'Pool' => ImageTypeEnum::poolSwimmingPool,
            'Exterior' => ImageTypeEnum::exterior,
            'Additional photos' => ImageTypeEnum::otherOther,
            'Office' => ImageTypeEnum::activitiesGameRoom,
            'Kitchenette' => ImageTypeEnum::roomsKitchenette,
            '' => ImageTypeEnum::interior,
            default => $section,
        };

        if (is_string($imageType)) {
            pnLog("[DownloadRentalPicturesFromAirbnbAction] Image type not mapped: $section");
            $imageType = ImageTypeEnum::interior;
        }

        return $imageType;
    }
}
