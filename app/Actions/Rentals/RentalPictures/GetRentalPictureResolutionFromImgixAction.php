<?php

namespace App\Actions\Rentals\RentalPictures;

use App\DataProviders\Providers\ChannelManagerProvider;
use App\Models\RentalPicture;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class GetRentalPictureResolutionFromImgixAction
{
    use AsAction;

    public string $commandSignature = 'ninja-dev:get-rental-picture-resolution-from-cm-rentals {team=0}';

    public function asCommand(Command $command): int
    {
        $teamId = $command->argument('team');
        RentalPicture::query()
            ->when($teamId, fn ($q) => $q->where('team_id', '=', $teamId))
            ->whereRelation('rental', 'provider_id', '=', ChannelManagerProvider::ID)
            ->whereNull('width')
            ->each(fn (RentalPicture $p) => $this->handle($p));

        return $command::SUCCESS;
    }

    public function asJob(int|RentalPicture $input): void
    {
        if (is_int($input)) {
            RentalPicture::query()
                ->whereRentalId($input)
                ->whereRelation('rental', 'provider_id', '=', ChannelManagerProvider::ID)
                ->whereNull('width')
                ->each(fn (RentalPicture $p) => $this->handle($p));
        } else {
            $this->handle($input);
        }
    }

    public function handle(RentalPicture $picture): bool
    {
        if ($picture->width && $picture->height) {
            return true;
        }
        if (Str::of($picture->url)->contains(['imgix', 'public.rental-ninja.com'])) {
            $response = Http::get("$picture->url?fm=json")->json();
            if (is_null($response)) {
                return false;
            }
            $picture->width = $response['PixelWidth'];
            $picture->height = $response['PixelHeight'];

            return $picture->save();
        } else {
            pnLog('[GetRentalPictureResolutionFromImgixAction] Trying to get the resolution from a non imgix image');

            return false;
        }
    }
}
