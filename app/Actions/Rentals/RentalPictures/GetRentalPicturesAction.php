<?php

namespace App\Actions\Rentals\RentalPictures;

use App\DTO\ChannelManager\RentalPictureData;
use App\Models\Rental;
use App\Models\RentalPicture;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsController;
use Spatie\LaravelData\DataCollection;

class GetRentalPicturesAction
{
    use AsController;

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): DataCollection
    {
        $photosWithUrl = $teamRental->photos->filter(fn (RentalPicture $rp) => ! empty($rp->url));

        return RentalPictureData::collection($photosWithUrl);
    }
}
