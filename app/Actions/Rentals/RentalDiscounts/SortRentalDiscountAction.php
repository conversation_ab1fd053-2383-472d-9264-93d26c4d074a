<?php

namespace App\Actions\Rentals\RentalDiscounts;

use App\Actions\ChannelManager\Pushers\UpdateLastMinuteDiscountsInChannelManagerAction;
use App\Actions\ChannelManager\Pushers\UpdateLongStayDiscountsInChannelManagerAction;
use App\DTO\ChannelManager\RentalDiscountData;
use App\Enum\RentalDiscountEnum;
use App\Models\Rental;
use App\Models\RentalDiscount;
use App\Models\Team;
use App\Rules\ExistsInTeamRule;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

class SortRentalDiscountAction
{
    use AsAction;

    // TODO ensure in the ExistsInTeamRule that type matches:
    // How: optional extra were in ExistsInTeamRule(RentalDiscount::class, where: ['type', $type] )
    public function rules(): array
    {
        $type = request()->route()->type;

        return [
            '*.id' => ['required', 'integer', new ExistsInTeamRule(RentalDiscount::class)],
            '*.order' => ['required', 'integer'],
        ];
    }

    public function asController(Team $team, Rental $teamRental, RentalDiscountEnum $type, ActionRequest $request): DataCollection
    {
        $attributes = $request->input();
        $this->handle($team, $teamRental, $type, $attributes);

        $discounts = GetRentalDiscountsAction::run($teamRental, $type);

        return RentalDiscountData::collection($discounts);
    }

    public function handle(Team $team, Rental $rental, RentalDiscountEnum $type, array $attributes): void
    {
        if ($type == RentalDiscountEnum::longStay) {
            $discounts = $rental->longStayDiscounts;
            UpdateLongStayDiscountsInChannelManagerAction::dispatch($team, $rental->id);
        } else {
            $discounts = $rental->lastMinuteDiscounts;
            UpdateLastMinuteDiscountsInChannelManagerAction::dispatch($team, $rental->id);
        }
        foreach ($attributes as $row) {
            $discounts->firstWhere('id', '=', $row['id'])->update(['order' => $row['order']]);
        }
    }
}
