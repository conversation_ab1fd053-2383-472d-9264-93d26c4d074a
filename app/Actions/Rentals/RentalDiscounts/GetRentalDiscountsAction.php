<?php

namespace App\Actions\Rentals\RentalDiscounts;

use App\DTO\ChannelManager\RentalDiscountData;
use App\Enum\RentalDiscountEnum;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Eloquent\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

class GetRentalDiscountsAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental, RentalDiscountEnum $type): DataCollection
    {
        $discounts = $this->handle($teamRental, $type);

        return RentalDiscountData::collection($discounts);
    }

    public function handle(Rental $rental, RentalDiscountEnum $type): Collection
    {
        if ($type == RentalDiscountEnum::longStay) {
            return $rental->longStayDiscounts;
        } else {
            return $rental->lastMinuteDiscounts;
        }
    }
}
