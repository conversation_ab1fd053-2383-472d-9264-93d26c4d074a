<?php

namespace App\Actions\Rentals\RentalDiscounts;

use App\Actions\ChannelManager\Pushers\UpdateLastMinuteDiscountsInChannelManagerAction;
use App\Actions\ChannelManager\Pushers\UpdateLongStayDiscountsInChannelManagerAction;
use App\DTO\ChannelManager\RentalDiscountData;
use App\DTO\ChannelManager\RentalDiscountsValuesData;
use App\Enum\RentalDiscountEnum;
use App\Models\Rental;
use App\Models\RentalDiscount;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

class UpdateRentalDiscountAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental, RentalDiscountEnum $type, RentalDiscount $rentalDiscount, RentalDiscountData $request): RentalDiscountData
    {
        abort_if($rentalDiscount->type != $type, 404);
        $rDiscount = $this->handle($team, $teamRental, $type, $rentalDiscount, $request);

        return RentalDiscountData::from($rDiscount);
    }

    public function handle(Team $team, Rental $rental, RentalDiscountEnum $type, RentalDiscount $rentalDiscount, RentalDiscountData $request): RentalDiscount
    {
        abort_if($request->type != $type, 400, 'Type does not match');
        $request->discounts = new DataCollection(
            dataClass: RentalDiscountsValuesData::class,
            items: $request->discounts->toCollection()->sortByDesc(fn (RentalDiscountsValuesData $d) => $d->discount)->values()
        );

        $rentalDiscount->update($request->all());

        if ($type == RentalDiscountEnum::longStay) {
            UpdateLongStayDiscountsInChannelManagerAction::dispatch($team, $rental->id);
        } else {
            UpdateLastMinuteDiscountsInChannelManagerAction::dispatch($team, $rental->id);
        }

        return $rentalDiscount;
    }
}
