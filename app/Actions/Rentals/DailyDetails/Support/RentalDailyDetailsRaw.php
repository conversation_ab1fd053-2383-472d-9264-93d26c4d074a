<?php

namespace App\Actions\Rentals\DailyDetails\Support;

use App\Enum\ChangeOverEnum;
use App\Enum\PricingModelEnum;
use Illuminate\Support\Carbon;

/**
 * This is using snake case for the attributes. It is a low level class, used to compare raw database attributes.
 */
class RentalDailyDetailsRaw
{
    public function __construct(
        public string $date,
        public ?int $strategy,
        public ?int $season_id,
        public ?int $booked,
        public ?bool $strategy_closed,
        public ?int $strategy_price_in_cents,
        public ?int $strategy_extra_guest_in_cents,
        public ?int $strategy_min_stay,
        public ?int $strategy_changeover,
        public ?int $manual_price_in_cents, // Only used in the setup
        public ?int $id = null,
    ) {
    }

    public static function create(
        Carbon $date,
        ?PricingModelEnum $model = null,
        ?int $seasonId = null,
        ?bool $booked = null,
        ?bool $strategyClosed = null,
        ?int $strategyPriceInCents = null,
        ?int $strategyExtraGuestInCents = null,
        ?int $strategyMinStay = null,
        ?ChangeOverEnum $strategyChangeover = null,
        ?int $manualPriceInCents = null, // Only used in the setup
    ): self {
        return new self(
            date: $date->toDateString(),
            strategy: is_null($model) ? null : $model->value,
            season_id: $seasonId,
            booked: $booked,
            strategy_closed: $strategyClosed,
            strategy_price_in_cents: $strategyPriceInCents,
            strategy_extra_guest_in_cents: $strategyExtraGuestInCents,
            strategy_min_stay: $strategyMinStay,
            strategy_changeover: is_null($strategyChangeover) ? null : $strategyChangeover->value,
            manual_price_in_cents: $manualPriceInCents,
        );
    }
}
