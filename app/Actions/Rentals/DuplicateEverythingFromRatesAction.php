<?php

namespace App\Actions\Rentals;

use App\Actions\ChannelManager\Pushers\UpdateEverythingInChannelManagerAction;
use App\Actions\ChannelManager\RentalChannelManagerCompletedAction;
use App\DTO\ChannelManager\ExtendedRentalData;
use App\DTO\ChannelManager\RentalDiscountData;
use App\DTO\ChannelManager\RentalFeeData;
use App\DTO\ChannelManager\SeasonalRuleData;
use App\Models\Rental;
use App\Models\RentalDiscount;
use App\Models\RentalFee;
use App\Models\RentalSeasonalPrice;
use App\Models\Team;
use App\Rules\ExistsInTeamRule;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class DuplicateEverythingFromRatesAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'rental_id' => ['required', 'integer', new ExistsInTeamRule(Rental::class)],
        ];
    }

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): ExtendedRentalData
    {
        $sourceRental = Rental::getRentalModel($team, $request->input('rental_id'));
        $this->handle($team, $teamRental, $sourceRental);

        $teamRental->refresh();

        return ExtendedRentalData::from($teamRental);
    }

    public function handle(Team $team, Rental $rental, Rental $sourceRental): void
    {
        $rental->base_rate = $sourceRental->base_rate;
        $rental->min_stay = $sourceRental->min_stay;
        $rental->max_stay = $sourceRental->max_stay;
        $rental->min_price = $sourceRental->min_price;
        $rental->max_price = $sourceRental->max_price;
        $rental->extra_guest_price = $sourceRental->extra_guest_price;
        $rental->damage_deposit = $sourceRental->damage_deposit;
        $rental->damage_deposit_type = $sourceRental->damage_deposit_type;
        $rental->down_payment = $sourceRental->down_payment;
        $rental->down_payment_type = $sourceRental->down_payment_type;
        $rental->collect_down_payment = $sourceRental->collect_down_payment;
        $rental->cancellation_policy = $sourceRental->cancellation_policy;
        $rental->pricing_model = $sourceRental->pricing_model;
        $rental->save();
        // Wheelhouse config is missing.

        $rental->seasonalPrices()->delete();
        $sourceRental->seasonalPrices->each(function (RentalSeasonalPrice $season) use ($rental) {
            $seasonData = SeasonalRuleData::from($season);
            $rental->seasonalPrices()->create($seasonData->all());
        });

        $rental->rentalDiscounts()->delete();
        $sourceRental->rentalDiscounts->each(function (RentalDiscount $rDiscount) use ($rental) {
            $rentalDiscountData = RentalDiscountData::from($rDiscount);
            $rental->rentalDiscounts()->create($rentalDiscountData->all());
        });

        $rental->rentalFees()->delete();
        $sourceRental->rentalFees->each(function (RentalFee $rFee) use ($rental) {
            $rentalFeeData = RentalFeeData::from($rFee);
            $rental->rentalFees()->create($rentalFeeData->all());
        });

        if (RentalChannelManagerCompletedAction::run($rental)) {
            UpdateEverythingInChannelManagerAction::dispatch($team, $rental->id);
        }
    }
}
