<?php

namespace App\Actions\Rentals\Availability;

use App\Actions\Providers\Generic\ShouldPostToProviderInLocalEnvAction;
use App\DataProviders\ChannelManager\ChannelManagerPusher;
use App\Events\Booking\BookingAbstractEvent;
use App\Models\Booking;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Lorisleiva\Actions\Concerns\AsAction;

class SendDeltaUpdateForAvailabilityAction implements ShouldBeUniqueUntilProcessing
{
    use AsAction;

    public int $jobMaxExceptions = 2;
    public int $jobBackoff = 120;

    // Create and cancel always affect the availability. For updates, only when date is updated.
    public function asListener(BookingAbstractEvent $event): void
    {
        self::dispatch($event->team, $event->bookingId)->delay(1);
    }

    public function getJobUniqueId(Team $team, int $bookingId): string
    {
        return "{$team->id}_$bookingId";
    }

    public function asJob(Team $team, int $bookingId): void
    {
        $booking = Booking::getBookingModel($team, $bookingId);
        $rental = $booking->rental;

        $this->handle($team, $rental, $booking);
    }

    public function handle(Team $team, ?Rental $rental, Booking $booking): void
    {
        // If the rental is not found, it may have been deleted
        if (is_null($rental)) {
            pnLog("[SendDeltaUpdateForAvailabilityAction] Booking with no rental $booking->id/$booking->rental_id", $team);

            return;
        }

        // Only send availability updates if the rental is cm active
        if (! $rental->cm_active) {
            return;
        }

        // Check ShouldPostToProviderInLocalEnvAction
        if (ShouldPostToProviderInLocalEnvAction::run()) {
            pnLog("[SendDeltaUpdateForAvailabilityAction] Booking $booking->id availability", $team);
            ChannelManagerPusher::putAvailabilityChange($team, $rental, $booking);
        }
    }
}
