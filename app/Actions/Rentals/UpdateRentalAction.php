<?php

namespace App\Actions\Rentals;

use App\Actions\Cache\ClearUserRentalListCacheAction;
use App\Actions\Users\TouchTeamUsersAction;
use App\DataProviders\Providers\NoProvider;
use App\Http\Requests\Rental\RentalRequest;
use App\Http\Resources\RentalResource;
use App\Models\Rental;
use App\Models\Team;
use Auth;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateRentalAction
{
    use AsAction;

    public function asController(Team $team, int $rentalId, RentalRequest $request): RentalResource
    {
        $teamRental = Rental::query()
            ->whereId($rentalId)
            ->onTeam($team)
            ->withUncompleted() // The users must be able to update the uncompleted rentals to complete them
            ->first();

        abort_unless($teamRental->provider_id == 0, 401, 'Unauthorized');

        $attributes = $request->input();
        $rental = $this->handle($team, $teamRental, $attributes);

        return new RentalResource($rental);
    }

    public function handle(Team $team, Rental $rental, array $attributes): Rental
    {
        $attributes = array_merge($attributes, [
            'provider_id' => NoProvider::ID,
            'deleted_at' => null,
            'updated_at' => now()->timestamp,
            'completed' => true, // The frontend makes sure that the user completes all mandatory fields to ensure the rental is completed
        ]);

        $rental->fill($attributes)->save();

        // We do not have events for rental updates. We trigger here this action to invalidate the rental cache for the team.
        ClearUserRentalListCacheAction::run(Auth::user(), clearAllUsersTeam: true);
        TouchTeamUsersAction::run($team, self::class);

        return $rental;
    }
}
