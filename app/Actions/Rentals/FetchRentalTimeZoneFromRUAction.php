<?php

namespace App\Actions\Rentals;

use App\Actions\Providers\RentalsUnited\GetLocationFromCoordinatesRentalsUnitedAction;
use App\Actions\Support\Location\GetTimeZoneFromCoordinatesAction;
use App\Models\Rental;
use App\Models\RentalsUnitedLocation;
use App\Models\Team;
use DateTimeZone;
use Lorisleiva\Actions\Concerns\AsAction;

class FetchRentalTimeZoneFromRUAction
{
    use AsAction;

    public function asJob(Team $team, int $rentalId): void
    {
        $rental = Rental::getRentalModel($team, $rentalId, withTrashed: true);

        $this->handle($team, $rental);
    }

    public function handle(Team $team, Rental $rental): void
    {
        if (! $rental->lat || ! $rental->lng) {
            pnLog("[FetchRentalTimeZoneFromRUAction] Rental $rental->id has no lat/lng", $rental->team_id);

            return;
        }
        $ruId = GetLocationFromCoordinatesRentalsUnitedAction::run($rental->lat, $rental->lng);
        $ruLocation = RentalsUnitedLocation::query()->whereExternalId($ruId)->first();

        if ($ruLocation && ! empty($ruLocation->timezone)) {
            $rental->timezone = new DateTimeZone($ruLocation->timezone);
            $rental->saveQuietly();
        } else {
            $rental->timezone = GetTimeZoneFromCoordinatesAction::run($rental->lat, $rental->lng, $rental->country_code);
            $rental->saveQuietly();
        }
    }
}
