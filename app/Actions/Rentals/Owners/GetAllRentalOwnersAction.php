<?php

namespace App\Actions\Rentals\Owners;

use App\Http\Resources\RentalOwnerResource;
use App\Models\RentalOwner;
use App\Models\Team;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetAllRentalOwnersAction
{
    use AsAction;

    public function asController(Team $team): AnonymousResourceCollection
    {
        return RentalOwnerResource::collection($this->handle($team));
    }

    public function handle(Team $team): Collection
    {
        return RentalOwner::whereTeamId($team->id)->get();
    }
}
