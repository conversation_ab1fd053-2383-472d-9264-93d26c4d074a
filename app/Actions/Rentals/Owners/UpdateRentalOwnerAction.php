<?php

namespace App\Actions\Rentals\Owners;

use App\Http\Resources\RentalOwnerResource;
use App\Models\RentalOwner;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateRentalOwnerAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'name' => ['required', 'string'],
            'email' => ['required', 'email'],
            'notes' => ['sometimes', 'nullable', 'string'],
        ];
    }

    public function asController(Team $team, RentalOwner $rentalOwner, ActionRequest $request): RentalOwnerResource
    {
        $name = $request->input('name');
        $email = $request->input('email');
        $notes = $request->input('notes');

        $owner = $this->handle($rentalOwner, $name, $email, $notes);

        return new RentalOwnerResource($owner);
    }

    public function handle(RentalOwner $owner, ?string $name, ?string $email, ?string $notes = null): RentalOwner
    {
        $owner->name = $name;
        $owner->email = $email;
        $owner->notes = $notes;
        $owner->save();

        return $owner;
    }
}
