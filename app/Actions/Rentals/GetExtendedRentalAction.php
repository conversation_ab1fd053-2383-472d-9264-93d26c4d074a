<?php

namespace App\Actions\Rentals;

use App\DTO\ChannelManager\ExtendedRentalData;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsController;

class GetExtendedRentalAction
{
    use AsController;

    public function asController(Team $team, Rental $teamRental): ExtendedRentalData
    {
        return ExtendedRentalData::from($teamRental)->fillAdditional($teamRental);
    }
}
