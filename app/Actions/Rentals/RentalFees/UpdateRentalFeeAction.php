<?php

namespace App\Actions\Rentals\RentalFees;

use App\Actions\ChannelManager\Pushers\UpdateRentalInChannelManagerAction;
use App\Actions\Guests\Upsales\UpdateRentalUpsalesEnabledAction;
use App\DTO\ChannelManager\RentalFeeData;
use App\Models\Rental;
use App\Models\RentalFee;
use App\Models\Team;
use App\Models\User;
use Auth;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateRentalFeeAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental, RentalFee $rentalFee, RentalFeeData $request): RentalFeeData
    {
        $sPrice = $this->handle($team, $teamRental, $rentalFee, $request, Auth::user());

        return RentalFeeData::from($sPrice);
    }

    public function handle(Team $team, Rental $rental, RentalFee $rentalFee, RentalFeeData $request, User $initiator): RentalFee
    {
        // If the rental fee is not an upsale and the order is not set, we set it to the last order + 1.
        if (! $request->isUpsale && $request->order === null) {
            $rentalFee->order = ($rental->rentalFees->last()?->order ?? 0) + 1;
            $rentalFee->save();
        }
        // If the rental fee is an upsale and the order is set, we decrement the order of all rental fees with an order greater than the current one.
        if ($request->isUpsale && ! is_null($rentalFee->order)) {
            $rental->rentalFees()->where('order', '>', $rentalFee->order)->decrement('order');
            $request->order = null;
        }

        $rentalFee->update($request->all());

        UpdateRentalInChannelManagerAction::dispatch($team, $rental->id);

        // Update Upsales Enabled in GuestsApplicationRentalSettings
        UpdateRentalUpsalesEnabledAction::run($team, $rental, $initiator);

        return $rentalFee;
    }
}
