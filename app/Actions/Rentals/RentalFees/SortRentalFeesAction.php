<?php

namespace App\Actions\Rentals\RentalFees;

use App\Actions\ChannelManager\Pushers\UpdateRentalInChannelManagerAction;
use App\DTO\ChannelManager\RentalFeeData;
use App\Models\Rental;
use App\Models\RentalFee;
use App\Models\Team;
use App\Rules\ExistsInTeamRule;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

class SortRentalFeesAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            '*.id' => ['required', 'integer', new ExistsInTeamRule(RentalFee::class)],
            '*.order' => ['required', 'integer'],
        ];
    }

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): DataCollection
    {
        $attributes = $request->input();
        $this->handle($team, $teamRental, $attributes);

        $teamRental->load('rentalFees');

        return RentalFeeData::collection($teamRental->rentalFees);
    }

    public function handle(Team $team, Rental $rental, array $attributes): void
    {
        $rentalFees = $rental->rentalFees;

        foreach ($attributes as $row) {
            $rentalFees->firstWhere('id', '=', $row['id'])->update(['order' => $row['order']]);
        }

        UpdateRentalInChannelManagerAction::dispatch($team, $rental->id);
    }
}
