<?php

namespace App\Actions\Rentals\RentalFees;

use App\Actions\ChannelManager\Pushers\UpdateRentalInChannelManagerAction;
use App\Actions\Guests\Upsales\UpdateRentalUpsalesEnabledAction;
use App\Models\Rental;
use App\Models\RentalFee;
use App\Models\Team;
use App\Models\User;
use Auth;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;

class DeleteRentalFeeAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental, RentalFee $rentalFee): Response
    {
        $this->handle($team, $teamRental, $rentalFee, Auth::user());

        return response()->noContent();
    }

    public function handle(Team $team, Rental $rental, RentalFee $rentalFee, User $initiator): void
    {
        UpdateRentalInChannelManagerAction::dispatch($team, $rental->id);

        if (! is_null($rentalFee->order)) {
            $rental->rentalFees()->where('order', '>', $rentalFee->order)->decrement('order');
        }

        $rentalFee->delete();
        $rental->refresh();

        // Update Upsales Enabled in GuestsApplicationRentalSettings
        UpdateRentalUpsalesEnabledAction::run($team, $rental, $initiator);
    }
}
