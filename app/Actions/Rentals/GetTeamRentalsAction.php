<?php

namespace App\Actions\Rentals;

use App\Http\Resources\SlimRentalResource;
use App\Models\Rental;
use App\Models\Team;
use App\Models\User;
use App\Query\RentalQuery;
use Auth;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTeamRentalsAction
{
    use AsAction;

    public function asOnlyLastYearDeletedSlimRentals(Team $team): AnonymousResourceCollection
    {
        return SlimRentalResource::collection($this->handle(
            team: $team,
            onlyTrashedLastYear: true
        ));
    }

    public function asAllDeletedSlimRentals(Team $team): AnonymousResourceCollection
    {
        return SlimRentalResource::collection($this->handle(
            team: $team,
            onlyTrashed: true
        ));
    }

    public function handle(
        Team|int $team,
        ?User $user = null,
        bool $includeDeleted = false,
        bool $onlyTrashedLastYear = false,
        bool $onlyTrashed = false,
        bool $withUncompleted = false,
        bool $withDistribution = false,
        ?int $singleId = null,
    ): Collection {
        return Rental::query()
            ->onTeam($team)
            ->when($includeDeleted, fn (RentalQuery $query) => $query->withTrashed())
            ->when($withUncompleted, fn (RentalQuery $query) => $query->withUncompleted())
            ->when($withDistribution, fn (RentalQuery $query) => $query->with(['photos', 'channelRentals']))
            ->when(! is_null($user), fn (RentalQuery $query) => $query->whereIn('id', $user->getRentalsForUser(withUncompleted: $withUncompleted)))
            ->when($onlyTrashedLastYear, fn (RentalQuery $query) => $query->onlyTrashedLastYear())
            ->when($onlyTrashed, fn (RentalQuery $query) => $query->onlyTrashed())
            ->when($singleId, fn (RentalQuery $query) => $query->whereId($singleId))
            ->with(['innerPoster', 'team'])
            ->get()
            ->sortBy('deleted_at'); // Non deleted first // DB is not using the index when ordering internally.
    }

    public function asSlimRentals(Team $team, Request $request): AnonymousResourceCollection
    {
        return SlimRentalResource::collection($this->handle(
            team: $team,
            includeDeleted: $request->boolean('include-deleted')
        ));
    }

    public function asSingleSlimRental(Team $team, int $rentalId, Request $request): SlimRentalResource
    {
        // We use the handle instead of the model binding to be able to use the withs.
        $rental = $this->handle(
            team: $team,
            includeDeleted: $request->boolean('include-deleted'),
            withDistribution: $request->boolean('include-distribution'),
            singleId: $rentalId,
        )->first();

        abort_unless($rental, 404, 'Rental not found');

        return new SlimRentalResource($rental);
    }

    public function asUserSlimRentals(Team $team, Request $request): AnonymousResourceCollection
    {
        $withDistribution = $request->boolean('include-distribution');

        return SlimRentalResource::collection($this->handle(
            team: $team,
            user: Auth::user(),
            withUncompleted: true,
            withDistribution: $withDistribution
        ));
    }

    /**
     * @throws Exception
     */
    public function asCachedUserSlimRentals(): AnonymousResourceCollection
    {
        $user = Auth::user();

        $rentals = cache()->remember(
            "$user->id.slim_rentals",
            now()->addMinutes(10),
            fn () => $this->handle($user->current_team_id, $user));

        return SlimRentalResource::collection($rentals);
    }
}
