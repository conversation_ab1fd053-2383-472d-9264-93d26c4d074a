<?php

namespace App\Actions\BillableAddons;

use App\Enum\BillableAddonTypeEnum;
use App\Models\BillableAddon;
use App\Models\RentalNinjaTeam;
use App\Models\Team;
use App\Models\TeamPerformanceIndicator;
use App\Notifications\Internal\RentalNinjaTeamMailNotification;
use Exception;
use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsAction;

class ChargeBillableAddonsForYearlysAction
{
    use AsAction;

    public string $commandSignature = 'ninja:charge-yearly-addons';

    public function asCommand(Command $command): void
    {
        pnLog('[Charge Yearly Billable Addons + KPI] Starting command.');
        $addons = BillableAddon::query()
                               ->with(['team.teamSettings', 'team.subscriptions'])
                               ->get();

        foreach ($addons->pluck('team_id')->unique() as $teamId) {
            /** @var Team $team */
            $team = $addons->firstWhere('team_id', $teamId)->team;

            // If the team is not in yearly or if it is, but on generic trial, don't do anything and skip team
            if (! $team->isYearlySubscription() || $team->onGenericTrial()) {
                continue;
            }

            // In case it's a partner test account, give it for free. Just add all ids to delete and skip the team
            if ($team->isPartnerTestAccount()) {
                BillableAddon::query()->where('team_id', $team->id)->delete();
                continue;
            }

            // Stripe says that the minimum amount that can be charged in euros is 50 cents. Below this amount, it applies credit rather than actually charging the customer
            // Besides this, we pay a fixed commission per each transaction. So we are interested in ensuring the minimum amount charged is quite high.
            // Ensure we are above the chosen threshold otherwise we will try to charge the next month
            $teamAddons = $addons->where('team_id', $teamId);
            $totalToCharge = $teamAddons->sum(fn (BillableAddon $addon) => $addon->getTotalRevenue($addon->team->teamSettings));
            if ($totalToCharge < 5) { // EUR, not cents
                continue;
                // TODO: this may cause addons to pile up for situations where team unsubscribes. In the future, we may need to clean the DB
            }

            // Stripe needs to have a default payment method at the customer level to be able to charge the customer.
            if (! $this->ensureDefaultPaymentMethod($team)) {
                continue;
            }

            // Beyond the point, we are certain we will invoice the customer
            $kpi = TeamPerformanceIndicator::getOrCreateForTeam($team);
            /** @var BillableAddon $addon */
            foreach ($teamAddons as $addon) {
                $addon->reportAddonToKpi($kpi, $team->teamSettings);
            }

            // Ensure each price is created in Stripe + add price to invoice
            $teamAddons->pluck('type')->unique()->each(function (BillableAddonTypeEnum $type) use ($team, $teamAddons) {
                $thisTypeAddons = $teamAddons->where('type', $type);
                $stripePriceId = EnsureBillableAddonPriceCreatedInStripeAction::run($team, $thisTypeAddons->first());
                $quantity = $thisTypeAddons->sum(fn (BillableAddon $addon) => $addon->getUsageToReportToStripe($team, $team->teamSettings));
                $team->tabPrice($stripePriceId, $quantity, ['description' => $type->getInvoiceDescription()]);

                // Delete already invoiced items right away. If the code breaks after, we don't want to charge our customer again
                BillableAddon::query()->whereIn('id', $thisTypeAddons->pluck('id'))->delete();
            });

            // Save the KPIs now that the subscription contains the addons. If invoicing fails, we will try again from stripe directly
            $kpi->save();

            // Invoice the team
            try {
                $team->invoice([
                    'footer' => 'For more information about commissions charged on Bookings (such as Upsells or our Booking Engine), you can export the bookings from our Bookings page and check any commission amount.',
                ]);
            } catch (Exception $e) {
                $rentalNinja = RentalNinjaTeam::getInstance();
                $rentalNinja->email = RentalNinjaTeam::ACCOUNTANCY_EMAIL;
                $rentalNinja->notify(new RentalNinjaTeamMailNotification(
                    'Warning! Failed when invoicing addons of a Yearly Subscription',
                    "The team with id $team->id has addons that should be invoiced but we couldn't invoice them. Total charge amount: $totalToCharge €. Go to Stripe, check the reason and contact the team to charge the invoice.",
                    "Note: The addons are already in Stripe's and in the KPIs, so we just need to charge the customer and no extra action is required.",
                ));
                continue; // Abort this team
            }
        }

        pnLog('[Charge Yearly Billable Addons + KPI] Finishing command.');
    }

    private function ensureDefaultPaymentMethod(Team $team): bool
    {
        $customerDefault = $team->asStripeCustomer()->invoice_settings['default_payment_method'];

        if ($customerDefault) {
            return true;
        }

        $subscriptionPaymentMethod = $team->subscription()->asStripeSubscription()->default_payment_method;
        if (! $subscriptionPaymentMethod) {
            report(new Exception("[BIG WARNING] Team $team->id has no default payment method for his subscription. Yearly Billable Addons not charged."));

            return false;
        }

        $team->updateStripeCustomer([
            'invoice_settings' => [
                'default_payment_method' => $subscriptionPaymentMethod,
            ],
        ]);

        return true;
    }
}
