<?php

namespace App\Actions\BillableAddons;

use App\Models\BillableAddon;
use App\Models\Team;
use App\Support\NinjaCashier;
use Lorisleiva\Actions\Concerns\AsAction;

class EnsureBillableAddonPriceCreatedInStripeAction
{
    use AsAction;

    public function handle(Team $team, BillableAddon $addon): string
    {
        $priceLookupKey = $addon->type->stripePriceLookupKey($team, $team->teamSettings);
        $cashier = NinjaCashier::initialize();
        $stripePriceId = $cashier->getPriceByLookupKey($priceLookupKey)?->id;

        if (! $stripePriceId) {
            $stripePriceId = $cashier->createPrice($addon->getDataToCreateStripePrice($team, $team->teamSettings, $priceLookupKey))
                ->id;
        }

        return $stripePriceId;
    }
}
