<?php

namespace App\Actions\BillableAddons;

use App\Enum\BillableAddonTypeEnum;
use App\Models\BillableAddonLog;
use App\Models\Rental;
use App\Models\Team;
use App\Rules\ExistsInTeamRule;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetAddonsUsageAction
{
    use AsAction;

    public function rules(): array
    {
        // We only allow frontend AI actions to use the controller.
        return [
            'type' => ['nullable'],
            'from' => ['nullable', 'date'],
            'to' => ['nullable', 'date'],
            'rental_id' => ['nullable', 'integer', new ExistsInTeamRule(Rental::class)],
            'user_id' => ['nullable', 'integer'],
        ];
    }

    public function asController(Team $team, ActionRequest $request): array
    {
        $rawType = $request->input('type');
        $type = is_null($rawType) ? null : BillableAddonTypeEnum::from($rawType);
        $from = $request->date('from');
        $to = $request->date('to');
        $rentalId = $request->input('rental_id');
        $userId = $request->input('user_id');

        $data = $this->handle($team, $type, $from, $to, $rentalId, $userId);

        return ['data' => $data];
    }

    public function handle(Team $team, ?BillableAddonTypeEnum $type, ?Carbon $from, ?Carbon $to, ?int $rentalId, ?int $userId): Collection
    {
        return BillableAddonLog::query()
            ->whereTeamId($team->id)
            ->when($type, fn ($query) => $query->whereType($type))
            ->when($from, fn ($query) => $query->where('created_at', '>=', $from))
            ->when($to, fn ($query) => $query->where('created_at', '<=', $to))
            ->when($rentalId, fn ($query) => $query->whereRentalId($rentalId))
            ->when($userId, fn ($query) => $query->whereUserId($userId))
            ->get();
    }
}
