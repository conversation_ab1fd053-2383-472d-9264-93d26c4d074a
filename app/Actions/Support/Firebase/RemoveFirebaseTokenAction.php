<?php

namespace App\Actions\Support\Firebase;

use App\Actions\Users\GetApiUserAction;
use App\Models\UserNotificationToken;
use Lorisleiva\Actions\Concerns\AsAction;

class RemoveFirebaseTokenAction
{
    use AsAction;

    public function asController(int $userId, ?string $token = null)
    {
        $this->handle($userId, $token);

        return GetApiUserAction::run();
    }

    public function handle(int $userId, ?string $token): void
    {
        UserNotificationToken::whereUserId($userId)
            ->when(! empty($token), fn ($query) => $query->whereToken($token))
            ->delete();
    }
}
