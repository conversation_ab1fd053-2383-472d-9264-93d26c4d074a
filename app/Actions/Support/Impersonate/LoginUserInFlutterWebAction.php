<?php

namespace App\Actions\Support\Impersonate;

use App\Actions\Auth\GetLoginQrCodeAction;
use App\Flavors\DefaultFlavor;
use App\Models\User;
use Lorisleiva\Actions\Concerns\AsAction;

class LoginUserInFlutterWebAction
{
    use AsAction;

    /**
     * Execute the action and return a result.
     */
    public function handle(User $userToImpersonate): string
    {
        // Get users email and generated password
        $email = $userToImpersonate->email;
        $qrPassword = GetLoginQrCodeAction::run($userToImpersonate);
        // Get the domain to redirect this user to.
        if ($userToImpersonate->current_team_id !== null) {
            $domain = $userToImpersonate->config()->impersonationTargetDomain();
        } else {
            $domain = (new DefaultFlavor())->impersonationTargetDomain();
        }

        // encode email
        $email = urlencode($email);
        // encode password
        $qrPassword = urlencode($qrPassword);

        // Redirect the user to the domain with the QR code.
        return "$domain/__/auth/login?email=$email&code=$qrPassword";
    }
}
