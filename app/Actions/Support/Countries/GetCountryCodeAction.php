<?php

namespace App\Actions\Support\Countries;

use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCountryCodeAction
{
    use AsAction;

    /**
     * Returns the Country code of the given country.
     * Execute the action and return a result.
     */
    public function handle(string $countryName)
    {
        if ($country = GetCountryAction::run($countryName)) {
            $val = Str::of($country->code_2)->upper();

            return $val->isNotEmpty() ? (string) $val : '';
        }

        return '';
    }
}
