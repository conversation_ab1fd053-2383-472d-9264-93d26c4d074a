<?php

namespace App\Actions\Support\Countries;

use App\Models\Country;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCountryAction
{
    use AsAction;

    /**
     * Execute the action and return a result.
     */
    public function handle(string $countryName): ?Country
    {
        return Country::query()
            ->where('name', 'like', "%$countryName%")
            ->orderBy('calling_code')
            ->first();
    }
}
