<?php

namespace App\Actions\Support;

use App\Models\Booking;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsAction;
use Psr\Log\LogLevel;

class FillUpNewBookingAndRentalColumnsTemporalAction
{
    use AsAction;

    public string $commandSignature = 'ninja-temp:fill-up-new-booking-and-rental-info';

    private ?Command $command = null;

    public function asCommand(Command $command): void
    {
        $this->command = $command;
        $this->handle();
    }

    public function handle(): void
    {
        $providers = collect([1, 2, 3, 4]);

        $this->info('Starting!');
        pnLog('[FillUpNewBookingAndRentalColumnsTemporalAction] Starting', level: LogLevel::NOTICE);
        // We start with active teams
        foreach ($providers as $provider) {
            $active = Team::query()->activeTeams()->whereProviderId($provider)->pluck('id');
            Rental::query()->whereIn('team_id', $active)->withTrashed()->update(['provider_id' => $provider]);
            Booking::query()->whereIn('team_id', $active)->update(['provider_id' => $provider]);
            pnLog("[FillUpNewBookingAndRentalColumnsTemporalAction] Active $provider done", level: LogLevel::NOTICE);
        }
        $this->info('Half Done!');
        pnLog('[FillUpNewBookingAndRentalColumnsTemporalAction] Active ALL done', level: LogLevel::NOTICE);

        // Other teams
        foreach ($providers as $provider) {
            $active = Team::query()->activeTeams()->whereProviderId($provider)->pluck('id');
            $teamsIds = Team::query()->whereProviderId($provider)->pluck('id');
            $other = $teamsIds->diff($active)->values();
            Rental::query()->whereIn('team_id', $other)->withTrashed()->update(['provider_id' => $provider]);
            Booking::query()->whereIn('team_id', $other)->update(['provider_id' => $provider]);
            pnLog("[FillUpNewBookingAndRentalColumnsTemporalAction] Rest $provider done", level: LogLevel::NOTICE);
        }
        $this->info('Done!');
        pnLog('[FillUpNewBookingAndRentalColumnsTemporalAction] Completed', level: LogLevel::NOTICE);
    }

    private function info(string $message): void
    {
        $this->command?->info($message);
    }
}
