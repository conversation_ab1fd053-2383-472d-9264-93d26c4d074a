<?php

namespace App\Actions\Support;

use App\Models\Locale;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetAllLanguagesAction
{
    use AsAction;

    public function asController(ActionRequest $request): Collection
    {
        return $this->handle();
    }

    public function handle(): Collection
    {
        return Locale::query()->get();
    }
}
