<?php

namespace App\Actions\Support\Queue;

use App\DTO\Queue\NinjaQueueJobDto;
use App\Models\NinjaQueueJob;
use Illuminate\Queue\Jobs\Job;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class GetNinjaQueueJobAction
{
    use AsAction;

    /**
     * Execute the action and return a result.
     */
    public function handle(mixed $event): NinjaQueueJobDto
    {
        /** @var Job $job */
        $job = $event->job;
        $payload = $job->payload();
        $name = data_get($payload, 'displayName', 'Unknown');
        $serializedData = json_decode(json_encode(unserialize($job->payload()['data']['command'], ['allowed_classes' => false])), true);
        $model = NinjaQueueJob::query()
            ->firstOrCreate([
                'job_class' => $name,
                'date' => now()->toDateString(),
                'connection' => $job->getConnectionName(),
                'endpoint' => data_get($serializedData, 'table_name') ?? data_get($serializedData, 'endpoint') ?? null,
                'provider' => $provider ?? collect(data_get($serializedData, 'team.relations'))
                    ->filter(fn ($relation) => $relation != 'providerAccount')
                    ->filter(fn ($relation) => Str::contains($relation, ['Account', 'account']))
                    ->map(fn ($provider) => Str::before($provider, 'Account'))
                    ->first() ?? null,
            ]);

        return new NinjaQueueJobDto(model: $model, job: $job);
    }
}
