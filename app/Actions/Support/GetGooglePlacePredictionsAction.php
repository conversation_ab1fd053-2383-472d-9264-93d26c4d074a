<?php

namespace App\Actions\Support;

use Illuminate\Support\Facades\Http;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsController;

class GetGooglePlacePredictionsAction
{
    use AsController;

    public function asController(ActionRequest $request): array
    {
        $places = $request->input('input');
        $googlePlacesApiKey = config('services.google_maps');

        $url = 'https://maps.googleapis.com/maps/api/place/autocomplete/json';

        return Http::timeout(5)
            ->get($url, [
                'input' => $places,
                'key' => $googlePlacesApiKey,
                'types' => 'address',
            ])
            ->json('predictions');
    }
}
