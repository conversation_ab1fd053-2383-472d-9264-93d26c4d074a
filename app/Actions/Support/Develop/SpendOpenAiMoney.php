<?php

namespace App\Actions\Support\Develop;

use Faker\Factory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class SpendOpenAiMoney
{
    use AsAction;

    public string $commandSignature = 'ninja-dev:spend-openai-money';

    private array $models = ['gpt-4o', 'gpt-4o-mini', 'gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo'];

    public function asCommand(Command $command): int
    {
        // use the factory to create a Faker\Generator instance
        $faker = Factory::create();

        // Generate a random person, place, and action for the question
        $person = $faker->name;
        $place = $faker->city;
        $action = $faker->randomElement(['visited', 'talked about', 'learned about']);

        // Construct the custom question
        $question = "What can you tell me about the time when $person $action $place?";

        foreach ($this->models as $model) {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer '.config('openai.api_key'),
            ])
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => $model,
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => [
                                [
                                    'type' => 'text',
                                    'text' => $question,
                                ],
                            ],
                        ],
                    ],
                ]);

            // If response failed, log the error and return
            if ($response->failed()) {
                $command->error("[AI Answer][$model]: Failed to get response from OpenAI");
                $error = $response->json('error.message');
                pnLog("[AI Answer][$model]: Failed to get response from OpenAI: $error");
            } else {
                $response = $response->json('choices.0.message.content');

                $command->info($response);
                pnLog("[AI Answer][$model]: ".Str::limit($response, 50, '...'));
            }
        }

        return $command::SUCCESS;
    }
}
