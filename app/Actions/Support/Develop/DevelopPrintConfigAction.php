<?php

namespace App\Actions\Support\Develop;

use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsCommand;

class DevelopPrintConfigAction
{
    use AsCommand;

    public string $commandSignature = 'ninja-dev:print-config {param}';

    public function asCommand(Command $command): int
    {
        $param = $command->argument('param');
        $command->info("Printing $param");
        $result = config($param);
        if (is_array($result)) {
            $result = json_encode($result);
        }
        $command->info("Result: $result");

        return $command::SUCCESS;
    }
}
