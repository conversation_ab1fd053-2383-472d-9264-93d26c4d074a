<?php

namespace App\Actions\Support\Develop;

use App\DataProviders\ApiResolvers\ProviderResourceResolver;
use App\Models\RentalSeasonalPrice;
use App\Models\Team;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class BSRatesResolver extends ProviderResourceResolver
{
    protected function callback(Team $team, Collection $response, bool $force = false, $singleId = 0, $extraInfo = []): void
    {
        $rates = $response->get('rates', $response);
        foreach ($rates as $rate) {
            $order = Carbon::parse($rate['start_date'])->diffInDays(Carbon::today());
            $name = 'Rate from '.Str::before($rate['start_date'], 'T');
            RentalSeasonalPrice::updateOrInsert([
                'team_id' => $team->id,
                'rental_id' => $rate['links']['rental'],
                'start_from' => $rate['start_date'],
                'valid_until' => $rate['end_date'],
            ], [
                'name' => $name,
                'weekdays' => '[0,1,2,3,4,5,6]',
                'value_in_cents' => $rate['final_nightly_rate'] * 100,
                'extra_guest_amount_in_cents' => 1000,
                'min_stay' => $rate['minimum_stay'],
                'strategy' => 'fixed',
                'order' => $order,
            ]);
        }
    }
}
