<?php

namespace App\Actions\Support\Develop;

use App\Models\ProviderEvent;
use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsCommand;

class DevelopFixProviderEventsAction
{
    use AsCommand;

    public string $commandSignature = 'ninja-dev:fix-provider-events {team=null}';

    const AFFECTED = ProviderEvent::TASK_EVENTS;

    public function asCommand(Command $command): int
    {
        $done = 0;
        $team = $command->argument('team');
        $team = $team == 'null' ? null : $team;

        ProviderEvent::query()
            ->whereIn('type', self::AFFECTED)
            ->when(! is_null($team), fn ($q) => $q->where('team_id', '=', $team))
            ->where('data', 'like', '%"requester_id": "20%')
            ->limit(10000)
            ->get()
            ->each(function (ProviderEvent $event) use (&$done) {
                $data = $event->data;

                $data['requester'] = null;
                $data['requester_id'] = null;
                $event->data = $data;
                $event->save();
                $done++;
            });
        $command->info("$done task events migrated");

        return $command::SUCCESS;
    }
}
