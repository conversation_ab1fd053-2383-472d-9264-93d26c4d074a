<?php

namespace App\Actions\Support\Jobs;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;

class RegisterJobReleaseAction
{
    use AsAction;

    public string $commandSignature = 'ninja:register-job-release {provider} {endpoint}';

    public function asCommand(Command $command): void
    {
        $this->handle($command->argument('provider'), $command->argument('endpoint'));
    }

    public function handle(int $provider, string $endpoint): void
    {
        $key = "release-counter-$provider-$endpoint";
        Cache::tags('release-counter')->increment($key);
    }
}
