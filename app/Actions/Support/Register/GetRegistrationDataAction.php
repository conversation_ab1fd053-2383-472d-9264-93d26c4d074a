<?php

namespace App\Actions\Support\Register;

use App\DataProviders\Providers\NinjaProvider;
use App\DataProviders\Providers\NoProvider;
use App\DTO\Team\PlanData;
use App\Flavors\DefaultFlavor;
use App\Models\TeamRegistrationSteps;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsController;
use Symfony\Component\HttpFoundation\Response;

class GetRegistrationDataAction
{
    use AsController;

    public function asController(ActionRequest $request): JsonResponse
    {
        $ninjaRegistrationToken = $request->header('Ninja-Register-Token') ?? Str::random(64);

        $data = new TeamRegistrationSteps();
        $data->setAskingForProvider();
        $data->token = $ninjaRegistrationToken;
        // Default parameters
        $data->provider_id = NoProvider::ID;
        $data->flavor_id = DefaultFlavor::FLAVOR_ID;
        $data->save();

        return response()->json(
            data: [
                'registration_token' => $ninjaRegistrationToken,
                'providers' => NinjaProvider::getProvidersWithSignup()
                    ->map(fn (NinjaProvider $p) => $p->providerConfiguration())->values(),
                'plans' => PlanData::getPlans(availableForSaleOnly: true), // TODO: Jayesh is checking if this is used in the frontend
            ],
            status: Response::HTTP_OK,
        );
    }
}
