<?php

namespace App\Actions\Support\Postman;

use App\Models\ApiRoute;
use Illuminate\Console\Command;
use Illuminate\Routing\Route;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ExportPostmanCollectionFromApiRoutesAction
{
    use AsAction;

    public string $commandSignature = 'ninja:export-postman-import-api-routes';

    public string $commandDescription = 'Export Postman Collection From Api Routes';

    private ?Command $command = null;

    private ?array $structure = null;

    /**
     * @throws Throwable
     */
    public function asCommand(Command $command): int
    {
        $this->command = $command;
        $this->handle();

        return $command::SUCCESS;
    }

    /**
     * Execute the action and return a result.
     */
    public function handle(): void
    {
        // Initialize the structure of the postman.
        $this->initializeStructure();

        // Load the Ninja Routes to get the prefix
        $routes = ApiRoute::query()->get();
        $parsedRoutes = collect();
        foreach ($routes as $route) {
            try {
                // Skip frontend routes, which give problems when trying to parse path parameters and substitute them by url parameters (makeRequest() method)
                if (Str::of($route->url)->contains('{all}')) {
                    continue;
                }

                // Generate a false route from Laravel to see if we can get the prefix of that route.
                $appRoute = app('router')
                    ->getRoutes()
                    ->match(app('request')->create(uri: $route->url, method: $route->method));
                /** @var ApiRoute $route */
                if ($this->routeIsIgnored($route, $appRoute)) {
                    continue;
                } elseif ($appRoute->uri() === '{fallbackPlaceholder}') {
                    continue;
                } elseif (Str::startsWith($appRoute->uri(), config('postman.ignore_routes_starting_with'))) {
                    continue;
                } elseif (in_array($appRoute->getPrefix(), config('postman.ignored_prefixes'))) {
                    continue;
                }

                // If this is a web route, check if we have an exact match with an api route.
                if ($apiRoute = $this->isWebRouteAndHasAPiCounterPart($appRoute)) {
                    $appRoute = $apiRoute;
                    // Modify request to match new url.
                    $prefix = $appRoute->getPrefix().'/';
                    $route->url = Str::of($route->url)->prepend($prefix)->__toString();
                    $route->path = Str::of($route->path)->prepend($prefix)->__toString();
                }

                // Ignore possible duplicates added from either web or api routes (they both use ninja_routes).
                if ($parsedRoutes->where('url', $route->url)->where('method', $route->method)->count() > 0) {
                    continue;
                }

                $request = $this->makeRequest($route);
                $routeUri = explode('/', $route->url);
                $routeNames = implode('.', $routeUri);
                $routeNames = explode('.', $routeNames);
                /** @var array $routeNames */
                $routeNames = array_filter($routeNames, function ($value) {
                    if (is_null($value) || $value === '') {
                        return false;
                    }

                    return ! Str::contains($value, ['{', 'v2', 'v3', 'api']);
                });

                if (Arr::first($routeNames) === 'team') {
                    array_shift($routeNames);
                }

                if ($prefix = $appRoute->getPrefix()) {
                    if (Str::contains($prefix, config('postman.ignored_prefixes'))) {
                        continue;
                    }
                    array_unshift($routeNames, $prefix);
                } else {
                    array_unshift($routeNames, 'web');
                }

                $this->buildTree($this->structure, $routeNames, $request);
                $this->info($route->url);
                $parsedRoutes->add($route);
            } catch (Throwable $e) {
                $this->warning($e->getMessage());
            }
        }

        $fileName = $this->fileName();
        $structure = $this->structure;
        Storage::disk('s3')->put($exportName = "postman/$fileName", json_encode($structure, JSON_PRETTY_PRINT));
        $this->info("Postman Collection Exported: $exportName");
    }

    protected function initializeStructure(): void
    {
        $this->structure = [
            'info' => [
                'name' => $this->name(),
                'schema' => 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
                'description' => 'Rental Ninja API Documentation',
            ],
            'variable' => config('postman.collection_variables'),
            'auth' => config('postman.collection_auth'),
            'item' => $this->oauthItems(),
        ];
    }

    private function name(): string
    {
        $env = app()->environment();

        return Str::title("Rental Ninja $env");
    }

    /**
     * Returns Oauth items included by default in the Postman Collection.
     *
     * @return array[]
     */
    private function oauthItems(): array
    {
        return [
            [
                'name' => 'oauth',
                'item' => [
                    [
                        'name' => 'token',
                        'request' => [
                            'method' => 'POST',
                            'url' => [
                                'raw' => '{{SERVER}}/oauth/token',
                                'host' => [
                                    '{{SERVER}}',
                                ],
                                'path' => [
                                    'oauth',
                                    'token',
                                ],
                            ],
                            'body' => [
                                'mode' => 'formdata',
                                'formdata' => [
                                    [
                                        'key' => 'grant_type',
                                        'value' => 'password',
                                        'type' => 'text',
                                    ],
                                    [
                                        'key' => 'client_id',
                                        'value' => '{{CLIENT_ID}}',
                                        'type' => 'text',
                                    ],
                                    [
                                        'key' => 'client_secret',
                                        'value' => '{{CLIENT_SECRET}}',
                                        'type' => 'text',
                                    ],
                                    [
                                        'key' => 'username',
                                        'value' => '{{USERNAME}}',
                                        'type' => 'text',
                                    ],
                                    [
                                        'key' => 'password',
                                        'value' => '{{PASSWORD}}',
                                        'type' => 'text',
                                    ],
                                    [
                                        'key' => 'qr_password',
                                        'type' => 'text',
                                    ],
                                ],
                            ],
                        ],
                        'event' => [
                            [
                                'listen' => 'test',
                                'script' => [
                                    'type' => 'text/javascript',
                                    'exec' => $this->oauthScript(),
                                ],
                            ],
                        ],
                    ],
                    [
                        'name' => 'refresh-token',
                        'request' => [
                            'method' => 'POST',
                            'url' => [
                                'raw' => '{{SERVER}}/oauth/token',
                                'host' => [
                                    '{{SERVER}}',
                                ],
                                'path' => [
                                    'oauth',
                                    'token',
                                ],
                            ],
                            'body' => [
                                'mode' => 'formdata',
                                'formdata' => [
                                    [
                                        'key' => 'grant_type',
                                        'value' => 'refresh_token',
                                        'type' => 'text',
                                    ],
                                    [
                                        'key' => 'client_id',
                                        'value' => '{{CLIENT_ID}}',
                                        'type' => 'text',
                                    ],
                                    [
                                        'key' => 'client_secret',
                                        'value' => '{{CLIENT_SECRET}}',
                                        'type' => 'text',
                                    ],
                                    [
                                        'key' => 'refresh_token',
                                        'value' => '{{REFRESH_TOKEN}}',
                                        'type' => 'text',
                                    ],
                                ],
                            ],
                        ],
                        'event' => [
                            [
                                'listen' => 'test',
                                'script' => [
                                    'type' => 'text/javascript',
                                    'exec' => $this->oauthScript(),
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    private function oauthScript(): array
    {
        return [
            'var data = JSON.parse(responseBody);',
            'const access_token = data.access_token;',
            'const refresh_token = data.refresh_token;',
            'const team = data.team;',
            'pm.collectionVariables.set("ACCESS_TOKEN", access_token);',
            'pm.collectionVariables.set("REFRESH_TOKEN", refresh_token);',
            'pm.collectionVariables.set("TEAM", team);',

            'const getRequest = {',
            '   url: pm.variables.get("SERVER") + "/api/v3/slim-rentals/" + team + "/for-user",',
            '   method: "GET",',
            '   header: {',
            '       "Content-Type": "application/json",',
            '       "Accept": "application/json",',
            '       "Authorization": "Bearer " + access_token,',
            '   },',
            '};',

            'pm.sendRequest(getRequest, (error, response) => {',
            '   const rentals = response.json().data;',
            '   const rentalIds = rentals.map(rental => rental.id).join(",");',
            '   pm.collectionVariables.set("RENTALS", "["+ rentalIds +"]")',
            '});',
        ];
    }

    private function routeIsIgnored(ApiRoute $apiRoute, Route $appRoute)
    {
        return in_array($appRoute->getPrefix(), config('postman.ignored_prefixes')) ||
            in_array($apiRoute->url, config('postman.ignored_routes'));
    }

    private function isWebRouteAndHasAPiCounterPart(Route $route): ?Route
    {
        if (! in_array('web', $route->middleware())) {
            return null;
        }

        /** @var Collection $apiRoutes */
        $apiRoutes = once(
            fn () => collect(app('router')->getRoutes())
                ->filter(fn (Route $route) => $route->getPrefix() === 'api' || $route->getPrefix() === 'api/v3')
                ->mapWithKeys(fn (Route $route) => [
                    Str::of($route->uri())
                        ->replaceFirst($route->getPrefix(), '')
                        ->replaceFirst('/', '')
                        ->prepend(collect($route->methods)->join('-').'-')
                        ->__toString() => $route,
                ])
        );

        $slug = Str::of($route->uri())->prepend(collect($route->methods)->join('-').'-')->__toString();

        return $apiRoutes->get($slug);
    }

    private function makeRequest(ApiRoute $route): array
    {
        $params = Str::of($route->url)->matchAll('/{+(.*?)}/');
        $index = 0;
        $values = [];
        $uri = Str::of($route->path)->replaceMatches('/((?<=\/)(\d+))/', function ($matches) use (&$index, &$values, $params) {
            $paramName = Str::camel($params[$index++]);
            $key = ':'.$paramName;
            $values[$paramName] = $matches[0];

            return $key;
        });
        $variables = collect($values)->map(function ($value, $key) {
            return [
                'key' => $key,
                'value' => $this->variableValueForKey($value, $key),
            ];
        });
        $headers = collect($route->headers)->map(fn ($value, $key) => ['key' => Str::title($key), 'value' => stripslashes(Str::lower(Arr::first($value)))])->toArray();
        $headers = $this->routeHeaders()->push(...$headers)->unique(fn ($val) => $val['key'])->filter()->toArray();
        $data = [
            'name' => $route->url,
            'request' => [
                'method' => strtoupper($route->method),
                'header' => $headers,
                'url' => [
                    'raw' => '{{SERVER}}/'.$uri,
                    'host' => ['{{SERVER}}'],
                    'path' => $uri->explode('/')->filter()->toArray(),
                ],
            ],
        ];

        if (collect($route->query)->isNotEmpty()) {
            $data['request']['url']['query'] = collect($route->query)
                ->map(function ($value, $key) {
                    return [
                        'key' => $key,
                        'value' => is_array($value) ? implode(',', $value) : $value,
                    ];
                })->toArray();
        } else {
            $data['request']['url']['query'] = [];
        }
        if ($variables->isNotEmpty()) {
            $data['request']['url']['variable'] = $variables->values()->toArray();
        }

        if (! empty($route->body)) {
            $data['request']['body'] = [
                'mode' => 'raw',
                'raw' => stripslashes(json_encode($this->parsedBody($route->body), JSON_PRETTY_PRINT)),
            ];
        }
        $tests = trim($route->tests);
        if (! empty($tests)) {
            $data['event'] = [[
                'listen' => 'test',
                'script' => [
                    'type' => 'text/javascript',
                    'exec' => [$tests],
                ],
            ]];
        }

        $description = trim($route->description);
        if (! empty($description)) {
            $data['request']['description'] = [
                'content' => $description,
                'type' => 'text/markdown',
            ];
        }

        return $data;
    }

    private function variableValueForKey(string $value, string $key): string
    {
        $variablesNames = collect(config('postman.variables_to_env_variables'));
        if ($variablesNames->keys()->contains($key)) {
            return $variablesNames->get($key);
        }

        return $value;
    }

    private function routeHeaders(): Collection
    {
        return once(fn () => collect([
            [
                'key' => 'Accept',
                'value' => 'application/json',
            ],
            [
                'key' => 'Content-Type',
                'value' => 'application/json',
            ],
            [
                'key' => 'Authorization',
                'value' => 'Bearer {{ACCESS_TOKEN}}',
            ],
        ]));
    }

    private function parsedBody(array $body): array
    {
        $variables = collect(config('postman.variables_to_env_variables'));
        $keys = $variables->keys();
        $dotted = Arr::dot($body);
        if (empty($dotted)) {
            return $body;
        }
        $toAdd = collect();
        $dotted = collect($dotted)->filter(function ($value, $key) use ($toAdd, $keys) {
            if (! Str::contains($key, $keys->toArray())) {
                return true;
            }
            $numeric = is_numeric(Str::afterLast($key, '.'));
            if ($numeric) {
                $toAdd->put(Str::beforeLast($key, '.'), []);

                return false;
            }

            return true;
        });
        if ($toAdd->isNotEmpty()) {
            $dotted = $dotted->merge($toAdd);
        }
        foreach ($dotted as $key => $value) {
            if ($keys->contains($key)) {
                data_set($body, $key, $variables->get($key));
            } elseif ($keys->contains($last = Str::afterLast($key, '.'))) {
                data_set($body, $key, $variables->get($last));
            }
        }

        return $body;
    }

    protected function buildTree(array &$routes, array $segments, array $request): void
    {
        $parent = &$routes;
        $destination = Arr::last($segments);

        foreach ($segments as $segment) {
            $matched = false;

            foreach ($parent['item'] as &$item) {
                if ($item['name'] === $segment) {
                    $parent = &$item;
                    $segmentEqualsDestination = $segment === $destination;
                    if ($segmentEqualsDestination) {
                        $parent['item'][] = $request;
                    }
                    $matched = true;
                    break;
                }
            }

            unset($item);

            if (! $matched) {
                if ($segment == $destination) {
                    $item = ['name' => $segment, 'item' => [$request]];
                } else {
                    $item = ['name' => $segment, 'item' => []];
                }

                $parent['item'][] = &$item;
                $parent = &$item;
            }

            unset($item);
        }
    }

    private function info(string $message)
    {
        $this->command?->info($message);
    }

    private function warning(string $message)
    {
        $this->command?->warn($message);
    }

    private function fileName(): string
    {
        $env = app()->environment();

        return "ninja_postman_$env.json";
    }
}
