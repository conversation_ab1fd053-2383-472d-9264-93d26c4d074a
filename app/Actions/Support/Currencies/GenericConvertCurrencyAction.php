<?php

namespace App\Actions\Support\Currencies;

use Lorisleiva\Actions\Concerns\AsAction;

class GenericConvertCurrencyAction
{
    use AsAction;

    public function handle(float $amount, string $fromCurrency, string $toCurrency): float
    {
        if (strcmp($fromCurrency, $toCurrency) == 0) {
            return $amount;
        }

        $exchange = GetCurrencyExchangeRateByCurrencyAction::run($fromCurrency, $toCurrency);

        return round($amount / $exchange['rate'], 2);
    }
}
