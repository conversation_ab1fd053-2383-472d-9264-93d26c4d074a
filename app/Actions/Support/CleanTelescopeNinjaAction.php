<?php

namespace App\Actions\Support;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Lorisle<PERSON>\Actions\Concerns\AsCommand;

/**
 * This is a custom command to be able to delete the table telescope_entry_tags.
 * The Laravel Telescope command assumes that there are foreign keys on the database, which are not allowed in planet scale.
 * Moreover, we delete all entries except those generated in the past 15 minutes. This avoids the annoying situation in
 * which a entry is deleted just when is going to be checked.
 */
class CleanTelescopeNinjaAction
{
    use AsCommand;

    const SIGNATURE = 'ninja:clean-telescope';

    public string $commandSignature = self::SIGNATURE;

    public function asCommand(Command $command): void
    {
        $this->cleanTelescopeMonitoring();
        $this->cleanTelescopeTags();
        $this->cleanTelescopeEntries($command);
    }

    private function cleanTelescopeEntries(Command $command): void
    {
        $query = DB::table('telescope_entries')
            ->where('created_at', '<', now()->subMinutes(15));

        $totalDeleted = 0;

        do {
            $deleted = $query->take(1000)->delete();

            $totalDeleted += $deleted;
        } while ($deleted !== 0);

        $command->info("A total of $totalDeleted rows have been deleted");
    }

    private function cleanTelescopeMonitoring(): void
    {
        do {
            $deleted = DB::table('telescope_monitoring')->take(10000)->delete();
        } while ($deleted !== 0);
    }

    private function cleanTelescopeTags(): void
    {
        do {
            $deleted = DB::table('telescope_entries_tags')->take(10000)->delete();
        } while ($deleted !== 0);
    }
}
