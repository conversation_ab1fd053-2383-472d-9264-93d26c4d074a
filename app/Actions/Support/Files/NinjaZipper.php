<?php

namespace App\Actions\Support\Files;

use App\Actions\Storage\CreateHashDownloadableRouteAction;
use App\Actions\Storage\GetStorageDirectoryAction;
use Illuminate\Support\Facades\Storage;
use Spatie\TemporaryDirectory\Exceptions\PathAlreadyExists;
use Spatie\TemporaryDirectory\TemporaryDirectory;
use ZipArchive;

class NinjaZipper
{
    private ZipArchive $zip;

    private ?string $currentFolder = null;

    private TemporaryDirectory $directory;

    private string $name;

    /**
     * @throws PathAlreadyExists
     */
    public function __construct(string $name)
    {
        $this->name = $name;
        $this->directory = (new TemporaryDirectory())
            ->force()
            ->create()
            ->empty();
        $this->zip = new ZipArchive();
    }

    /**
     * @throws PathAlreadyExists
     */
    public static function create(string $name): self
    {
        $self = new self($name);
        $self->zip->open($self->zipPath(), ZipArchive::CREATE);

        return $self;
    }

    public function zipPath(): string
    {
        return $this->directory->path($this->name);
    }

    public function folder(?string $folder): self
    {
        $this->currentFolder = $folder;

        return $this;
    }

    public function addString(string $fileName, string $contents): self
    {
        $name = $this->getFileName($fileName);
        $this->zip->addFromString($name, $contents);

        return $this;
    }

    public function onRootFolder(): self
    {
        $this->currentFolder = null;

        return $this;
    }

    public function download(string $amazonPath): string
    {
        $hashRoute = CreateHashDownloadableRouteAction::run($this->store($amazonPath));

        return route('ninjaDownloader', ['hash' => $hashRoute]);
    }

    public function store(string $amazonPath): string
    {
        $amazonPath = GetStorageDirectoryAction::run($amazonPath);
        Storage::disk('s3')->put($amazonPath, $this->getCompressedContents());

        return $amazonPath;
    }

    public function getCompressedContents(): string
    {
        $this->close();
        $contents = file_get_contents($this->zipPath());

        $this->directory->delete();

        return $contents;
    }

    private function getFileName(string $name): string
    {
        return $this->getInternalPath().$name;
    }

    private function getInternalPath(): string
    {
        return empty($this->currentFolder) ? '' : $this->currentFolder.'/';
    }

    private function close(): void
    {
        $this->zip->close();
    }
}
