<?php

namespace App\Actions\Support\RUBackDoorAccess;

use App\DataProviders\Providers\ChannelManagerProvider;
use App\Models\ProviderAccount;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use PhpMimeMailParser\Parser;
use Throwable;

class GetBackDoorLoginUrlAction
{
    use AsAction;

    public function handle(ProviderAccount $providerAccount): ?string
    {
        if ($providerAccount->provider_id != ChannelManagerProvider::ID) {
            throw new Exception('This is not a Rentals United Channel Manager account');
        }

        try {
            // Attempt to acquire the lock and execute the closure
            return Cache::lock('unique-code-lock', 30)->block(25, function () use ($providerAccount) {
                return $this->getLoginUrl($providerAccount);
            });
        } catch (Throwable) {
            return null;
        }
    }

    private function getLoginUrl(ProviderAccount $providerAccount): string
    {
        $success = BackDoorRULoginAction::run($providerAccount, false);

        if (! $success) {
            throw new Exception('RU is not available');
        }

        $url = null;
        $maxAttempts = 15;
        while ($maxAttempts > 0 && empty($url)) {
            sleep(1);
            $maxAttempts--;

            // The email redirect is unique, does not depend on the environment.
            $baseDir = 'support/ru_service_login/';

            if (empty(Storage::disk('s3')->files($baseDir))) {
                continue;
            }
            $filePath = collect(Storage::disk('s3')->files($baseDir))->first();
            pnLog("[BackDoorLogin] Fetching email $filePath", $providerAccount->team_id);

            try {
                $emailContent = Storage::disk('s3')->get($filePath);
                // Package documentation: https://github.com/php-mime-mail-parser/php-mime-mail-parser?tab=readme-ov-file
                $parser = new Parser();
                $parser->setText($emailContent);
                $url = Str::of($parser->getMessageBody('html'))
                    ->after('https://new.rentalsunited.com/Account/Mfa')
                    ->before('"')
                    ->prepend('https://new.rentalsunited.com/Account/Mfa');
            } catch (Exception) {
            }
            collect(Storage::disk('s3')->files($baseDir))->each(function ($file) {
                Storage::disk('s3')->delete($file);
            });
        }
        $logStatus = $url ? 'success' : 'failure';
        pnLog("[BackDoorLogin] Login attempt status: $logStatus", $providerAccount->team_id);

        return $url;
    }
}
