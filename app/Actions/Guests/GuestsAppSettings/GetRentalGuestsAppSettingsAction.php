<?php

namespace App\Actions\Guests\GuestsAppSettings;

use App\Actions\Guests\RentalSettings\EnsureTeamGuestsApplicationRentalSettingsCreated;
use App\Http\Resources\GuestsAppRentalSettingsResource;
use App\Models\GuestsApplicationRentalSettings;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class GetRentalGuestsAppSettingsAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental): GuestsAppRentalSettingsResource
    {
        $rentalSettings = $this->handle($team, $teamRental);

        return new GuestsAppRentalSettingsResource($rentalSettings);
    }

    public function handle(Team $team, Rental $rental): GuestsApplicationRentalSettings
    {
        EnsureTeamGuestsApplicationRentalSettingsCreated::run($team);

        return $rental->guestsApplicationRentalSettings;
    }
}
