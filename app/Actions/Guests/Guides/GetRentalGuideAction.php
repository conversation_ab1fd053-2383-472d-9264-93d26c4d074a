<?php

namespace App\Actions\Guests\Guides;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Actions\Guests\RentalSettings\EnsureTeamGuestsApplicationRentalSettingsCreated;
use App\Models\Rental;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetRentalGuideAction
{
    use AsAction;

    private function authorizeAccess(User $user, Rental $rental): void
    {
        VerifyUserHasAccessToRentalsAction::run($user, $rental);
    }

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): JsonResponse
    {
        $this->authorizeAccess($request->user(), $teamRental);

        return response()->json($this->handle($team, $teamRental));
    }

    public function pdfUrl(Team $team, Rental $teamRental, ActionRequest $request): JsonResponse
    {
        $this->authorizeAccess($request->user(), $teamRental);
        $language = $request->query('language');
        abort_if($language === null, 400, 'Invalid language');

        $pdfUrl = GetTemporaryUploadedPdfGuideAction::run($team, $teamRental, $language);
        abort_if(! $pdfUrl, 400, 'Guide Not Available');

        return response()->json($pdfUrl);
    }

    public function getPreviewSecret(Team $team, Rental $teamRental, ActionRequest $request): JsonResponse
    {
        $this->authorizeAccess($request->user(), $teamRental);

        $secret = Str::random(32);
        $key = 'guest_preview_'.$secret;
        Cache::put($key, [$team->id, $teamRental->id], now()->addMinutes(8 * 60));

        return response()->json([
            'uuid' => $secret,
            'url' => $team->config()->guestPortalDomain('/guide-preview/'.$secret),
        ]);
    }

    public function handle(Team $team, Rental $rental): array
    {
        EnsureTeamGuestsApplicationRentalSettingsCreated::run($team);

        return $rental->guestsApplicationRentalSettings->guide ?? [];
    }
}
