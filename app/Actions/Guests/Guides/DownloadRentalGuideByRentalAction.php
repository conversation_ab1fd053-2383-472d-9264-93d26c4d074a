<?php

namespace App\Actions\Guests\Guides;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Actions\Guests\ApplicationSettings\GetGuestAppSettingsAction;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Http\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class DownloadRentalGuideByRentalAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): Response
    {
        // Increase timeout as this takes long in multiple cases
        set_time_limit(60);

        VerifyUserHasAccessToRentalsAction::run($request->user(), $teamRental);

        // THEN CHECK GUEST APP SETTINGS: TEAM SUBSCRIPTION OK, TEAM HAS ENABLED GUEST APP & RENTAL IS ENABLED:
        $guest_app_general_and_rental_settings = GetGuestAppSettingsAction::run($team, $teamRental, false);

        return CreateGuidePdfAction::run(
            $team,
            $guest_app_general_and_rental_settings['rental_settings_query'],
            strtoupper($request->input('language', 'EN')),
            $teamRental,
            $guest_app_general_and_rental_settings['general_settings']
        );
    }
}
