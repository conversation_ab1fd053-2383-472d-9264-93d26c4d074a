<?php

namespace App\Actions\Guests\Upsales;

use App\Models\Booking;
use Lorisleiva\Actions\Concerns\AsAction;

class GetGuestsPortalUpsalesUrlAction
{
    use AsAction;

    public function handle(Booking $booking): string
    {
        return $booking->team
            ->config()
            ->guestPortalDomain(config('ninja.guests.upsales_view').'?rf='.$booking->reference.'&ci='.$booking->getCheckInDay());
    }
}
