<?php

namespace App\Actions\Guests\Upsales;

use App\DTO\Bookings\BookingFeeData;
use App\Models\Booking;
use App\Models\Fee;
use Lorisleiva\Actions\Concerns\AsAction;

/** Use this action to get the BookingFee data to create a BookingFee model in 2 situations:
 * 1. When you just have the $timesBooked selected by the user, and you need to calculate $price and the modified $timesBooked (adjusted per nights, guests...)
 * 2. You already have the $timesBooked and $price, and you just need to create the BookingFee model.
 * */
class GetBookingFeeDataForUpsaleAction
{
    use AsAction;

    public function handle(Booking $booking, Fee $fee, int $timesBooked, ?float $price = null): BookingFeeData
    {
        // If price is null, means we just have the $timesBooked selected by the user, but we need to calculate values for BookingFee.
        // Otherwise, we understand you pass the BookingFee values of $timesBooked and $price
        if (! $price) {
            [$feeAmountInCents, $timesBooked, $feePerUnitInCents] = $fee->calculateNewBookingFeeValues($booking, $timesBooked);
            $price = ninjaIntval($feePerUnitInCents / 100);
        }

        $price = round($price, 2);

        return BookingFeeData::from([
            'fee_id' => $fee->id,
            'fee_name' => $fee->name,
            'price' => $price, // This is the unit price with decimals.
            'team_id' => $booking->team_id,
            'booking_id' => $booking->id,
            'required' => false,
            'times_booked' => $timesBooked,
            'internal' => true,
            'kind' => $fee->kind,
            'rate_kind' => $fee->rate_kind,
        ]);
    }
}
