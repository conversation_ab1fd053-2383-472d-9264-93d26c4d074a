<?php

namespace App\Actions\Guests\PreCheckInSettings;

use App\Events\PreCheckInDisabledEvent;
use App\Events\PreCheckInEnabledEvent;
use App\Http\Middleware\VerifyUserCanModifyGuestsSettings;
use App\Http\Resources\PreCheckInSettingsResource;
use App\Models\GuestsApplicationRentalSettings;
use App\Models\GuestsApplicationSettings;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateTeamPreCheckInSettingsAction
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return [VerifyUserCanModifyGuestsSettings::class];
    }

    public function rules(): array
    {
        return [
            'sms_sender_id' => ['nullable', 'string', 'min:4', 'max:11'],
        ];
    }

    public function asController(ActionRequest $request, Team $team): PreCheckInSettingsResource
    {
        $settings = $this->handle($request, $team);

        return new PreCheckInSettingsResource($settings);
    }

    public function handle(ActionRequest $request, Team $team): GuestsApplicationSettings
    {
        $settings = $team->guestApplicationSettings;

        $settings->update($request->input());

        $pastActive = GuestsApplicationRentalSettings::query()
            ->where('team_id', '=', $team->id)
            ->where('pre_checkin', '=', true)
            ->pluck('rental_id');
        $rentalSettings = collect($request->input('rental_settings'));
        $newActive = $rentalSettings->where('pre_checkin', '=', true)->pluck('rental_id');

        $newActive->diff($pastActive)->each(fn (int $rentalId) => event(new PreCheckInEnabledEvent($team, $rentalId)));
        $pastActive->diff($newActive)->each(fn (int $rentalId) => event(new PreCheckInDisabledEvent($team, $rentalId)));

        $this->setGuestApplicationRentalSetting($team, $newActive, 'pre_checkin');
        $rentalsIds = $rentalSettings->where('use_rn_settings', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'use_rn_settings');
        $rentalsIds = $rentalSettings->where('require_passport_number', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'require_passport_number');
        $rentalsIds = $rentalSettings->where('require_passport_upload', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'require_passport_upload');
        $rentalsIds = $rentalSettings->where('require_passports_all_guests', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'require_passports_all_guests');
        $rentalsIds = $rentalSettings->where('accept_any_document', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'accept_any_document');

        $rentalsIds = $rentalSettings->where('scan_documents', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'scan_documents');
        $rentalsIds = $rentalSettings->where('extra_information_guests', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'extra_information_guests');
        $rentalsIds = $rentalSettings->where('main_postal_address', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'main_postal_address');
        $rentalsIds = $rentalSettings->where('guests_postal_address', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'guests_postal_address');
        $rentalsIds = $rentalSettings->where('request_signature', '=', true)->pluck('rental_id');
        $this->setGuestApplicationRentalSetting($team, $rentalsIds, 'request_signature');

        return $settings;
    }

    private function setGuestApplicationRentalSetting(Team $team, ?iterable $rentals, string $setting): void
    {
        if (is_null($rentals)) {
            return;
        }

        // We do not need to check if rental is of the team, since we only update the team GuestsApplicationRentalSettings.
        GuestsApplicationRentalSettings::query()
            ->where('team_id', '=', $team->id)
            ->whereNotIn('rental_id', $rentals)
            ->update([$setting => false]);

        GuestsApplicationRentalSettings::query()
            ->where('team_id', '=', $team->id)
            ->whereIn('rental_id', $rentals)
            ->update([$setting => true]);
    }
}
