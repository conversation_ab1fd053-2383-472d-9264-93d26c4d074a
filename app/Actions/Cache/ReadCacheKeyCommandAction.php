<?php

namespace App\Actions\Cache;

use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsCommand;

class ReadCacheKeyCommandAction
{
    use AsCommand;

    public string $commandSignature = 'ninja-dev:read-cache-key {key}';

    public function asCommand(Command $command): void
    {
        $key = $command->argument('key');
        $value = Cache::get($key);

        if ($value instanceof Collection) {
            $value = $value->toArray();
        }
        if (is_array($value)) {
            $value = json_encode($value);
        }
        $command->info($key);
        $command->info($value);
    }
    /*
     *  Useful keys:
     * - 1.18847.rentals_for_user (team.user.rentals_for_user)
    */
}
