<?php

namespace App\Actions\Cache;

use App\Models\User;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetRentalsIdForUserAction
{
    use AsAction;

    public function handle(User $user, bool $withUncompleted): array
    {
        // If $withUncompleted is true, do not access the cache, because the user may have visited first another page,
        // then the rentals list page and thus will not see the non completed rentals. Then, use the cache only for completed rentals.
        if ($withUncompleted) {
            return $this->updateCache($user, $withUncompleted);
        }

        return GetOrSetFromCacheAction::run($user, 'rentals_for_user', fn () => $this->updateCache($user, $withUncompleted), 60 * 25);
    }

    private function updateCache(User $user, bool $withUncompleted): array
    {
        $team = $user->team;

        // If the user is queried with a select without the current_team_id, the team relation is created but set
        // as null. We need to force loading the team.
        if (is_null($team)) {
            $user->load('team');
            $team = $user->team;
        }
        // In this case, user has been removed from the team.
        if (is_null($team)) {
            return [];
        }

        $rentals = $team->teamRentalIds(withUncompleted: $withUncompleted);

        // userRentals includes always deleted rentals, we intersect with the valid rentals list to filter them if needed.
        if (! $user->isTeamOwner()) {
            $rentals = $user
                ->userRentals()
                ->select('rental_id')
                ->pluck('rental_id')
                ->when($withUncompleted && $user->isRentalManagerOrAbove(), function (Collection $collection) use ($team) {
                    return $collection->merge(
                        $team->teamRentals() // This query should only be executed a few times: when a non team owner visits the rentals page: so cost is low
                            ->select('id')
                            ->withUncompleted()
                            ->where('completed', false)
                            ->pluck('id')
                    );
                })
                ->unique()
                ->sortBy('id')
                ->intersect($rentals)
                ->values()
                ->toArray();
        }

        return $rentals;
    }
}
