<?php

namespace App\Actions\Teams;

use App\DataProviders\Providers\NinjaProvider;
use App\DTO\Team\PlanData;
use App\DTO\Team\RegisterFormData;
use App\DTO\Team\TeamRegistrationData;
use App\Models\TeamRegistrationSteps;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetRegisterTeamAction
{
    use AsAction;

    public function asController(ActionRequest $request): RegisterFormData
    {
        $token = $request->header('Ninja-Register-Token');
        $steps = TeamRegistrationSteps::findOrFail($token);
        /** @var TeamRegistrationData $data */
        $data = $steps->account_data;

        $providersConfiguration = NinjaProvider::getProvidersWithSignup()
            ->map(fn (NinjaProvider $p) => $p->providerConfiguration())
            ->values()
            ->toArray();

        return RegisterFormData::from([
            'registrationToken' => $token,
            'providers' => $providersConfiguration,
            'plans' => PlanData::getPlans(availableForSaleOnly: true)->withoutWrapping(),
            'providerId' => $data->providerId,
            'providerAccountId' => $data->providerAccountId,
            'flavorId' => $data->flavorId,
            'rentalCount' => $data->providerRentalCount,
            'teamName' => $data->teamName,
            'name' => $data->userName ?? '',
            'email' => $data->userEmail ?? '',
            'phoneCountry' => $data->userPhoneCountry ?? '',
            'phone' => $data->userPhone ?? '',
            'password' => '',
            'plan' => '',
            'coupon' => null,
        ])->withoutWrapping();
    }
}
