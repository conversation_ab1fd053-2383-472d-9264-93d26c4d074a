<?php

namespace App\Actions\Teams;

use App\Actions\Segment\DeleteUserFromSegmentAction;
use App\Actions\Segment\SyncTeamToSegmentAction;
use App\Actions\Support\Database\GetTablesWithColumnAction;
use App\Enum\TeamStatusEnum;
use App\Models\Team;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class RemoveTeamAction
{
    use AsAction;

    public string $commandSignature = 'ninja:remove-team {team}';

    public ?Command $command = null;

    public function asCommand(Command $command)
    {
        $this->command = $command;

        $id = $command->argument('team');
        if (! $id) {
            $command->error('Team id not found');
        }
        $command->info("Team Id: $id");
        $team = Team::find((int) $id);
        if (! $team) {
            $command->error('Team id not found');

            return $command::FAILURE;
        }
        $command->info("Team: $team->name");
        $this->handle($team);

        return $command::SUCCESS;
    }

    public function handle(Team $team, ?Command $command = null): void
    {
        $this->command ??= $command;

        $this->removeTeamRelatedModels($team);
        foreach ($team->users as $user) {
            /** @var User $user */
            $this->removeUserRelatedModels($user);
            $user->current_team_id = null;
            $user->external_id = null;
            $user->name = Str::random(7);
            $user->email = Str::random(10).'-<EMAIL>';
            $user->photo_url = null;
            $user->country_code = null;
            $user->phone = null;
            $user->save();
            DeleteUserFromSegmentAction::dispatch($team, $user); // We could send multiple users at once, but this Action is barely used
        }
        $this->info("Team $team->name deleted");
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        if ($team->mainProvider()->enabled() && $team->config()->useSegment()) {
            SyncTeamToSegmentAction::dispatch($team, false);
        }
    }

    private function removeTeamRelatedModels(Team $team)
    {
        $models = $this->teamTables();

        foreach ($models as $model) {
            DB::table($model)
                ->where('team_id', $team->id)
                ->delete();
        }
        $team->providerAccounts()->forceDelete();

        $team->name = "Deleted $team->name";
        $team->email = '';
        $team->status = TeamStatusEnum::disabled;

        // Note: do not delete provider_id nor flavor_id because we need it to dispatch SyncTeamToSegmentAction
        $team->save();
    }

    private function teamTables(): array
    {
        return GetTablesWithColumnAction::run('team_id')
            ->filter(fn (string $table) => ! in_array($table, [
                'invoices',
                'team_performance_indicators',
                'team_monthly_performance_indicators',
                'team_subscriptions',
                'team_subscription_items',
                'provider_accounts',
            ]))
            ->toArray();
    }

    private function removeUserRelatedModels(User $user)
    {
        $models = $this->userTables();

        foreach ($models as $model) {
            DB::table($model)
                ->where('user_id', $user->id)
                ->delete();
        }
    }

    private function userTables(): array
    {
        return GetTablesWithColumnAction::run('user_id')
            ->filter(fn (string $table) => ! in_array($table, [
                'invoices',
                'oauth_clients',
            ]))
            ->toArray();
    }

    private function info(string $info)
    {
        $this->command?->info($info);
    }
}
