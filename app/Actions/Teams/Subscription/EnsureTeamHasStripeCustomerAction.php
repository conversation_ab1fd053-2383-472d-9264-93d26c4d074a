<?php

namespace App\Actions\Teams\Subscription;

use App\Models\Team;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;

class EnsureTeamHasStripeCustomerAction
{
    use AsAction;

    /**
     * Execute the action and return a result.
     */
    public function handle(Team $team)
    {
        if ($team->config()->billedThroughRentalNinja() && ! $team->hasStripeId() && ! isLocal()) {
            Cache::lock('stripe-customer-create-'.$team->id)
                ->block(5, fn () => $team->createAsStripeCustomer());
        }
    }
}
