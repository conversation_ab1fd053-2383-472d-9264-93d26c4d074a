<?php

namespace App\Actions\Teams;

use App\Actions\Users\RegisterUserAction;
use App\DTO\Team\RegisterFormData;
use App\DTO\Team\TeamRegistrationData;
use App\Enum\TeamRolesEnum;
use App\Flavors\DefaultFlavor;
use App\Models\TeamRegistrationSteps;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class RegisterTeamAction
{
    use AsAction;

    public function asController(ActionRequest $request, RegisterFormData $input): JsonResponse
    {
        $ninjaRegistrationToken = $request->header('Ninja-Register-Token');
        $teamData = TeamRegistrationSteps::findOrFail($ninjaRegistrationToken);

        $this->handle($teamData, $input);

        return response()->json('');
    }

    public function handle(TeamRegistrationSteps $steps, RegisterFormData $input): void
    {
        // This is in a transaction to delete the user in case the team creation fails.
        DB::transaction(function () use ($input, $steps) {
            // Create User
            $user = RegisterUserAction::run(
                name: $input->name,
                email: $input->email,
                password: $input->password,
                role: TeamRolesEnum::OWNER,
                phoneCountry: $input->phoneCountry,
                phone: $input->phone,
                locale: $input->locale,
            );

            // Create Team
            /** @var TeamRegistrationData $data */
            $data = $steps->account_data;
            $data = $this->combineDataAndInput($data, $input);

            // Overwrite $data information with $input information (Currently, only team name)
            $team = CreateTeamAction::run($user, $data);

            $steps->setTeamCreated($team);
            $steps->save();
        });
    }

    private function combineDataAndInput(?TeamRegistrationData $data, RegisterFormData $input): TeamRegistrationData
    {
        if (is_null($data)) {
            $data = new TeamRegistrationData(
                providerId: $input->providerId,
                flavorId: DefaultFlavor::FLAVOR_ID,
            );
        }
        $data->teamName = $input->teamName;
        $data->teamEmail = $data->teamEmail ?? $input->email;
        $data->userPhoneCountry = $input->phoneCountry;
        $data->userPhone = $input->phone;
        $data->rentalCount = $input->rentalCount;

        return $data;
    }
}
