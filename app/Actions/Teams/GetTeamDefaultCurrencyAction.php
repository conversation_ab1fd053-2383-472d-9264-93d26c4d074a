<?php

namespace App\Actions\Teams;

use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Rental;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTeamDefaultCurrencyAction
{
    use AsAction;

    /**
     * Execute the action and return a result.
     * Returns the first found currency that is most used.
     */
    public function handle(int $teamId): string
    {
        if ($currency = request('currency')) {
            return $currency;
        }

        $currencyFromPayments = BookingPayment::query()
            ->select('currency')
            ->selectRaw('COUNT(*) as count')
            ->onTeam($teamId)
            ->groupBy('currency')
            ->orderByDesc('count')
            ->first();

        if (! empty($currencyFromPayments)) {
            return $currencyFromPayments->currency;
        }

        $currencyFromBookings = Booking::query()
            ->select('currency')
            ->selectRaw('COUNT(*) as count')
            ->onTeam($teamId)
            ->groupBy('currency')
            ->orderByDesc('count')
            ->first();

        if (! empty($currencyFromBookings)) {
            return $currencyFromBookings->currency;
        }

        $currencyFromRentals = Rental::query()
            ->select('currency')
            ->selectRaw('COUNT(*) as count')
            ->onTeam($teamId)
            ->groupBy('currency')
            ->orderByDesc('count')
            ->first();

        if (! empty($currencyFromRentals)) {
            return $currencyFromRentals->currency;
        }

        abort(400, 'No Currencies found');
    }
}
