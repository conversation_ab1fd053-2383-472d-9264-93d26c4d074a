<?php

namespace App\Actions\Teams\TeamDetails;

use App\Actions\ChannelManager\EnsureTeamCreatedInChannelManager;
use App\DataProviders\ChannelManager\ChannelManagerPusher;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\DTO\Team\TeamDetailsData;
use App\Models\Team;
use App\Models\TeamDetails;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateTeamDetailsAction
{
    use AsAction;

    public function asController(Team $team, TeamDetailsData $data): TeamDetailsData
    {
        abort_unless(request()->user()->isAdminOrAbove(), 404);

        return TeamDetailsData::from($this->handle($team, $data));
    }

    public function handle(Team $team, TeamDetailsData $data): TeamDetails
    {
        if (is_null($team->teamDetails)) {
            return $team->teamDetails()->create($data->all());
        }
        $team->teamDetails->update($data->all());

        if ($team->providerAccounts->where('provider_id', '=', ChannelManagerProvider::ID)->isNotEmpty()) {
            ChannelManagerPusher::fillCompanyDetails($team);
        } else {
            EnsureTeamCreatedInChannelManager::run($team);
        }

        return $team->teamDetails;
    }
}
