<?php

namespace App\Actions\Teams;

use App\Models\Team;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTeamVerificationErrorsAction
{
    use AsAction;

    public function asController(Team $team): JsonResponse
    {
        return response()->json(['errors' => $this->handle($team)]);
    }

    public function handle(Team $team): ?string
    {
        return Cache::get("team-{$team->id}-verification-error");
    }
}
