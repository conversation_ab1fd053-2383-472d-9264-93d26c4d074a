<?php

namespace App\Actions\Teams\TeamSettings;

use App\DTO\TeamSettings\TeamSettingsRequestDto;
use App\Http\Requests\TeamSettings\UpdateSlackNotificationSettingsRequest;
use App\Http\Resources\TeamSettingsResource;
use App\Models\Team;
use App\Models\TeamSettings;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateSlackNotificationSettingsAction
{
    use AsAction;

    public function asController(Team $team, UpdateSlackNotificationSettingsRequest $request): TeamSettings
    {
        return $this->handle($team->teamSettings, $request->asDto());
    }

    public function handle(TeamSettings $teamSettings, TeamSettingsRequestDto $data): TeamSettings
    {
        $teamSettings->update($data->toArray());

        return $teamSettings;
    }

    public function jsonResponse(TeamSettings $teamSettings): TeamSettingsResource
    {
        return new TeamSettingsResource($teamSettings);
    }
}
