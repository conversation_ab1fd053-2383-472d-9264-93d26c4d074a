<?php

namespace App\Actions\Teams\TeamSettings;

use App\Actions\Users\TouchTeamUsersAction;
use App\DTO\Team\TeamCustomizationData;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;
use <PERSON><PERSON>\LaravelData\Optional;

class CustomizeTeamAction
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['verifyUserIsAdmin'];
    }

    public function asController(Team $team, TeamCustomizationData $request): TeamCustomizationData
    {
        $team = $this->handle($team, $request);

        return TeamCustomizationData::from($team);
    }

    public function handle(Team $team, TeamCustomizationData $data): Team
    {
        if (! empty($data->all())) {
            // This route is used in the app, so we need to transform nulls to old API nulls to Optionals
            if (is_null($data->name)) {
                $data->name = new Optional();
            }
            if (is_null($data->photoUrl)) { // Request from frontend already has imgix firebase url
                $data->photoUrl = new Optional();
            }
            if (is_null($data->email)) { // Request from frontend already has imgix firebase url
                $data->email = new Optional();
            }
            $team->update($data->all());

            TouchTeamUsersAction::run($team, CustomizeTeamAction::class);
        }

        return $team;
    }
}
