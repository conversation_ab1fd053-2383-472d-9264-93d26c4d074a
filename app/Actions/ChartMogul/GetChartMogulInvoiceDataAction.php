<?php

namespace App\Actions\ChartMogul;

use App\DTO\Support\ChartmogulPlanDto;
use App\Enum\TeamStatusEnum;
use App\Models\CarbonPeriod;
use App\Models\ChartmogulData;
use App\Models\ChartmogulInvoice;
use App\Models\Team;
use App\Models\TeamMonthlyPerformanceIndicator;
use App\Support\CustomBatch;
use Exception;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Http\Client\Pool;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GetChartMogulInvoiceDataAction
{
    use AsAction;
    use UsesChartMogulApi;

    /**
     * @throws RequestException
     * @throws Throwable
     */
    public function handle(Collection $chartMogulCustomers, ChartmogulPlanDto $plan): void
    {
        // Delete this months invoices so we can re-create them.
        $this->deleteInvoicesThatNeedToBeRecreated($chartMogulCustomers);

        // Create invoices.
        $invoicesToCreate = $chartMogulCustomers
            ->map(function (ChartmogulData $chartMogulCustomer) use ($plan) {
                return TeamMonthlyPerformanceIndicator::query()
                    ->where('team_id', $chartMogulCustomer->team_id)
                    // Only Get Stuff Before Yesterday
                    ->where('date', '<=', Carbon::yesterday()->endOfMonth()->toDateString())
                    ->where('revenue', '>', 0)
                    ->where('active_rentals', '>', 0)
                    ->where('status', '!=', TeamStatusEnum::disabled)
                    ->orderBy('date')
                    ->get()
                    ->map(function (TeamMonthlyPerformanceIndicator $indicator) use ($chartMogulCustomer, $plan) {
                        // Look for the date of this indicator in the ChartMogul invoices.
                        $date = Carbon::parse($indicator->date)->endOfDay();

                        // Look internally saved invoices for this period.
                        $invoice = ChartmogulInvoice::query()
                            ->where('invoice_raised_at', $date->toDateString())
                            ->where('invoice_due_at', $date->toDateString())
                            ->where('chartmogul_data_id', $chartMogulCustomer->id)
                            ->first();

                        // If no invoices exist, create one.
                        if (is_null($invoice)) {
                            return $this->getChartMogulInvoiceData(
                                team: $chartMogulCustomer->team,
                                indicator: $indicator,
                                date: $date,
                                plan: $plan,
                            );
                        }

                        return null;
                    })
                    ->filter()
                    ->values();
            })
            ->filter(fn (Collection|EloquentCollection $c) => $c->isNotEmpty());

        // Delete invoices for the current month if they exist.
        if ($invoicesToCreate->isNotEmpty()) {
            $responses = $this
                ->client()
                ->pool(function (Pool $pool) use ($chartMogulCustomers, $invoicesToCreate) {
                    $requests = [];
                    foreach ($invoicesToCreate as $teamInvoices) {
                        $teamId = Arr::get($teamInvoices->first(), 'customer_external_id');
                        $customer = $chartMogulCustomers->firstWhere('team_id', $teamId);
                        $requests[] = $this
                            ->poolAs($pool, $teamId)
                            ->post(
                                url: $this->endpoint("import/customers/$customer->chartmogul_uuid/invoices"),
                                data: ['invoices' => $teamInvoices->toArray()]
                            );
                    }

                    return $requests;
                });

            $data = collect();
            foreach ($responses as $teamId => $response) {
                try {
                    $invoices = $response->throw()->collect();
                    /** @var ChartmogulData $customer */
                    $customer = $chartMogulCustomers->firstWhere('team_id', $teamId);
                    $customer->update(['chartmogul_subscription_uuid' => $this->getChartmogulSubscriptionUuid($invoices)]);

                    $data->push(collect($invoices->get('invoices'))
                        ->map(function ($invoice) use ($customer) {
                            return [
                                'team_id' => $customer->team_id,
                                'chartmogul_data_id' => $customer->id,
                                'chartmogul_invoice_id' => $invoice['uuid'],
                                'invoice_raised_at' => Carbon::parse($invoice['date'])->toDateString(),
                                'invoice_due_at' => Carbon::parse($invoice['due_date'])->toDateString(),
                                'is_transaction_created' => collect($invoice['transactions'])->isNotEmpty(),
                            ];
                        }));
                } catch (Exception $e) {
                    report($e);
                }
            }

            $data = $data->flatten(1);
            // Store the invoices locally.
            if ($data->isNotEmpty()) {
                CustomBatch::insertBatch(
                    table: new ChartmogulInvoice(),
                    columns: array_keys($data->first()),
                    values: array_values($data->toArray())
                );
            }

            $this->cancelSubscriptionsIfNeeded($data, $chartMogulCustomers);
        }
    }

    private function deleteInvoicesThatNeedToBeRecreated(Collection $chartMogulCustomers): void
    {
        ChartmogulInvoice::query()
            ->whereIn('team_id', $chartMogulCustomers->pluck('team_id'))
            ->where('invoice_due_at', '=', now()->endOfMonth()->toDateString())
            ->get()
            ->whenNotEmpty(function ($invoices) {
                $deletion = $this
                    ->client()
                    ->pool(function (Pool $pool) use ($invoices) {
                        $requests = [];
                        foreach ($invoices as $invoice) {
                            $endpoint = $this->endpoint('invoices/'.$invoice->chartmogul_invoice_id);
                            $requests[] = $this
                                ->poolAs($pool, $invoice->id)
                                ->delete($endpoint);
                        }

                        return $requests;
                    });

                ChartmogulInvoice::query()
                    ->whereIn('id', array_keys($deletion))
                    ->delete();
            });
    }

    /**
     * Execute the action and return a result.
     */
    private function getChartMogulInvoiceData(
        Team $team,
        TeamMonthlyPerformanceIndicator $indicator,
        Carbon $date,
        ChartmogulPlanDto $plan): array
    {
        $provider = Str::lower($team->mainProvider()->internalFullName());
        $invoiceDate = $date->isCurrentMonth() ? now()->toIso8601ZuluString() : $date->toIso8601ZuluString();
        $subscriptionRevenue = $this->getInvoiceRevenue($indicator, $date);
        $smsRevenue = intval($indicator->pre_check_in_form_sms_sent * 2 * 0.09 * 100);
        $invIdentifier = $date->format('Ymd');
        $rnd = rand(0, 99999);
        $setIdentifier = $date->format('Ym');
        $lineItems = collect([[
            'type' => 'subscription',
            'subscription_external_id' => "sub-$team->id",
            'subscription_set_external_id' => "set-$team->id-$setIdentifier",
            'plan_uuid' => config("services.chartmogul.providers.$provider.plan"),
            'service_period_start' => $date->startOfMonth()->toIso8601ZuluString(),
            'service_period_end' => $date->endOfMonth()->toIso8601ZuluString(),
            'amount_in_cents' => $subscriptionRevenue,
            'quantity' => $indicator->active_rentals,
        ]])
            ->when($indicator->pre_check_in_form_sms_sent > 0, fn (Collection $collection) => $collection->add([
                'type' => 'one_time',
                'description' => 'SMS Pre-Check-In',
                'amount_in_cents' => $smsRevenue,
                'quantity' => $indicator->pre_check_in_form_sms_sent,
            ]));
        if ($date->isBefore(Carbon::today()->startOfMonth())) {
            $transaction = [
                'type' => 'payment',
                'date' => $date->endOfDay()->toIso8601ZuluString(),
                'result' => 'successful',
            ];
        } else {
            $transaction = null;
        }

        return [
            'external_id' => Str::slug("inv-$invIdentifier-$indicator->provider_id-$indicator->team_id-$rnd"),
            'date' => $invoiceDate,
            'due_date' => $date->toIso8601ZuluString(),
            'currency' => 'EUR',
            'customer_external_id' => $indicator->team_id,
            'data_source_uuid' => $plan->source,
            'line_items' => $lineItems->toArray(),
            'transactions' => [$transaction],
        ];
    }

    private function getInvoiceRevenue(TeamMonthlyPerformanceIndicator $indicator, Carbon $date): int
    {
        $revenue = $indicator->revenue * 100;
        if (! $date->isCurrentMonth() && $date->equalTo($date->endOfMonth())) {
            return intval($revenue);
        }

        return intval($revenue / $date->day * $date->daysInMonth);
    }

    private function getChartmogulSubscriptionUuid(Collection $invoices): mixed
    {
        return collect(data_get($invoices->toArray(), 'invoices.*.line_items.*.subscription_uuid'))
            ->unique()
            ->values()
            ->first();
    }

    /**
     * Cancel Subscriptions on missing invoiced days.
     *
     * @throws Exception
     */
    private function cancelSubscriptionsIfNeeded(Collection $data, Collection $customers): void
    {
        $invoicesByTeam = $data->groupBy('team_id');

        $patches = collect();
        foreach ($invoicesByTeam as $teamId => $teamInvoices) {
            // Get list of created invoices.
            $dates = collect(data_get($teamInvoices, '*.invoice_raised_at'))->sort();
            if ($dates->isEmpty() || $dates->count() == 1) {
                continue;
            }

            $start = $dates->first();
            $end = $dates->last();
            $range = CarbonPeriod::instance(Carbon::parse($start)->startOfDay(), Carbon::parse($end)->endOfDay());
            $datesToCancel = collect();
            $range->eachMonths(
                months: 1,
                callback: function ($x) use ($dates, $datesToCancel) {
                    /** @var string $date */
                    $date = $x->end()->endOfMonth()->toDateString();
                    if (! $dates->contains($date)) {
                        $datesToCancel->push(Carbon::parse($date)->endOfDay()->toIso8601ZuluString());
                    }
                }
            );

            if ($datesToCancel->isNotEmpty()) {
                $dates = $datesToCancel->toArray();
                $customer = $customers->firstWhere('team_id', $teamId);
                $patches->add([
                    'url' => $this->endpoint("import/subscriptions/$customer->chartmogul_subscription_uuid"),
                    'data' => ['cancellation_dates' => $dates],
                ]);
            }
        }
        if ($patches->isNotEmpty()) {
            $this->client()
                ->pool(fn (Pool $pool) => $patches->map(function (array $patch) use ($pool) {
                    return $this
                        ->pool($pool)
                        ->patch(
                            url: $patch['url'],
                            data: $patch['data']
                        );
                }));
        }
    }

    private function getLastInvoiceCreated(int $teamId)
    {
        return ChartmogulInvoice::query()
            ->where('team_id', $teamId)
            ->orderBy('invoice_raised_at', 'desc')
            ->take(1)
            ->max('invoice_due_at');
    }
}
