<?php

namespace App\Actions\ChartMogul;

use App\DTO\Support\ChartmogulPlanDto;
use App\Models\ChartmogulData;
use App\Models\Team;
use App\Support\CustomBatch;
use Exception;
use Illuminate\Http\Client\Pool;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

/**
 * Gets the internal ChartMogulData Customer for the given Team.
 * If it does not exist internally, creates one in ChartMogul.
 */
class GetChartMogulCustomersAction
{
    use AsAction;
    use UsesChartMogulApi;

    /**
     * Execute the action and return a result.
     *
     * @throws Throwable
     */
    public function handle(Collection $teams, ChartmogulPlanDto $plan): Collection
    {
        $notExisting = $teams->whereNotIn('id', $this->existingCustomers($teams));

        if ($notExisting->isNotEmpty()) {
            $responses = $this
                ->client()
                ->pool(fn (Pool $pool) => $notExisting
                    ->map(function (Team $team) use ($pool, $plan) {
                        return $this
                            ->poolAs($pool, $team->id)
                            ->post(
                                url: $this->endpoint('customers'),
                                data: [
                                    'data_source_uuid' => $plan->source,
                                    'external_id' => $team->id,
                                    'name' => $team->owner->name,
                                    'email' => $team->owner->email,
                                    'company' => $team->name,
                                    'lead_created_at' => $team->created_at->toIso8601String(),
                                    'attributes' => [
                                        'custom' => [
                                            [
                                                'type' => 'String',
                                                'key' => 'channel',
                                                'value' => $plan->name,
                                            ],
                                        ],
                                    ],
                                ]
                            );
                    })
                    ->toArray()
                );

            $newCustomers = $notExisting
                ->map(function (Team $team) use ($responses, $plan) {
                    /** @var Response $response */
                    $response = $responses[$team->id];
                    if ($response->serverError()) {
                        $response->throw();
                    }
                    if ($response->clientError()) {
                        if (Str::of($response->body())->contains('The external ID for this customer already exists in our system')) {
                            $customer = collect($this
                                ->client()
                                ->withBasicAuth($this->key(), $this->key())
                                ->get($this->endpoint('customers'), ['external_id' => $team->id])
                                ->collect('entries')
                                ->first());
                        } else {
                            throw new Exception('ChartMogul Customer could not be retrieved: '.$response->body());
                        }
                    } else {
                        $customer = $response->collect();
                    }

                    if (empty($customer)) {
                        throw new Exception('ChartMogul Customer not found');
                    }

                    return [
                        'team_id' => $team->id,
                        'data_source_uuid' => $plan->source,
                        'chartmogul_uuid' => $customer->get('uuid'),
                        'chartmogul_id' => $customer->get('id'),
                    ];
                });

            CustomBatch::insertBatch(
                table: new ChartmogulData(),
                columns: array_keys($newCustomers->first()),
                values: array_values($newCustomers->toArray())
            );
        }

        return ChartmogulData::query()
            ->whereIn('team_id', $teams->pluck('id'))
            ->with('team')
            ->get();
    }

    private function existingCustomers(Collection $teams): Collection
    {
        return ChartmogulData::query()
            ->whereIn('team_id', $teams->pluck('id'))
            ->pluck('team_id');
    }
}
