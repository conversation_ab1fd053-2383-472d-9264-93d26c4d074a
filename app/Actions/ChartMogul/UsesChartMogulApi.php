<?php

namespace App\Actions\ChartMogul;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;

trait UsesChartMogulApi
{
    /**
     * Execute the action and return a result.
     */
    public function client(): PendingRequest
    {
        $key = config('services.chartmogul.key');

        return Http::withBasicAuth($key, $key)
            ->acceptJson()
            ->retry(3)
            ->connectTimeout(10)
            ->timeout(120);
    }

    protected function endpoint(string $string): string
    {
        return 'https://api.chartmogul.com/v1/'.$string;
    }

    protected function poolAs(Pool $pool, string $as)
    {
        return $pool
            ->as($as)
            ->withBasicAuth($this->key(), $this->key())
            ->acceptJson()
            ->retry(3)
            ->connectTimeout(10)
            ->timeout(120);
    }

    protected function key(): string
    {
        return config('services.chartmogul.key');
    }

    protected function pool(Pool $pool)
    {
        return $pool
            ->withBasicAuth($this->key(), $this->key())
            ->retry(3)
            ->connectTimeout(20)
            ->timeout(20);
    }
}
