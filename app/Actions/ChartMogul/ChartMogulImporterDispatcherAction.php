<?php

namespace App\Actions\ChartMogul;

use App\DTO\Support\ChartmogulPlanDto;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ChartMogulImporterDispatcherAction
{
    use AsAction;
    use UsesChartMogulApi;

    public string $commandSignature = 'ninja:chart-mogul-exporter';

    public string $commandDescription = 'Import Data to Chart Mogul';

    private ?array $plans;

    public function __construct()
    {
        $this->plans = config('services.chartmogul.plans');
    }

    /**
     * @throws Throwable
     */
    public function asCommand(Command $command): int
    {
        // The goal is to get full months.
        $command->info('Importing data to ChartMogul');
        $this->handle();

        return $command::SUCCESS;
    }

    /**
     * Execute the action and return a result.
     *
     * @throws Throwable
     */
    public function handle(): void
    {
        foreach ($this->plans as $plan) {
            $plan = ChartmogulPlanDto::from($plan);
            $this->dispatchPerPlan($plan);
        }
    }

    public function dispatchPerPlan(ChartmogulPlanDto $plan): void
    {
        Team::query()
            ->whereProviderId($plan->providerId)
            ->whereFlavorId($plan->flavorId)
            ->orderBy('id')
            ->pluck('id')
            ->unique()
            ->chunk(20)
            ->map(function (Collection $teams) use ($plan) {
                ChartMogulTeamHistoryExporterAction::dispatch($teams, $plan);
            });
    }
}
