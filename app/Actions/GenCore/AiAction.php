<?php

namespace App\Actions\GenCore;

use App\Actions\GenCore\Prompts\PromptFiles;
use RuntimeException;

enum AiAction: string
{
    case TRANSLATE = 'translate';
    case EMAIL_TRANSLATE = 'email_translate';
    case CONTENT_GENERATION = 'content_generation';
    case LISTING_CONTENT_GENERATION = 'listing_content_generation';
    case PICTURE_DESCRIPTION = 'picture_description';
    case EMAIL_RESPONSE = 'email_response';

    /**
     * Map action to the corresponding prompts.
     */
    public function getPrompt(): PromptFiles
    {
        return match ($this) {
            self::TRANSLATE, self::EMAIL_TRANSLATE => PromptFiles::TRANSLATOR_PROMPT,
            self::CONTENT_GENERATION => PromptFiles::CONTENT_GENERATOR_PROMPT,
            self::PICTURE_DESCRIPTION => PromptFiles::PICTURE_DESCRIPTION_PROMPT,
            self::EMAIL_RESPONSE => PromptFiles::EMAIL_RESPONSE_PROMPT,
            self::LISTING_CONTENT_GENERATION => PromptFiles::LISTING_CONTENT_GENERATION_PROMPT,
        };
    }

    /**
     * Fetch the content of the prompt file.
     */
    public function getContent(): string
    {
        $filePath = $this->filePath();

        if (! file_exists($filePath)) {
            throw new RuntimeException("File not found: {$filePath}");
        }

        return file_get_contents($filePath);
    }

    /**
     * Get the full file path of the prompt file.
     */
    public function filePath(): string
    {
        return __DIR__.'/'.$this->value;
    }
}
