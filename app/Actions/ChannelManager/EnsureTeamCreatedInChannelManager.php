<?php

namespace App\Actions\ChannelManager;

use App\Actions\ChannelManager\Pushers\UpdateEverythingInChannelManagerAction;
use App\DataProviders\ChannelManager\ChannelManagerPusher;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class EnsureTeamCreatedInChannelManager
{
    use AsAction;

    public function handle(Team $team): bool
    {
        if (! $team->teamDetails?->isCompleted()) {
            pnLog('[Channel Manager] Team details is not completed', $team);

            return false;
        }

        $provider = $team->providerAccounts->firstWhere('provider_id', '=', ChannelManagerProvider::ID);
        if (is_null($provider)) {
            pnLog('[Channel Manager] Creating account', $team);

            ChannelManagerPusher::createAccount($team);
            $team->refresh();
            ChannelManagerPusher::fillCompanyDetails($team);
            ChannelManagerPusher::putHandlerUrl($team->getProviderAccount(ChannelManagerProvider::ID));

            UpdateEverythingInChannelManagerAction::dispatch($team);
        }

        return true;
    }
}
