<?php

namespace App\Actions\ChannelManager\Fetchers;

use App\DataProviders\ProviderApi\ChannelManagerApi;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\DTO\Providers\ProviderSyncRequestData;
use App\Models\ChannelRental;
use App\Models\Rental;
use App\Models\Team;
use App\Models\TeamPerformanceIndicator;
use Illuminate\Console\Command;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Psr\Log\LogLevel;

class FetchDistributionChannelsStatsAction
{
    use AsAction;

    const SIGNATURE = 'ninja:fetch-distribution-channels-stats';

    public string $commandSignature = self::SIGNATURE.' {team=0}';

    public function asController(Team $team): JsonResponse
    {
        $executed = RateLimiter::attempt(
            key:"refresh-distribution-channels-$team->id",
            maxAttempts: 2,
            callback: fn () => $this->handle($team),
        );

        if (! $executed) {
            return response()->json(['message' => 'Too many requests, please try again later'], 429);
        }

        return response()->json(['message' => 'Distribution channels stats fetched']);
    }

    public function asCommand(Command $command): void
    {
        $teamId = $command->argument('team');
        $team = $teamId > 0 ? Team::find($teamId) : null;

        $this->handle($team);
    }

    public function handle(?Team $team = null): void
    {
        if ($team) {
            $this->fetchStatsForTeam($team);

            return;
        }
        Team::query()
            ->whereRelation('providerAccounts', 'provider_id', '=', ChannelManagerProvider::ID)
            ->each(fn (Team $team) => $this->fetchStatsForTeam($team));
    }

    private function fetchStatsForTeam(Team $team): void
    {
        abort_unless($team->providerAccounts->where('provider_id', '=', ChannelManagerProvider::ID)->isNotEmpty(), 404);

        $syncRequest = new ProviderSyncRequestData($team, ChannelManagerApi::ENDPOINT_CM_SALES_CHANNELS);
        $channels = ChannelManagerApi::instance()->doSyncRequest($syncRequest)->data;

        $status = data_get($channels, 'Status.data', 'No status found');
        if ($status != 'Success') {
            pnLog("Received a wrong response with $status", level: LogLevel::WARNING);

            return;
        }
        $activeChannels = collect(data_get($channels, 'Channels.Channel'))
            ->where('YourConfigurationComplete', 'true');

        if ($activeChannels->isEmpty()) {
            return;
        }

        // Preload relations:
        $team->connectedRentals->load('channelRentals');
        // Fetch data per channel (not possible to fetch it per rental)
        $activeChannels->each(fn (array $channel) => $this->fetchChannelStats($team, $channel)
        );
        // Delete disconnected channels
        $team->channelRentals()
            ->whereNotIn('channel', $activeChannels->pluck('CompanyName'))
            ->update(['active' => false]);

        // Mark connected rentals
        $connectedRentals = $team->channelRentals->pluck('rental_id')->unique();
        $team->connectedRentals
            ->where('has_ever_been_in_ota', '=', false)
            ->whereIn('id', $connectedRentals)
            ->each(fn (Rental $r) => $r->update(['has_ever_been_in_ota' => true]));

        $kpi = TeamPerformanceIndicator::getOrCreateForTeam($team);
        $kpi->cm_active_channels_count = $activeChannels->count();
        $kpi->cm_active_channels = $activeChannels->pluck('CompanyName');

        $kpi->cm_active_rentals = $team->connectedRentals->pluck('id');
        $kpi->cm_active_rental_count = $team->connectedRentals->count();

        $distributed = $team->connectedRentals->whereNotNull('external_id')->pluck('id');
        $kpi->cm_distributed_rentals = $distributed;
        $kpi->cm_distributed_rentals_count = $distributed->count();

        $inChannel = $team->channelRentals()->where('active', true)->groupBy('rental_id')->pluck('rental_id');
        $kpi->cm_in_channels_rentals = $inChannel;
        $kpi->cm_in_channels_rental_count = $inChannel->count();

        $kpi->save();
    }

    private function fetchChannelStats(Team $team, array $channel): void
    {
        $channelName = $channel['CompanyName'];
        $rentals = $team->connectedRentals;
        $syncRequest = new ProviderSyncRequestData($team,
            endpoint: ChannelManagerApi::ENDPOINT_CM_RENTALS,
            singleId: $channel['ChannelID']);
        $channelInfo = ChannelManagerApi::instance()->doSyncRequest($syncRequest)->data;

        $status = data_get($channelInfo, 'Status.data', 'No status found');
        if ($status != 'Success') {
            pnLog("Received a wrong response with $status", level: LogLevel::WARNING);

            return;
        }
        $clusters = collect(data_get($channelInfo, 'Channels.Channel.Clusters.Cluster'));
        $rentalsIdsInChannel = collect();

        foreach ($clusters as $cluster) {
            $clusterId = data_get($cluster, 'attr_ClusterId');

            if (data_get($cluster, 'Active') == 'false') {
                continue;
            }
            $unitsInChannel = collect(data_get($cluster, 'Units.Unit'));

            // A channel unit may contain multiple properties
            $unitsInChannel->each(function (array $unitInChannel) use ($rentals, $channelName, $team, &$rentalsIdsInChannel, $clusterId) {
                $propertiesInChannel = collect(data_get($unitInChannel, 'Properties.Property'));
                // Iterate for all properties in the unit.
                $propertiesInChannel->each(function (array $propertyInChanel) use ($rentals, &$rentalsIdsInChannel, $channelName, $team, $unitInChannel, $clusterId) {
                    $ruExternalId = data_get($propertyInChanel, 'attr_Id');
                    /** @var Rental $rental */
                    $rental = $rentals->firstWhere('external_id', $ruExternalId);
                    if (! is_null($rental)) {
                        $rentalsIdsInChannel->add($rental->id);
                        /** @var ChannelRental $dRental */
                        $dRental = $rental->channelRentals->firstWhere('channel', $channelName);
                        if (is_null($dRental)) {
                            $dRental = $rental->channelRentals()->create(['channel' => $channelName]);
                        }
                        $dRental->channel_account_id = $clusterId;
                        $dRental->channel_listing_id = $this->getOrGenerateListingId($channelName, $unitInChannel);
                        $dRental->markup = $this->parseMarkUp(data_get($unitInChannel, 'Rates.Rate.0.attr_Markup'));
                        $dRental->active = data_get($unitInChannel, 'attr_Active') == 'true';
                        $dRental->content_status = data_get($unitInChannel, 'Status.ContentStatus');
                        $dRental->ari_status = data_get($unitInChannel, 'Status.ARIStatus');
                        $dRental->url = $this->getOrGenerateUrl($channelName, $unitInChannel);
                        $dRental->save();
                    } else {
                        pnLog("[Channel Manager] This property should be deleted from Rentals United ($ruExternalId)", $team, LogLevel::WARNING);
                    }
                });
            });
        }
        $team->channelRentals
            ->where('channel', '=', $channelName)
            ->whereNotIn('rental_id', $rentalsIdsInChannel)
            ->each(fn (ChannelRental $rd) => $rd->update(['active' => false]));
    }

    private function getOrGenerateUrl(string $channelName, array $rentalInChannel): ?string
    {
        if ($channelName == 'Airbnb') {
            return 'https://www.airbnb.com/rooms/'.data_get($rentalInChannel, 'attr_ExternalUnitId');
        }

        return data_get($rentalInChannel, 'Status.URLtoProperty');
    }

    private function getOrGenerateListingId(string $channelName, array $rentalInChannel): ?string
    {
        if (in_array($channelName, ['Airbnb', 'Booking.com', 'Expedia', 'Agoda'])) {
            return data_get($rentalInChannel, 'attr_ExternalUnitId');
        }
        // Rentals united does not provide the OTA identifier, but we can extract it from the URL.
        $url = data_get($rentalInChannel, 'Status.URLtoProperty');
        if ($channelName == 'Vrbo') {
            return Str::of($url)->after('https://www.vrbo.com/')->before('?')->before('?')->before('h');
        }
        if ($channelName == 'Homelike') {
            return Str::of($url)->after('https://www.thehomelike.com/apartment/')->before('?')->before('/');
        }
        if ($channelName == 'HomeToGo') {
            return Str::of($url)->after('https://hometogo.de/rental/')->before('?')->before('/');
        }
        if ($channelName == 'Holidu GmbH') {
            return Str::of($url)->after('https://www.holidu.com/d/')->before('?')->before('/');
        }
        if ($channelName == 'Travelstaytion.com') {
            return Str::of($url)->after('https://www.travelstaytion.com/rooms/')->before('?')->before('/');
        }
        if ($channelName == 'Leboncoin') {
            return Str::of($url)->after('https://www.locasun.fr/')->before('-');
        }
        if ($channelName == 'Spacest.com') {
            return Str::of($url)->after('https://spacest.com/rent-listing/')->before('?')->before('/');
        }

        return null;
    }

    private function parseMarkUp(?int $data_get): float
    {
        return $data_get - 100;
    }
}
