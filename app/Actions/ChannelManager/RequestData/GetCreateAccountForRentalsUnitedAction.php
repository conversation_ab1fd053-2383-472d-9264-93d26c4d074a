<?php

namespace App\Actions\ChannelManager\RequestData;

use App\DataProviders\ProviderApi\RentalsUnitedApi;
use App\Models\Country;
use App\Models\Team;

class GetCreateAccountForRentalsUnitedAction extends GetAbstractDataRentalsUnitedAction
{
    public function handle(Team $team, string $accountId, string $password): array
    {
        $country = Country::find($team->teamDetails->company_country)->name;
        $city = $team->teamDetails->company_city;

        /** @var RentalsUnitedApi $api */
        $api = RentalsUnitedApi::instance();
        $locationId = $api->getLocationByName($country, $city);

        return [
            'FirstName' => $team->name,
            'LastName' => 'Managed by Rental Ninja',
            'Email' => $accountId,
            'Password' => $password,
            'Locations' => ['LocationId' => $locationId],
        ];
    }
}
