<?php

namespace App\Actions\ChannelManager\RequestData;

use App\DTO\ChannelManager\RentalDiscountsValuesData;
use App\Enum\RentalDiscountEnum;
use App\Models\Rental;
use App\Models\RentalDiscount;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ArrayToXml\ArrayToXml;

class GetDiscountsForRentalsUnitedAction
{
    use AsAction;

    public $commandSignature = 'ninja-dev:get-discounts-for-rentals-united';

    public function asCommand(Command $command): int
    {
        $team = Team::find(5);
        $rental = Rental::getRentalModel($team, 2978829371);
        $array = $this->handle($rental, RentalDiscountEnum::lastMinute);

        $command->info(json_encode($array, JSON_PRETTY_PRINT));

        $arrayToXml = new ArrayToXml($array, 'TEST');
        $command->info($arrayToXml->dropXmlDeclaration()->prettify()->toXml());

        return $command::SUCCESS;
    }

    public function handle(Rental $rental, RentalDiscountEnum $type, ?Carbon $firstDay = null, ?Carbon $endDay = null): array
    {
        if (is_null($firstDay)) {
            $firstDay = Carbon::today();
        }
        if (is_null($endDay)) {
            $endDay = Carbon::today()->addMonths($rental->bookable_months);
        }

        // Prepare data
        $groups = [];
        $fromDay = $firstDay->copy();
        $currentDiscount = $this->getDiscount($rental, $type, $firstDay);
        foreach ($firstDay->toPeriod($endDay) as $periodDay) {
            $checkingDay = Carbon::instance($periodDay);
            $discount = $this->getDiscount($rental, $type, $checkingDay);
            // Perform needed action when availability changes
            if ($currentDiscount?->id != $discount?->id) {
                // Register previous discounts
                $toDay = $checkingDay->copy()->subDay();
                $groups = array_merge($groups, $this->periodDiscounts($type, $fromDay, $toDay, $currentDiscount));
                // Prepare next period
                $fromDay = $checkingDay->copy();
                $currentDiscount = $discount;
            }
            $checkingDay->addDay();
        }
        // Saves the last discount
        if ($fromDay < $endDay) {
            $groups = array_merge($groups, $this->periodDiscounts($type, $fromDay, $endDay, $currentDiscount));
        }
        $name = $type == RentalDiscountEnum::longStay ? 'LongStay' : 'LastMinute';

        return [
            $name => $groups,
            '_attributes' => ['PropertyID' => $rental->external_id],
        ];
    }

    private function getDiscount(Rental $rental, RentalDiscountEnum $type, ?Carbon $firstDay): ?RentalDiscount
    {
        if ($type == RentalDiscountEnum::longStay) {
            return $rental->getLongStayDiscountForDay($firstDay);
        }

        return $rental->getLastMinuteDiscountForDay($firstDay);
    }

    private function periodDiscounts(RentalDiscountEnum $type, Carbon $fromDay, Carbon $endDay, ?RentalDiscount $currentDiscount): array
    {
        if ($type == RentalDiscountEnum::longStay) {
            return $this->periodLongStayDiscounts($fromDay, $endDay, $currentDiscount);
        }

        return $this->periodLastMinuteDiscounts($fromDay, $endDay, $currentDiscount);
    }

    private function periodLastMinuteDiscounts(Carbon $fromDay, Carbon $endDay, ?RentalDiscount $currentDiscount): array
    {
        if (is_null($currentDiscount)) {
            return [['_attributes' => [
                'DateFrom' => $fromDay->toDateString(),
                'DateTo' => $endDay->toDateString(),
                'DaysToArrivalFrom' => 0,
                'DaysToArrivalTo' => 180,
            ],
                '_value' => strval(0),
            ]];
        }
        $data = [];
        $daysToArrival = 0;
        /** @var RentalDiscountsValuesData $discount */
        foreach ($currentDiscount->discounts as $discount) {
            if ($discount->days >= $daysToArrival) {
                $data[] = ['_attributes' => [
                    'DateFrom' => $fromDay->toDateString(),
                    'DateTo' => $endDay->toDateString(),
                    'DaysToArrivalFrom' => $daysToArrival,
                    'DaysToArrivalTo' => $discount->days,
                ],
                    '_value' => strval(round($discount->discount / 100)),
                ];
                $daysToArrival = $discount->days + 1;
            }
        }

        return $data;
    }

    private function periodLongStayDiscounts(Carbon $fromDay, Carbon $endDay, ?RentalDiscount $currentDiscount): array
    {
        if (is_null($currentDiscount)) {
            return [['_attributes' => [
                'DateFrom' => $fromDay->toDateString(),
                'DateTo' => $endDay->toDateString(),
                'Smaller' => 180,
                'Bigger' => 1,
            ],
                '_value' => strval(0),
            ]];
        }
        $data = [];
        $longStay = 180;
        /** @var RentalDiscountsValuesData $discount */
        foreach ($currentDiscount->discounts as $discount) {
            if ($discount->days <= $longStay) {
                $data[] = ['_attributes' => [
                    'DateFrom' => $fromDay->toDateString(),
                    'DateTo' => $endDay->toDateString(),
                    'Smaller' => $longStay,
                    'Bigger' => $discount->days,
                ],
                    '_value' => strval(round($discount->discount / 100)),
                ];
                $longStay = $discount->days - 1;
            }
        }

        return $data;
    }
}
