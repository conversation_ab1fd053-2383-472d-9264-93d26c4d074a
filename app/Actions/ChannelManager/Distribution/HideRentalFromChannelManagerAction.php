<?php

namespace App\Actions\ChannelManager\Distribution;

use App\Actions\Providers\Generic\ShouldPostToProviderInLocalEnvAction;
use App\DataProviders\ChannelManager\ChannelManagerPusher;
use App\Events\RentalUnpublishedFromChannelManagerEvent;
use App\Events\SubscriptionCancelledEvent;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\Concerns\AsAction;

use function response;

class HideRentalFromChannelManagerAction implements ShouldQueue
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental): JsonResponse
    {
        $this->handle($team, $teamRental);

        return response()->json('', 204);
    }

    public function handle(Team $team, Rental $rental): void
    {
        if ($team->config()->canUseChannelManager() && $team->config()->canChooseDistributedRentals() && $rental->is_rental_ninja && $rental->cm_active) {
            $rental->cm_active = false;
            $rental->save();

            // Prevent un-publishing from local environments
            if (ShouldPostToProviderInLocalEnvAction::run()) {
                ChannelManagerPusher::hideRental($team, $rental);
            }

            event(new RentalUnpublishedFromChannelManagerEvent($team, $rental->id));
        }
    }

    public function asListener(SubscriptionCancelledEvent $event): void
    {
        $team = $event->team;
        $subscription = $team->subscription();

        pnLog('[HideRentalFromChannelManagerAction] asListener for connected rentals', $team->id);

        // When the subscription is set to stripe_status = past_due, the SubscriptionCanceledEvent is not triggered. So, when such event is triggered,
        // ensure we disconnect all rentals from the channel manager
        if (! $subscription || $subscription->statusCanceled()) {
            $team->connectedRentals->each(fn (Rental $rental) => $this->handle($team, $rental));
        }
    }
}
