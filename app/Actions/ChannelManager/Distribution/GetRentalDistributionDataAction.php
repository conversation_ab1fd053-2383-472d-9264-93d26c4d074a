<?php

namespace App\Actions\ChannelManager\Distribution;

use App\DTO\ChannelManager\DistributionData;
use App\Models\DistributionWebsite;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

class GetRentalDistributionDataAction
{
    use AsAction;

    public function asController(Team $team, Rental $teamRental): DataCollection
    {
        return $this->handle($team, $teamRental);
    }

    public function handle(Team $team, Rental $rental): DataCollection
    {
        $channels = $rental->channelRentals->sortBy('channel');
        $rentalWebsites = $team->distributionWebsites
            ->filter(fn (DistributionWebsite $w) => collect($w->rentals)->contains($rental->id));
        $distribution = $channels->merge($rentalWebsites);

        return DistributionData::collection($distribution);
    }
}
