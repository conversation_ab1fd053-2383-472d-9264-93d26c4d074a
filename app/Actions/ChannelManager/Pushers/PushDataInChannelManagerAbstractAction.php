<?php

namespace App\Actions\ChannelManager\Pushers;

use App\Models\Rental;
use App\Models\Team;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;

abstract class PushDataInChannelManagerAbstractAction implements ShouldBeUniqueUntilProcessing
{
    use AsAction;

    public int $jobMaxExceptions = 2;
    public int $jobBackoff = 120;

    public string $jobQueue = 'pushes';
    public int $jobUniqueFor = 60;

    public function getJobUniqueId(Team $team, int $rentalId): string
    {
        pnLog("[Channel Manager] Job unique {$team->id}_{$rentalId}", $team);

        return "{$team->id}_{$rentalId}";
    }

    public function configureJob(JobDecorator $job): void
    {
        $job->delay(60);
    }

    public function asJob(Team $team, int $rentalId): void
    {
        $rental = Rental::getRentalModel($team, $rentalId);
        pnLog("[Channel Manager] AsJob $rental->id $rental->team_id", $team);

        if (! $rental->cm_active) {
            return;
        }
        $this->handle($team, $rental);
    }

    protected function checkConfiguration(Team $team, Rental $rental): void
    {
        // It does not load parameters properly
        if (! $team->config()->canManageExtraRentals()) {
            abort(400, 'Team cannot manage extra rentals');
        }
        if (! $rental->cm_active) {
            abort(400, 'This rental is not published');
        }
    }
}
