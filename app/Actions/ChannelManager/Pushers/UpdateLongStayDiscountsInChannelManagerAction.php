<?php

namespace App\Actions\ChannelManager\Pushers;

use App\DataProviders\ChannelManager\ChannelManagerPusher;
use App\Models\Rental;
use App\Models\Team;

class UpdateLongStayDiscountsInChannelManagerAction extends PushDataInChannelManagerAbstractAction
{
    // To generate a correct helper
    public function asJob(Team $team, int $rentalId): void
    {
        parent::asJob($team, $rentalId);
    }

    public function handle(Team $team, Rental $rental): void
    {
        pnLog("[Channel Manager] Updating long stay discounts for rental $rental->id", $team);

        $this->checkConfiguration($team, $rental);

        ChannelManagerPusher::putLongStayDiscounts($team, $rental);
    }
}
