<?php

namespace App\Actions\Tasks\RecurrentTask;

use App\Models\RecurrentTask;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateFutureTasksForRecurrentTaskAction
{
    use AsAction;

    const SIGNATURE = 'ninja:create-future-tasks-for-recurrent-task';

    public string $commandSignature = 'ninja:create-future-tasks-for-recurrent-task {rTaskId}';

    public function asCommand(Command $command): void
    {
        $rTaskId = $command->argument('rTaskId');

        $rTask = RecurrentTask::findOrFail($rTaskId);

        $this->handle($rTask);
    }

    public function asJob(RecurrentTask $task): void
    {
        $this->handle($task);
    }

    public function handle(RecurrentTask $task): void
    {
        $task->load('recurrentTaskRentals', 'recurrentTaskRentals.rental');

        $daysInAdvance = $task->team->config()->tasksCreationCalculationDays();
        $lastDate = Carbon::today()->endOfDay()->addDays($daysInAdvance);

        $nextDay = $task->next_recurrent;
        while (! is_null($nextDay) && $nextDay < $lastDate) {
            if (! $nextDay->isBefore(Carbon::today())) {
                $this->processTasksPerDate($task, $nextDay);
            }
            $nextDay = $task->getNextDate();
        }
        $task->save();
    }

    public function processTasksPerDate(RecurrentTask $task, Carbon $nextDay): void
    {
        if ($task->is_team_task) {
            TaskFromRecurrentAction::run($task, $nextDay);
        }
        foreach ($task->recurrentTaskRentals as $recurrentTaskRental) {
            TaskFromRecurrentAction::run($task, $nextDay, $recurrentTaskRental->rental);
        }
    }
}
