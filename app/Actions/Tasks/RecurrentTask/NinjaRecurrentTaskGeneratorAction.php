<?php

namespace App\Actions\Tasks\RecurrentTask;

use App\Models\RecurrentTask;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class NinjaRecurrentTaskGeneratorAction
{
    use AsAction;

    const SIGNATURE = 'ninja:recurrent-task-generator';

    public string $commandSignature = 'ninja:recurrent-task-generator {--team=0}';

    public function asCommand(Command $command)
    {
        $team = $command->option('team');
        $team = $team == 0 ? null : $team;

        $this->handle($team);
        $command->info('Recurrent tasks generated');

        return $command::SUCCESS;
    }

    public function handle(?int $teamId)
    {
        Team::query()
            ->when($teamId, fn ($q) => $q->whereId($teamId))
            ->professionalTeams()
            ->each(function (Team $team) {
                $daysInAdvance = $team->config()->tasksCreationCalculationDays();
                RecurrentTask::query()
                    ->where('team_id', $team->id)
                    ->where('next_recurrent', '<', Carbon::today()->addDays($daysInAdvance)) //
                    ->get()
                    ->each(fn (RecurrentTask $rTask) => CreateFutureTasksForRecurrentTaskAction::dispatch($rTask));
            });
    }
}
