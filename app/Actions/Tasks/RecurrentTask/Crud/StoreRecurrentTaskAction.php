<?php

namespace App\Actions\Tasks\RecurrentTask\Crud;

use App\Actions\Tasks\RecurrentTask\CreateFutureTasksForRecurrentTaskAction;
use App\DTO\Tasks\RecurrentTaskDto;
use App\Http\Requests\ScheduledTask\RecurrentTaskRequest;
use App\Models\RecurrentTask;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreRecurrentTaskAction
{
    use AsAction;

    public function handle(RecurrentTaskDto $dto): RecurrentTask
    {
        $rTask = new RecurrentTask($dto->toArray());
        $rTask->next_recurrent = $rTask->getFirstDate();
        $rTask->save();
        $rTask->attachRentals($dto->rentals); // We need the id

        // Create the tasks according to this new scheduled task to avoid the user waiting for the next scheduled task update
        CreateFutureTasksForRecurrentTaskAction::dispatch($rTask);

        return $rTask;
    }

    public function asController(RecurrentTaskRequest $request, Team $team)
    {
        $rTask = $this->handle($request->toDto());

        return GetRecurrentTaskWithRelationsAction::make()->asController($team, $rTask);
    }
}
