<?php

namespace App\Actions\Tasks\RecurrentTask\Crud;

use App\Http\Resources\RecurrentTaskResource;
use App\Models\RecurrentTask;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class GetRecurrentTaskWithRelationsAction
{
    use AsAction;

    public function asController(Team $team, RecurrentTask $recurrentTask)
    {
        $recurrentTask = $this->handle($recurrentTask);

        return new RecurrentTaskResource($recurrentTask);
    }

    public function handle(RecurrentTask $task): RecurrentTask
    {
        $task->checklist?->load('items');
        $task->load(['assignee', 'supervisor', 'recurrentTaskRentals']);

        return $task;
    }
}
