<?php

namespace App\Actions\Tasks\RecurrentTask\Crud;

use App\Actions\Tasks\RecurrentTask\CreateFutureTasksForRecurrentTaskAction;
use App\DTO\Tasks\RecurrentTaskDto;
use App\Http\Requests\ScheduledTask\RecurrentTaskRequest;
use App\Http\Resources\RecurrentTaskResource;
use App\Models\RecurrentTask;
use App\Models\Task;
use App\Models\Team;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

use function now;

class UpdateRecurrentTaskAction
{
    use AsAction;

    public function asController(RecurrentTaskRequest $request, Team $team, RecurrentTask $recurrentTask): RecurrentTaskResource
    {
        $recurrentTask = $this->handle($recurrentTask, $request->toDto());

        return GetRecurrentTaskWithRelationsAction::make()->asController($team, $recurrentTask);
    }

    public function handle(RecurrentTask $rTask, RecurrentTaskDto $dto): RecurrentTask
    {
        // 1: Save recurrent start date, if it has changed, we will use the new one to recreate the tasks. Otherwise,
        //    we will calculate the next task taking into account the last recurrent task.
        $oldStartDate = $rTask->recurrent_start_date;

        // 2: Update Recurrent Task Model
        $rTask->update($dto->toArray());
        $rTask->attachRentals($dto->rentals);
        $this->recalculateNextRecurringDate($rTask, $oldStartDate);
        $rTask->save();

        // 3: Delete and regenerate future tasks
        $this->deleteTasks($rTask);
        CreateFutureTasksForRecurrentTaskAction::dispatch($rTask);

        return $rTask;
    }

    private function recalculateNextRecurringDate(RecurrentTask $task, Carbon $oldStartDate): void
    {
        // Get the last task. If it exists and the recurrent_start_date has not changed, the start_from date will be used.
        $lastTask = Task::query()
            ->where('recurrent_task_id', $task->id)
            ->where('start_from', '<', now())
            ->orderByDesc('start_from')
            ->first();

        if (is_null($lastTask) || $oldStartDate != $task->recurrent_start_date) {
            $task->next_recurrent = $task->getFirstDate();
        } else {
            // This is actually the last job, so we compute the next job.
            $task->next_recurrent = $lastTask->start_from;
            $task->getNextDate();
        }
    }

    private function deleteTasks(RecurrentTask $task): void
    {
        Task::query()
            ->withTrashed()
            ->where('recurrent_task_id', $task->id)
            ->where('start_from', '>', Carbon::now())
            ->whereNull('completed_at')
            ->forceDelete(); // We should force delete here because if the user adds any of these rentals again, tasks won't be re-created if they are soft deleted.
    }
}
