<?php

namespace App\Actions\Tasks;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Actions\Bookings\Calendar\GetBookingDataForCalendarAction;
use App\DTO\Tasks\TaskRequestForRentalFiltersData;
use App\Http\Requests\Tasks\TasksForRentalRequest;
use App\Models\Booking;
use App\Models\Task;
use App\Models\Team;
use App\Query\TaskQuery;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTasksForRentalAction
{
    use AsAction;

    public function asController(TasksForRentalRequest $request, Team $team, int $rentalId)
    {
        VerifyUserHasAccessToRentalsAction::run($request->user(), $rentalId);
        $dto = TaskRequestForRentalFiltersData::fromRequest($request, $team, $rentalId);

        return $this->handle($dto);
    }

    public function handle(TaskRequestForRentalFiltersData $data): array
    {
        return [
            'rental_id' => $data->rentals[0],
            'team_id' => $data->team->id,
            'from' => $data->start()->toDateString(),
            'to' => $data->end()->toDateString(),
            'bookings' => $this->bookingsToReturn($data),
            'tasks' => $this->tasksToReturn($data),
        ];
    }

    private function bookingsToReturn(TaskRequestForRentalFiltersData $data): Collection
    {
        return once(callback: function () use ($data) {
            $firstDayOfMonth = $data->start()->copy();
            $lastDayOfMonth = $data->end()->copy();
            $bookings = $this->bookings($data);
            $used = collect();
            $dates = collect($data->dates());

            return $dates
                ->mapWithKeys(callback: function (Carbon $date) use (
                    $lastDayOfMonth,
                    $firstDayOfMonth,
                    $bookings,
                    &$used
                ) {
                    $startOfDay = $date->copy()->startOfDay()->timestamp;
                    $endOfDay = $date->copy()->endOfDay()->timestamp;
                    $validBookings = $bookings
                        ->where('end_at', '>=', $startOfDay)
                        ->where('start_at', '<=', $endOfDay)
                        ->whereNotIn('id', $used);

                    $ids = $validBookings->pluck('id');
                    $used = $used->push($ids)->flatten()->filter();

                    return [
                        $date->toDateString() => $validBookings
                            ->map(fn (Booking $booking) => GetBookingDataForCalendarAction::run(
                                booking: $booking,
                                date: $date,
                                firstDayOfMonth: $firstDayOfMonth,
                                lastDayOfMonth: $lastDayOfMonth
                            ))
                            ->values()
                            ->toArray(),
                    ];
                });
        });
    }

    private function bookings(TaskRequestForRentalFiltersData $data): Collection
    {
        return once(fn () => Booking::query()
            ->onTeam($data->team)
            ->filledBetween($data->start(), $data->end())
            ->onRentals($data->rentals)
            ->with(['source', 'client', 'team'])
            ->select([
                'id',
                'team_id',
                'rental_id',
                'client_id',
                'start_at',
                'end_at',
                'adults',
                'children',
                'reference',
                'status',
                'source_id',
                'final_price',
                'final_rental_price',
                'paid_amount',
                'provider_id',
                rawQueryBookingCheckInTime($data->defaultCheckInTime()), // We are skipping here the rental default ci time but this only affects a few pixels
                rawQueryBookingCheckOutTime($data->defaultCheckOutTime()), // We are skipping here the rental default co time but this only affects a few pixels
            ])
            ->oldest('start_at')
            ->get()
        );
    }

    private function tasksToReturn(TaskRequestForRentalFiltersData $data)
    {
        return once(function () use ($data) {
            $tasks = $this->tasks($data);
            $used = collect();
            $dates = collect($data->dates());
            $tasksPreviousToRows = $dates->mapWithKeys(function (Carbon $date) use (&$used, $tasks, $data) {
                $validTasks = $tasks
                    ->filter(fn (Task $task) => $task->canCompleteInDate($date))
                    ->whereNotIn('id', $used);
                $ids = $validTasks->pluck('id');
                $used = $used->push($ids)->flatten()->filter();
                $tasks = $validTasks
                    ->map(fn (Task $task) => $this->dataForSingleTask($task, $date, $data))
                    ->values()
                    ->toArray();

                return [$date->toDateString() => $tasks];
            });

            // Get all the tasks and their corresponding important attributes to calculate rows.
            $rows = $tasksPreviousToRows
                ->flatten(1)
                ->sortBy([
                    fn ($a, $b) => $this->sortTask('start_from', $a, $b),
                    fn ($a, $b) => $this->sortTask('finish_before', $a, $b),
                ]);

            $rows = $rows
                ->map(fn (array $task) => collect($task)->only('id', 'absolute_left', 'end_at_from_left')->toArray())
                ->toArray();

            $taskAndRows = collect();
            array_walk($rows, function ($task, $index, $rows) use (&$taskAndRows) {
                $row = 0;
                // Get tasks
                /** @noinspection CollectFunctionInCollectionInspection */
                $conflicting = collect($rows)
                    ->where('id', '!=', $task['id'])
                    ->filter(fn ($item) => $this->tasksConflict($item, $task));

                if ($conflicting->isNotEmpty()) {
                    $conflictingIds = $conflicting->pluck('id');
                    $usedRows = $taskAndRows
                        ->whereIn('id', $conflictingIds)
                        ->pluck('row')
                        ->toArray();
                    $totalRows = range(0, count($usedRows));
                    $missing = collect(array_diff($totalRows, $usedRows))->first(); // (3,6)
                    $row = $missing;
                }

                $task['row'] = $row;
                $taskAndRows->push($task);
            }, $rows);

            return $tasksPreviousToRows->mapWithKeys(function ($value, $index) use ($taskAndRows) {
                /** @noinspection CollectFunctionInCollectionInspection */
                $values = collect($value)
                    ->map(function (array $task) use ($taskAndRows) {
                        $task['row'] = ((object) $taskAndRows->firstWhere('id', $task['id']))->row;

                        return $task;
                    })
                    ->values()
                    ->toArray();

                return [$index => $values];
            });
        });
    }

    private function tasks(TaskRequestForRentalFiltersData $data): Collection
    {
        return once(function () use ($data) {
            $start = $data->start()->copy();
            $end = $data->end()->copy();

            return Task::query()
                ->onRentals($data->rentals)
                ->where('team_id', $data->team->id)
                ->whereDate('finish_before', '>=', $start)
                ->whereDate('start_from', '<=', $end)
                ->when(! $data->includeAllTasks(), fn (TaskQuery $query) => $query
                    ->where(fn (TaskQuery $query) => $query
                        ->where('assignee_id', $data->user()->id)
                        ->orWhere('role', '=', $data->role())))
                ->with(['assignee', 'supervisor', 'team.users.userRentals'])
                ->orderBy('finish_before')
                ->orderBy('priority', 'desc')
                ->filterByScheduledTasksAndJobTitle($data->job_title, $data->scheduled_ids)
                ->filterByScheduledTasks($data->scheduled_ids)
                ->filterByJobTitle($data->job_title)
                ->filterByUsersAndRoles($data->users, $data->roles)
                ->get();
        });
    }

    private function dataForSingleTask(Task $task, Carbon $date, $data): array
    {
        $firstDayOfMonth = $data->start()->copy();
        $lastDayOfMonth = $data->end()->copy();

        return $task->dataForTasks($date, $firstDayOfMonth, $lastDayOfMonth);
    }

    private function sortTask(string $attribute, $a, $b): int
    {
        return Carbon::parse($a[$attribute])->timestamp <=> Carbon::parse($b[$attribute])->timestamp;
    }

    /**
     * Checks if two tasks conflict with each
     * other.
     */
    private function tasksConflict($item, $task): bool
    {
        // TOP
        $left = $item['absolute_left']; // 3050
        $endAtFromLeft = $item['end_at_from_left']; // 3290

        // BOTTOM
        $taskAbsoluteLeft = $task['absolute_left']; // 3200
        $taskEndAtFromLeft = $task['end_at_from_left']; // 3440

        // Check 3290. High 3440. Low 3200 -> True
        // Check 3200. High 3290. Low 3050 -> Also True
        return $this->isBetween($endAtFromLeft, $taskEndAtFromLeft, $taskAbsoluteLeft) || $this->isBetween($taskAbsoluteLeft, $endAtFromLeft, $left);
    }

    public function isBetween($varToCheck, $high, $low): bool
    {
        if ($varToCheck < $low || $varToCheck > $high) {
            return false;
        }

        return true;
    }
}
