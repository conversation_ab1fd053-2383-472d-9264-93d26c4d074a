<?php

namespace App\Actions\Tasks;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Actions\Bookings\Calendar\GetBookingDataForCalendarAction;
use App\DTO\Tasks\TaskRequestForRentalFiltersData;
use App\Http\Requests\Tasks\TasksForRentalRequest;
use App\Models\Booking;
use App\Models\Task;
use App\Models\Team;
use App\Query\TaskQuery;
use Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTasksForRentalsQueryAction
{
    use AsAction;

    private ?Collection $bookings = null;

    private ?Collection $tasks = null;

    public function asController(TasksForRentalRequest $request, Team $team): array
    {
        $dto = TaskRequestForRentalFiltersData::fromRequest($request, $team);
        VerifyUserHasAccessToRentalsAction::run(Auth::user(), $dto->rentals);

        return $this->handle($dto);
    }

    public function handle(TaskRequestForRentalFiltersData $data): array
    {
        $dataPerRental = [];
        foreach ($data->rentals as $rentalId) {
            $dataPerRental[] = [
                'rental_id' => $rentalId,
                'bookings' => $this->bookingsToReturn($data, $rentalId),
                'tasks' => $this->tasksToReturn($data, $rentalId),
            ];
        }
        if ($data->includeAdminTasks) {
            $dataPerRental[] = [
                'rental_id' => null,
                'bookings' => null,
                'tasks' => $this->tasksToReturn($data, null),
            ];
        }

        return [
            'team_id' => $data->team->id,
            'from' => $data->start()->toDateString(),
            'to' => $data->end()->toDateString(),
            'data' => $dataPerRental,
        ];
    }

    private function bookingsToReturn(TaskRequestForRentalFiltersData $data, int $rentalId): Collection
    {
        $firstDayOfMonth = $data->start()->copy();
        $lastDayOfMonth = $data->end()->copy();
        $bookings = $this->bookings($data)->where('rental_id', '=', $rentalId)->values();

        return $bookings->map(function (Booking $booking) use ($firstDayOfMonth, $lastDayOfMonth) {
            $firstDay = Carbon::createFromTimestampUTC($booking->start_at)->max($firstDayOfMonth);

            return GetBookingDataForCalendarAction::run(
                booking: $booking,
                date: $firstDay,
                firstDayOfMonth: $firstDayOfMonth,
                lastDayOfMonth: $lastDayOfMonth
            );
        });
    }

    private function bookings(TaskRequestForRentalFiltersData $data): Collection
    {
        if (! is_null($this->bookings)) {
            return $this->bookings;
        }

        $this->bookings = Booking::query()
            ->onTeam($data->team)
            ->filledBetween($data->start(), $data->end())
            ->onRentals($data->rentals)
            ->with(['source', 'client'])
            ->select([
                'id',
                'team_id',
                'rental_id',
                'client_id',
                'start_at',
                'end_at',
                'adults',
                'children',
                'reference',
                'status',
                'source_id',
                'provider_id',
                rawQueryBookingCheckInTime($data->defaultCheckInTime()), // We are skipping here the rental default ci time but this only affects a few pixels
                rawQueryBookingCheckOutTime($data->defaultCheckOutTime()), // We are skipping here the rental default co time but this only affects a few pixels
            ])
            ->oldest('start_at')
            ->get();

        return $this->bookings;
    }

    private function tasksToReturn(TaskRequestForRentalFiltersData $data, ?int $rentalId): Collection
    {
        $data = $this->tasks($data)->where('rental_id', '=', $rentalId)
            ->map(fn (Task $task) => $this->dataForSingleTask($task, $task->start_from, $data))
            ->sortBy([
                fn ($a, $b) => $this->sortTask('start_from', $a, $b),
                fn ($a, $b) => $this->sortTask('finish_before', $a, $b),
            ])
            ->values();

        $taskAndRows = $this->calculateTasksRows($data);

        return $data->map(function (array $task) use ($taskAndRows) {
            $task['row'] = ((object) $taskAndRows->firstWhere('id', $task['id']))->row;

            return $task;
        });
    }

    public function calculateTasksRows(Collection $data): Collection
    {
        $rows = $data
            ->map(fn (array $task) => collect($task)->only('id', 'absolute_left', 'end_at_from_left')->toArray())
            ->toArray();

        $taskAndRows = collect();
        array_walk($rows, function ($task, $index, $rows) use (&$taskAndRows) {
            $row = 0;

            // Get tasks
            /** @noinspection CollectFunctionInCollectionInspection */
            $conflicting = collect($rows)
                ->where('id', '!=', $task['id'])
                ->filter(fn ($item) => $this->tasksConflict($item, $task));

            if ($conflicting->isNotEmpty()) {
                $conflictingIds = $conflicting->pluck('id');
                $usedRows = $taskAndRows
                    ->whereIn('id', $conflictingIds)
                    ->pluck('row')
                    ->toArray();
                $totalRows = range(0, count($usedRows));
                $missing = collect(array_diff($totalRows, $usedRows))->first(); // (3,6)
                $row = $missing;
            }

            $task['row'] = $row;
            $taskAndRows->push($task);
        }, $rows);

        return $taskAndRows;
    }

    private function tasks(TaskRequestForRentalFiltersData $data): Collection
    {
        if (! is_null($this->tasks)) {
            return $this->tasks;
        }
        $start = $data->start()->copy();
        $end = $data->end()->copy();

        $this->tasks = Task::query()
            ->onRentalsOrAdmin($data->rentals, $data->includeAdminTasks)
            ->where('team_id', $data->team->id)
            ->whereDate('finish_before', '>=', $start)
            ->whereDate('start_from', '<=', $end)
            ->when(! $data->includeAllTasks(), fn (TaskQuery $query) => $query
                ->where(fn (TaskQuery $query) => $query
                    ->where('assignee_id', $data->user()->id)
                    ->orWhere('role', '=', $data->role())))
            ->with(['assignee', 'supervisor', 'team.users.userRentals'])
            ->orderBy('finish_before')
            ->orderBy('priority', 'desc')
            ->filterByScheduledTasksAndJobTitle($data->job_title, $data->scheduled_ids)
            ->filterByScheduledTasks($data->scheduled_ids)
            ->filterByJobTitle($data->job_title)
            ->filterByUsersAndRoles($data->users, $data->roles)
            ->get();

        return $this->tasks;
    }

    private function dataForSingleTask(Task $task, Carbon $date, $data): array
    {
        $firstDayOfMonth = $data->start()->copy();
        $lastDayOfMonth = $data->end()->copy();

        return $task->dataForTasks($date, $firstDayOfMonth, $lastDayOfMonth);
    }

    private function sortTask(string $attribute, $a, $b): int
    {
        return Carbon::parse($a[$attribute])->timestamp <=> Carbon::parse($b[$attribute])->timestamp;
    }

    /**
     * Checks if two tasks conflict with each
     * other.
     */
    private function tasksConflict($item, $task): bool
    {
        // TOP
        $left = $item['absolute_left']; // 3050
        $endAtFromLeft = $item['end_at_from_left']; // 3290

        // BOTTOM
        $taskAbsoluteLeft = $task['absolute_left']; // 3200
        $taskEndAtFromLeft = $task['end_at_from_left']; // 3440

        // Check 3290. High 3440. Low 3200 -> True
        // Check 3200. High 3290. Low 3050 -> Also True
        return $this->isBetween($endAtFromLeft, $taskEndAtFromLeft, $taskAbsoluteLeft) || $this->isBetween($taskAbsoluteLeft, $endAtFromLeft, $left);
    }

    public function isBetween($varToCheck, $high, $low): bool
    {
        if ($varToCheck < $low || $varToCheck > $high) {
            return false;
        }

        return true;
    }
}
