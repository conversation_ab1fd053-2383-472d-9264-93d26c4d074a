<?php

namespace App\Actions\Tasks\Team;

use App\DTO\Tasks\TaskRequestFiltersData;
use App\Http\Resources\TaskResource;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTeamCurrentNoUsersTasksAction
{
    use AsAction;

    public function asController(ActionRequest $request, Team $team)
    {
        $filtered = GetTeamCurrentTasksAction::run(
            data: TaskRequestFiltersData::fromRequest($request, $team),
            users: null,
            roles: null,
            paginated: true
        );

        return TaskResource::collection($filtered);
    }
}
