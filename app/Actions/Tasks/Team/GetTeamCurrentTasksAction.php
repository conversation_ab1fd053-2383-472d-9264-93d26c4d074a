<?php

namespace App\Actions\Tasks\Team;

use App\Actions\Users\GetUsersFromRequestFilterAction;
use App\DTO\Tasks\TaskRequestFiltersData;
use App\Http\Resources\TaskResource;
use App\Models\Task;
use App\Models\Team;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTeamCurrentTasksAction
{
    use AsAction;

    public function asController(Team $team, Request $request): AnonymousResourceCollection
    {
        [$users, $roles] = GetUsersFromRequestFilterAction::run();
        $data = TaskRequestFiltersData::fromRequest($request, $team, $users);
        $tasks = $this->handle($data, $users, $roles, true);

        return TaskResource::collection($tasks);
    }

    public function handle(TaskRequestFiltersData $data, ?array $users, ?array $roles, bool $paginated = false): Collection|Paginator
    {
        $now = today()->endOfDay();

        $query = Task::query()
            ->onTeam($data->team)
            ->onRentalsOrAdmin($data->rentals, $data->includeAdminTask())
            ->whereIncomplete()
            ->whereDate('start_from', '<=', $now)
            ->filterByJobTitle($data->job_title)
            ->filterByScheduledTasksAndJobTitle($data->job_title, $data->scheduled_ids)
            ->filterByScheduledTasks($data->scheduled_ids)
            ->filterByUsersAndRoles($users, $roles)
            ->orderBy('start_from', 'desc')
            ->orderBy('priority', 'desc')
            ->orderBy('id')
            ->with(
                [
                    'booking.rental',
                    'booking.alerts.snoozed',
                    'booking.source',
                    'booking.team',
                    'rental',
                    'team',
                    'assignee',
                ]
            );

        $paginated ? $tasks = $query->paginate(50) : $tasks = $query->get();

        return $tasks;
    }
}
