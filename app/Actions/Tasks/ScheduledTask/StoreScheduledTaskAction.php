<?php

namespace App\Actions\Tasks\ScheduledTask;

use App\DTO\Tasks\ScheduledTaskDto;
use App\Http\Requests\ScheduledTask\ScheduledTaskRequest;
use App\Models\ScheduledTask;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreScheduledTaskAction
{
    use AsAction;

    public function handle(Team $team, ScheduledTaskDto $dto): ScheduledTask
    {
        $task = ScheduledTask::create($dto->toArray());

        SaveScheduledTaskRentalsAction::run($dto->rentals, $task);
        // Create the tasks according to this new scheduled task to avoid the user waiting for the next scheduled task update
        GetJobsForScheduledTaskAction::dispatch($task, $team);

        return $task;
    }

    public function asController(ScheduledTaskRequest $request, Team $team)
    {
        $task = $this->handle($team, $request->toDto($team));

        return GetScheduledTaskWithRelationsAction::make()->asController($team, $task);
    }
}
