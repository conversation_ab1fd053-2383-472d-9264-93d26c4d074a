<?php

namespace App\Actions\Tasks\ScheduledTask;

use App\Actions\Auth\VerifyRequestResourceIsFromSameTeamAction;
use App\DTO\Tasks\ScheduledTaskDto;
use App\Http\Requests\ScheduledTask\ScheduledTaskRequest;
use App\Models\ScheduledTask;
use App\Models\Task;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateScheduleTaskAction
{
    use AsAction;

    public function handle(Team $team, ScheduledTask $task, ScheduledTaskDto $dto, bool $removeTasks, bool $needsSupervision): ScheduledTask
    {
        $old_assignee = $task->assignee_id;
        $old_role = $task->role;
        $old_supervisor = $task->supervisor_id;
        $task->update($dto->toArray());

        // Load up the new assignee and the new role and check if they have changed.
        $new_assignee = $task->assignee_id;
        $new_role = $task->role;
        $new_supervisor = $task->supervisor_id;

        // Check if there have been changes in the role or assignee in this update.
        if ($old_assignee != $new_assignee || $old_role != $new_role) {
            // Create a query to look for tasks in the future that relate to this scheduled task
            $query = Task::query()
                ->where('scheduled_task_id', $task->id)
                ->where('finish_before', '>', now());

            // If the old assignee is not null and not empty... query those tasks
            if ($old_assignee != null) {
                $query->whereAssigneeId($old_assignee);
            } // If the old role is not null and not empty... query those tasks.
            elseif ($old_role != null) {
                $query->whereRole($old_role);
            }

            // If the new assignee isn't null and not empty, ASSIGN THE TASK TO THE NEW USER.
            if ($new_assignee != null) {
                // THIS UPDATES IN THE DATABASE DIRECTLY.
                $query->update(['assignee_id' => $new_assignee, 'role' => null]);
            } // If the new role isn't null and not empty, ASSIGN THE TASK TIO THE NEW ROLE.
            elseif ($new_role != null) {
                // THIS UPDATES IN THE DATABASE DIRECTLY.
                $query->update(['role' => $new_role, 'assignee_id' => null]);
            }
        }

        // If the supervisors have changed....
        if ($old_supervisor != $new_supervisor) {
            // Create a query to look for tasks in the future that relate to this scheduled task
            $query = Task::where('scheduled_task_id', $task->id)
                ->where('finish_before', '>', now());

            // If the old assignee is not null and not empty... query those tasks
            if ($old_supervisor != null) {
                $query->whereSupervisorId($old_supervisor);
            }

            // If the new supervisor isn't null and not empty, ASSIGN THE SUPERVISION TO THE NEW USER.
            if ($new_supervisor != null) {
                // THIS UPDATES IN THE DATABASE DIRECTLY.
                $query->update(['supervisor_id' => $new_supervisor]);
            }
        }

        if (! $needsSupervision) {
            $task->supervisor_id = null;
        }
        if ($task->role != null) {
            $task->assignee_id = null; // Ensure no assignee if assigned to a role
        }
        if ($task->isDirty()) {
            $task->save();
        }
        if ($removeTasks) {
            $this->deleteTasks($task, $dto);
        }

        SaveScheduledTaskRentalsAction::run($dto->rentals, $task);

        // Update the tasks according to the modified scheduled task to avoid the user waiting for the next scheduled task update
        GetJobsForScheduledTaskAction::dispatch($task, $team);

        return $task;
    }

    public function asController(ScheduledTaskRequest $request, Team $team, ScheduledTask $task)
    {
        VerifyRequestResourceIsFromSameTeamAction::run($team, $task);
        $task = $this->handle($team, $task, $request->toDto($team), $request->query('remove') === 'true', $request->input('needs_supervision') === true);

        return GetScheduledTaskWithRelationsAction::make()->asController($team, $task);
    }

    private function deleteTasks(ScheduledTask $task, ScheduledTaskDto $dto): void
    {
        $old_rental_ids = Task::query()->where('scheduled_task_id', $task->id)->pluck('rental_id')->toArray();
        $new_rental_ids = $dto->rentals;
        $diff = array_diff($old_rental_ids, $new_rental_ids);
        Task::query()
            ->where('scheduled_task_id', $task->id)
            ->where('start_from', '>', now())
            ->whereNull('completed_at')
            ->whereIn('rental_id', $diff)
            ->forceDelete(); // We should force delete here because if the user adds any of these rentals again, tasks won't be re-created if they are soft deleted.
    }
}
