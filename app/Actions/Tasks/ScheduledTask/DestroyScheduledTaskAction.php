<?php

namespace App\Actions\Tasks\ScheduledTask;

use App\Actions\Auth\VerifyRequestResourceIsFromSameTeamAction;
use App\Models\ScheduledTask;
use App\Models\Task;
use App\Models\Team;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;

class DestroyScheduledTaskAction
{
    use AsAction;

    public function asController(Team $team, ScheduledTask $scheduled_task): Response
    {
        VerifyRequestResourceIsFromSameTeamAction::run($team, $scheduled_task);
        $this->handle($team, $scheduled_task);

        return response('', 200);
    }

    public function handle(Team $team, ScheduledTask $scheduled_task): void
    {
        $this->removeUncompletedFutureTasks($scheduled_task->id);
        $scheduled_task->delete();
        GetJobsForScheduledTaskAction::dispatch($scheduled_task, $team);
    }

    private function removeUncompletedFutureTasks($scheduledTaskId): void
    {
        $class_name = getCleanClassName($this);

        Task::query()
            ->where('scheduled_task_id', $scheduledTaskId)
            ->whereNull('completed_at')
            ->where(function ($query) {
                $query->where('start_from', '>', now())
                    ->orWhereNull('start_from');
            })
            ->softDeleteWithDeletedBy("Deleted by $class_name because the parent scheduled task was destroyed.");
    }
}
