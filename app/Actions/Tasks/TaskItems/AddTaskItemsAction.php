<?php

namespace App\Actions\Tasks\TaskItems;

use App\Models\ChecklistItem;
use App\Models\RecurrentTask;
use App\Models\ScheduledTask;
use App\Models\Task;
use App\Models\TaskItem;
use Illuminate\Database\Eloquent\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class AddTaskItemsAction
{
    use AsAction;

    public function handle(Task $task, ScheduledTask|RecurrentTask $schRecTask): void
    {
        $protectedAttributes = $task->manually_modified_attributes ?: [];
        $itemsProtected = $task->manually_modified && in_array('task_items', $protectedAttributes);

        // Only add checklists if not completed.
        if ($task->completed_at == null && ! $itemsProtected && $schRecTask->checklist->items->isNotEmpty()) {
            // Trying to minimize:
            $items = $task->items;

            $itemIds = [];
            foreach ($schRecTask->checklist->items as $index => $item) {
                $i = $this->getTaskItem($items, $item, $task, $index);
                $i->fillForTask($index, $item, $task);
                $i->saveOrFail();
                $itemIds[] = $i->id;
            }

            /// Delete extra items
            $items->filter(fn (TaskItem $item) => ! in_array($item->id, $itemIds))
                ->pluck('id')
                ->whenNotEmpty(fn (\Illuminate\Support\Collection $collection) => TaskItem::query()
                    ->where('team_id', '=', $schRecTask->team_id)
                    ->where('task_id', '=', $task->id)
                    ->whereIn('id', $collection)
                    ->delete()
                );
        }
    }

    private function getTaskItem(Collection $items, ChecklistItem $item, Task $task, int $index): TaskItem
    {
        return $items
            ->where('team_id', '=', $item->team_id)
            ->where('task_id', '=', $task->id)
            ->firstWhere('order', '=', $index) ??
            new TaskItem([
                'team_id' => $item->team_id,
                'task_id' => $task->id,
                'order' => $index,
            ]);
    }
}
