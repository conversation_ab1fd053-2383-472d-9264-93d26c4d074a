<?php

namespace App\Actions\Tasks\Checklists;

use App\Http\Resources\ChecklistResource;
use App\Models\Checklist;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTeamChecklistsAction
{
    use AsAction;

    public function asController(Team $team, ActionRequest $request): AnonymousResourceCollection
    {
        $query = $this->handle(
            teamId: $team->id,
            forPagination: true,
            search: $request->input('search'),
        );

        return ChecklistResource::collection($query->paginate(500));
    }

    public function handle(int $teamId, bool $forPagination = false, bool $slimVersion = false, ?string $search = null): Builder|Collection
    {
        return Checklist::query()
            ->when($slimVersion, fn (Builder $query) => $query->select('id', 'title'))
            ->where(['team_id' => $teamId])
            ->when($search, fn (Builder $query) => $query->where('title', 'like', "%$search%"))
            ->when(! $forPagination, fn (Builder $query) => $query->get());
    }

    public function slimTeamCheckLists(Team $team): Collection
    {
        return $this->handle($team->id, slimVersion: true);
    }
}
