<?php

namespace App\Actions\Tasks;

use App\Events\TaskDeletedEvent;
use App\Models\Task;
use App\Models\Team;
use Illuminate\Support\Facades\Auth;
use Lorisleiva\Actions\Concerns\AsAction;

class DestroyTaskAction extends BaseTaskAction
{
    use AsAction;

    public function handle(Task $task, string $deleted_by): bool
    {
        $task->deleted_by = $deleted_by;
        $task->deleted_at = now();
        event(new TaskDeletedEvent($task, Auth::user()));

        // WARNING: this won't soft delete any child models, but right now, none can be soft deleted, so we just need to soft delete this task

        return $task->save();
    }

    public function asController(Team $team, Task $task)
    {
        $this->check($team, $task);
        $user = Auth::user();
        $result = $this->handle($task, "$user->id");
        if ($result) {
            return response('', 200);
        } else {
            return response("We couldn't delete the Job. Try again later.", 200);
        }
    }
}
