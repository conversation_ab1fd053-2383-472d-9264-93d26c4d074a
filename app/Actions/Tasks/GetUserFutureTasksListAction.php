<?php

namespace App\Actions\Tasks;

use App\Actions\Rentals\GetRentalsFromFilterAction;
use App\DTO\Tasks\TaskRequestFiltersData;
use App\Http\Resources\TaskResource;
use App\Models\Team;
use App\Models\User;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Lorisleiva\Actions\Concerns\AsAction;

class GetUserFutureTasksListAction
{
    use AsAction;

    public function asController(Team $team): AnonymousResourceCollection
    {
        $rentals = GetRentalsFromFilterAction::run();
        $user = Auth::user();
        $tasks = $this->handle($team, $user, $rentals, true);

        return TaskResource::collection($tasks);
    }

    public function handle(Team $team, User $user, array $rentals, bool $paginated = false): Collection|Paginator
    {
        $data = TaskRequestFiltersData::fromRequest(request(), $team);

        return GetUserCurrentOrFutureTasksListAction::run($data, $team, $user, false, $rentals, $paginated);
    }
}
