<?php

namespace App\Actions\Backups;

use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Stringable;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\OutputInterface;

class DownloadDatabaseDumpAction
{
    use AsAction;

    public string $commandSignature = 'ninja:import-dump {--environment=production : Environment to load} {--complete=false : Import the complete version}';

    private ?ProgressBar $progressBar = null;

    private ?OutputInterface $output = null;

    private ?Command $command;

    public function asCommand(Command $command): int
    {
        if (empty(config('filesystems.disks.backup.secret')) || empty(config('filesystems.disks.backup.key'))) {
            $command->error("We couldn't download a backup because we didn't find the credentials for it");

            return 0;
        }

        $this->command = $command;
        $env = $command->option('environment');
        $complete = filter_var($command->option('complete'), FILTER_VALIDATE_BOOLEAN);
        $this->output = $command->getOutput();
        $this->handle($env, $complete);

        return 0;
    }

    public function handle(string $env = 'production', bool $complete = false): int
    {
        $path = Str::of('backups-planetscale/')
            ->append('backups-')
            ->append($env)
            ->when($complete, fn (Stringable $string) => $string->append('-complete'))
            ->append('/');

        $remoteDisk = Storage::disk('backup');
        $files = collect($remoteDisk->files($path));
        // Get Last Backup
        if ($files->isEmpty()) {
            $this->info('There are no backups to download.');

            return Command::FAILURE;
        }
        $lastBackup = $files->sort()->last();
        $file = Str::of($lastBackup)->afterLast('/')->__toString();
        $path = base_path('backups').'/ninja.tar.gz';
        $this->info("Downloading: $file");
        // Create stream context.
        $tmpUrl = $remoteDisk->temporaryUrl($lastBackup, now()->addMinutes(5));
        if (file_exists(base_path('backups'))) {
            rmdir(base_path('backups'));
        }
        mkdir(base_path('backups'), 0777, true);
        fopen($path, 'w');
        $context = stream_context_create([], ['notification' => [$this, 'progress']]);
        file_put_contents($path, fopen($tmpUrl, 'r', null, $context));
        $this->info('');
        $this->info("Downloaded: $path");

        return Command::SUCCESS;
    }

    private function info(string $value)
    {
        if (isset($this->command)) {
            $time = Carbon::now()->toTimeString();
            $this->command->info($time.' - '.$value);
        }
    }

    /**
     * @noinspection ALL
     */
    public function progress(int $notificationCode, int $severity, string $message, int $messageCode, int $bytesTransferred, int $bytesMax)
    {
        if ($notificationCode === STREAM_NOTIFY_REDIRECTED) {
            $this->progressBar->clear();
            $this->progressBar = null;

            return;
        }

        if ($notificationCode === STREAM_NOTIFY_FILE_SIZE_IS) {
            $this->progressBar?->clear();
            $this->progressBar = new ProgressBar($this->output, $bytesMax);
        }

        if ($notificationCode === STREAM_NOTIFY_PROGRESS) {
            $this->progressBar ??= new ProgressBar($this->output);
            $this->progressBar->setProgress($bytesTransferred);
        }

        if ($notificationCode === STREAM_NOTIFY_COMPLETED) {
            $this->finish();
        }
    }

    private function finish()
    {
        $this?->progressBar->finish();
    }
}
