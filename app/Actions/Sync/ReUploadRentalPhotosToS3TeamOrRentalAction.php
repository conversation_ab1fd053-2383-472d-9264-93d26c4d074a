<?php

namespace App\Actions\Sync;

use App\Models\RentalPicture;
use App\Query\RentalPictureQuery;
use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsAction;

class ReUploadRentalPhotosToS3TeamOrRentalAction
{
    use AsAction;

    public string $jobQueue = 'cron-sync';

    public string $commandSignature = 'ninja:re-upload-rental-photo-to-s3-team-rental {team} {rental=0}';

    public function asCommand(Command $command)
    {
        $teamId = $command->argument('team');
        $rentalId = $command->argument('rental');

        $this->handle($teamId, $rentalId);

        return $command::SUCCESS;
    }

    public function handle(int $teamId, int $rentalId): void
    {
        RentalPicture::query()
            ->whereTeamId($teamId)
            ->when($rentalId != 0, fn (RentalPictureQuery $q) => $q->whereRentalId($rentalId))
            ->each(fn (RentalPicture $p) => UploadRentalPhotosToS3Action::run($p));
    }
}
