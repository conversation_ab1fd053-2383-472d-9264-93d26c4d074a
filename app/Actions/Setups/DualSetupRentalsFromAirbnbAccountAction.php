<?php

namespace App\Actions\Setups;

use App\Models\SetupCompany;
use App\Models\SetupRental;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class DualSetupRentalsFromAirbnbAccountAction
{
    use AsAction;

    public function handle(Team $team, SetupCompany $setupCompany, string $airbnbHost, ?DualSetupRentalFilter $filters = null): int
    {
        $imported = 0;
        $setupCompany->setupRentals()
            ->where('airbnb_owner', '=', $airbnbHost)
            ->get()
            ->each(function (SetupRental $setupRental) use ($team, $filters, &$imported) {
                DualSetupRentalAction::dispatch($team, $setupRental, $filters)->delay(2 * $imported);
                $imported++;
            });

        return $imported;
    }
}
