<?php

namespace App\Actions\Setups;

use App\Actions\ChannelManager\Pushers\UpdateEverythingInChannelManagerAction;
use App\Actions\Guests\GuestsAppSettings\CreateTemplateGuideAction;
use App\Actions\Providers\RentalsUnited\GetLocationRentalsUnitedAction;
use App\Actions\Rentals\DailyDetails\Support\FillFutureDailyRentalDetailsAction;
use App\Actions\Rentals\SeasonalPrices\Import\FetchRatesFromRUImportDataAction;
use App\Actions\Rentals\StoreNewRentalAction;
use App\Actions\Support\Location\GetTimeZoneFromCoordinatesAction;
use App\DataProviders\ApiConnectors\NoProviderConnector;
use App\DataProviders\ProviderApi\ChannelManagerApi;
use App\DataProviders\ProviderConstants;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\DataProviders\Providers\NoProvider;
use App\Domains\Hostboost\Actions\GetScrapeFileAction;
use App\Domains\Wheelhouse\Actions\Backup\CreateWheelhouseConfigAction;
use App\Domains\Wheelhouse\Actions\SyncRentalWithWhAction;
use App\DTO\ChannelManager\AmenityWithCountData;
use App\DTO\ChannelManager\CompositionRoomAmenityData;
use App\DTO\ChannelManager\TranslationArray;
use App\DTO\Providers\ProviderSyncRequestData;
use App\Enum\DepositEnum;
use App\Enum\FeeKindEnum;
use App\Enum\FeeRateKindEnum;
use App\Enum\RentalTypeEnum;
use App\Models\Amenity;
use App\Models\Country;
use App\Models\Fee;
use App\Models\Rental;
use App\Models\RentalOwner;
use App\Models\RentalPicture;
use App\Models\SetupBlock;
use App\Models\SetupRental;
use App\Models\Team;
use DateTimeZone;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class DualSetupRentalAction
{
    use AsAction;

    public string $commandSignature = 'ninja-dev:dual-setup-rental {team} {setupRental} {--filter=dual} {--photos=true}';

    public function getJobTags(Team $team, SetupRental $sRental, ?DualSetupRentalFilter $filter = null): array
    {
        return ["team:$team->id"];
    }

    public function asCommand(Command $command): void
    {
        $team = Team::find($command->argument('team'));
        $setupRental = SetupRental::whereTeamId($team->id)->find($command->argument('setupRental'));
        $filter = $command->option('filter') == 'dual' ? DualSetupRentalFilter::defaultDual() : DualSetupRentalFilter::defaultScrapper();
        if ($command->option('photos') != 'true') {
            $filter->pictures = false;
        }

        $this->handle($team, $setupRental, $filter);
    }

    public function handle(Team $team, SetupRental $sRental, DualSetupRentalFilter $filter): bool
    {
        pnLog('[DualSetupRental Action] Starting for: '.$sRental->id, $team);
        $ruRental = $this->fetchDataFromRU($team, $sRental);
        $scRental = $this->fetchDataFromScrapper($sRental)['parsed_data'];
        pnLog('[DualSetupRental Action] Data fetched', $team);

        if ($filter->requireRU && $ruRental === false) {
            return false;
        }

        return DB::transaction(function () use ($team, $sRental, $ruRental, $scRental, $filter) {
            $externalId = data_get($ruRental, 'ID.data');
            $rental = $this->getRental($team, $sRental, $externalId);

            $ruRental = $ruRental === false ? [] : $ruRental;
            $this->resolver($team, $rental, $sRental, $ruRental, $scRental, $filter);
            CreateTemplateGuideAction::run($team, $rental);

            pnLog('[DualSetupRental Action] Rental resolved', $team);

            if ($filter->cmEnabled) {
                $rental->cm_active = true;
                $rental->save();

                UpdateEverythingInChannelManagerAction::dispatch($team, $rental->id)->delay(10);
            }
            pnLog('[DualSetupRental Action] Done for: '.$sRental->id, $team);

            return true;
        });
    }

    private function fetchDataFromRU(Team $team, SetupRental $sRental): array|false
    {
        $ruApi = ChannelManagerApi::instance();

        if (! is_null($sRental->external_id)) {
            $ruId = $sRental->external_id;
        } else {
            $syncRequest = new ProviderSyncRequestData($team, ProviderConstants::ENDPOINT_RENTALS);
            $response = $ruApi->doSyncRequest($syncRequest);
            $mapExternalToRU = collect(data_get($response->data, 'Properties.Property'))
                ->pluck('ID.data', 'PUID.data');

            $ruId = $mapExternalToRU->get($sRental->airbnb_id);

            if (is_null($ruId)) {
                pnLog('[SetupRentalAction] Rental not found in RU', $team);

                return false;
            }
        }

        $syncRequest = new ProviderSyncRequestData($team, 'rentals', $ruId);
        $response = $ruApi->doSyncRequest($syncRequest)->data;
        $ruRental = data_get($response, 'Property.0');
        if (is_null($ruRental)) {
            pnLog('[SetupRentalAction] Rental not found in RU', $team);

            return false;
        }

        return $ruRental;
    }

    private function fetchDataFromScrapper(SetupRental $sRental): array
    {
        $filePath = $sRental->getScrappingFile();
        $file = Storage::disk('rn-setups')->get($filePath);

        if (is_null($file)) {
            return ['parsed_data' => GetScrapeFileAction::run($sRental)->toArray()];
        }

        return json_decode($file, true);
    }

    private function getRental(Team $team, SetupRental $sRental, ?int $externalId): Rental
    {
        $rental = $sRental->rental;
        // When we define the correct flow, we will remove this check. Rental must be created from scrapped data.
        if (is_null($rental)) {
            pnLog('[SetupRentalAction] Creating a new rental');
            $rental = StoreNewRentalAction::run($team, [
                'id' => $externalId ?? rand(1, 4294967295),
                'cm_active' => false,
                'name' => $sRental->name,
                'currency' => $sRental->currency ?? $sRental->setupCompany->currency ?? $team->currency,
            ]);
            $sRental->rental_id = $rental->id;
            $sRental->save();
            FillFutureDailyRentalDetailsAction::run(rental: $rental);
        }

        return $rental;
    }

    private function resolver(Team $team, Rental $rental, SetupRental $sRental, ?array $ruRental, array $scRental, DualSetupRentalFilter $filter): void
    {
        $locale = $scRental['locale'];
        $amenityRules = collect();

        // Internal information
        $rental->external_id = data_get($ruRental, 'ID.data');
        $rental->provider_id = ChannelManagerProvider::ID;
        $rental->is_rental_ninja = true;

        // Currency
        if (! is_null($ruRental)) {
            $rental->ru_currency = data_get($ruRental, 'attr_Currency');
        }

        // Basic info
        if ($filter->basicInfo) {
            $rental->channel_name = Str::after(data_get($ruRental, 'Name'), ' · ') ?? data_get($scRental, 'public_name') ?? $rental->channel_name;
            $rental->rental_type = (! empty($ruRental) ? SetupRentalFromRUAction::mapRentalType($ruRental['ObjectTypeID']) : RentalTypeEnum::from($scRental['rental_type'])) ?? $rental->rental_type;
            $rental->sleeps_max = intval(data_get($ruRental, 'CanSleepMax') ?? data_get($scRental, 'max_sleeps') ?? $rental->sleeps_max ?? 4);
            $rental->sleeps = intval(data_get($ruRental, 'StandardGuests') ?? data_get($scRental, 'sleeps') ?? $rental->sleeps ?? 2);
            $rental->surface = $rental->surface ?? data_get($scRental, 'surface') ?? (20 + 15 * $rental->sleeps + 5 * ($rental->sleeps_max - $rental->sleeps));
            $rental->surface_unit = $rental->surface_unit ?? 'metric';

            $rental->license_details = $this->setLicenseDetails($rental->license_details, $scRental, $ruRental);
        }

        if ($filter->createOwner) {
            if (is_null($rental->rentalOwner) && data_get($scRental, 'host')) {
                $host = data_get($scRental, 'host');
                $owner = RentalOwner::firstOrCreate([
                    'team_id' => $team->id,
                    'name' => data_get($host, 'name'),
                    'email' => '<EMAIL>',
                    'is_external' => false,
                ]);
                $rental->rental_owner_id = $owner->id;
            }
        }

        if ($filter->content) {
            if (data_get($scRental, 'headline')) {
                $limited = Str::limit(data_get($scRental, 'headline'), 80, '');
                $rental->headline = new TranslationArray([$locale => $limited]);
            }
            if (data_get($scRental, 'description')) {
                $rental->description = new TranslationArray([$locale => $scRental['description']]);
            }
        }

        if ($filter->location) {
            if (empty($ruRental)) {
                $address = $scRental['location'];
                $rental->address1 = data_get($address, 'address') ?? $rental->address1;
                $rental->city = data_get($address, 'city') ?? $rental->city;
                $rental->zip = data_get($address, 'zip_code') ?? $rental->zip;
                if (data_get($address, 'country')) {
                    $rental->country_code = Country::firstWhere('name', $address['country'])->id;
                }
                $rental->lat = data_get($address, 'latitude');
                $rental->lng = data_get($address, 'longitude');
            } else {
                $rental->address1 = $ruRental['Street'];
                $rental->zip = $ruRental['ZipCode'] ?? '00000';
                $lat = data_get($ruRental, 'Coordinates.Latitude');
                $lng = data_get($ruRental, 'Coordinates.Longitude');
                $rental->lat = is_numeric($lat) ? $lat : 0;
                $rental->lng = is_numeric($lng) ? $lng : 0;
                [$city, $countryCode, $timezone] = GetLocationRentalsUnitedAction::run(Arr::get($ruRental, 'DetailedLocationID.data'), true);
                $rental->city = $city;
                $rental->country_code = $countryCode;
                $rental->timezone = empty($timezone) ? GetTimeZoneFromCoordinatesAction::run($rental->lat, $rental->lng, $rental->country_code) : new DateTimeZone($timezone);
            }
        }

        if ($filter->houseRules) {
            $checkInTime = data_get($ruRental, 'CheckInOut.CheckInFrom') ?? data_get($scRental, 'house_rules.check_in_after');
            $checkInBefore = data_get($ruRental, 'CheckInOut.CheckInTo') ?? data_get($scRental, 'house_rules.check_out_before');

            $checkOutTime = data_get($ruRental, 'CheckInOut.CheckOutUntil') ?? data_get($scRental, 'house_rules.check_out_before');
            if ($checkInBefore < $checkInTime) {
                $checkInBefore = null;
            }

            $rental->checkin_time = Str::before($checkInTime, ':') ?? $rental->checkin_time;
            $rental->max_checkin_time = Str::before($checkInBefore, ':') ?? $rental->max_checkin_time;
            $rental->checkout_time = Str::before($checkOutTime, ':') ?? $rental->checkout_time;
            $rental->min_advance_booking_hours = data_get($scRental, 'availability_window') ?? $rental->min_advance_booking_hours ?? 24;

            if (data_get($scRental, 'house_rules.additional_rules')) {
                $rental->house_rules = new TranslationArray([$locale => data_get($scRental, 'house_rules.additional_rules')]);
            }

            $rental->checkin_place = data_get($ruRental, 'CheckInOut.Place') ?? $this->defaultCheckInPlace($locale);

            if (data_get($scRental, 'house_rules.pets_allowed')) {
                $amenityRules = $amenityRules->push(new AmenityWithCountData(595));
            } else {
                $amenityRules = $amenityRules->push(new AmenityWithCountData(733));
            }
            if (data_get($scRental, 'house_rules.smoking_allowed')) {
                $amenityRules = $amenityRules->push(new AmenityWithCountData(802));
            } else {
                $amenityRules = $amenityRules->push(new AmenityWithCountData(1735));
            }
            // parties_or_events
            if (data_get($scRental, 'house_rules.parties_or_events')) {
                $amenityRules = $amenityRules->push(new AmenityWithCountData(1733));
            } else {
                $amenityRules = $amenityRules->push(new AmenityWithCountData(856));
            }
        }

        if ($filter->cancellationAndDeposits) {
            $this->resolveCancellationPolicy($ruRental, $rental, $sRental);
            // Damage deposit
            $rental->damage_deposit_type = $this->getDepositType(data_get($ruRental, 'SecurityDeposit.attr_DepositTypeID'));
            $rental->damage_deposit = data_get($ruRental, 'SecurityDeposit.data') * 100;
            // Down payment is called deposit in RU
            $rental->down_payment_type = $this->getDepositType(data_get($ruRental, 'Deposit.attr_DepositTypeID'));
            $rental->down_payment = data_get($ruRental, 'Deposit.data') * 100;
            $rental->min_advance_booking_hours = data_get($ruRental, 'PreparationTimeBeforeArrivalInHours', 24);
        }

        if ($filter->amenities) {
            $scrappedAmenities = collect($scRental['amenities'])
                ->pluck('id')
                ->merge(collect($scRental['rooms'])->pluck('beds')->flatten(1)->pluck('amenity_id'))
                ->unique();
            $existingDatabaseAmenities = Amenity::whereIn('id', $scrappedAmenities)
                ->pluck('id')
                ->toArray();

            $rental->amenities = collect($scRental['amenities'])
                ->filter(fn (array $a) => in_array($a['id'], $existingDatabaseAmenities))
                ->map(fn (array $am) => ['count' => 1, 'amenity_id' => $am['id']]);

            $rental->room_amenities = collect($scRental['rooms'])
                ->map(function (array $r, int $key) use ($existingDatabaseAmenities) {
                    $amenities = AmenityWithCountData::collection(
                        collect($r['beds'])
                            ->pluck('amenity_id')
                            ->filter(fn (int $amenityId) => in_array($amenityId, $existingDatabaseAmenities))
                            ->groupBy(fn ($amenityId) => $amenityId)
                            ->map(fn ($b) => new AmenityWithCountData($b->first(), $b->count()))
                            ->values()
                    );
                    $roomName = data_get($r, 'room_name');
                    $roomType = data_get($r, 'room_number') ?? data_get($r, 'room_id');

                    if (! in_array($roomType, Amenity::ROOMS)) {
                        return null;
                    }

                    return new CompositionRoomAmenityData($roomType, $roomName, $key + 1, $amenities);
                })
                ->filter()
                ->values();
            SetupRentalFromScrapperAction::makeSureAmenitiesAreCorrect($rental);
        }
        // Merge rule amenities with standard amenities
        if ($amenityRules->isNotEmpty()) {
            $rental->amenities = collect($rental->amenities)
                ->filter(fn (array $a) => ! in_array($a['amenity_id'], [595, 733, 802, 1735, 1733, 856]))
                ->merge($amenityRules);
        }

        // Images
        if ($filter->pictures) {
            $rental->photos->each(fn (RentalPicture $photo) => $photo->delete());
            SetupRentalFromScrapperAction::importPictures($team, $rental, $scRental['images']);
        }

        // Availability
        if ($filter->availability && data_get($scRental, 'availability')) {
            [$firstDate, $importedAvailability] = SetupRentalFromScrapperAction::parseAvailability($scRental['availability']);
            $sRental->tmp_availability = [
                'first_date' => $firstDate,
                'rental_availability' => $importedAvailability,
            ];
            $sRental->save();
        }
        $this->prefillLicenseDetails($rental);

        $rental->updated_at = now()->timestamp;
        $rental->save();

        // Rates
        if ($filter->rates == DualSetupRentalFilter::RU && ! empty($ruRental)) {
            $this->fetchRURates($team, $rental, $sRental->setupCompany->airbnb_markup);
        } elseif ($filter->rates == DualSetupRentalFilter::SCRAPPER) {
            $this->fetchScrappedRates($scRental['listing_rates'], $rental);
        }

        // Fees
        if ($filter->fees == DualSetupRentalFilter::RU && ! empty($ruRental)) {
            $this->fetchRUFees($team, $rental, $ruRental);
        } elseif ($filter->fees == DualSetupRentalFilter::SCRAPPER) {
            $rental->rentalFees()->delete();
            // TODO: Cleaning fee came separately in the scrapper
            $scFees = data_get($scRental, 'listing_rates.fees');
            foreach ($scFees as $fee) {
                $this->fetchScrappedFees($fee, $team, $rental);
            }
        }

        $rental->updated_at = now()->timestamp;
        $rental->save();

        // Create blocks (if they have not been created previously)
        if ($rental->bookings->isEmpty() && $sRental->setupBlocks->isNotEmpty()) {
            $connector = new NoProviderConnector($team);
            $rentalId = $rental->id;
            $sRental->setupBlocks->each(function (SetupBlock $block) use ($connector, $rentalId, $team) {
                $start = $block->start_date->copy()->setHours($team->arrival_time);
                $end = $block->end_date->copy()->setHours($team->departure_time);

                $connector->createBlockInRental($rentalId, $start, $end, 'Imported from setup');
            });
        }

        // Delete iCals
        if ($rental->iCalInputs->isNotEmpty()) {
            pnLog('[DualSetupRental Action] (^-_-^ NO ^-_-^) Deleting iCals', $team);
//            if ($sRental->airbnb_id) {
//                $rental->iCalInputs()->where('url', 'like', "%airbnb.%")->each(fn(ICalInput $iCal) => $iCal->delete());
//            }
//            if ($sRental->booking_hotel_id) {
//                $rental->iCalInputs()->where('url', 'like', "%booking.com%")->each(fn(ICalInput $iCal) => $iCal->delete());
//            }
        }

        // Create Wheelhouse config and push it into wheelhouse
        CreateWheelhouseConfigAction::run($rental);
        SyncRentalWithWhAction::dispatch($team, $rental->id)->delay(10);
    }

    private function setLicenseDetails(?array $existing, array $scRental, ?array $ruRental): array
    {
        $imported = empty($scRental['license']) ? data_get($ruRental, 'LicenceInfo.LicenceNumber') : $scRental['license'];

        $licenseDetails = [
            ['key' => 'IsExempt', 'value' => is_null($imported) ? 'true' : 'false'],
        ];
        if (! is_null($imported)) {
            $licenseDetails[] = ['key' => 'LicenceNumber', 'value' => $imported];
        }
        $existing = $existing ?? [];
        // Remove any existing key 'LicenceNumber'
        $existing = collect($existing)
            ->filter(fn ($l) => $l['key'] != 'LicenceNumber')
            ->filter(fn ($l) => $l['key'] != 'IsExempt')
            ->toArray();

        return array_merge($existing, $licenseDetails);
    }

    private function defaultCheckInPlace(string $locale): string
    {
        return match ($locale) {
            'en' => 'At the property',
            'fr' => 'À la propriété',
            'de' => 'Am Objekt',
            'es' => 'En la propiedad',
            'it' => 'Presso la struttura',
            'nl' => 'Bij de accommodatie',
            'pt' => 'Na propriedade',
            'pl' => 'W obiekcie',
            'ru' => 'В объекте',
            'zh' => '在物业内',
            'ca' => 'A la propietat',
            default => 'At the property',
        };
    }

    protected function resolveCancellationPolicy(?array $ruRental, Rental $rental, SetupRental $sRental): void
    {
        $policies = data_get($ruRental, 'CancellationPolicies.CancellationPolicy');
        $rental->cancellation_policy = collect($policies)
            ->map(fn (array $policy) => [
                'to' => intval(data_get($policy, 'attr_ValidTo')),
                'percentage' => ninjaIntval(data_get($policy, 'data') * 100),
            ])
            ->sortBy('to')
            ->toArray();

        $cp = collect($rental->cancellation_policy);

        try {
            if (data_get($cp->firstWhere('to', '=', 1), 'percentage') === 0) {
                $sRental->airbnb_cancellation_policy = 'Flexible';
            } elseif (data_get($cp->firstWhere('to', '=', 4), 'percentage') == 10000) {
                $sRental->airbnb_cancellation_policy = 'Moderate';
            } elseif (data_get($cp->firstWhere('to', '>=', 13), 'percentage') == 5000) {
                $sRental->airbnb_cancellation_policy = 'Strict';
            } elseif (data_get($cp->firstWhere('to', '>=', 29), 'percentage') == 5000) {
                $sRental->airbnb_cancellation_policy = 'Firm';
            } else {
                $sRental->airbnb_cancellation_policy = 'Unknown';
            }
            $sRental->save();
        } catch (Exception $e) {
            report($e);
        }
    }

    protected function getDepositType(?string $type): DepositEnum
    {
        return match ($type) {
            '1' => DepositEnum::no_deposit,
            '2' => DepositEnum::percentage_of_rental_price,
            '3' => DepositEnum::percentage_of_final_price,
            '4' => DepositEnum::fixed_per_day,
            '5' => DepositEnum::fixed,
            default => DepositEnum::no_deposit,
        };
    }

    protected function fetchRURates(Team $team, Rental $rental, float $markup): void
    {
        $rental->seasonalPrices()->delete();
        // 2: Process rates
        FetchRatesFromRUImportDataAction::run($team, $rental, $markup);
    }

    protected function fetchScrappedRates($listing_rates, Rental $rental): void
    {
        $rates = $listing_rates;
        $rental->base_rate = data_get($rates, 'base_rate') * 100 ?? $rental->base_rate;
        $rental->min_price = data_get($rates, 'min_rate') * 100 ?? $rental->min_price;
        $rental->min_stay = data_get($rates, 'absolute_min_stay') ?? $rental->min_stay;
        $seasons = data_get($rates, 'seasons');
        if ($seasons) {
            SetupRentalFromScrapperAction::parseSeasons($rental, $seasons);
        }
    }

    protected function fetchRUFees(Team $team, Rental $rental, array $resp): void
    {
        // 1: Process fees
        $cleaningFee = collect(data_get($resp, 'AdditionalFees.AdditionalFee'))
            ->firstWhere('attr_FeeTaxType', '=', 41);

        if (! is_null($cleaningFee)) {
            $rate = round(data_get($cleaningFee, 'Value'));
            $intVal = ninjaIntval($rate);
            $fee = Fee::firstOrCreate([
                'team_id' => $team->id,
                'kind' => FeeKindEnum::cleaning,
                'rate_kind' => FeeRateKindEnum::fixed,
                'rate' => $rate,
            ], [
                'name' => "Cleaning fee $intVal",
                'provider_id' => NoProvider::ID,
            ]);

            $rental->rentalFees()->updateOrCreate(
                ['fee_id' => $fee->id], [
                    'required' => true,
                    'public' => true,
                    'rate' => data_get($cleaningFee, 'value'),
                    'rate_kind' => FeeRateKindEnum::fixed,
                    'collect_at_booking_time' => true,
                ]);
        }
    }

    protected function fetchScrappedFees(mixed $fee, Team $team, Rental $rental): void
    {
        $rate = round(data_get($fee, 'fee_rate'), 2);
        $intVal = ninjaIntval($rate);

        // Cleaning fee
        if (data_get($fee, 'type') == 'cleaning-fee') {
            $fee = Fee::firstOrCreate([
                'team_id' => $team->id,
                'kind' => FeeKindEnum::cleaning,
                'rate_kind' => FeeRateKindEnum::fixed,
                'rate' => $rate,
            ], [
                'name' => "Cleaning fee $intVal",
                'provider_id' => NoProvider::ID,
            ]);

            $rental->rentalFees()->updateOrCreate(
                ['fee_id' => $fee->id], [
                    'required' => true,
                    'public' => true,
                    'rate' => $rate,
                    'rate_kind' => FeeRateKindEnum::fixed,
                    'collect_at_booking_time' => true,
                ]);
        }
    }

    private function prefillLicenseDetails(Rental $rental): void
    {
        if ($rental->country_code == 'FR') {
            $licenseDetails = $rental->license_details;
            //[{"key": "IsExempt", "value": "false"}, {"key": "LicenceNumber", "value": "74010006897Y1"},
            // {"key": "LicenceType", "value": "TouristicAccommodation"},
            $licenseDetails[] = ['key' => 'LicenceType', 'value' => 'TouristicAccommodation'];
            // {"key": "IsVATRegistered", "value": "false"},
            $licenseDetails[] = ['key' => 'IsVATRegistered', 'value' => 'false'];
            // {"key": "BusinessTaxLicenceInfo/BusinessTaxIDNotSpecified", "value": "false"},
            $licenseDetails[] = ['key' => 'BusinessTaxLicenceInfo/BusinessTaxIDNotSpecified', 'value' => 'false'];
            // {"key": "IsManagedByPrivatePerson", "value": "true"},
            $licenseDetails[] = ['key' => 'IsManagedByPrivatePerson', 'value' => 'true'];
            // {"key": "FrenchLicenceInfo/IsRegisteredAtTradeCommercialRegister", "value": "true"},
            $licenseDetails[] = ['key' => 'FrenchLicenceInfo/IsRegisteredAtTradeCommercialRegister', 'value' => 'true'];
            // {"key": "FrenchLicenceInfo/PropertyTypeForTaxPurposes", "value": "FurnishedTourism"},
            $licenseDetails[] = ['key' => 'FrenchLicenceInfo/PropertyTypeForTaxPurposes', 'value' => 'FurnishedTourism'];
            // {"key": "FrenchLicenceInfo/DeclaresRevenuesAsProfessionalForDirectTaxPurposes", "value": "false"},
            $licenseDetails[] = ['key' => 'FrenchLicenceInfo/DeclaresRevenuesAsProfessionalForDirectTaxPurposes', 'value' => 'false'];
            // {"key": "FrenchLicenceInfo/TypeOfResidence", "value": "2"},
            $licenseDetails[] = ['key' => 'FrenchLicenceInfo/TypeOfResidence', 'value' => '2'];
            // {"key": "FrenchLicenceInfo/CityTaxCategory", "value": "19"},
            $licenseDetails[] = ['key' => 'FrenchLicenceInfo/CityTaxCategory', 'value' => '19'];
            // {"key": "TOTRegistrationLicenceInfo/TOTRegistrationIDNotSpecified", "value": "true"}]
            $licenseDetails[] = ['key' => 'TOTRegistrationLicenceInfo/TOTRegistrationIDNotSpecified', 'value' => 'true'];

            $rental->license_details = $licenseDetails;
        }
    }
}
