<?php

namespace App\Actions\Setups;

use App\Models\SetupAirbnbHost;
use App\Models\SetupRental;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Lorisleiva\Actions\Concerns\AsAction;

class FetchRentalFromScrapperAction
{
    use AsAction;

    public string $commandSignature = 'ninja-dev:setup-scraped-rental {setup}';

    public function asCommand(Command $command): int
    {
        /** @var SetupRental $sRental */
        $sRental = SetupRental::findOrFail($command->argument('setup'));

        $this->handle($sRental);

        return $command::SUCCESS;
    }

    public function handle(SetupRental $sRental): bool
    {
        $setupCompany = $sRental->setupCompany;

        if (! $sRental->locked) {
            return false;
        }

        $filePath = $sRental->getScrappingFile();
        $data = json_decode(Storage::disk('rn-setups')->get($filePath), true);

        if (is_null($data)) {
            return false;
        }

        $lastMod = Carbon::createFromTimestamp(Storage::disk('rn-setups')->lastModified($filePath));

        $sRental->fetched = true;
        $sRental->fetched_at ??= $lastMod;
        $sRental->last_fetched_at = $lastMod;
        $sRental->airbnb_name = data_get($data, 'parsed_data.public_name');

        $hosts = array_merge(
            [data_get($data, 'parsed_data.host')],
            data_get($data, 'parsed_data.co_hosts', [])
        );

        $hostIds = [];
        // Ensure hosts are in the setup
        foreach ($hosts as $host) {
            /** @var SetupAirbnbHost $host */
            $host = $setupCompany->setupAirbnbHosts()->updateOrCreate([
                'name' => $host['name'],
                'airbnb_user_id' => $host['airbnb_user_id'],
            ]);
            $hostIds[] = $host->id;
        }
        $sRental->airbnb_hosts = $hostIds;
        if (count($hostIds) == 1) {
            $sRental->airbnb_owner = $hostIds[0];
        }

        $sRental->save();

        return true;
    }
}
