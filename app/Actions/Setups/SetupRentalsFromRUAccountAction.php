<?php

namespace App\Actions\Setups;

use App\Models\SetupCompany;
use App\Models\SetupRental;
use App\Models\Team;
use App\Nova\Actions\Setups\SetUpRentalFilter;
use Lorisleiva\Actions\Concerns\AsAction;

class SetupRentalsFromRUAccountAction
{
    use AsAction;

    public function handle(Team $team, SetupCompany $setupCompany, string $airbnbHost, ?SetUpRentalFilter $filters = null): int
    {
        $imported = 0;
        $setupCompany->setupRentals()->where('airbnb_owner', '=', $airbnbHost)->get()
            ->each(function (SetupRental $setupRental) use ($team, $filters, &$imported) {
                SetupRentalFromRUAction::dispatch($team, $setupRental, $filters);
                $imported++;
            });

        return $imported;
    }
}
