<?php

namespace App\Actions\Setups;

use App\Actions\Selenium\BaseSeleniumAction;
use App\Actions\Selenium\GetSeleniumDriverAction;
use App\Models\SetupAirbnbHost;
use App\Models\SetupCompany;
use App\Models\Team;
use Exception;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;

class SetupImportAirbnbBookingsAction extends BaseSeleniumAction
{
    public int $jobTries = 1;

    public int $jobMaxExceptions = 1;

    /**
     * @throws Exception
     */
    public function asJob(Team $team, SetupCompany $setup, string $airbnbUserId, string $chainId)
    {
        $this->initializeJobStatus($chainId, "Import Airbnb bookings for $airbnbUserId", $airbnbUserId);
        $this->handle($team, $setup, $airbnbUserId);
    }

    /**
     * @throws Exception
     */
    public function handle(Team $team, SetupCompany $setup, string $airbnbUserId): void
    {
        $this->team = $team;
        $host = SetupAirbnbHost::query()
            ->where('airbnb_user_id', $airbnbUserId)
            ->first();

        if (! $host) {
            $this->error('Error: Airbnb Host not found internally.');
        }

        $this->info('Importing Airbnb bookings for '.$host->name);

        $airbnbHostId = $host->airbnb_user_id;

        /** @var RemoteWebDriver $driver */
        $driver = GetSeleniumDriverAction::run();
        try {
            $url = $this->getUrlForSetup($setup);
            $this->navigateToUrl($driver, $url);

            $this->info('Website loaded');

            // Find Angular App
            $app = $this->findElement($driver, WebDriverBy::tagName('app-white-pms-page'), 'Channel Manager UI');

            // Sales Channels
            $channels = $this->findElement($app, WebDriverBy::tagName('app-channels-tile-container'), 'Sales Channels');

            // Airbnb Channel
            $airbnbChannel = $this->findElement($channels, WebDriverBy::cssSelector('.channel-card img[alt="Airbnb"]'), 'Airbnb Channel');
            $this->clickElement($airbnbChannel, 'Airbnb Channel');

            $this->doSleep();

            // Filter Account and wait
            $filterInput = $this->findElement($driver, WebDriverBy::id('filterInput'), 'Filter Input');
            $filterInput->sendKeys($airbnbHostId);

            $this->doSleep();

            // Find Airbnb Host Tile
            $airbnbHostTile = $this->findElement($driver, WebDriverBy::cssSelector('app-cluster-tile[id="'.$airbnbHostId.'"]'), 'Airbnb Account');

            // Click on Three Dots Menu
            $threeDotsMenu = $this->findElement($airbnbHostTile, WebDriverBy::cssSelector('.three-dots-menu'), 'Three Dots Menu');
            $this->hoverOverElement($driver, $threeDotsMenu, 'Three Dots Menu');

            // Identify the popover for later.
            $ariaDescribedBy = $threeDotsMenu->getAttribute('aria-describedby');

            // Find popover
            $popOver = $this->findElement($driver, WebDriverBy::id($ariaDescribedBy), 'Popover Container');

            // Click on Import Airbnb Bookings
            $importBookings = $this->findElement($popOver, WebDriverBy::cssSelector('.ru-popover-menu-item'), 'Import Airbnb Bookings');
            $this->clickElement($importBookings, 'Import Airbnb Bookings');
            $modal = $this->findElement($driver, WebDriverBy::tagName('modal-container'), 'Modal');
            $importButton = $this->findElement($modal, WebDriverBy::cssSelector('div.modal-footer.sh-message-box-footer > button.ru-btn.font-bold.ru-btn-primary.ladda-button'), 'Import Button');
            $this->clickElement($importButton, 'Import Button');

            // Wait for modal to disappear
            $this->waitForModalToDisappear($driver);

            $this->complete();
        } catch (Exception $e) {
            $this->error('Error importing bookings: '.$e->getMessage());
        } finally {
            $driver->quit();
        }
    }
}
