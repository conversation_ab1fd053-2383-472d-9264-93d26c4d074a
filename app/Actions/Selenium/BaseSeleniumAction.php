<?php

namespace App\Actions\Selenium;

use App\Actions\Setups\SetupGetWhiteLabelAction;
use App\Models\Team;
use App\Traits\TrackSeleniumJobStatus;
use Exception;
use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Interactions\WebDriverActions;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

abstract class BaseSeleniumAction
{
    use AsAction;
    use TrackSeleniumJobStatus;

    protected static int $sleepTime = 4;
    protected ?Team $team = null;

    protected function doSleep($multiplier = 1): void
    {
        sleep(self::$sleepTime * $multiplier);
    }

    /**
     * @throws Exception
     */
    protected function navigateToUrl(RemoteWebDriver $driver, string $url): void
    {
        try {
            $this->info('Navigating to '.$url);
            $driver->get($url);
        } catch (Exception) {
            $this->error('Error: Could not navigate to '.$url.'.');
        }
    }

    protected function info(string $message): void
    {
        $this->updateJobStatus('in_progress', $message);
        pnLog($message, $this->team);
    }

    /**
     * @throws Exception
     */
    protected function error(string $message): void
    {
        $this->updateJobStatus('failed', $message);
        pnLog($message, $this->team);
        throw new Exception($message);
    }

    /**
     * @throws Exception
     */
    protected function clickElement($element, string $elementName): void
    {
        try {
            $this->info('Clicking '.$elementName);
            $element->click();
            sleep(self::$sleepTime);
        } catch (Exception) {
            $this->error('Error: Could not click on '.$elementName.'.');
        }
    }

    /**
     * @noinspection PhpRedundantCatchClauseInspection
     *
     * @throws Exception
     */
    protected function hoverOverElement(RemoteWebDriver $driver, $element, string $elementName): void
    {
        try {
            $this->info('Hovering over '.$elementName);
            $action = new WebDriverActions($driver);
            $action->moveToElement($element)
                ->perform();
        } catch (NoSuchElementException) {
            $this->error('Error: Could not hover over '.$elementName.'.');
        }
    }

    /**
     * @noinspection PhpRedundantCatchClauseInspection
     *
     * @throws Exception
     */
    protected function waitForModalToDisappear(RemoteWebDriver $driver): void
    {
        $this->info('Waiting for modal to disappear...');
        $timeout = 760;

        while (true) {
            if ($timeout <= 0) {
                $this->error('Error: Timeout reached while waiting for modal to disappear.');
            }

            try {
                $driver->findElement(WebDriverBy::tagName('modal-container'));
            } catch (NoSuchElementException) {
                $this->info('Modal disappeared, continuing...');
                break;
            }

            sleep(1);
            $this->info("Waiting for modal to disappear... Timeout: $timeout");
            $timeout--;
        }
    }

    /**
     * @throws Exception
     *
     * @noinspection PhpRedundantCatchClauseInspection
     */
    protected function findElement(RemoteWebDriver|RemoteWebElement $driver, WebDriverBy $by, string $elementName, int $timeoutSeconds = 10)
    {
        $start = time();
        $end = $start + $timeoutSeconds;

        while (time() < $end) {
            try {
                $this->info("Looking for $elementName (Attempt ".(time() - $start + 1).')');

                return $driver->findElement($by);
            } catch (NoSuchElementException) {
                if (time() >= $end) {
                    $this->error("Error: $elementName not found after $timeoutSeconds seconds.");
                }
                sleep(1);
            }
        }

        $this->error("Error: $elementName not found after $timeoutSeconds seconds.");
    }

    /**
     * @throws Exception
     *
     * @noinspection PhpRedundantCatchClauseInspection
     */
    protected function findElements(RemoteWebDriver|RemoteWebElement $driver, WebDriverBy $by, string $elementName, int $timeoutSeconds = 10)
    {
        $start = time();
        $end = $start + $timeoutSeconds;

        while (time() < $end) {
            try {
                $this->info("Looking for $elementName (Attempt ".(time() - $start + 1).')');
                $elements = $driver->findElements($by);
                if (count($elements) > 0) {
                    return $elements;
                }
            } catch (NoSuchElementException $e) {
                // Ignore and continue
            }

            if (time() >= $end) {
                $this->error("Error: $elementName not found after $timeoutSeconds seconds.");
            }
            sleep(1);
        }

        $this->error("Error: $elementName not found after $timeoutSeconds seconds.");
    }

    /**
     * @throws Exception
     */
    protected function getUrlForSetup($setup): string
    {
        try {
            $domain = config('ninja.target_domain');
            $domain = ! str_starts_with($domain, 'https://') ? 'https://'.$domain : $domain;

            return $domain.SetupGetWhiteLabelAction::getUrl($setup);
        } catch (Exception) {
            $this->error('Error: Could not get URL for setup.');
        }
    }

    protected function complete(): void
    {
        $this->updateJobStatus('completed', 'Job completed successfully.');
    }
}
