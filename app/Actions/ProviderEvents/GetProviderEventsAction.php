<?php

namespace App\Actions\ProviderEvents;

use App\DTO\ProviderEvent\ProviderEventFilter;
use App\Http\Resources\ProviderEventResource;
use App\Models\ProviderEvent;
use App\Models\Team;
use App\Models\User;
use App\Query\ProviderEventQuery;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetProviderEventsAction
{
    use AsAction;

    public function asController(Request $request, Team $team): AnonymousResourceCollection
    {
        /** @var User $user */
        $user = Auth::user();

        $filter = ProviderEventFilter::fromRequest($request, $team, $user);

        $events = $this->handle($filter, true)->paginate(50);

        return ProviderEventResource::collection($events);
    }

    public function handle(ProviderEventFilter $filter, bool $forPagination): Collection|Builder
    {
        $user = $filter->user->load('team');

        // $defaultSize = DB::select('select @@sort_buffer_size as size')[0]->size;
        DB::statement('set sort_buffer_size=8388608;');

        $query = ProviderEvent::query()
            ->onTypes($filter->types())
            ->onRentals($filter->filteredRentalsForUser())
            ->onTeam($filter->team)
            ->when(! empty($filter->bookings), fn (ProviderEventQuery $query) => $query->onBookings($filter->bookings))
            // Only if the user is not Rental Manager or above, filter out the tasks that are not for him:
            ->when(! $user->isRentalManagerOrAbove() && $filter->types()->containsAny(ProviderEvent::TASK_EVENTS),
                fn (ProviderEventQuery $query) => $query->filterByTasksAccess($user)
            )->orderByDesc('created_at');

        if ($forPagination) {
            return $query;
        }

        return $query->get();
    }
}
