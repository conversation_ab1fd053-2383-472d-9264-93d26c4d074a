<?php

namespace App\Actions\Model179;

use App\Models\Booking;
use App\Models\Payee;
use App\Models\PreCheckInForm;
use App\Models\PreCheckInFormPassport;
use App\Models\RentalLegalDetails;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\Regex\Regex;

class Generate179BookingXMLAction
{
    use AsAction;

    /**
     * Entorno producción: https://www1.agenciatributaria.gob.es/static_files/common/internet/dep/explotacion/ws/ClienteWSAEATModelos.html
     * Entorno pruebas: https://www6.agenciatributaria.gob.es/static_files/common/internet/dep/explotacion/ws/ClienteWSAEATModelos.html.
     *
     *
     * https://sede.agenciatributaria.gob.es/static_files/Sede/Procedimiento_ayuda/GI44/Ayuda/DDII_Descripcion_ServicioWeb.pdf
     * https://sede.agenciatributaria.gob.es/static_files/Sede/Procedimiento_ayuda/GI44/Ayuda/Validaciones_errores_DDII.pdf
     * https://sede.agenciatributaria.gob.es/static_files/Sede/Procedimiento_ayuda/GI44/Preguntas/FAQ_179.pdf
     * Documento:
     * 01 - NIF - ES
     * 02 - NIF-IVA - ES??
     * 03 - PASSPORT
     * 04 - Documento oficial de identificación expedido por el país o territorio de residencia - NOT ES?
     * 05 - Certificado de residencia - ES?
     * 06 - Otro documento probatorio - NOT ES
     * 07 - No censado
     *
     * @throws Exception
     */
    public function handle(Booking $booking, RentalLegalDetails $rental): array
    {
        return [
            'dec1:IDRegistroDeclarado' => $booking->reference,
            'dec1:IDDeclarado' => $this->getDeclarado($rental),
            'dec1:Detalle' => [
                'dec1:IDCesionario' => $this->getCesionario($booking),
                'dec1:IDInmueble' => [
                    'dec1:SituacionInmueble' => $rental->situacion,
                    'dec1:DatosInmueble' => [
                        'dec1:ReferenciaCatastral' => strtoupper($rental->referencia_catastral),
                        'dec1:Direccion' => [
                            'dec1:CodigoMunicipio' => substr($rental->codigo_municipio, -2),
                            'dec1:CodigoProvincia' => $rental->codigo_provincia,
                            'dec1:TipoVia' => $rental->tipo_via,
                            'dec1:NombreVia' => ucwords(strtolower($rental->nombre_via)),
                            'dec1:TipoNumeracion' => $rental->tipo_numeracion,
                        ],
                    ],
                ],
                'dec1:DesgloseOperacion' => [
                    'dec1:PeriodoCesion' => [
                        'dec1:FechaInicioCesion' => $this->inicioCesion($booking),
                        'dec1:NumeroDiasDisfrute' => $booking->nights,
                    ],
                    'dec1:Importe' => number_format((float) $booking->final_price, 2, '.', ''),
                ],
            ],
        ];
    }

    private function getDeclarado(RentalLegalDetails $rental): array
    {
        /** @var Payee $propietario */
        $propietario = Payee::query()->find($rental->payee_id);
        if ($propietario->id_type == '01') {
            $declarado = [
                'dec1:Clave' => $rental->titular_cedente,
                'dec1:NombreRazon' => ucwords(strtolower($propietario->legal_name)),
                'dec1:NIF' => $this->cleanNIF($propietario->id_num),
            ];
        } elseif ($propietario->id_type == '02') {
            $declarado = [
                'dec1:Clave' => $rental->titular_cedente,
                'dec1:NombreRazon' => ucwords(strtolower($propietario->legal_name)),
                'dec1:NIF-IVA' => $this->cleanNIF($propietario->id_num),
            ];
        } else {
            $declarado = [
                'dec1:Clave' => $rental->titular_cedente,
                'dec1:NombreRazon' => ucwords(strtolower($propietario->legal_name)),
                'dec1:IDOtro' => [
                    'dec1:CodigoPais' => 'FR', // TODO: Add country in rental_legal_details or payees?...
                    'dec1:IDType' => $propietario->id_type,
                    'dec1:ID' => $propietario->id_num,
                ],
            ];
        }

        return $declarado;
    }

    private function cleanNIF(string $id): ?string
    {
        return Regex::replace(
            pattern: '/[^\p{L}\p{N} ]+/',
            replacement: '',
            subject: $id
        )->result();
    }

    private function getCesionario(Booking $booking): array
    {
        $cesionario = [];

        $form = PreCheckInForm::query()
            ->fromBooking($booking->id)
            ->onTeam($booking->team_id)
            ->first();

        if ($form != null) {
            /** @var PreCheckInFormPassport[] $passports */
            $passports = PreCheckInFormPassport::query()->wherePreCheckInId($form->id)
                ->orderBy('id', 'desc')
                ->get();

            foreach ($passports as $index => $passport) {
                $cesionario = $this->singleCesionario($passport, $cesionario, $index + 1, $booking);
            }
        }

        if (empty($cesionario)) {
            $clientName = $booking->clientNameForModel179();
            if (empty($clientName)) {
                nLog("Generating XML 179: Client is null on booking $booking->id", $booking->team_id);
            }
            abort_unless(! empty($clientName), 422, 'Client Not Found');

            $cesionario[] = [
                'dec1:NombreRazon' => $clientName,
                'dec1:IDOtro' => [
                    'dec1:IDType' => '07',
                    'dec1:CodigoPais' => 'ES',
                    'dec1:ID' => $booking->client_id,
                ],
            ];
        }

        return $cesionario;
    }

    private function singleCesionario(
        PreCheckInFormPassport $passport,
        array &$cesionario,
        int|string $index,
        Booking $booking
    ): array {
        $legalName = $passport->legal_name ?? trim("$passport->first_name $passport->last_name");
        $countryCode = Str::upper($passport->country_code);

        // NIFS Españoles van por delante de todos los otros, tanto en nif como en nie.
        if (($passport->id_type == '01' || $passport->id_type == '05') && ! empty($passport->passport_id) && $countryCode == 'ES') {
            $cesionario[] = [
                'dec1:NombreRazon' => $legalName,
                'dec1:NIF' => $this->cleanNIF($passport->passport_id),
            ];
        }

        // FIXME: This should be invalid on validation process... we should not modify here.
        // Después... El valor del CodigoPais solo puede ser 'ES' cuando el IDType sea '03' (Pasaporte).
        // Qué pasa con certificados de residencia?
        //        else if($passport->country_code == 'ES' && $passport->id_type != '03'){
        //
        //        }
        // FIXME: This should be invalid on validation process... we should not modify here.
        elseif ($passport->id_type == '01' && ! empty($passport->passport_id)) {
            $cesionario[] = [
                'dec1:NombreRazon' => $legalName,
                'dec1:IDOtro' => [
                    'dec1:CodigoPais' => $countryCode,
                    'dec1:IDType' => '06', // $passport->id_type
                    'dec1:ID' => $passport->passport_id,
                ],
            ];
        } elseif ($passport->id_type == '07') {
            // IF TYPE IS 07 AND COUNTRY CODE IS NOT ES... CHANGE.
            $cesionario[] = [
                'dec1:NombreRazon' => $legalName,
                'dec1:IDOtro' => [
                    'dec1:CodigoPais' => $countryCode == 'ES' ? 'FR' : $countryCode, // FIXME... hack, spain does not allow "no-censados".
                    'dec1:IDType' => '06', // $passport->id_type
                    'dec1:ID' => empty($passport->passport_id) ? $index.$booking->client_id : $passport->passport_id,
                ],
            ];
        } elseif (! empty($passport->passport_id)) {
            $cesionario[] = [
                'dec1:NombreRazon' => $legalName,
                'dec1:IDOtro' => [
                    'dec1:CodigoPais' => $countryCode,
                    'dec1:IDType' => $passport->id_type,
                    'dec1:ID' => $passport->passport_id,
                ],
            ];
        }

        return $cesionario;
    }

    private function inicioCesion(Booking $booking): string
    {
        return Carbon::createFromTimestampUTC($booking->start_at)
            ->format('d-m-Y');
    }
}
