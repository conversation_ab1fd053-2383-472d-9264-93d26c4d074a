<?php

namespace App\Actions\Model179;

use App\Http\Resources\RentalLegalDetailsResource;
use App\Models\RentalLegalDetails;
use App\Models\Team;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTeamLegalDetailsModel179Action
{
    use AsAction;

    public function asController(Team $team): AnonymousResourceCollection
    {
        EnsureDataForModel179Action::run($team);
        $details = $this->handle($team);

        return RentalLegalDetailsResource::collection($details);
    }

    public function handle(Team $team): Collection
    {
        return RentalLegalDetails::whereTeamId($team->id)
            ->orderBy('rental_id')
            ->get();
    }
}
