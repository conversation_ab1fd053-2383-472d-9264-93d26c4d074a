<?php

namespace App\Actions\Model179;

use App\Http\Resources\RentalLegalDetailsResource;
use App\Models\RentalLegalDetails;
use App\Models\Team;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\Concerns\AsController;

class GetOneLegalDetailsModel179Action
{
    use AsController;

    public function asController(Team $team, int $detailsId): AnonymousResourceCollection
    {
        $details = RentalLegalDetails::query()->whereTeamId($team->id)->whereId($detailsId)->firstOrFail();
        EnsureDataForModel179Action::run($team);

        return RentalLegalDetailsResource::collection($details);
    }
}
