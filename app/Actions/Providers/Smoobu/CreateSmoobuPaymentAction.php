<?php

namespace App\Actions\Providers\Smoobu;

use App\DataProviders\ProviderApi\SmoobuApi;
use App\DataProviders\ProviderConstants;
use App\DataProviders\Providers\Smoobu;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Team;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

/**
 * This action adds a payment to a Smoobu Booking and
 * then it makes sures that the data in the Smoobu API is correct.
 *
 *
 * Class CreateSmoobuPaymentAction
 */
class CreateSmoobuPaymentAction
{
    use AsAction;

    private Team $team;

    private Booking $booking;

    /**
     * @throws Throwable
     */
    public function handle(Team $team, Booking $booking, BookingPayment $payment): void
    {
        // Payment is not used, since smoobu only updates the payment status, so all booking payments are needed.
        try {
            // Here, we need to check the status of the booking...
            SmoobuApi::instance()->put(
                account: $team->getProviderAccount(Smoobu::ID),
                endpoint: ProviderConstants::ENDPOINT_BOOKINGS,
                body: [
                    'prepaymentStatus' => intval($this->isAmountEnoughForPrepayment($booking)),
                    'priceStatus' => intval($this->isAmountEnoughForPrice($booking)),
                    'depositStatus' => intval($this->isAmountEnoughForDepositPayment($booking)),
                ],
                single_id: $booking->id
            );
        } // We report but do not throw as we don't want this to make errors on our end.
        catch (Exception $e) {
            report($e);
        }
    }

    private function isAmountEnoughForPrepayment(Booking $booking): bool
    {
        return $this->getPaidAmount($booking) >= $booking->getDownPaymentInCents();
    }

    private function getPaidAmount(Booking $booking): int
    {
        return once(fn () => $booking->bookingPayments()
            ->whereNull('canceled_at')
            ->sum('amount_in_cents'));
    }

    private function isAmountEnoughForPrice(Booking $booking): bool
    {
        return $this->getPaidAmount($booking) >= $booking->getFinalPriceInCents();
    }

    private function isAmountEnoughForDepositPayment(Booking $booking): bool
    {
        return $this->getPaidAmount($booking) >= $this->getTotalExpectedAmountInCents($booking);
    }

    private function getTotalExpectedAmountInCents(Booking $booking): float
    {
        return $booking->getFinalPriceInCents() + $booking->getDamageDepositInCents();
    }
}
