<?php

namespace App\Actions\Providers\Smoobu;

use App\DataProviders\Providers\Smoobu;
use App\Jobs\HandleSmoobuWebhook;
use App\Models\ProviderAccount;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Lorisleiva\Actions\Concerns\AsAction;

class HandleSmoobuWebhookAction
{
    use AsAction;

    public function handle(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required',
            'user' => 'required', // If it does not exist, it will fail when fetching the SmoobuAccount
            'data' => 'required|array',
        ]);

        $input = request()->input();
        // Get the team first.
        $smoobuAccount = ProviderAccount::query()
            ->where('provider_id', '=', Smoobu::ID)
            ->where('account_id', '=', Arr::get($input, 'user'))
            ->first();

        if (empty($smoobuAccount)) {
            return response()->json(['info' => 'No Team found for given user']);
        }

        $team = $smoobuAccount->team;

        if ($team->subscribed() || $team->onGenericTrial()) { // We don't want queue invocations unless we really need to. Teams must be force synced when they resubscribe.
            if (HandleSmoobuWebhook::isValidAction(Arr::get($input, 'action'))) {
                $action = Arr::get($input, 'action');
                pnLog("[SM] Webhook for $action received and dispatched", $team);
                dispatch(new HandleSmoobuWebhook($team, $input));
            }
            /*
             * Price element webhook are currently ignored, since any change in the price theoretically triggers the
             *   booking updated/created webhook.
             *     priceElementCreated // priceElementDeleted // priceElementUpdated
             * else { pnLog('[SM error] Webhook not valid action: '.Arr::get($input, 'action'), $team); }
            */
        } else {
            pnLog('[SM error] Team not subscribed', $team);
        }

        return response()->json(['success' => 'OK']);
    }
}
