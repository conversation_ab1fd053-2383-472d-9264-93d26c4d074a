<?php

namespace App\Actions\Providers\Smoobu;

use App\Events\PaymentCreatedEvent;
use App\Events\PaymentRefundedEvent;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Source;
use App\Models\Team;
use App\Query\BookingPaymentQuery;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

/**
 * Given a response from the Smoobu API, we check what's the status of all our current payments
 * and generate the payments required, if necessary, to match the current status of the smoobu
 * API.
 */
class FillSmoobuPaymentsFromApiAction
{
    use AsAction;

    private Team $team;

    private Booking $booking;

    private array $reservationFromSmoobu;

    public function handle(array $smoobuData, Team $team, Booking $booking): float
    {
        $this->reservationFromSmoobu = $smoobuData;
        $this->team = $team;
        $this->booking = $booking;

        $totalPaidInSmoobu = $this->getPaidAmountFromSmoobu();
        $totalPaidInRentalNinja = $this->getPaidAmount();

        $bookingTotal = $booking->getBookingTotalInCents();
        if (IsFromChannelAction::run($booking, Source::AIRBNB)) {
            if ($totalPaidInRentalNinja < $bookingTotal) {
                $this->createAirbnbPayment($bookingTotal - $totalPaidInRentalNinja);
            }
        } else {
            if ($totalPaidInSmoobu != $totalPaidInRentalNinja) {
                $this->ensurePaymentsMatch($totalPaidInSmoobu, $totalPaidInRentalNinja);
            }
        }

        return floatval($this->getPaidAmount() / 100);
    }

    private function getPaidAmountFromSmoobu(): int
    {
        // Booking Price
        $price = $this->paid('price-paid') ? $this->booking->final_price - $this->booking->downpayment : 0;

        // Pre Payment
        $prePayment = $this->paid('prepayment-paid') ? $this->booking->downpayment : 0;

        // Damage Deposit
        $depositPaid = $this->paid('deposit-paid') ? $this->booking->damage_deposit : 0;

        return intval(round(($price + $depositPaid + $prePayment) * 100, 0));
    }

    private function paid(string $element): bool
    {
        return strtolower(data_get($this->reservationFromSmoobu, $element, 'No')) === 'yes';
    }

    private function getPaidAmount(): int
    {
        return $this->paymentsQuery()
            ->whereNull('canceled_at')
            ->sum('amount_in_cents');
    }

    private function paymentsQuery(): BookingPaymentQuery
    {
        return BookingPayment::query()
            ->onTeam($this->team->id)
            ->onBooking($this->booking->id);
    }

    private function createAirbnbPayment(int $amount): void
    {
        $payment = $this->fillAndSavePayment(
            amountInCents: $amount,
            kind: 'Airbnb Payment',
            notes: 'Airbnb Payment. Automatically Generated.'
        );

        event(new PaymentCreatedEvent($this->team, $payment->id));
    }

    /**
     * @throws Throwable
     */
    private function ensurePaymentsMatch(float $totalPaidInSmoobu, int $totalPaidInRentalNinja): void
    {
        $amount = $this->getPaymentAmount($totalPaidInSmoobu, $totalPaidInRentalNinja);
        if ($amount == 0) {
            return;
        }

        $payment = $this->fillAndSavePayment(amountInCents: $amount, kind: 'Smoobu Payment', notes: 'Smoobu Payment. Automatically Generated.');

        if ($payment->amount_in_cents >= 0) {
            event(new PaymentCreatedEvent($this->team, $payment->id));
        } else {
            event(new PaymentRefundedEvent($this->team, $payment->id));
        }
    }

    public function getPaymentAmount(float $totalPaidInSmoobu, int $totalPaidInRentalNinja): int
    {
        $amount = $totalPaidInSmoobu - $totalPaidInRentalNinja;

        if ($amount > 0) {
            return $amount;
        } elseif ($totalPaidInSmoobu == 0 && $totalPaidInRentalNinja < 0) {
            // If Smoobu is saying 0 and RN negative, it means a PM canceled a payment in RN
            // while we already had a negative payment canceling that amount (result of marked booking as un-paid from smoobu).
            // We don't do anything, this must be solved by the PM manually because they did a mistake
            // by canceling a payment which was already balanced out by another negative payment.
            return 0;
        }

        $amount = 0;
        if (! $this->paid('deposit-paid') &&
            $totalPaidInRentalNinja >= $this->booking->getDamageDepositInCents() + $this->booking->getFinalPriceInCents()) {
            $amount -= $this->booking->getDamageDepositInCents();
        }
        if (! $this->paid('price-paid') &&
            $totalPaidInRentalNinja + $amount >= $this->booking->getFinalPriceInCents()) {
            $amount -= $this->booking->getFinalPriceInCents() - $this->booking->getDownPaymentInCents();
        }
        if (! $this->paid('price-paid') &&
            ! $this->paid('prepayment-paid') &&
            $totalPaidInRentalNinja + $amount >= $this->booking->getDownPaymentInCents()) {
            $amount -= $this->booking->getDownPaymentInCents();
        }

        return $amount;
    }

    private function fillAndSavePayment(int $amountInCents, string $kind, string $notes): BookingPayment
    {
        $time = now();
        $payment = new BookingPayment();
        $payment->team_id = $this->team->id;
        $payment->provider_id = 3;
        $payment->booking_id = $this->booking->id;
        $payment->kind = $kind;
        $payment->notes = $notes;
        $payment->amount_in_cents = $amountInCents;
        $payment->currency ??= $this->booking->currency;
        $payment->locked = false; //TODO attention
        $payment->paid_at ??= $time;
        $payment->created_at ??= $time;
        $payment->updated_at ??= $time;
        $payment->canceled_at ??= null;
        $payment->saveOrFail();

        return $payment;
    }
}
