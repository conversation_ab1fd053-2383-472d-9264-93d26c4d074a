<?php

namespace App\Actions\Providers\Generic;

use App\Actions\Bookings\Times\BookingDatesAreEqualAction;
use App\Actions\Bookings\Times\BookingTimesAreEqualAction;
use App\Actions\Bookings\Times\TriggerCheckInOutDateModifiedEventAction;
use App\Actions\Bookings\Times\TriggerCheckInOutTimeModifiedEventAction;
use App\DTO\Bookings\BookingDto;
use App\Events\Booking\BookingUpdatedEvent;
use App\Models\Team;

class HandleProviderWebhookAction
{
    public string $jobQueue = 'webhooks';

    protected function triggerBookingUpdatedEvents(?BookingDto $oldBookingData, BookingDto $updatedBookingData, Team $team): void
    {
        // Notify if the dates of the booking have changed
        if (! BookingDatesAreEqualAction::run($oldBookingData, $updatedBookingData)) {
            TriggerCheckInOutDateModifiedEventAction::run($team, $updatedBookingData);
        }
        if (! BookingTimesAreEqualAction::run($oldBookingData, $updatedBookingData)) {
            TriggerCheckInOutTimeModifiedEventAction::run($team, $updatedBookingData);
        }

        event(new BookingUpdatedEvent($team, $updatedBookingData->id));
    }
}
