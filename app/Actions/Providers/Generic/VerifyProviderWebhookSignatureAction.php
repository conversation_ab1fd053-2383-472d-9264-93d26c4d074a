<?php

namespace App\Actions\Providers\Generic;

use Lorisleiva\Actions\Concerns\AsAction;

class VerifyProviderWebhookSignatureAction
{
    use AsAction;

    /**
     * Based on Stripe\WebhookSignature class.
     */
    public function handle(string $payload, string $signatureHeader, string $secret, string $algorithm = 'sha256', bool $base64EncodeAfterHash = false): bool
    {
        if (empty($signatureHeader)) {
            return false;
        }

        $expectedSignature = $this->computeSignature($payload, $secret, $algorithm, $base64EncodeAfterHash);

        return $this->sameHeader($expectedSignature, $signatureHeader);
    }

    /**
     * Takes a request payload and creates it's expected signature with the given secret.
     */
    private function computeSignature(string $payload, string $secret, string $algorithm, bool $base64EncodeAfterHash): string
    {
        if ($base64EncodeAfterHash) {
            return base64_encode(\hash_hmac($algorithm, $payload, $secret, true)); // To be able to base 64 encode, we need the result in plain binary
        } else {
            return \hash_hmac($algorithm, $payload, $secret);
        }
    }

    private function sameHeader(string $a, string $b): bool
    {
        return \hash_equals($a, $b);
    }
}
