<?php

namespace App\Actions\Providers\ChannelManager;

use App\Actions\Providers\RentalsUnited\HandleRentalsUnitedRestWebhookAction;
use App\DataProviders\ApiResolvers\RentalsUnited\GuestCommunications\RUPmsMessagesResolver;
use App\DataProviders\ApiResolvers\RentalsUnited\GuestCommunications\RUPmsThreadsResolver;
use App\DataProviders\Providers\RentalsUnitedPms;

class HandleRentalsUnitedPmsRestWebhookAction extends HandleRentalsUnitedRestWebhookAction
{
    protected function getProviderId(): int
    {
        return RentalsUnitedPms::ID;
    }

    protected function getThreadsResolver(): string
    {
        return RUPmsThreadsResolver::class;
    }

    protected function getMessagesResolver(): string
    {
        return RUPmsMessagesResolver::class;
    }

    protected function syncsMessages(): bool
    {
        return false;
    }
}
