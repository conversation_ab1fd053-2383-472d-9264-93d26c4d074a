<?php

namespace App\Actions\Providers\ChannelManager;

use App\Actions\Providers\Generic\HandleProviderWebhookAction;
use App\DataProviders\ChannelManager\ChannelManagerPusher;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\Models\ProviderAccount;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;

class EnableRLNMChannelManagerWebhookAction extends HandleProviderWebhookAction
{
    use AsAction;

    const SIGNATURE = 'ninja:cm-enable-rlnm';

    public string $commandSignature = self::SIGNATURE;

    public function asCommand(Command $command): int
    {
        $command->info('Enabling Reservation Live Notification Mechanism for Rentals United Channel Managed accounts');

        $url = config('app.url').route('cm-webhook', absolute: false);
        $command->info("Set to: $url");
        ProviderAccount::whereProviderId(ChannelManagerProvider::ID)
            ->each(fn (ProviderAccount $pa) => $this->handle($pa));
        $command->info('Enabled RLNM for Rentals United');

        return $command::SUCCESS;
    }

    public function handle(ProviderAccount $account): void
    {
        $hashValue = ChannelManagerPusher::putHandlerUrl($account);
        Cache::forever('channel-manager-webhook-hash-'.$account->team_id, $hashValue);
        // Rest Api is done where this action is called
    }
}
