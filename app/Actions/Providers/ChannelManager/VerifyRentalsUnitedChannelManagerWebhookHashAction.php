<?php

namespace App\Actions\Providers\ChannelManager;

use App\DataProviders\Providers\ChannelManagerProvider;
use App\Models\Team;
use Exception;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;

class VerifyRentalsUnitedChannelManagerWebhookHashAction
{
    use AsAction;

    public function handle(Team $team, string $hash): void
    {
        if (isNotProduction()) {
            return;
        }

        $secureHash = Cache::get('channel-manager-webhook-hash-'.$team->id);
        if (is_null($secureHash)) {
            report(new Exception('Rentals United Webhook hash not found. Asking for a new one.'));
            EnableRLNMChannelManagerWebhookAction::run($team->getProviderAccount(ChannelManagerProvider::ID));

            // We do not abort in this case to not lose the message.
            return;
        }
        if ($hash != $secureHash) {
            report(new Exception('Received a Rentals United Webhook with an incorrect hash. Aborting.'));
            abort(403, 'Hash verification code do not match');
        }
    }
}
