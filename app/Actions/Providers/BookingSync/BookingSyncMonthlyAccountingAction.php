<?php

namespace App\Actions\Providers\BookingSync;

use App\Actions\Support\Files\GetDomPdfAction;
use App\Actions\Support\Files\NinjaZipper;
use App\DataProviders\Providers\BookingSync;
use App\Models\Invoice as NinjaInvoice;
use App\Models\RentalNinjaTeam;
use App\Models\TeamPerformanceIndicator;
use App\Notifications\Internal\AccountingRentalNinjaNotification;
use App\Support\NinjaCashier;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsCommand;
use Psr\Log\LogLevel;

class BookingSyncMonthlyAccountingAction
{
    use AsCommand;

    public string $commandSignature = 'ninja:accounting {initial_day?} {final_day?} {commission_percentage=20}';

    public string $commandDescription = 'This command is used to calculate the monthly 
    commission to be paid to BookingSync based on the Rental Ninja invoices. 
    Introduce dates in the following format: yyyy-mm-dd';

    public function asCommand(Command $command): void
    {
        $initial_day = $command->argument('initial_day');
        $final_day = $command->argument('final_day');
        $commission_percentage = $command->argument('commission_percentage');
        $commission_percentage = floatval($commission_percentage);
        $start = is_null($initial_day) ? Carbon::today()
            ->subMonth()
            ->startOfMonth() : Carbon::parse($initial_day)
            ->startOfDay();
        $end = is_null($final_day) ? Carbon::today()
            ->subMonth()
            ->endOfMonth() : Carbon::parse($final_day)
            ->endOfDay();

        // We will do this command using the invoice table of our DB. The invoice is only generated when the payment is done
        // Be aware that refunded invoices will have to be indicated afterwards, as we generate manual refund invoices

        // Get all invoices for the given month
        $invoices = NinjaInvoice::query()
            ->where('team_id', '<>', 1)
            ->orderBy('invoices.created_at')
            ->whereBetween('invoices.created_at', [$start, $end])
            ->where('total', '>=', 0.00)
            ->with('team')
            ->get();

        if ($invoices->isEmpty()) {
            return;
        }

        $pdf_entries = [];
        $statement_total = 0;
        $discounts_total = 0;

        // Get all fields for each invoice:
        foreach ($invoices as $invoice) {
            $team = $invoice->team;
            if (! ($team->isBookingSyncTeam() && $team->config()->billedThroughRentalNinja())) {
                continue;
            }

            try {
                // Check if the invoice is refunded:
                $stripe_invoice = $team->findInvoice($invoice->provider_id)->asStripeInvoice();
                if (is_null($stripe_invoice->charge)) {
                    continue;
                }
                $stripe_charge = NinjaCashier::initialize()->retrieveCharge($stripe_invoice->charge);
                if ($stripe_charge->refunded) {
                    continue;
                }
                if ($stripe_invoice->lines->first()->description == 'Personal Setup') {
                    continue;
                }

                $owner = $team->owner;
                $company_name = $team->name;
                // Team id is interesting because company name & owner email can be changed by the user. This id does never change:
                $invoice_id = $invoice->id;
                $owner_email = $owner->email;
                $subtotal_all = $stripe_invoice->subtotal / 100;
                $total_all_wo_tax = $invoice->total - $invoice->tax;

                // Get the discount value:
                $discount_value = $subtotal_all - $total_all_wo_tax < 0.01 ? 0 : $subtotal_all - $total_all_wo_tax;
                $discount_percentage = $discount_value / $subtotal_all;

                // Get billable addons
                $lastDay = $invoice->created_at->copy()->subDay();
                $firstDay = $team->isYearlySubscription() ? $lastDay->copy()->subYear() : $lastDay->copy()->subMonth();
                $kpi = TeamPerformanceIndicator::query()
                    ->where('team_id', $team->id)
                    ->where('date', '>', $firstDay->toDateString())
                    ->where('date', '<=', $lastDay->toDateString())
                    ->groupBy('team_id')
                    ->selectRaw('sum(addons_revenue) as addons')
                    ->first();

                $rentalsFactor = $team->rentals != 0 ? ($team->getProviderAccount(BookingSync::ID)->rentals) / $team->rentals : 0;
                $subtotal_rn = ($subtotal_all - $kpi->addons) * $rentalsFactor;
                if ($subtotal_rn <= 0) {
                    continue; // Do not show invoices with zero, as per Pau's comment
                }
                $discount_rn = $subtotal_rn * $discount_percentage;
                $total_rn = $subtotal_rn - $discount_rn;

                $pdf_entries[] = [
                    'company_name' => $company_name,
                    'invoice_id' => $invoice_id,
                    'owner_email' => $owner_email,
                    'subtotal' => $subtotal_rn,
                    'discount_value' => $discount_rn,
                    'total_wo_tax' => $total_rn,
                ];

                $statement_total += $total_rn;
                $discounts_total += $discount_rn;
            } catch (Exception $e) {
                report($e);
                nLog(
                    string: 'Error generating the BookingSync Monthly Accounting File',
                    team: $team,
                    level: LogLevel::ERROR,
                    context: ['message' => $e->getMessage(), 'trace' => $e->getTrace()]
                );
            }
        }

        $commission_amount = $statement_total * $commission_percentage / 100;

        // Generate PDF:
        $dompdf = GetDomPdfAction::run();

        // Create the view:
        $view = view(
            view: 'ninja.kiosk.pdf-booking-sync-accounting.booking_sync_accounting',
            data: [
                'start' => $start->toDateString(),
                'end' => $end->toDateString(),
                'invoices' => $pdf_entries,
                'discounts_total' => $discounts_total,
                'statement_total' => $statement_total,
                'commission_percentage' => $commission_percentage,
                'commission_amount' => $commission_amount,
            ]
        );

        $dompdf->loadHtml($view->render());
        $dompdf->render();
        $pdf_output = $dompdf->output();

        // Create a zip, put it in amazon and send the url by email.
        //Note: commands can't return anything unless a message to print in screen.
        $name = 'BS-RN_accounting.zip';
        $zipper = NinjaZipper::create($name);
        $file_name = 'BS - RN Accounting from '.$start->toDateString().' to '.$end->toDateString().'.pdf';
        $zipper->addString($file_name, $pdf_output);

        $month = $start->month;
        $year = $start->year;
        $amazon_path = "bs-rn-accounting/$year-$month/$name";

        // Send the full path via email to the accounting email:
        $zipper->store($amazon_path);
        $notification = new AccountingRentalNinjaNotification($amazon_path, 'BookingSync');
        $team = RentalNinjaTeam::getInstance();
        $team->email = '<EMAIL>';
        $team->notifyNow($notification);
        nLog('BS - RN accounting created. See your email');
    }
}
