<?php

namespace App\Actions\Providers\RentalsUnited;

use App\Actions\Alerts\UpdateBookingPaymentAlertsAction;
use App\Actions\Providers\Generic\HandleProviderWebhookAction;
use App\Actions\Providers\Generic\IncreaseWebhookInvocationCountAction;
use App\Actions\Support\Files\XmlToArrayAction;
use App\Actions\Tasks\ScheduledTask\HandleTasksForBookingAction;
use App\DataProviders\ApiResolvers\RentalsUnited\Bookings\RUBookingsResolver;
use App\DataProviders\Providers\NinjaProvider;
use App\DataProviders\Providers\RentalsUnited;
use App\DTO\Bookings\BookingDto;
use App\Enum\BookingStatusEnum;
use App\Events\Booking\BookingCancelledEvent;
use App\Events\Booking\BookingConfirmedEvent;
use App\Models\Booking;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Lorisleiva\Actions\Concerns\AsAction;

class HandleRentalsUnitedWebhookAction extends HandleProviderWebhookAction
{
    public NinjaProvider $provider;
    const LEAD = 0;
    const CONFIRM = 1;
    const CANCEL = 2;
    const UNCONFIRMED = 3;

    const LEAD_RESERVATION_WEBHOOK = 'LNM_PutLeadReservation_RQ';
    const CONFIRMED_RESERVATION_WEBHOOK = 'LNM_PutConfirmedReservation_RQ';
    const CANCEL_RESERVATION_WEBHOOK = 'LNM_CancelReservation_RQ';
    const UNCONFIRMED_RESERVATION_WEBHOOK = 'LNM_PutUnconfirmedReservation_RQ';

    use AsAction;

    public function __construct()
    {
        $this->provider = RentalsUnited::get();
    }

    public function asController(Request $request): Response
    {
        $xmlContent = simplexml_load_string($request->getContent());
        $method = match ($xmlContent->getName()) {
            self::LEAD_RESERVATION_WEBHOOK => self::LEAD,
            self::CONFIRMED_RESERVATION_WEBHOOK => self::CONFIRM,
            self::CANCEL_RESERVATION_WEBHOOK => self::CANCEL,
            self::UNCONFIRMED_RESERVATION_WEBHOOK => self::UNCONFIRMED,
        };

        $data = XmlToArrayAction::run($xmlContent, ['alwaysArray' => ['Image', 'Property', 'StayInfo', 'Reservation', 'TotalFeeTax', 'Text', 'ChannelTotalFeeTax', 'DayPrices']]);
        $data = collect($data)->first();

        // Verify the authenticity of the webhook.
        $this->verifyTheAuthenticityOfTheWebhook($data);

        // Log data to S3
        $this->webhooksLogging($method, $request->getContent());

        // This could be a dispatch, but since RU ignore our reply, we can use the same call.
        self::dispatch($method, $data);

        return response(''); // Empty 200 response
    }

    private function webhooksLogging(int $method, string $content): void
    {
        if (isNotProduction()) {
            return;
        }
        $currentTS = now()->timestamp;
        $filename = "wh_{$method}_$currentTS.txt";

        $providerName = $this->provider->internalFullName();
        $currentDate = Carbon::today()->format('Y-m-d');
        $env = app()->environment();
        $blobName = "$providerName/logs/$env/$currentDate/webhook/$filename";

        Storage::disk('provider-logs')->put($blobName, $content);
    }

    public function asJob(int $method, array $input): void
    {
        $this->handle($method, $input);
    }

    public function handle(int $method, array $input): void
    {
        switch ($method) {
            case self::CONFIRM:
                $team = $this->handleConfirmation($input['Reservation'][0]);
                break;
            case self::LEAD:
            case self::UNCONFIRMED:
                // Leads and unconfirmed are ignored, at this time.
                return;
                break;
            case self::CANCEL:
                $team = $this->handleCancellation($input['ReservationID']);
                break;
        }

        if ($team) {
            IncreaseWebhookInvocationCountAction::run($team); // This should be dispatched in a job to avoid the response to wait
        }
    }

    private function handleConfirmation(array $input): ?Team
    {
        // Get team
        $rentalIds = data_get($input, 'StayInfos.StayInfo.*.PropertyID');

        $rentals = Rental::query()
            ->onProvider($this->provider->id())
            ->whereIn('external_id', $rentalIds)
            ->get();

        if ($rentals->isEmpty()) {
            // We receive webhooks for all customers who provided us access, even if they are not RN users.
            return null;
        }

        $team = $rentals->first()->team;

        if (! $this->isValidTeam($team)) {
            return null;
        }
        $shortId = $this->provider->shortIdentifier();
        pnLog("[$shortId] Received a booking confirmation webhook", $team);

        $status = $input['ReservationStatusID'];

        // 1 Confirmed | 2 Canceled | 3 Modified
        $oldBookingsData = null;
        if ($status == 3) {
            $externalId = Arr::get($input, 'ReservationID');
            $oldBookings = Booking::query()
                ->withoutGlobalScopes()
                ->onTeam($team)
                ->whereProviderId($this->provider->id())
                ->whereExternalIdStartsWith($externalId)
                ->get();
            $oldBookingsData = $oldBookings?->map(fn (Booking $b) => BookingDto::fromModel($b));
        }
        $resolverData = collect([
            'Status' => ['data' => 'Success'],
            'Reservations' => ['Reservation' => $input],
        ]); // TODO: This could be substituted by a RentalsUnitedResponseDto
        $externalId = Arr::get($input, 'ReservationID');
        $this->resolveBooking($team, $resolverData, $externalId);

        $bookings = Booking::query()
            ->withoutGlobalScopes()
            ->onTeam($team)
            ->whereProviderId($this->provider->id())
            ->whereExternalIdStartsWith($externalId)
            ->get();

        $updatedBookingsData = $bookings->map(fn (Booking $b) => BookingDto::fromModel($b));
        $rentals->each(fn (Rental $r) => $r->updateAvailability());

        foreach ($bookings as $booking) {
            UpdateBookingPaymentAlertsAction::run($booking, issuer: 0);
            HandleTasksForBookingAction::run($team, $booking);

            $old = $oldBookingsData?->firstWhere('id', $booking->id);
            $updated = $updatedBookingsData->firstWhere('id', $booking->id);
            $this->triggerBookingEvents($team, $status, $old, $updated);
        }

        return $team;
    }

    private function isValidTeam(Team $team): bool
    {
        // Only Subscribed teams or trialing teams can have access to webhooks
        return $team->subscribed() || $team->onGenericTrial();
    }

    private function triggerBookingEvents(Team $team, int $event, ?BookingDto $oldBookingData, BookingDto $updatedBookingData): void
    {
        if ($team->shouldNotify()) {
            switch ($event) {
                case 2: // Cancelled
                    event(new BookingCancelledEvent($team, $updatedBookingData->id));
                    break;
                case 1: // Created
                    if ($updatedBookingData->status == BookingStatusEnum::BOOKED) {
                        event(new BookingConfirmedEvent($team, $updatedBookingData->id));
                    }
                    break;
                default: // This will be for 3 (updated) and -1
                    if ($oldBookingData?->status != BookingStatusEnum::BOOKED && $updatedBookingData->status == BookingStatusEnum::BOOKED) {
                        event(new BookingConfirmedEvent($team, $updatedBookingData->id));
                    } else {
                        $this->triggerBookingUpdatedEvents($oldBookingData, $updatedBookingData, $team);
                    }
                    break;
            }
        }
    }

    private function handleCancellation(int $bookingId): ?Team
    {
        $booking = Booking::query()
            ->withCancellations()
            ->onProvider($this->provider->id())
            ->with('team')
            ->find($bookingId);

        // The Cancellation can come from a lead or the team may not be valid. We ignore it.
        if (is_null($booking) || ! $this->isValidTeam($booking->team)) {
            return null;
        }

        $team = $booking->team;
        pnLog('[RU] Received a booking cancellation webhook', $team);

        $booking->canceled_at = now()->timestamp;
        $booking->status = BookingStatusEnum::CANCELED;
        $booking->save();

        HandleTasksForBookingAction::run($team, $booking);

        if ($team->shouldNotify()) {
            event(new BookingCancelledEvent($team, $booking->id));
        }

        return $team;
    }

    /** @noinspection PhpUnused */
    public function getJobTags(int $method, array $input): array
    {
        $rentalId = data_get($input, 'Reservation.0.StayInfos.StayInfo.0.PropertyID');
        $teamId = is_null($rentalId) ? 'undefined' :
            Rental::whereProviderId(RentalsUnited::ID)->whereId($rentalId)->pluck('team_id')->first();

        return [
            'provider-webhook',
            'provider:'.$this->provider->id(),
            'team:'.$teamId,
        ];
    }

    protected function verifyTheAuthenticityOfTheWebhook(array $data): void
    {
        VerifyRentalsUnitedWebhookHashAction::run(data_get($data, 'Authentication.Password'), pmsIntegration: false);
    }

    protected function resolveBooking(mixed $team, Collection $resolverData, int $externalId)
    {
        RUBookingsResolver::runSingle($team, $resolverData, singleId: $externalId);
    }
}
