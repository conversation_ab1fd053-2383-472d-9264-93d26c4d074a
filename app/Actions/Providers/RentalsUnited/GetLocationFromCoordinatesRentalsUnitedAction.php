<?php

namespace App\Actions\Providers\RentalsUnited;

use App\DataProviders\ProviderApi\RentalsUnitedApi;
use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsAction;

class GetLocationFromCoordinatesRentalsUnitedAction
{
    use AsAction;

    const SIGNATURE = 'ninja:ru-location-from-coordinates';

    public string $commandSignature = 'ninja:ru-list-locations {lat} {lng}';

    public function asCommand(Command $command)
    {
        $lat = $command->argument('lat');
        $lng = $command->argument('lng');

        $command->info("Get location from ($lat, $lng) coordinates");
        [$locationId, $locationType] = $this->handle($lat, $lng);
        [$city, $country] = GetLocationRentalsUnitedAction::run($locationId);
        $command->info('Location id '.$locationId.' and type '.$locationType.' is '.$city.' in '.$country);

        pnLog("[Channel Manager] Getting Location from coordinates ($lat, $lng): $city in $country");

        return $command::SUCCESS;
    }

    public function handle(float $lat, float $lng): ?int
    {
        $api = RentalsUnitedApi::instance();

        return $api->getLocationFromCoordinates($lat, $lng);
        // $locationDetails = $api->getLocationDetails($locationId);
    }
}
