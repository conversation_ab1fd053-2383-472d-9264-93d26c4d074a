<?php

namespace App\Actions\Providers\RentalsUnited;

use App\DataProviders\ProviderApi\RentalsUnitedApi;
use DateInterval;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCancellationTypesRentalsUnitedAction
{
    use AsAction;

    const SIGNATURE = 'ninja:ru-cancellation-types';

    public string $commandSignature = 'ninja:ru-cancellation-types {id=1}';

    public function asCommand(Command $command)
    {
        $id = $command->argument('id');
        $command->info('Get cancellationType id: '.$id);
        $cancellationType = $this->handle($id);
        $command->info('CancellationType id '.$id.' is '.$cancellationType);

        return $command::SUCCESS;
    }

    public function handle(int $cancellationTypeId): ?string
    {
        $value = Cache::get('rentals-united.cancellationType.expires');
        if (is_null($value) || Carbon::now()->gt($value)) {
            $this->refreshCancellationType();
        }

        return Cache::get('rentals-united.cancellationType.'.$cancellationTypeId);
    }

    private function refreshCancellationType(): void
    {
        $api = RentalsUnitedApi::instance();
        $xmlCancellationType = $api->getListCancellationType();
        $types = $xmlCancellationType->CancellationTypes;
        $typeChildren = $types->children() ?? [];

        foreach ($typeChildren as $cancellationType) {
            $id = (int) $cancellationType['Id'];
            $value = (string) $cancellationType;

            $key = 'rentals-united.cancellationType.'.$id;
            Cache::set($key, $value, new DateInterval('P3M'));
        }

        Cache::set('rentals-united.cancellationType.expires', Carbon::now()->addMonths(2)->subDay(), new DateInterval('P3M'));
    }
}
