<?php

namespace App\Actions\Providers\RentalsUnited;

use App\DataProviders\Providers\NinjaProvider;
use App\DataProviders\Providers\RentalsUnited;
use App\Flavors\DefaultFlavor;
use App\Models\ProviderAccount;
use App\Models\TeamRegistrationSteps;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Psr\Log\LogLevel;
use Symfony\Component\HttpFoundation\Response;

class GetRegisterDataRentalsUnitedAction
{
    use AsAction;

    public function asController(ActionRequest $request): JsonResponse
    {
        $ninjaRegistrationToken = $request->header('Ninja-Register-Token');
        $data = TeamRegistrationSteps::findOrFail($ninjaRegistrationToken);
        $data->provider_id = RentalsUnited::ID;

        $ruUserId = $request->input('id');

        $this->handle($ruUserId, $data);

        return response()->json();
    }

    protected function handle(string $ruUserId, TeamRegistrationSteps $data): void
    {
        pnLog('[RU] Register Attempt', level: LogLevel::NOTICE, context: ['userId' => $ruUserId]);

        // Check if the user (team in this case) is already registered.
        $exists = ProviderAccount::query()
            ->onProvider(RentalsUnited::ID)
            ->where('secondary_id', '=', $ruUserId)
            ->exists();

        if ($exists) {
            $data->setTeamExists();
            $data->save();
            pnLog('[RU] Register - Team already register', level: LogLevel::NOTICE, context: ['userId' => $ruUserId]);

            abort(Response::HTTP_CONFLICT);
        }

        $hasAccess = CheckUserAccessRentalsUnited::run(ownerId: $ruUserId);
        if (! $hasAccess) {
            $data->save();

            abort(Response::HTTP_NOT_FOUND);
        }
        $locale = App::getLocale();
        pnLog(
            string: '[RU] SendRegistrationReminderRentalsUnitedAction Dispatching delayed job',
            level: LogLevel::NOTICE,
            context: ['userId' => $ruUserId]
        );

        $registrationData = NinjaProvider::provider(RentalsUnited::ID)->getRegistrationData($ruUserId);

        SendRegistrationReminderRentalsUnitedAction::dispatch($ruUserId, $locale)->delay(14 * 60);

        $data->flavor_id = DefaultFlavor::FLAVOR_ID;
        $data->account_id = $registrationData->providerAccountId;
        $data->rentals = $registrationData->rentalCount;

        $data->team_exists = false;
        $data->account_data = $registrationData;
        $data->billed_by_rn = true;
        $data->save();
    }
}
