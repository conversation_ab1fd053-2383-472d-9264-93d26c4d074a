<?php

namespace App\Actions\Providers\RentalsUnited;

use App\Actions\Providers\Generic\HandleProviderWebhookAction;
use App\DataProviders\Providers\NinjaProvider;
use App\DataProviders\Providers\RentalsUnited;
use App\Models\ProviderAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;

class HandleRentalsUnitedRentalWebhookAction extends HandleProviderWebhookAction
{
    public NinjaProvider $provider;

    const CREATED = 'PropertyCreated';

    use AsAction;

    public function __construct()
    {
        $this->provider = RentalsUnited::get();
    }

    public function asController(Request $request): Response
    {
        if ($request->query('Type') != self::CREATED) {
            return response(''); // Empty 200 response
        }
        $ownerId = $request->query('OwnerId');
        $propertyId = $request->query('PropertyId');

        $this->webhooksLogging($ownerId, $propertyId);

        self::dispatch($ownerId, $propertyId);

        return response(''); // Empty 200 response
    }

    private function webhooksLogging(int $ownerId, int $propertyId): void
    {
        $providerName = $this->provider->internalFullName();

        pnLog("[HandleNewRental] $providerName Webhook received for PropertyID: $propertyId and OwnerID: $ownerId");
    }

    public function asJob(int $ownerId, int $propertyId): void
    {
        $this->handle($ownerId, $propertyId);
    }

    public function handle(int $ownerId, int $propertyId): void
    {
        $account = ProviderAccount::query()
            ->whereProviderId($this->provider->id())
            ->whereSecondaryId($ownerId)
            ->firstOrFail();
        $team = $account->team;

        $connector = $this->provider->getProviderConnector($team);
        $connector->updateRental($propertyId);
    }
}
