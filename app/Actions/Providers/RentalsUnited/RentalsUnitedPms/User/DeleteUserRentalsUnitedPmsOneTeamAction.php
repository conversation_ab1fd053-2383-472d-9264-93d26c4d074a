<?php

namespace App\Actions\Providers\RentalsUnited\RentalsUnitedPms\User;

use App\Actions\Providers\RentalsUnited\RentalsUnitedPms\Team\GetRentalsUnitedPmsOneTeamAction;
use App\Actions\Teams\TeamMembers\RemoveTeamMemberAction;
use App\Models\CustomUserToken;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class DeleteUserRentalsUnitedPmsOneTeamAction
{
    use AsAction;

    public function asController(ActionRequest $request, string $ruIdentifier, string $userId): JsonResponse
    {
        $team = GetRentalsUnitedPmsOneTeamAction::run($ruIdentifier);
        $user = $team->users()->whereExternalId($userId)->firstOrFail();

        abort_if($user->id == $team->owner_id, 400, 'Cannot remove team owner');

        CustomUserToken::revokeUserToken($user);
        RemoveTeamMemberAction::run($team, $user);

        // RU do not restore users, if user is re-added, they create a new ru id. Due to this, we need to free the email
        $user->email = 'DELETED$'.$user->email;
        $user->save();

        return response()->json();
    }
}
