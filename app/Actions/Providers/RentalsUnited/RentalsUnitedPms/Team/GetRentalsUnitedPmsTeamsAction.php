<?php

namespace App\Actions\Providers\RentalsUnited\RentalsUnitedPms\Team;

use App\DataProviders\Providers\RentalsUnitedPms;
use App\Enum\TeamStatusEnum;
use App\Flavors\RentalsUnitedPmsFlavor;
use App\Http\Resources\TeamRentalsUnitedPmsResource;
use App\Models\Team;
use App\Query\TeamQuery;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetRentalsUnitedPmsTeamsAction
{
    const ENABLED = 'enabled';
    const DISABLED = 'disabled';

    const ALL = 'all';

    const TYPES = [self::ENABLED, self::DISABLED, self::ALL];

    use AsAction;

    public function asController(ActionRequest $request, string $type): AnonymousResourceCollection
    {
        abort_if(! in_array($type, self::TYPES), 400, 'Invalid type');

        $collection = $this->handle($type);

        return TeamRentalsUnitedPmsResource::collection($collection);
    }

    protected function handle(string $type): LengthAwarePaginator
    {
        return Team::query()
            ->whereFlavorId(RentalsUnitedPmsFlavor::FLAVOR_ID)
            ->whereProviderId(RentalsUnitedPms::ID)
            ->when($type == self::ENABLED, fn (TeamQuery $q) => $q->whereStatus(TeamStatusEnum::enabled))
            ->when($type == self::DISABLED, fn (TeamQuery $q) => $q->whereStatus(TeamStatusEnum::disabled))
            ->paginate();
    }
}
