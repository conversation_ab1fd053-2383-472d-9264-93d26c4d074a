<?php

namespace App\Actions\Providers\RentalsUnited;

use Illuminate\Console\Command;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class CheckUserAccessRentalsUnited
{
    use AsAction;

    const SIGNATURE = 'ninja:ru-check-user-access {username}';

    public string $commandSignature = self::SIGNATURE;

    private Command $command;

    public function asCommand(Command $command)
    {
        $username = $command->argument('username');
        $hasAccess = $this->handle($username);
        $verb = $hasAccess ? 'HAVE' : 'DO NOT HAVE';

        $command->info("We $verb access to Rentals United $username account.");

        return $command::SUCCESS;
    }

    public function handle(?string $username = null, ?int $ownerId = null, bool $pmsIntegration = false): bool
    {
        $ruAccess = PullAllParentUsersRentalsUnitedAction::run($pmsIntegration);
        if (! is_null($username)) {
            return ! is_null($ruAccess->firstWhere('account_id', '=', $username));
        }

        return ! is_null($ruAccess->firstWhere('owner_id', '=', $ownerId));
    }
}

/*
 *     public function handle(): void
    {
        $existing = RentalsUnitedAccount::query()->pluck('rentals_united_account_id');
        $received = collect();

        $ruApi = new RentalsUnitedApiImplementation();
        $response = $ruApi->pullAllParentUsers();
        foreach ($response->ParentUsers->children() as $user) {
            if (! $existing->contains($user->UserId)) {
                // TO-DO Create team, provider account... or reactivate
            }
            $received->push($user->UserId);
        }

        $inactive = $existing->diff($received);
        // TO-DO Team unsubscribed?
    }
 */
