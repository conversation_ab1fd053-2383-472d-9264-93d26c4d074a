<?php

namespace App\Actions\Providers\Oauth;

use App\Actions\Sync\SyncTeamForceAction;
use App\DataProviders\Providers\Contracts\NinjaProviderHasOauth;
use App\DataProviders\Providers\NinjaProvider;
use App\Models\Team;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;

class OauthReconnectedFromAppAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'code' => ['required', 'min:8'],
            'state' => ['required', 'min:8'],
        ];
    }

    public function handle(Request $request): View
    {
        $state = $request->get('state');
        $cache = cache('oauth_state_'.$state);
        if (! $cache) {
            abort(400, 'Invalid state');
        }
        $teamId = $cache['team_id'];
        $userId = $cache['user_id'];
        $providerId = data_get($cache, 'provider_id');

        $user = User::find($userId);
        if (! $user) {
            abort(400, 'Invalid user');
        }

        $team = Team::find($teamId);
        $providerId = $providerId ?? $team?->provider_id;

        if (! $team) {
            abort(400, 'Invalid team');
        }
        if ($team->id !== $user->current_team_id) {
            abort(400, "Invalid team. User does not belong to this team. User: {$user->id} Team: {$team->id}");
        }

        $provider = NinjaProvider::provider($providerId);
        $api = $provider->getProviderApi();

        if (! $api instanceof NinjaProviderHasOauth) {
            abort(400, 'Provider Can not handle this operation');
        }
        $code = $request->get('code');
        $token = $api->getNewOauthToken(auth_code: $code, reconnect: true);

        $account = $team->getProviderAccount($providerId);
        $account->setNewOauthToken($token);

        alert()->success('Success!', "Reconnected Successfully to {$provider->fullName()}");
        SyncTeamForceAction::dispatch($team, $provider->id())->onQueue('priority-sync');

        // TODO: We could redirect to the same page, but it looks like nothing happened. It is better to redirect to the oauth success page
        // $url = $team->config()->rnAppTargetDomain('/settings/billing');
        // return redirect()->to($url);

        return view('ninja.oauth.oauth-successful');
    }
}
