<?php

/** @noinspection PhpUnused */

namespace App\Actions\Providers\Tokeet;

use App\Actions\Sync\SyncTeamForceAction;
use App\Actions\Teams\CreateTeamAction;
use App\Actions\Users\RegisterUserAction;
use App\DataProviders\Providers\NinjaProvider;
use App\DataProviders\Providers\Tokeet;
use App\DTO\Team\TeamRegistrationData;
use App\Enum\TeamRolesEnum;
use App\Enum\TeamStatusEnum;
use App\Models\ProviderAccount;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class RegisterTokeetTeamAction
{
    use AsAction;

    private string $errorMessage;

    public function asController(ActionRequest $request): JsonResponse
    {
        pnLog('[TK] Using RegisterTokeetTeamAction');
        $code = $request->get('code');
        abort_if(is_null($code), 400, 'Code is not present or not a string');

        // Handle php error
        try {
            $success = $this->handle($code);
        } catch (Exception $exception) {
            pnLog('[TK] ERROR registering an account. Check Sentry');
            report($exception);
            abort(500, $exception->getMessage()); // This is the case when Tokeet tries to register a user already exists
        }

        // Handle not positive result without braking the code: Tokeet is requesting something we can't handle
        if (! $success && isset($this->errorMessage)) {
            pnLog("[TK] ERROR registering an account. $this->errorMessage");
            abort(400, $this->errorMessage);
        } elseif (! $success) {
            pnLog("[TK] ERROR registering an account. We could't subscribe the team for an unknown reason.");
            abort(400, "We couldn't subscribe the team for an unknown reason.");
        }

        return response()->json(['status' => 'Account successfully registered']);
    }

    public function handle($code): bool
    {
        $provider = NinjaProvider::provider(Tokeet::ID);
        $data = $provider->getRegistrationData($code);

        // Check if this team already exists.
        if ($this->checkAccountExists($data)) {
            return true;
        }

        pnLog('[TK] Registering account');

        // Create user
        $user = RegisterUserAction::run(
            name: $data->userName,
            email: $data->userEmail,
            password: Str::random(40),
            role: TeamRolesEnum::OWNER,
            externalId: $data->userExternalId,
        );

        // Create team
        CreateTeamAction::run($user, $data);

        pnLog('[TK] Account registered');

        return true;
    }

    private function checkAccountExists(TeamRegistrationData $registrationData): bool
    {
        $accountExists = ProviderAccount::query()
            ->where('provider_id', Tokeet::ID)
            ->where('account_id', $registrationData->providerAccountId)
            ->first();

        if ($accountExists) {
            pnLog('[TK] Account already exists');
            $team = $accountExists->team;
            $accountExists->setNewOauthToken($registrationData->oauth);
            $team->status = TeamStatusEnum::enabled;
            $team->save();

            SyncTeamForceAction::dispatch($team, Tokeet::ID)->onQueue('priority-sync');

            return true;
        }

        return false;
    }
}
