<?php

/** @noinspection PhpUnused */

namespace App\Actions\Providers\Tokeet;

use App\DataProviders\ProviderConstants;
use App\Models\Booking;
use Exception;
use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsAction;

class DEVGetBreakdownsFromBookingTokeetAction
{
    use AsAction;

    public string $commandSignature = 'ninja:tokeet-get-breakdown-from-tokeet {team} {id}';

    public function asCommand(Command $command): int
    {
        $teamId = $command->argument('team');
        $bookingId = $command->argument('id');
        $booking = Booking::getBookingModel($teamId, $bookingId);

        $this->handle($booking);

        return $command::SUCCESS;
    }

    public function handle(Booking $booking): void
    {
        $connector = $booking->getProviderConnector();

        try {
            $connector->updateBooking($booking->external_id);
        } catch (Exception) {
            echo 'Bookings endpoint failed';
        }

        //  It is now inside the bookings endpoint
//        try {
//            $fetcher
//                ->endpoint(ProviderConstants::ENDPOINT_BOOKINGS_COST_BY_BOOKING)
//                ->setSingleId($booking->external_id)
//                ->doRequest();
//        } catch (Exception) {
//            echo 'Cost endpoint failed';
//        }
    }
}
