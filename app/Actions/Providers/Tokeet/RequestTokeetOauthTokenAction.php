<?php

namespace App\Actions\Providers\Tokeet;

use App\DataProviders\ProviderApi\TokeetApi;
use Illuminate\Http\RedirectResponse;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

/**
 * This is currently used for development purposes. We can delete it later.
 */
class RequestTokeetOauthTokenAction
{
    use AsAction;

    public function asController(ActionRequest $request): RedirectResponse
    {
        $api = TokeetApi::instance();
        $this->handle();

        return redirect()->to($api->oauthConnectUrl());
    }

    public function handle(): void
    {
    }
}
