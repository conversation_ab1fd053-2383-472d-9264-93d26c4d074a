<?php

namespace App\Actions\Providers\Tokeet;

use App\Actions\Support\Files\GetDomPdfAction;
use App\Actions\Support\Files\NinjaZipper;
use App\DataProviders\Providers\Tokeet;
use App\Flavors\TokeetFlavor;
use App\Models\RentalNinjaTeam;
use App\Models\Team;
use App\Models\TeamPerformanceIndicator;
use App\Notifications\Internal\AccountingRentalNinjaNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsCommand;

class TokeetMonthlyAccountingAction
{
    use AsCommand;

    const SIGNATURE = 'ninja:tokeet-accounting';

    public string $commandSignature = 'ninja:tokeet-accounting {initial_day?} {final_day?}';

    public string $commandDescription = 'This command is used to calculate the monthly revenue to be paid by Tokeet. 
    Introduce dates in the following format: yyyy-mm-dd';

    const PRICES = [
        3000 => 2.1,
        6000 => 1.71,
        9000 => 1.62,
        12000 => 1.53,
        15000 => 1.44,
        18000 => 1.35,
        21000 => 1.26,
        24000 => 1.17,
        27000 => 1.08,
        30000 => 0.99,
        ********* => 0.90,
    ];

    public function asCommand(Command $command): void
    {
        $initialDay = $command->argument('initial_day');
        $finalDay = $command->argument('final_day');

        $start = is_null($initialDay) ?
            Carbon::today()->subMonth()->startOfMonth() :
            Carbon::parse($initialDay)->startOfDay();
        $end = is_null($finalDay) ?
            Carbon::today()->subMonth()->endOfMonth() :
            Carbon::parse($finalDay)->endOfDay();

        // This is Tokeet release date. Previous data is testing data.
        if ($start->isBefore(Carbon::createFromDate(2023, 9, 4))) {
            $start = Carbon::createFromDate(2023, 9, 4);
        }

        // Team related metrics
        $teamDetails = [];
        $periodLength = $end->diffInDays($start) + 1; // Include last day.

        $rawTeamData = TeamPerformanceIndicator::query()
            ->whereProviderId(Tokeet::ID)
            ->whereFlavorId(TokeetFlavor::FLAVOR_ID)
            ->whereIn('status', ['enabled'])
            ->whereBetween('date', [$start->toDateString(), $end->toDateString()])
            ->select('team_id')
            ->selectRaw('SUM(active_rentals) as active_rentals')
            ->selectRaw('count(*) as active_days')
            ->groupBy(['team_id'])
            ->get();

        $tokeetTeamIds = $rawTeamData->pluck('team_id');
        $tokeetTeams = Team::query()->whereIn('id', $tokeetTeamIds)->get();

        $totalAccounts = 0;

        foreach ($tokeetTeams as $team) {
            $teamData = $rawTeamData->firstWhere('team_id', '=', $team->id);
            $teamDetails[] = [
                'name' => $team->name,
                'account_id' => rescue($team->getProviderAccount(Tokeet::ID)->account_id, fn () => 'No current provider account'),
                'days' => $teamData->active_days,
                'averaged_rentals' => round($teamData->active_rentals / $periodLength, 2),
                'daily_rental_count' => $teamData->active_rentals,
            ];
            $totalAccounts++;
        }

        // Date relate metrics
        $dateDetails = [];
        $rawDateData = TeamPerformanceIndicator::query()
            ->whereProviderId(Tokeet::ID)
            ->whereFlavorId(TokeetFlavor::FLAVOR_ID)
            ->whereIn('status', ['enabled'])
            ->whereBetween('date', [$start->toDateString(), $end->toDateString()])
            ->select('date')
            ->selectRaw('SUM(active_rentals) as active_rentals')
            ->selectRaw('count(*) as active_teams')
            ->groupBy(['date'])
            ->get();

        $periodDates = $rawDateData->pluck('date');
        $totalDays = 0;
        $totalPriceRentals = 0;
        foreach ($periodDates as $date) {
            $dayData = $rawDateData->firstWhere('date', '=', $date);
            $pricePerThisDay = self::calculatePriceAccordingToTotalCount($dayData->active_rentals);
            $dateDetails[] = [
                'date' => $date,
                'rentals' => $dayData->active_rentals,
                'active_teams' => $dayData->active_teams,
                'price' => round($pricePerThisDay, 2),
            ];

            $totalPriceRentals += $pricePerThisDay;
            $totalDays++;
        }
        $totalPriceRentals = round($totalPriceRentals, 2);

        $tokeetInvoicing = "[TOKEET INVOICING] A total of $totalAccounts accounts have been using power reporting from  {$start->toDateString()} to {$end->toDateString()} with a total of $totalPriceRentals daily invoiced rentals.
            This $totalDays days generates a total income of $totalPriceRentals €";
        RentalNinjaTeam::slackNotification($tokeetInvoicing);

        // Generate PDF:
        $dompdf = GetDomPdfAction::run();

        // Create the view:
        $view = view(
            view: 'ninja.kiosk.pdf-booking-sync-accounting.tokeet_accounting',
            data: [
                'start' => $start->toDateString(),
                'end' => $end->toDateString(),
                'teams' => $teamDetails,
                'dates' => $dateDetails,

                'numAccounts' => $totalAccounts,
                'numDays' => $totalDays,
                'finalPrice' => $totalPriceRentals,
            ]
        );

        $dompdf->loadHtml($view->render());
        $dompdf->render();
        $pdf_output = $dompdf->output();

        // Create a zip, put it in amazon and send the url by email.
        //Note: commands can't return anything unless a message to print in screen.
        $name = 'Tokeet-RN_accounting.zip';
        $zipper = NinjaZipper::create($name);
        $file_name = 'Tokeet - RN Accounting from '.$start->toDateString().' to '.$end->toDateString().'.pdf';
        $zipper->addString($file_name, $pdf_output);

        $amazon_path = "tokeet-rn-accounting/{$start->format('Y-m')}/$name";

        // Send the full path via email to the accounting email:
        $zipper->store($amazon_path);
        $notification = new AccountingRentalNinjaNotification($amazon_path, 'Tokeet');
        $team = RentalNinjaTeam::getInstance();
        $team->email = '<EMAIL>';
        $team->notifyNow($notification);
    }

    public static function calculatePriceAccordingToTotalCount(int $totalRentals): float
    {
        $amount = 0;
        $invoicedRentals = 0;
        foreach (self::PRICES as $max => $price) {
            $dailyPrice = $price * 12 / 365;
            $rentalsInRange = max(min($totalRentals, $max) - $invoicedRentals, 0);
            $amount += $dailyPrice * $rentalsInRange;
            $invoicedRentals += $rentalsInRange;
        }

        return $amount;
    }
}
