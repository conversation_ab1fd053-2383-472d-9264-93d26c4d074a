<?php

namespace App\Actions\KPIs;

use App\Models\CarbonPeriod;
use App\Models\Team;
use Illuminate\Bus\Batch;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Bus;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DispatchIndicatorsGenerationAction
{
    use AsAction;

    public string $commandSignature = 'ninja:dispatch-gen-kpis {from=1} {to=0} {--daily} {--monthly} {--revenue}';

    public string $commandDescription = 'Dispatches a Batch of Jobs that Generate Revenue Performance Indicators.';

    private string $today;

    private ?CarbonPeriod $range = null;

    private ?Command $command = null;

    public function __construct()
    {
        $this->today = now()->toDateString();
    }

    /**
     * @throws Throwable
     */
    public function asCommand(Command $command): int
    {
        $from = now()->subDays($command->argument('from'))->startOfDay();
        $to = now()->subDays($command->argument('to'))->endOfDay();
        $this->handle(
            from: $from,
            to: $to,
            calculateDaily: $command->option('daily'),
            calculateMonthly: $command->option('monthly'),
            calculateRevenue: $command->option('revenue')
        );

        return $command::SUCCESS;
    }

    /**
     * Execute the action and return a result.
     *
     * @throws Throwable
     */
    public function handle(
        Carbon $from,
        Carbon $to,
        bool $calculateDaily = true,
        bool $calculateMonthly = true,
        bool $calculateRevenue = true
    ): void {
        $teams = Team::query()
            ->where('flavor_id', '!=', 2) // Exclude Lodgify flavor, which is not even created.
            ->get();
        if ($teams->isEmpty()) {
            return;
        }

        $jobs = collect();
        $teams
            ->chunk(1000)
            ->each(function ($teams) use ($calculateMonthly, $calculateDaily, $jobs, $from, $to) {
                $chunk = collect()
                    ->when($calculateDaily, function ($chunk) use ($teams, $from, $to) {
                        $teamIds = collect($teams->pluck('id')->toArray());

                        return $chunk->push(DailyIndicatorsAction::makeJob($teamIds, $from, $to));
                    }
                    )
                    ->when($calculateMonthly, function ($chunk) use ($teams, $from, $to) {
                        $teamIds = collect($teams->pluck('id')->toArray());

                        return $chunk->push(MonthlyIndicatorsAction::makeJob($teamIds, $from, $to));
                    }
                    );
                $jobs->when($chunk->isNotEmpty(), fn ($jobs) => $jobs->add($chunk->toArray()));
            });

        if ($jobs->isEmpty()) {
            return;
        }

        // We calculate dates out here because
        // serialization fails if we pass them as arguments
        $f = $from->toDateTimeString();
        $t = $to->toDateTimeString();

        Bus::batch($jobs->toArray())
            ->onConnection('redis')
            ->onQueue('kpis')
            ->name('KPIs Generation')
            ->allowFailures(false)
            ->then(fn (Batch $batch) => RevenueIndicatorsAction::dispatchIf($calculateRevenue, $f, $t))
            ->dispatch();
    }
}
