<?php

namespace App\Actions\Pictures;

use App\Models\ApiUser;
use App\Models\Team;
use App\Models\User;
use Lorisleiva\Actions\Concerns\AsAction;
use Str;

class HandleTeamOrUserProfilePictureAction
{
    use AsAction;

    /**
     * Execute the action and return a result.
     */
    public function handle(Team|User|ApiUser $model, ?string $photoUrlValue)
    {
        // Warning, you can't call $model->photo_url here because this is used to get photo_url, thus you would create a loop
        // The null case:
        if (empty($photoUrlValue)) {
            if (! empty($model->name)) {
                $avatarInitials = $model->name;
            } elseif (! empty($model->email)) {
                $avatarInitials = $model->email;
            } else {
                $avatarInitials = Str::random(5);
            }

            return $model->config()->defaultTeamAndUserPhotoUrl($avatarInitials);
        }

        // The rest should be images from imgix (both firebase or backend):
        if ($model instanceof Team) {
            return $photoUrlValue.'?fit=crop&w=200&h=200';
        } else {
            return $photoUrlValue.'?fit=crop&w=150&h=150&crop=faces';
        }
    }
}
