<?php

namespace App\Actions\Stats\RevenueGenerated;

use App\Actions\Stats\StatsBaseClass;
use App\DTO\Statistics\DashboardDataResource;
use App\DTO\Statistics\StatisticsFilter;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetDetailsRevenueGeneratedStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team);

        return $this->handle($filter);
    }

    public function handle(StatisticsFilter $filter): array
    {
        $currencies = $this->currencies($filter, self::BOOKING);

        $tr = [];

        foreach ($currencies as $currency) {
            $impl = new DashboardDataResource;
            $impl->currency = $currency;
            $query = DB::table('booking')
                       ->selectRaw('SUM(final_price) as value')
                       ->selectRaw('DATE_FORMAT(FROM_UNIXTIME(`created_at`), "%Y-%m-%d") AS date')
                       ->where('booking.team_id', $filter->team_id)
                       ->where('currency', $currency)
                       ->whereIn('status', $filter->getFilterStatuses())
                       ->whereNotNull('final_price')
                       ->whereIn('booking.rental_id', $filter->filteredRentalsForUser())
                       ->groupBy('date')
                       ->orderBy('date');
            $impl->periods = $this->fillDashboardWithPeriods('booking', $filter, $query, 'created_at');
            $tr[] = $impl;
        }

        return $this->toReturn($filter, $tr);
    }
}
