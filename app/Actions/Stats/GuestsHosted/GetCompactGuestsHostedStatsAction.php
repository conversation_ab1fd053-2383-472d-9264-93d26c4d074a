<?php

namespace App\Actions\Stats\GuestsHosted;

use App\Actions\Stats\StatsBaseClass;
use App\DTO\Statistics\KpiDataResource;
use App\DTO\Statistics\StatisticsFilter;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCompactGuestsHostedStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team);

        return $this->handle($filter);
    }

    public function handle(StatisticsFilter $filter): array
    {
        $kpi = new KpiDataResource;

        $kpi->compare = DB::table('booking')
            ->selectRaw('IFNULL(SUM(IFNULL(IFNULL(adults, 0) + IFNULL(children, 0), 0)), 0) as value')
            ->where('booking.team_id', $filter->team_id)
            ->whereIn('status', $filter->getFilterStatuses())
            ->whereIn('rental_id', $filter->filteredRentalsForUser())
            ->where('end_at', '>=', $filter->secondPeriod()->start()->timestamp)
            ->where('start_at', '<=', $filter->secondPeriod()->end()->timestamp)
            ->first()->value;

        $kpi->total = DB::table('booking')
            ->selectRaw('IFNULL(SUM(IFNULL(IFNULL(adults, 0) + IFNULL(children, 0), 0)), 0) as value')
            ->where('booking.team_id', $filter->team_id)
            ->whereIn('status', $filter->getFilterStatuses())
            ->whereIn('rental_id', $filter->filteredRentalsForUser())
            ->where('end_at', '>=', $filter->firstPeriod()->start()->timestamp)
            ->where('start_at', '<=', $filter->firstPeriod()->end()->timestamp)
            ->first()->value;

        $query = DB::table('booking')
            ->selectRaw('IFNULL(SUM(IFNULL(IFNULL(adults, 0) + IFNULL(children, 0), 0)), 0) as value')
            ->where('booking.team_id', $filter->team_id)
            ->whereIn('status', $filter->getFilterStatuses())
            ->whereIn('rental_id', $filter->filteredRentalsForUser());

        $this->fillIntervals($filter, $kpi, $query, 'start_at', 'end_at', true);

        return $this->toReturn($filter, [$kpi]);
    }
}
