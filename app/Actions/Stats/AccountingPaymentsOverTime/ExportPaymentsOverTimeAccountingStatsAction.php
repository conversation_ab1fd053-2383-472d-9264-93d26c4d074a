<?php

namespace App\Actions\Stats\AccountingPaymentsOverTime;

use App\Actions\Stats\AccountingStatsBaseClass;
use App\DTO\Statistics\AccountingStatisticsFilter;
use App\Exports\DashboardExport;
use App\Models\Team;
use App\Models\User;
use App\Notifications\ExcelExportNotification;
use App\Support\SaveExcel;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;

class ExportPaymentsOverTimeAccountingStatsAction extends AccountingStatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): Response
    {
        $user = $request->user();
        $filter = $this->filter($request, $team);

        $this->handle($filter, $team, $user);

        return response('');
    }

    public function handle(AccountingStatisticsFilter $filter, Team $team, User $user): void
    {
        $data = GetDetailsPaymentsOverTimeAccountingStatsAction::run($filter);
        $export = new DashboardExport($data, 'Money');

        $user->notify(new ExcelExportNotification(SaveExcel::url($team, $export, 'payments_over_time')));
    }
}
