<?php

namespace App\Actions\Stats\AverageBookingLength;

use App\Actions\Stats\StatsBaseClass;
use App\DTO\Statistics\DashboardDataResource;
use App\DTO\Statistics\StatisticsFilter;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetDetailsAverageBookingLengthStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team);

        return $this->handle($filter);
    }

    public function handle(StatisticsFilter $filter): array
    {
        $tr = [];

        $impl = new DashboardDataResource;
        $query = DB::table('booking')
            ->selectRaw('SUM(nights) as value, COUNT(*) as count')
            ->selectRaw('DATE_FORMAT(FROM_UNIXTIME(`start_at`), "%Y-%m-%d") AS date')
            ->where('booking.team_id', $filter->team_id)
            ->whereIn('booking.status', $filter->getFilterStatuses())
            ->whereNotNull('nights')
            ->whereIn('booking.rental_id', $filter->filteredRentalsForUser())
            ->groupBy('date')
            ->orderBy('date');

        $impl->periods = $this->fillAverageDashboardWithPeriods('booking', $filter, $query, 'start_at');
        $tr[] = $impl;

        return $this->toReturn($filter, $tr);
    }
}
