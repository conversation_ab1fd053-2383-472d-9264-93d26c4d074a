<?php

namespace App\Actions\Stats\PaymentsReceived;

use App\Actions\Stats\StatsBaseClass;
use App\DTO\Statistics\DashboardDataResource;
use App\DTO\Statistics\StatisticsFilter;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetDetailsPaymentsReceivedStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team)->shouldAdjustToUserTimeZone();

        return $this->handle($filter);
    }

    public function handle(StatisticsFilter $filter): array
    {
        $currencies = $this->currencies($filter, self::PAYMENT);
        $tr = [];
        foreach ($currencies as $currency) {
            $impl = new DashboardDataResource();
            $impl->currency = $currency;
            $query = DB::table('booking_payments', 'payment')
                ->rightJoin('booking', 'payment.booking_id', '=', 'booking.id')
                ->selectRaw('SUM(payment.amount_in_cents / 100) as value')
                ->selectTimeZoneDate('payment.paid_at', true)
                ->where('payment.team_id', $filter->team_id)
                ->where('payment.currency', $currency)
                ->whereNull('payment.canceled_at')
                ->whereNotNull('payment.amount_in_cents')
                ->whereIn('booking.rental_id', $filter->filteredRentalsForUser())
                ->where('booking.team_id', $filter->team_id)
                ->whereIn('booking.status', $filter->getFilterStatuses())
                ->groupBy('date')
                ->orderBy('date');

            $impl->periods = $this->fillDashboardWithPeriods('booking', $filter, $query, 'payment.paid_at', true);
            $tr[] = $impl;
        }

        return $this->toReturn($filter, $tr);
    }
}
