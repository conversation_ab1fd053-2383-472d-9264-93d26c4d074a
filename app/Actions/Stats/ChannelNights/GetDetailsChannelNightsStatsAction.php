<?php

namespace App\Actions\Stats\ChannelNights;

use App\Actions\Stats\StatsBaseClass;
use App\DTO\Statistics\DashboardDataResource;
use App\DTO\Statistics\StatisticsFilter;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetDetailsChannelNightsStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team);

        return $this->handle($filter);
    }

    public function handle(StatisticsFilter $filter): array
    {
        $tr = [];
        $impl = new DashboardDataResource;
        $impl->type = 'doughnut';
        $query = DB::table('booking')
            ->rightJoin('source', 'booking.source_id', '=', 'source.id')
            ->selectRaw('sum(nights) as value')
            ->selectRaw("IF(source.name = 'OH', 'Website', source.name) as channel")
            ->where('booking.team_id', $filter->team_id)
            ->where('source.team_id', $filter->team_id)
            ->whereIn('booking.status', $filter->getFilterStatuses())
            ->whereNotNull('booking.source_id')
            ->whereIn('booking.rental_id', $filter->filteredRentalsForUser())
            ->groupBy('booking.source_id', 'source.name')
            ->orderBy('value', 'desc');

        $impl->periods = $this->fillDoughnut($filter, $query, 'start_at');
        $impl->sortedValues();
        $tr[] = $impl;

        return $this->toReturn($filter, $tr);
    }
}
