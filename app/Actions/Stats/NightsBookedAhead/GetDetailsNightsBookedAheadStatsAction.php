<?php

namespace App\Actions\Stats\NightsBookedAhead;

use App\Actions\Stats\StatsBaseClass;
use App\Actions\Support\Dates\GetDateIntervalAction;
use App\DTO\Statistics\DashboardDataResource;
use App\DTO\Statistics\DashboardPeriodResource;
use App\DTO\Statistics\DashboardRentalData;
use App\DTO\Statistics\StatisticsFilter;
use App\Models\CarbonPeriod;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetDetailsNightsBookedAheadStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team);

        return $this->handle($filter);
    }

    public function handle(StatisticsFilter $filter): array
    {
        $tr = [];
        foreach ($filter->getPeriods() as $carbon_period) {
            $from = $carbon_period->start();
            $to = $carbon_period->end();
            $interval = GetDateIntervalAction::run($from, $to);
            $period = new DashboardPeriodResource;
            $period->total = 0;
            $period->labels = [];
            $period->values = [];

            $carbon_period->each($interval, function (CarbonPeriod $range) use (&$filter, &$period) {
                $end = $range->end()->timestamp;
                $start = $range->start()->timestamp;
                $query = DB::table('booking')
                    ->selectRaw(
                        'SUM(IF(start_at >= ?, nights, DATEDIFF(DATE_FORMAT(FROM_UNIXTIME(end_at), "%Y-%m-%d"), DATE_FORMAT(FROM_UNIXTIME(?), "%Y-%m-%d")))) as nights',
                        [$start, $start]
                    )
                    ->where('team_id', $filter->team_id)
                    ->whereIn('rental_id', $filter->filteredRentalsForUser())
                    ->where('created_at', '<=', $end)
                    ->where('end_at', '>=', $start)
                    ->whereIn('status', $filter->getFilterStatuses());

                $value = $query->first()->nights;
                $period->values[] = $value;
                $r = clone $range;
                if ($range->end()
                    ->diffInDays($range->start()) >= 1 && ! $range->end()
                    ->isLastOfMonth()) {
                    $r = CarbonPeriod::instance($r->start(), $r->end()
                        ->subDay()
                        ->endOfDay());
                }

                $period->labels[] = $this->labelForPeriod($r);
            });

            $total = last($period->values);
            $period->total = empty($total) ? 0 : $total;
            $period->from_to_labels = $this->labelForPeriod($carbon_period);
            $period->label = $this->labelForPeriod($carbon_period);

            // We use $to and $to->startOfDay to calculate the report at the last day of the period.
            // This is coherent on the data shown on '$total = last($period->values)' and can be understood as "Nights booked ahead on the last day of the period"
            // Another option would be not to include the last day of the period in the calculation, but if the calculation is coherent among reports it does not matter.
            foreach ($filter->filteredRentalsForUser() as $rental) {
                $endOfDay = $to->timestamp;
                $startOfDay = $to->startOfDay()->timestamp;
                $w = DB::table('booking')
                    ->join('rental', fn (JoinClause $join) => $join->on('booking.rental_id', '=', 'rental.id')->whereColumn('booking.team_id', '=', 'rental.team_id'))
                    ->select('rental.name as name')
                    ->selectRaw(
                        'IFNULL(SUM(IFNULL(IF(booking.start_at >= ?, booking.nights, DATEDIFF(DATE_FORMAT(FROM_UNIXTIME(booking.end_at), "%Y-%m-%d"), DATE_FORMAT(FROM_UNIXTIME(?), "%Y-%m-%d"))), 0)), 0) as nights',
                        [$startOfDay, $startOfDay]
                    )
                    ->where('booking.team_id', $filter->team_id)
                    ->where('rental.team_id', $filter->team_id)
                    ->where('booking.rental_id', $rental)
                    ->where('booking.created_at', '<=', $endOfDay)
                    ->where('booking.end_at', '>=', $startOfDay)
                    ->whereIn('booking.status', $filter->getFilterStatuses());

                $w = $w->groupBy('booking.rental_id', 'rental.name')
                    ->first();

                $rental_data = new DashboardRentalData();
                if (! empty($w)) {
                    $rental_data->total = $w->nights;
                    $rental_data->label = $w->name;
                } else {
                    $rental_data->total = 0;
                    $rental_data->label = Rental::whereId($rental)
                        ->onTeam($filter->team_id)
                        ->select('name')
                        ->first()->name;
                }
                $period->rentals[] = $rental_data;
            }
            $tr[] = $period;
        }
        $impl = new DashboardDataResource;
        $impl->periods = $tr;

        foreach ($impl->periods as $period) {
            usort($period->rentals, function ($a, $b) {
                return $b->total - $a->total;
            });
        }

        return $this->toReturn($filter, [$impl]);
    }
}
