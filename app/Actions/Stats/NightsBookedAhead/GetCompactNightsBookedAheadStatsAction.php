<?php

namespace App\Actions\Stats\NightsBookedAhead;

use App\Actions\Stats\StatsBaseClass;
use App\Actions\Support\Dates\GetDateIntervalAction;
use App\DTO\Statistics\KpiDataResource;
use App\DTO\Statistics\StatisticsFilter;
use App\Models\CarbonPeriod;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCompactNightsBookedAheadStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team);

        return $this->handle($filter);
    }

    public function handle(StatisticsFilter $filter): array
    {
        $kpi = new KpiDataResource;
        $second = $filter->secondPeriod();
        $comp_interval = GetDateIntervalAction::run($second->start(), $second->end());
        $comp_start = 0;
        $comp_end = 0;
        $second->each($comp_interval, function (CarbonPeriod $comp_range) use (&$comp_start, &$comp_end) {
            $comp_start = $comp_range->start()->timestamp;
            $comp_end = $comp_range->end()->timestamp;
        });
        $cmp_nights = DB::table('booking')
            ->selectRaw(
                'SUM(IF(start_at >= ?, nights, DATEDIFF(DATE_FORMAT(FROM_UNIXTIME(end_at), "%Y-%m-%d"), DATE_FORMAT(FROM_UNIXTIME(?), "%Y-%m-%d")))) as nights',
                [$comp_start, $comp_start]
            )
            ->where('team_id', $filter->team_id)
            ->whereIn('rental_id', $filter->filteredRentalsForUser())
            ->where('created_at', '<=', $comp_end)
            ->where('end_at', '>=', $comp_start)
            ->whereIn('status', $filter->getFilterStatuses())
            ->first()->nights;
        $kpi->compare = $cmp_nights;
        $filter
            ->period()
            ->each($filter->interval(), function (CarbonPeriod $range) use (&$filter, &$kpi) {
                $start = $range->start()->timestamp;
                $end = $range->end()->timestamp;
                $nights = DB::table('booking')
                    ->selectRaw('SUM(IF(start_at >= ?, nights, DATEDIFF(DATE_FORMAT(FROM_UNIXTIME(end_at), "%Y-%m-%d"), DATE_FORMAT(FROM_UNIXTIME(?), "%Y-%m-%d")))) as nights',
                        [$start, $start])
                    ->where('team_id', $filter->team_id)
                    ->whereIn('rental_id', $filter->filteredRentalsForUser())
                    ->where('created_at', '<=', $end)
                    ->where('end_at', '>=', $start)
                    ->whereIn('status', $filter->getFilterStatuses())
                    ->first()->nights;

                $kpi->values[] = $nights;
                $kpi->labels[] = $this->label($filter);
            });

        $total = last($kpi->values);
        $kpi->total = empty($total) ? 0 : $total;

        return $this->toReturn($filter, [$kpi]);
    }
}
