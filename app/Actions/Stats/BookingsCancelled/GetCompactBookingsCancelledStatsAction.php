<?php

namespace App\Actions\Stats\BookingsCancelled;

use App\Actions\Stats\StatsBaseClass;
use App\DTO\Statistics\KpiDataResource;
use App\DTO\Statistics\StatisticsFilter;
use App\Enum\BookingStatusEnum;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCompactBookingsCancelledStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team)->shouldAdjustToUserTimeZone();

        return $this->handle($filter);
    }

    public function handle(StatisticsFilter $filter): array
    {
        $kpi = new KpiDataResource;
        $kpi->is_reversed = true;
        $query = DB::table('booking')
            ->selectRaw('IFNULL(Count(id), 0) AS value')
            ->where('team_id', $filter->team_id)
            ->whereColumn('created_at', '<=', 'end_at')
            ->whereIn('rental_id', $filter->filteredRentalsForUser())
            ->where('canceled_at', '>', 0)
            ->where('status', BookingStatusEnum::CANCELED);
        $kpi->compare = (clone $query)
            ->whereBetweenTimeZone('canceled_at', $filter->secondPeriodTimestamps(), $filter)
            ->first()->value;
        $kpi->total = (clone $query)
            ->whereBetweenTimeZone('canceled_at', $filter->firstPeriodTimestamps(), $filter)
            ->first()
            ->value;

        $this->fillIntervals($filter, $kpi, $query, 'canceled_at', 'canceled_at');

        return $this->toReturn($filter, [$kpi]);
    }
}
