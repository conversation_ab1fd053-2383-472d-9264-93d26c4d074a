<?php

namespace App\Actions\Stats\AccountingCategoryBreakdown;

use App\Actions\Stats\AccountingStatsBaseClass;
use App\DTO\Statistics\AccountingStatisticsFilter;
use App\DTO\Statistics\KpiDataResource;
use App\Models\Team;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCategoryBreakdownAccountingStatsAction extends AccountingStatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team);

        return $this->handle($filter);
    }

    public function handle(AccountingStatisticsFilter $filter): array
    {
        $currencies = $this->currencies($filter);

        $tr = [];

        foreach ($currencies as $currency) {
            $result = $this->payoutDetailsQuery($filter, $currency, $filter->from, $filter->to)
                ->selectRaw("IF(payout_details.original_type = 'expense', SUM(payout_details.value) * -1, SUM(payout_details.value)) as value")//
                ->selectRaw('payout_details.original_type as type')
                ->groupBy('payout_details.original_type')
                ->orderBy('value', 'desc')
                ->get();

            $data = $this->sqlToArray($result);
            $kpi = new KpiDataResource;
            $kpi->currency = $currency;
            $kpi->type = 'doughnut';
            $kpi->labels = $this->columnFromArray($data, 'type');
            $kpi->values = $this->columnFromArray($data, 'value');
            $kpi->total = array_sum($kpi->values);
            $kpi->currency = $currency;
            $tr[] = $kpi;
        }

        return $this->toReturn($filter, $tr);
    }
}
