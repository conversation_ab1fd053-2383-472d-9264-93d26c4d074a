<?php

namespace App\Actions\Stats\AdvanceBookings;

use App\Actions\Stats\StatsBaseClass;
use App\DTO\Statistics\KpiDataResource;
use App\DTO\Statistics\StatisticsFilter;
use App\Models\Team;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCompactAdvanceBookingsStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): array
    {
        $filter = $this->filter($request, $team)->shouldAdjustToUserTimeZone();

        return $this->handle($filter);
    }

    public function handle(StatisticsFilter $filter): array
    {
        $kpi = new KpiDataResource;
        $query = $this->queryForTotals($filter);
        $kpi->compare = (clone $query)
            ->whereBetweenTimeZone('created_at', $filter->secondPeriodTimestamps(), $filter)
            ->first()
            ->value;

        $kpi->total = (clone $query)
            ->whereBetweenTimeZone('created_at', $filter->firstPeriodTimestamps(), $filter)
            ->first()
            ->value;
        $this->fillIntervals($filter, $kpi, $query, 'created_at', 'created_at');

        return $this->toReturn($filter, [$kpi]);
    }

    private function queryForTotals(StatisticsFilter $filter): Builder
    {
        $q = DB::table('booking')
            ->selectDateDiff('start_at', 'created_at', false, true, $filter)
            ->where('team_id', $filter->team_id)
            ->whereColumn('created_at', '<=', 'end_at')
            ->whereIn('rental_id', $filter->filteredRentalsForUser())
            ->whereIn('status', $filter->getFilterStatuses());

        return $q;
    }
}
