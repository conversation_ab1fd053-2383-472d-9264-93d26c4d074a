<?php

namespace App\Actions\Stats\RevenueFromBookings;

use App\Actions\Stats\StatsBaseClass;
use App\DTO\Statistics\StatisticsFilter;
use App\Exports\DashboardExport;
use App\Models\Team;
use App\Support\SaveExcel;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;

class ExportRevenueFromBookingsStatsAction extends StatsBaseClass
{
    use AsAction;

    public function asController(Team $team, Request $request): Response
    {
        $filter = $this->filter($request, $team);

        $data = $this->handle($filter, $team);

        return response($data);
    }

    public function handle(StatisticsFilter $filter, Team $team): string
    {
        $data = GetDetailsRevenueFromBookingsStatsAction::run($filter);
        $export = new DashboardExport($data, 'Revenue');

        return SaveExcel::url($team, $export, 'revenue');
    }
}
