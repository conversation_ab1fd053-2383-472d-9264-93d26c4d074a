<?php

namespace App\Actions\WebsiteBuilder;

use App\DTO\DistributionWebsite\DistributionWebsiteData;
use App\Models\DistributionWebsite;
use App\Models\Team;
use App\Rules\ExistsInTeamRule;
use Lorisle<PERSON>\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

class DuplicateWebsiteAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'website_id' => ['required', 'integer', new ExistsInTeamRule(DistributionWebsite::class)],
        ];
    }

    public function asController(Team $team, ActionRequest $request): DataCollection
    {
        $sourceWebsite = DistributionWebsite::whereTeamId($team->id)->find($request->input('website_id'));
        $this->handle($team, $sourceWebsite);

        $team->load('distributionWebsites');

        return DistributionWebsiteData::slimCollection($team->distributionWebsites);
    }

    public function handle(Team $team, DistributionWebsite $sourceWebsite): void
    {
        $websiteData = DistributionWebsiteData::from($sourceWebsite);
        $team->distributionWebsites()
            ->create($websiteData->except('domain', 'fathomId', 'fathomPassword', 'googleAdsTagId', 'googleAdsConversionLabel')->all());
    }
}
