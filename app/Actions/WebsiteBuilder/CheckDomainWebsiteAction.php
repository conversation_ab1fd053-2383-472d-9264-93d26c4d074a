<?php

namespace App\Actions\WebsiteBuilder;

use App\DTO\DistributionWebsite\DistributionWebsiteData;
use App\Models\DistributionWebsite;
use App\Models\Team;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class CheckDomainWebsiteAction
{
    use AsAction;

    public function asController(Team $team, DistributionWebsite $distributionWebsite): DistributionWebsiteData
    {
        $this->handle($team, $distributionWebsite);

        return DistributionWebsiteData::from($distributionWebsite)->fillAdditional($distributionWebsite);
    }

    public function handle(Team $team, DistributionWebsite $distributionWebsite): bool
    {
        try {
            $dns = dns_get_record($distributionWebsite->domain, DNS_A);
        } catch (Exception $e) {
            return false;
        }

        $dnsOk = collect($dns)->where('ip', '=', DistributionWebsite::PROXY_HOST)->isNotEmpty();

        // 1. Domain has not been verified yet.
        if ($dnsOk && ! $distributionWebsite->domain_verified) {
            $this->checkApproximate($distributionWebsite);
            $this->createFathomSite($distributionWebsite);
        }
        // 2. Domain has been verified, it is active but has been inactive.
        elseif ($dnsOk && ! $distributionWebsite->domain_status) {
            $distributionWebsite->domain_status = true;
            $distributionWebsite->save();
        }
        // 3. Domain is not active, but it has been active.
        elseif (! $dnsOk && $distributionWebsite->domain_status) {
            $distributionWebsite->domain_status = false;
            $distributionWebsite->save();
        }

        return $dnsOk;
    }

    private function checkApproximate(DistributionWebsite $distributionWebsite): void
    {
        // Check if the domain is already used
        $exists = Http::withHeader('api-key', config('services.approximated.key'))
            ->post('https://cloud.approximated.app/api/vhosts/by/incoming', ['incoming_address' => $distributionWebsite->domain])
            ->collect()
            ->first();

        if ($exists) {
            $distributionWebsite->proxy_id = $exists['id'];
            $distributionWebsite->domain_status = true;
            $distributionWebsite->save();

            return;
        }

        // Create a new virtual host
        $body = [
            'incoming_address' => $distributionWebsite->domain,
            'target_address' => config('app.url').'/website',
            'target_ports' => '443',
        ];
        $response = Http::withHeader('api-key', config('services.approximated.key'))
            ->post('https://cloud.approximated.app/api/vhosts', $body)
            ->throw()
            ->collect()
            ->first();

        if (! is_null($response)) {
            $distributionWebsite->proxy_id = data_get($response, 'id');
            $distributionWebsite->domain_status = true;
            $distributionWebsite->save();
        }
    }

    private function createFathomSite(DistributionWebsite $distributionWebsite): void
    {
        // Creates a phantom site
        $passwrd = Str::random(24);
        $body = [
            'name' => $distributionWebsite->name,
            'sharing' => 'private',
            'share_password' => $passwrd,
        ];
        $response = Http::withToken(config('services.fathom.key'))
            ->post('https://api.usefathom.com/v1/sites', $body)
            ->throw()
            ->collect();

        if (! is_null($response)) {
            $distributionWebsite->fathom_id = data_get($response, 'id');
            $distributionWebsite->fathom_password = $passwrd;
            $distributionWebsite->save();
        }
    }
}
