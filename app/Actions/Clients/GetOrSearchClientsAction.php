<?php

namespace App\Actions\Clients;

use App\Http\Resources\SlimClientResource;
use App\Models\Client;
use App\Models\Team;
use App\Query\ClientQuery;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetOrSearchClientsAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'search' => ['sometimes', 'string'],
        ];
    }

    public function asController(Team $team, ActionRequest $request): AnonymousResourceCollection
    {
        $search = $request->input('search');

        $clients = $this->handle($team, $search);

        return SlimClientResource::collection($clients->paginate(50));
    }

    public function handle(Team $team, ?string $search = null): ClientQuery
    {
        $time = now();
        $data = Client::query()
            ->with(['allBookings.alerts', 'allBookings.source', 'allBookings.preCheckInForm'])
            ->onTeam($team)
            ->when(! is_null($search),
                // When search is provided, we will sort the clients by the number of bookings they have, otherwise we will sort them by their fullname.
                fn (ClientQuery $q) => $q
                    ->where('fullname', 'like', "%$search%")
                    ->withCount('bookings')
                    ->orderBy('bookings_count', 'desc'),
                fn (ClientQuery $q) => $q->orderBy('fullname')
            );

        $time = now()->diffInMilliseconds($time);
        pnLog("[GetOrSearchClientsAction] Time to handle: $time", $team);

        return $data;
    }
}
