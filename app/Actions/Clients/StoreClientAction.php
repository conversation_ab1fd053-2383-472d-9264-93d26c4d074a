<?php

namespace App\Actions\Clients;

use App\DataProviders\Providers\NoProvider;
use App\Http\Resources\ClientResource;
use App\Models\Client;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class StoreClientAction
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->permissionFor('can_manage_clients');
    }

    public function rules(): array
    {
        return [
            'firstname' => ['required', 'string'],
            'lastname' => ['nullable', 'string'],
            'notes' => ['nullable', 'string'],
            'address' => ['nullable', 'string'],
            'email' => ['nullable', 'email'],
            'phone' => ['nullable', 'string'],
            'preferred_locale' => ['nullable', 'string', 'size:2'],
        ];
    }

    public function asController(Team $team, ActionRequest $request): ClientResource
    {
        $firstname = $request->input('firstname');
        $lastname = $request->input('lastname');
        $notes = $request->input('notes');
        $address = $request->input('address');
        $email = $request->input('email');
        $phone = $request->input('phone');
        $locale = $request->input('preferred_locale');

        $client = $this->handle($team, $firstname, $lastname, $notes, $address, $email, null, $phone, null, $locale);

        return new ClientResource($client);
    }

    public function handle(Team $team, ?string $firstname = null, ?string $lastname = null, ?string $notes = null, ?string $address = null, ?string $email = null, ?string $emailLabel = null, ?string $phone = null, ?string $phoneLabel = null, ?string $locale = null): Client
    {
        $nextId = (Client::query()->whereNotProvider()->onTeam($team)->orderByDesc('id')->first()?->id ?? 0) + 1;

        return Client::query()->create([
            'id' => $nextId,
            'team_id' => $team->id,
            'provider_id' => NoProvider::ID,
            'firstname' => $firstname,
            'lastname' => $lastname,
            'fullname' => $this->composeFullName($firstname, $lastname),
            'notes' => $notes,
            'preferred_locale' => $locale,
            'addresses' => null, //json_encode($address),
            'emails' => $email ? $this->getEmails($email, $emailLabel) : null,
            'phones' => $phone ? $this->getPhones($phone, $phoneLabel) : null,
            'created_at' => now()->timestamp,
            'updated_at' => now()->timestamp,
        ]);
    }

    private function composeFullName(?string $firstName, ?string $lastName): ?string
    {
        $name = '';
        if (! empty($firstName)) {
            $name = $firstName;
        }
        if (! empty($lastName)) {
            $name = $name.' '.$lastName;
        }

        return empty($name) ? null : $name;
    }

    private function getPhones(?string $phone, ?string $phoneLabel): string
    {
        $phone = ValidateClientPhoneAction::run($phone);

        $phone = ! empty($phone) ? [
            ['label' => $phoneLabel ?? Client::INTERNAL_PHONE_LABEL, 'number' => $phone],
        ] : [];

        return json_encode($phone);
    }

    /**
     * Returns the valid emails we have for this guest.
     */
    private function getEmails($email, ?string $emailLabel): string
    {
        $email = ! empty($email) ? [
            [
                'label' => $emailLabel ?? Client::INTERNAL_EMAIL_LABEL,
                'email' => $email,
                'primary' => true,
            ],
        ] : [];

        return json_encode($email);
    }
}
