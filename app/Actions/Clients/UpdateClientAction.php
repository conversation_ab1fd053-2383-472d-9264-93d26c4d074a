<?php

namespace App\Actions\Clients;

use App\Actions\Providers\Generic\ShouldPostToProviderInLocalEnvAction;
use App\DataProviders\ApiConnectors\Interfaces\ClientsApi;
use App\Http\Resources\ClientResource;
use App\Models\Client;
use App\Models\Team;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateClientAction
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->permissionFor('can_manage_clients');
    }

    public function rules(): array
    {
        return [
            'firstname' => ['nullable', 'string'],
            'lastname' => ['nullable', 'string'],
            'notes' => ['nullable', 'string'],
            'address' => ['nullable', 'string'],
            'email' => ['nullable', 'email'],
            'phone' => ['nullable', 'string'],
            'preferred_locale' => ['nullable', 'string', 'size:2'],
        ];
    }

    public function asController(Team $team, Client $client, ActionRequest $request): ClientResource
    {
        $notes = $request->input('notes');
        $firstname = $request->input('firstname');
        $lastname = $request->input('lastname');
        $locale = $request->input('preferred_locale');

        //$address = $request->input('address'); Not used
        $email = $request->input('email');
        $phone = $request->input('phone');

        $client = $this->handle(
            team: $team,
            client: $client,
            firstname: $firstname,
            lastname: $lastname,
            notes: $notes,
            email: $email,
            phone: $phone,
            locale: $locale,
        );

        return new ClientResource($client);
    }

    public function asJob(
        Team $team,
        int $clientId,
        ?string $firstname = null,
        ?string $lastname = null,
        ?string $notes = null,
        ?string $email = null,
        ?string $emailLabel = null,
        ?string $phone = null,
        ?string $phoneLabel = null,
        ?string $locale = null,
    ): void {
        $client = Client::getClientModel($team, $clientId);
        $this->handle($team, $client, $firstname, $lastname, $notes, $email, $emailLabel, $phone, $phoneLabel, $locale);
    }

    public function handle(
        Team $team,
        Client $client,
        ?string $firstname = null,
        ?string $lastname = null,
        ?string $notes = null,
        ?string $email = null,
        ?string $emailLabel = null,
        ?string $phone = null,
        ?string $phoneLabel = null,
        ?string $locale = null,
    ): Client {
        collect()
            ->when($firstname, fn (Collection $collection) => $collection->put('firstname', $firstname))
            ->when($lastname, fn (Collection $collection) => $collection->put('lastname', $lastname))
            ->when($firstname || $lastname, fn (Collection $collection) => $collection->put('fullname', $this->composeFullName($firstname, $lastname)))
            ->when($notes, fn (Collection $collection) => $collection->put('notes', $notes))
            ->when($locale, fn (Collection $collection) => $collection->put('preferred_locale', $locale))
            ->whenNotEmpty(fn (Collection $collection) => $client->fill($collection->toArray())
            );

        if ($email) {
            AddEmailToClientAction::run($client, $email, $emailLabel ?? Client::INTERNAL_EMAIL_LABEL);
        }
        if ($phone) {
            AddPhoneToClientAction::run($client, $phone, $phoneLabel ?? Client::INTERNAL_PHONE_LABEL);
        }
        $client->updated_at = now()->timestamp;
        $client->save();

        // Then try to send it to the provider
        $api = $client->getProviderApiImplementation();
        UpdateClientToProviderAction::dispatchIf(
            $api instanceof ClientsApi && ShouldPostToProviderInLocalEnvAction::run(),
            $team,
            $client->id);

        return $client;
    }

    private function composeFullName(?string $firstName, ?string $lastName): ?string
    {
        $name = '';
        if (! empty($firstName)) {
            $name = $firstName;
        }
        if (! empty($lastName)) {
            $name = $name.' '.$lastName;
        }

        return empty($name) ? null : $name;
    }
}
