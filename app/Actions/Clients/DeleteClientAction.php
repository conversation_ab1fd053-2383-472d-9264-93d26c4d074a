<?php

namespace App\Actions\Clients;

use App\Models\Booking;
use App\Models\Client;
use App\Models\Team;
use Illuminate\Http\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class DeleteClientAction
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->permissionFor('can_manage_clients');
    }

    public function asController(Team $team, Client $client): Response
    {
        $result = $this->handle($client);
        abort_unless($result, 409, 'Client cannot be deleted');

        return response('', 204);
    }

    public function handle(Client $client): bool
    {
        if (Booking::query()->whereClientId($client->id)->exists()) {
            return false;
        }

        return $client->delete();
    }
}
