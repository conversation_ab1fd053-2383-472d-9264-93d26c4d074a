<?php

namespace App\Actions\Auth;

use App\Models\User;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Lorisleiva\Actions\Concerns\AsAction;

class DeleteLoginQrCodeAction implements ShouldBeUnique
{
    use AsAction;

    public int $jobUniqueFor = 600;

    public function getJobUniqueId(User $user): int
    {
        return $user->id;
    }

    public function handle(User $user): void
    {
        if (isLocal() and config('queue.default') == 'sync') {
            return;
        }
        $user->qr_password = null;
        $user->save();
    }
}
