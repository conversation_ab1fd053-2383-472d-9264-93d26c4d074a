<?php

namespace App\Actions\PreCheckIn;

use App\Actions\Auth\VerifyTeamCanUseGuestAppAction;
use App\Actions\Notifications\SendBookingNotificationAction;
use App\Enum\BookingStatusEnum;
use App\Http\Resources\PreCheckInFormResource;
use App\Mail\GuestNotifications\PreCheckinFormEmail;
use App\Models\Booking;
use App\Models\Team;
use Exception;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\Concerns\AsAction;

class SendPreCheckInFormBookingEmailAction
{
    use AsAction;

    /**
     * @throws Exception
     */
    public function asController(Team $team, Booking|int $booking): PreCheckInFormResource|JsonResponse
    {
        if (is_int($booking)) {
            $booking = Booking::getBookingModel($team->id, $booking);
        }

        VerifyTeamCanUseGuestAppAction::run($team);

        // Empty email means: use providerGuestCommunications
        $email = request()->get('email'); // This MUST be null in case of default email
        abort_unless($booking->client != null || $email != null, 400, 'Booking has NO client. Please add a client first.');

        $success = $this->handle($team, $booking, $email);

        abort_unless($success, 400, 'Could not send the pre-check-in form email');

        return GetPreCheckInBookingAction::make()->asController($team, $booking->id);
    }

    /**
     * @throws Exception
     */
    public function handle(Team $team, Booking $booking, ?string $email): bool
    {
        $form = $booking->preCheckInForm;
        $client = $booking->client;

        if ($form == null || empty($client) || $booking->status != BookingStatusEnum::BOOKED) {
            return false;
        }

        // Send the email according to the preCheck-in form status. If we have an e-mail, we use it. Otherwise, we attempt to send it thorough provider
        if ($form->completed === false) {
            $notificationEmail = new PreCheckinFormEmail($form);
            $method = SendBookingNotificationAction::run($team, $booking, $notificationEmail, $email);
            // If email is default, $email will be null, thus SendBookingNotificationAction will try to use the API if possible
            if (is_null($method)) {
                return false;
            }
            MarkAndNotifyGuestPreCheckInFormSentAction::run($form, $team, method: $method);
        } else {
            SendPrecheckInFormCompletedEmailAction::run($team, $booking, $form, $client, $email);
        }

        return true;
    }
}
