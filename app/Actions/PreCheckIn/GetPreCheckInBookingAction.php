<?php

namespace App\Actions\PreCheckIn;

use App\Actions\Auth\VerifyUserHasAccessToBookingAction;
use App\Http\Resources\PreCheckInFormResource;
use App\Models\Booking;
use App\Models\PreCheckInForm;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class GetPreCheckInBookingAction
{
    use AsAction;

    public function asController(Team $team, Booking|int $booking): PreCheckInFormResource
    {
        if (is_int($booking)) {
            $booking = Booking::getBookingModel($team->id, $booking);
        }
        VerifyUserHasAccessToBookingAction::run($booking);
        $preCheckIn = $this->handle($team, $booking);

        return new PreCheckInFormResource($preCheckIn);
    }

    public function handle(Team $team, Booking $booking): PreCheckInForm
    {
        $form = PreCheckInForm::query()
            ->onTeam($team)
            ->fromBooking($booking->id);

        // If it doesn't exist... we should create it, right?
        if (! $form->exists()) {
            PreCheckInForm::createAndSaveFor($team, $booking);
        }

        $form = PreCheckInForm::query()
            ->onTeam($team)
            ->fromBooking($booking->id)
            ->with(['booking', 'passports']);

        return $form->first();
    }
}
