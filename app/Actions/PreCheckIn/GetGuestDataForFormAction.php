<?php

namespace App\Actions\PreCheckIn;

use App;
use App\Actions\Auth\EnsureAccessAllowedBasedOnProviderAction;
use App\Actions\Guests\GetGuestsPortalAutologinAction;
use App\Actions\Guests\GuestsPortalRoutesBaseAction;
use App\Actions\Guests\SetGuestsPortalRequestLocaleAction;
use App\Http\Resources\GuestsPortal\GuestsPortalBookingResource;
use App\Http\Resources\GuestsPortal\GuestsPortalPreCheckInFormResource;
use App\Http\Resources\GuestsPortal\GuestsPortalRentalResource;
use App\Http\Resources\GuestsPortal\GuestsPortalSettingsResource;
use App\Http\Resources\SlimTeamResource;
use App\Http\Resources\TeamSettingsResource;
use App\Models\GuestsApplicationRentalSettings;
use App\Models\Rental;
use App\Models\RentalLegalDetails;
use App\Models\TeamSettings;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Lorisleiva\Actions\ActionRequest;

class GetGuestDataForFormAction extends GuestsPortalRoutesBaseAction
{
    public function handle(ActionRequest $request, string $reference): array|JsonResponse|RedirectResponse
    {
        // 1-Check if the booking is valid.
        $bookingModel = $this->getBookingFromRequest($request, $reference, ['preCheckInForm.passports', 'client']);
        abort_unless($bookingModel->guestApplicationRentalSettings->pre_checkin, 400, 'Precheck-in form is not activated for this Rental.');

        $formModel = $bookingModel->preCheckInForm;
        $team = $bookingModel->team;
        $rentalModel = $bookingModel->rental;
        $client = $bookingModel->client;

        // 2- Ensure guests from other providers don't access to the wrong portal
        EnsureAccessAllowedBasedOnProviderAction::run($team);

        // 3- Set the correct locale
        SetGuestsPortalRequestLocaleAction::run($request, $formModel);

        // 4- If the request doesn't expect a jason, we don't need to go deeper
        if (! request()->expectsJson()) {
            return redirect()->away(GetGuestsPortalAutologinAction::run($team, $bookingModel));
        }

        // 5- Get all settings needed
        $guestApplicationSettings = $team->guestApplicationSettings;
        $rentalSettings = $rentalModel->guestsApplicationRentalSettings;
        $teamSettings = TeamSettings::firstOrCreate(['team_id' => $team->id]);

        return [
            'locale' => $client?->preferred_locale ?? App::getLocale(),
            'model' => new GuestsPortalPreCheckInFormResource($formModel),
            'team' => new SlimTeamResource($team),
            'booking' => new GuestsPortalBookingResource($bookingModel),
            'rental' => new GuestsPortalRentalResource($rentalModel),
            'settings' => $guestApplicationSettings ? new GuestsPortalSettingsResource($guestApplicationSettings) : null,
            'rentalSettings' => $this->getRentalSettings($rentalSettings),
            'guest_portal_enabled' => $guestApplicationSettings != null && $rentalSettings->guest_app,
            'teamSettings' => new TeamSettingsResource($teamSettings), // TODO: remove this. It is only used the value model_179 and now is legacy. Remove while adding new SESHospedajes fields
            'dropdownData' => $this->dropdownData(),
            'rentalEarliestArrivalTime' => $this->getDefaultArrivalTimeForRental($rentalModel),
            'rentalLatestArrivalTime' => $rentalModel->max_checkin_time,
            'teamEmail' => $team->email,
        ];
    }

    public function dropdownData(): array
    {
        $towns = RentalLegalDetails::codigoMunicipio();
        $towns_by_province = [];

        foreach ($towns as $key => $value) {
            $province = substr($key, 0, 2);
            $towns_by_province[$province][$key] = $value;
        }

        return [
            'sitaucion' => RentalLegalDetails::dropdownSituacion(),
            'municipio' => $towns_by_province,
            'provincia' => RentalLegalDetails::codigoProvincia(),
            'tipo_via' => RentalLegalDetails::tipoVia(),
            'tipo_num' => RentalLegalDetails::tipoNumeracion(),
            'country_code' => RentalLegalDetails::countryCode(),
        ];
    }

    protected function getDefaultArrivalTimeForRental(?Rental $rentalModel): ?int
    {
        $rentalEarliestArrivalTime = $rentalModel->checkin_time;
        $rentalEarliestArrivalTime = is_int($rentalEarliestArrivalTime) && $rentalEarliestArrivalTime > 0 ? $rentalEarliestArrivalTime : null;
        if ($rentalEarliestArrivalTime != null) {
            while ($rentalEarliestArrivalTime > 23) {
                $rentalEarliestArrivalTime -= 24;
            }
        }

        return $rentalEarliestArrivalTime;
    }

    public function getRentalSettings(GuestsApplicationRentalSettings $settings): array
    {
        return [
            'require_passport_number' => $settings->require_passport_number,
            'require_passport_upload' => $settings->require_passport_upload,
            'require_passports_all_guests' => $settings->require_passports_all_guests,
            'accept_any_document' => $settings->accept_any_document,
            'scan_documents' => $settings->scan_documents,
            'extra_information_guests' => $settings->extra_information_guests,
            'main_postal_address' => $settings->main_postal_address,
            'guests_postal_address' => $settings->guests_postal_address,
            'request_signature' => $settings->request_signature,
        ];
    }
}
