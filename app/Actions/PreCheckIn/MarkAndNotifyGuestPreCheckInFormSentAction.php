<?php

namespace App\Actions\PreCheckIn;

use App\Enum\NotificationMethodEnum;
use App\Enum\NotificationTypeEnum;
use App\Events\GuestNotificationSentEvent;
use App\Models\PreCheckInForm;
use App\Models\Team;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class MarkAndNotifyGuestPreCheckInFormSentAction
{
    use AsAction;

    public function handle(PreCheckInForm $form, Team $team, ?int $iteration = null, NotificationMethodEnum $method = NotificationMethodEnum::EMAIL): void
    {
        if ($method === NotificationMethodEnum::EMAIL) {
            // For manually sent emails, we don't save the date in the automatic emails fields:
            if ($iteration != null) {
                match ($iteration) {
                    1 => $form->first_email = now(),
                    2 => $form->second_email = now(),
                    3 => $form->third_email = now(),
                    4 => $form->last_reminder = now(),
                };
            }

            $sent = $form->emails_sent;
            if ($sent == null) {
                $sent = [];
            }
            $sent[] = now()->toIso8601ZuluString();
            $form->emails_sent = $sent;
        } elseif ($method === NotificationMethodEnum::SMS) {
            $sent = $form->sms_sent;

            if ($sent == null) {
                $sent = [];
            }
            $sent[] = now()->toIso8601ZuluString();
            $form->sms_sent = $sent;
        }

        // New method
        $communicationsSent = $form->communications_sent ?? [];
        $communicationsSent[] = [
            'type' => $method,
            'time' => now()->toIso8601ZuluString(),
            'trigger' => is_null($iteration) ? 'manual' : 'automatic',
        ];
        $form->communications_sent = $communicationsSent;

        $form->save();

        // New event
        $notificationType = NotificationTypeEnum::PCIF;
        event(new GuestNotificationSentEvent($team, $form->booking_id, $notificationType, $method));
    }
}
