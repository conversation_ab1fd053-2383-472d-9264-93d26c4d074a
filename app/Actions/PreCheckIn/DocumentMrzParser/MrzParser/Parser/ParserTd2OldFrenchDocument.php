<?php

namespace App\Actions\PreCheckIn\DocumentMrzParser\MrzParser\Parser;

use App\Actions\PreCheckIn\DocumentMrzParser\MrzParser\Exception\CheckDigitFailedException;
use App\Models\Country;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ParserTd2OldFrenchDocument extends Parser
{
    protected function type(): string
    {
        return $this->cleanString($this->firstLine(1));
    }

    protected function issuingCountry(): Country
    {
        $country = $this->cleanString($this->firstLine(2));

        return $this->parseCountry($country);
    }

    protected function lastName(): string
    {
        $fullName = $this->firstLine(3);
        $firstName = Str::of($fullName)->before('<<')->trim()->__toString();

        return $this->cleanString($firstName);
    }

    protected function firstName(): string
    {
        $fullName = $this->secondLine(3);
        $lastName = Str::of($fullName)->before('<<')->trim()->__toString();

        return $this->cleanString($lastName);
    }

    protected function nationality(): Country
    {
        $country = $this->cleanString($this->firstLine(2));

        return $this->parseCountry($country);
    }

    /**
     * @throws CheckDigitFailedException
     */
    protected function dateOfBirth(): string
    {
        $date = $this->cleanString($this->secondLine(4));
        $check = $this->cleanString($this->secondLine(5));
        $this->checkDigit($date, $check, 'Date of birth. TD3');
        $date = Carbon::createFromFormat('ymd', $date);
        if (! $date) {
            throw new CheckDigitFailedException('Date of birth is not valid');
        }
        if ($date->isFuture()) {
            $date->subCentury();
        }

        return $date->toDateString();
    }

    protected function sex(): string
    {
        return $this->cleanSex($this->secondLine(6));
    }

    protected function dateOfExpiry(): string
    {
        return 'Not Present';
    }

    /**
     * @throws CheckDigitFailedException
     */
    protected function personalNumber(): string
    {
        return $this->documentNumber();
    }

    /**
     * @throws CheckDigitFailedException
     */
    protected function documentNumber(): string
    {
        $documentNumber = $this->cleanString($this->secondLine(1));
        $checkDigit = $this->cleanString($this->secondLine(2));
        $this->checkDigit($documentNumber, $checkDigit, 'Document Number. TD3');

        return $documentNumber;
    }
}
