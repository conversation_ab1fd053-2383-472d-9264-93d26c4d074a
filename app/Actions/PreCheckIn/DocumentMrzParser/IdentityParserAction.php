<?php

namespace App\Actions\PreCheckIn\DocumentMrzParser;

use App\Actions\BillableAddons\BillAddonAction;
use App\Actions\Guests\GuestsPortalRoutesBaseAction;
use App\Actions\PreCheckIn\DocumentMrzParser\MrzParser\Document\IdentityDocument;
use App\Actions\PreCheckIn\DocumentMrzParser\MrzParser\Exception\NoImagesFoundException;
use App\Actions\PreCheckIn\DocumentMrzParser\MrzParser\Exception\ParserNotImplementedException;
use App\Enum\BillableAddonTypeEnum;
use App\Models\Booking;
use App\Models\PreCheckInFormPassport;
use App\Models\RentalNinjaTeam;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Lorisleiva\Actions\ActionRequest;
use Throwable;

/**
 * MRZ Documentation: https://www.icao.int/publications/Documents/9303_p3_cons_en.pdf
 * MRZ Documentation: https://www.doubango.org/SDKs/mrz/docs/MRZ_parser.html
 * MRZ Documentation: https://en.wikipedia.org/wiki/Machine-readable_passport
 * https://hergen.nl/processing-identity-documents-in-laravel
 * Location test:
 * https://console.firebase.google.com/u/0/project/rental-ninja-guests/storage/rental-ninja-guests.appspot.com/files/~2Fpassports~2F1/pol-test.
 *
 * Face cropping: https://rental-ninja-guests.imgix.net/passports/1/pol-test/passport-anu.jpg?w=150&h=150&fit=face ??
 */
class IdentityParserAction extends GuestsPortalRoutesBaseAction
{
    public string $commandSignature = 'ninja-dev:test-ocr-identity-parser {element=all}';

    /**
     * This is only used for testing purposes.
     */
    public function asCommand(Command $command): int
    {
        $data = config('test_documents');
        $element = $command->argument('element');
        if ($element != 'all') {
            $data = is_numeric($element) ? [$data[(int) $element]] : [$element];
        }

        $successFulAttempts = 0;
        $attempts = 0;
        foreach ($data as $item) {
            $attempts++;
            $name = Str::of(is_array($item) ? $item[0] : $item)->afterLast('/')->beforeLast('.')->title();
            $command->alert("Handling $name");
            $time = microtime(true);
            try {
                $data = $this->handle($item);
            } catch (Throwable $e) {
                $command->error($e->getMessage());
                $command->info('');

                continue;
            }
            if ($data == null) {
                $command->error("No data  on $name");

                continue;
            } else {
                $command->info("Data on $name:");
                $command->info(json_encode($data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT));
            }

            $successFulAttempts++;
            $command->info('Seconds: '.(microtime(true) - $time));
        }

        $successRate = $successFulAttempts / $attempts * 100;
        $command->info("Success rate: {$successRate}% out of $attempts attempts");

        return $command::SUCCESS;
    }

    public function asJob(PreCheckInFormPassport $passport): void
    {
        $passportUrl = $passport->passport_url;
        try {
            $passport->document_data = $this->handle($passportUrl);
            $passport->save();
        } catch (ParserNotImplementedException) {
            pnLog("[ParserNotImplementedException] Parser not found for $passportUrl");
        } catch (Throwable $e) {
            report($e);
        }
    }

    public function rules(): array
    {
        return []; // To override the parent class, as this controller is shared for two routes with different parameters
    }

    /*
     * This controller is shared in two routes with different parameters:
     * - Public route: PreCheckIn hash.
     * - Private route: Team id and booking id.
     */
    public function asController(ActionRequest $request, mixed $teamOrReference, ?int $bookingId = null): ?array
    {
        if (is_null($bookingId)) {
            // $teamOrReference is Reference
            $booking = $this->getBookingFromRequest($request, $teamOrReference);
        } else {
            // $teamOrReference is Team and $bookingId is Booking
            $booking = Booking::getBookingModel(intval($teamOrReference), $bookingId);
        }

        abort_unless($booking->rental->guestsApplicationRentalSettings->scan_documents, 403, 'Cannot scan documents');

        $passportUrl = $request->input('passport_url');
        try {
            $data = $this->handle($passportUrl);
        } catch (ParserNotImplementedException) {
            pnLog("[ParserNotImplementedException] Parser not found for $passportUrl");
            $data = [];
        } catch (Throwable $e) {
            report($e);
            $data = [];
        }

        if ($data) {
            // Invoice document scan
            $this->invoiceScanToCustomer($booking, $request->user());
        }

        abort_if(empty($data), 400, 'Error parsing the document');

        $documentAlreadyIntroduced = $booking->preCheckInForm->passports->pluck('passport_id')
            ->contains(data_get($data, 'document-number'));
        abort_if($documentAlreadyIntroduced, 400, 'Document already scanned');

        return $data;
    }

    /**
     * @throws ParserNotImplementedException
     * @throws NoImagesFoundException
     */
    public function handle(string|array $urls): ?array
    {
        $images = [];
        // Download the files from the storage
        if (is_array($urls)) {
            foreach ($urls as $url) {
                $images[] = $url;
            }
        } else {
            $images = [$urls];
        }

        $document = new IdentityDocument();
        foreach ($images as $image) {
            $document->addImage($image);
        }
        $mrz = $this->parseMrz($document);
        if ($mrz == null) {
            return null;
        }

        $mrz['faceImage'] = $this->storeImage($document);
        $mrz['images'] = $urls;

        return $mrz;
    }

    /**
     * @throws ParserNotImplementedException
     * @throws NoImagesFoundException
     */
    private function parseMrz(IdentityDocument $document): array
    {
        return $document->parser()->handle();
    }

    private function storeImage(IdentityDocument $document): ?string
    {
        // TODO: FIXME.
        return null;
        /*
        $face = $document->getFace()?->encode('jpg');
        if ($face == null) {
            return null;
        }
        $random = Str::random(10);
        $amazon_path = "passport - tests / $random - face . jpg";
        Storage::disk('s3')->put($amazon_path, $face);

        return GetPictureUrlAction::run(conf('ninja.amazon_s3_url').$amazon_path, null);
        */
    }

    private function invoiceScanToCustomer(Booking $booking, ?User $user = null): void
    {
        $team = $booking->team;
        RentalNinjaTeam::slackNotification(
            instance: "One document scan in booking $booking->id of team $team->name ($team->id)",
            provider: $team->provider_id,
        );

        BillAddonAction::run(
            team: $team,
            type: BillableAddonTypeEnum::SCAN,
            rentalId: $booking->rental_id,
            bookingId: $booking->id,
            user: $user,
        );
    }
}
