<?php

namespace App\Actions\Accounting\Payees;

use App\Enum\PayeeRecurringItemStrategyTypeEnum;
use App\Http\Requests\Payee\PayeeRequest;
use App\Http\Resources\PayeeResource;
use App\Models\Payee;
use App\Models\Team;
use Illuminate\Contracts\Validation\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdatePayeeAction
{
    use AsAction;

    public function afterValidator(Validator $validator, ActionRequest $request): void
    {
        foreach ($request->input('recurring_items') as $recurringItem) {
            if ($recurringItem['type'] == PayeeRecurringItemStrategyTypeEnum::FIXED_PER_SETTLEMENT->value && ! empty($recurringItem['rentals'])) {
                $validator->errors()->add('recurring_items', "Recurring items with type Statement can't have rentals selected.");
            }
        }
    }

    public function asController(Team $team, Payee $payee, PayeeRequest $request): PayeeResource
    {
        $request->merge(['team_id' => $team->id]);

        $payee = $this->handle($request->input(), $payee);

        return new PayeeResource($payee);
    }

    public function handle(array $attributes, Payee $payee): Payee
    {
        $payee->update($attributes);
        $payee->save();
        $payee->refresh();

        return $payee;
    }
}
