<?php

namespace App\Actions\Accounting\Payees;

use App\Http\Resources\PayeeInvoiceResource;
use App\Models\Payee;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\Concerns\AsController;

class GetPayeeInvoicesReceivedAction
{
    use AsController;

    public function asController(Team $team, Payee $payee, Request $request): AnonymousResourceCollection
    {
        return PayeeInvoiceResource::collection(
            $payee->invoicesReceived()
                ->orderByDesc('id')
                ->paginate(50)
        );
    }
}
