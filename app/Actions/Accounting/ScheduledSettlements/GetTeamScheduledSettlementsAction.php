<?php

namespace App\Actions\Accounting\ScheduledSettlements;

use App\Http\Resources\ScheduledSettlementResource;
use App\Models\ScheduledSettlement;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTeamScheduledSettlementsAction
{
    use AsAction;

    protected const PER_PAGE = 50;

    public function asController(Team $team): AnonymousResourceCollection
    {
        $query = $this->handle(team: $team, forPagination: true);

        return ScheduledSettlementResource::collection($query->paginate(self::PER_PAGE));
    }

    public function handle(Team $team, bool $forPagination = false, ?string $search = null): Collection|Builder
    {
        return ScheduledSettlement::whereBelongsTo($team)
            ->when($search, fn (Builder $query) => $query->where('name', 'like', "%$search%"))
            ->when(! $forPagination, fn ($query) => $query->get());
    }

    public function getSingle(Team $team, ScheduledSettlement $scheduledSettlement): ScheduledSettlementResource
    {
        return new ScheduledSettlementResource($scheduledSettlement);
    }
}
