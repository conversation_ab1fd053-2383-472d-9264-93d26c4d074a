<?php

namespace App\Actions\Accounting\AdvancedSettings;

use App\Actions\Sources\GetTeamSourcesAction;
use App\DTO\Accounting\AccountingAdvancedSettingsDto;
use App\DTO\Accounting\SourceCommissionStrategyDto;
use App\Http\Resources\AccountingAdvancedSettingsResource;
use App\Models\AccountingAdvancedSettings;
use App\Models\SourceCommissionStrategy;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateAccountingAdvancedSettingsAction
{
    use AsAction;

    public function handle(AccountingAdvancedSettings $settings, AccountingAdvancedSettingsDto $data): AccountingAdvancedSettings
    {
        $settings->update($data->toArray());

        // Ensure $advanced_settings_id
        $data->sources_commission_strategies
            ->through(fn (SourceCommissionStrategyDto $source_strategy) => $source_strategy->advanced_settings_id = $settings->id
            );

        // Update the Source Commission Strategies given in the dto. Let's use upsert for improved performance
        SourceCommissionStrategy::upsert(
            $data->sources_commission_strategies->toArray(),
            ['team_id', 'source_id'],
        );

        return $settings->refresh();
    }

    public function asController(Team $team, AccountingAdvancedSettingsDto $data): AccountingAdvancedSettingsResource
    {
        $data = $this->validate($team, $data);
        $settings = $this->handle($team->accountingAdvancedSettings, $data);

        return new AccountingAdvancedSettingsResource($settings);
    }

    /**
     * This is because currently, laravel-data doesn't have a prepareForValidation() method:
     * https://github.com/spatie/laravel-data/discussions/14
     * Furthermore, withValidator() doesn't allow me to modify values after validation.
     */
    private function validate(Team $team, AccountingAdvancedSettingsDto $data): AccountingAdvancedSettingsDto
    {
        // Never trust the user, he may change the team_id in the request payload:
        $data->team_id = $team->id;
        $teamSourcesIds = GetTeamSourcesAction::run($team->id)->pluck('id');

        $data->sources_commission_strategies
            ->through(function (SourceCommissionStrategyDto $source_strategy) use ($team, $teamSourcesIds, &$data) {
                $source_strategy->team_id = $team->id;
                abort_unless($source_strategy->source_id == 0 || $teamSourcesIds->contains($source_strategy->source_id),
                    404,
                    "Not allowed. At least one source doesn't belong to your team."
                );

                if ($source_strategy->enabled === true && $data->enabled === false) {
                    $data->enabled = true;
                }

                return $source_strategy;
            });

        return $data;
    }
}
