<?php

namespace App\Actions\Accounting\AdvancedSettings;

use App\Actions\Sources\GetTeamSourcesAction;
use App\Http\Resources\AccountingAdvancedSettingsResource;
use App\Models\AccountingAdvancedSettings;
use App\Models\SourceCommissionStrategy;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetOrRefreshAccountingAdvancedSettingsAction
{
    use AsAction;

    public function asController(Request $request, Team $team): AccountingAdvancedSettingsResource
    {
        $advanceSettings = $this->handle($team);

        return new AccountingAdvancedSettingsResource($advanceSettings);
    }

    public function asJob(Team $team): void
    {
        $this->handle($team);
    }

    public function handle(Team $team): AccountingAdvancedSettings
    {
        $settings = $team->accountingAdvancedSettings ??
            AccountingAdvancedSettings::create([
                'team_id' => $team->id,
            ]);

        $teamSources = GetTeamSourcesAction::run($team->id)->pluck('id');
        $this->updateWithNewSourcesStrategies($team, $teamSources, $settings);

        // Remove deleted source commission strategies from deleted sources. We could do this though upon source delation,
        // but we need to clean first from the db the source commission strategies from already deleted sources.
        $this->removeDeletedSourcesStrategies($team, $teamSources, $settings);
        // No need to refresh sourceCommissionStrategies ($settings) as new ones correspond to new sources which must exist

        return $settings->refresh();
    }

    private function updateWithNewSourcesStrategies(Team $team, Collection $teamSources, AccountingAdvancedSettings $settings): void
    {
        $existingStrategies = $settings->sourceCommissionStrategies->pluck('source_id');

        $teamSources->diff($existingStrategies)
            ->each(fn (int $newSourceId) => SourceCommissionStrategy::forceCreate([
                'team_id' => $team->id,
                'enabled' => false,
                'advanced_settings_id' => $settings->id,
                'source_id' => $newSourceId,
            ]));
    }

    private function removeDeletedSourcesStrategies(Team $team, Collection $teamSources, AccountingAdvancedSettings $settings): void
    {
        $toDelete = $settings->sourceCommissionStrategies->pluck('source_id')->diff($teamSources);
        SourceCommissionStrategy::query()
            ->where('team_id', $team->id)
            ->whereIn('source_id', $toDelete)
            ->forceDelete();
    }
}
