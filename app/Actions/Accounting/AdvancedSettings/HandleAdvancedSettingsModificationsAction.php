<?php

namespace App\Actions\Accounting\AdvancedSettings;

use App\Actions\Accounting\CheckFeeOrTaxNameMatchesRuleAction;
use App\Actions\Sources\ReturnNewProviderSourceAction;
use App\DTO\Accounting\AdvancedAccountingRuleDto;
use App\DTO\Settlements\ExpandedSettlementDTO;
use App\Models\AccountingAdvancedSettings;
use App\Models\Booking;
use App\Models\BookingFee;
use App\Models\BookingTax;
use App\Models\Source;
use App\Models\SourceCommissionStrategy;
use App\Models\Team;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class HandleAdvancedSettingsModificationsAction
{
    use AsAction;

    public function handle(ExpandedSettlementDTO $settlementDTO, Team $team): ExpandedSettlementDTO
    {
        $advancedSettings = AccountingAdvancedSettings::query()
            ->onTeam($settlementDTO->team_id)
            ->first();

        if (! $advancedSettings->enabled || $settlementDTO->bookings->isEmpty() || $advancedSettings->sourceCommissionStrategies->isEmpty()) {
            return $settlementDTO;
        }

        // Prepare stuff:
        $advancedSettings->load('sourceCommissionStrategies.source');
        $commissionStrategiesSources = $advancedSettings->sourceCommissionStrategies
            ->map(function (SourceCommissionStrategy $commissionStrategy) use ($team) {
                return $commissionStrategy->source ?? ReturnNewProviderSourceAction::run($team->id, $team->mainProvider()->fullName());
            });

        $settlementDTO->bookings
            ->transform(function (Booking $booking) use ($advancedSettings, $commissionStrategiesSources) {
                if ($booking->isUnavailable()) {
                    return $booking;
                }
                // If booking's source name is in the list of sources of Advanced Settings, apply modifications.
                // Why do we compare by source name instead of source_id?
                // Because we may have duplicated source_names in a team and we just save 1 source Commission
                /** @var Source $bookingSource */
                $bookingSource = $commissionStrategiesSources->filter(function (Source $source) use ($booking) {
                    return strtolower($source->name) == strtolower($booking->source_public_name);
                })->first();

                if ($bookingSource) {
                    // This means there is a source commission strategy to be applied to this booking
                    /** @var SourceCommissionStrategy $sourceCommissionStrategy */
                    $sourceCommissionStrategy = $advancedSettings->sourceCommissionStrategies
                        ->firstWhere('source_id', $bookingSource->id);

                    if ($sourceCommissionStrategy->enabled) {
                        return $this->makeBookingModifications($booking, $sourceCommissionStrategy);
                    }
                }

                return $booking;
            });

        return $settlementDTO;
    }

    private function makeBookingModifications(Booking $booking, SourceCommissionStrategy $commissionStrategy): Booking
    {
        // Only assign a new Commission amount if the new value is different from null, which means we have a match. Otherwise keep the original value.
        // We must compare against null because 0.0 is also a valid result to assign.
        if ($commissionStrategy->calculate_from_final_price) {
            $newCommission = $this->getCommissionFromFinalOrRentalPricesRules(
                $commissionStrategy->getFinalPriceRulesDataCollection(),
                $booking,
                'final_price'
            );

            if ($newCommission !== null) {
                $booking->commission = $newCommission;
            }
        } else {
            $newCommission = null;

            // Rental Price
            $rentalPriceCommission = $this->getCommissionFromFinalOrRentalPricesRules(
                $commissionStrategy->getRentalPriceRulesDataCollection(),
                $booking,
                'final_rental_price'
            );
            if ($rentalPriceCommission !== null) {
                $newCommission += $rentalPriceCommission;
            }

            // Fees
            $feesCommission = $this->getCommissionFromFeeRules($commissionStrategy->getFeesPriceRulesDataCollection(), $booking);
            if ($feesCommission !== null) {
                $newCommission += $feesCommission;
            }

            // Taxes
            $taxesCommission = $this->getCommissionFromTaxRules($commissionStrategy->getTaxesPriceRulesDataCollection(), $booking);
            if ($taxesCommission !== null) {
                $newCommission += $taxesCommission;
            }

            // Assign
            if ($newCommission !== null) {
                $booking->commission = $newCommission;
            }
        }

        return $booking;
    }

    private function getCommissionFromFinalOrRentalPricesRules(Collection $rules, Booking $booking, string $bookingConceptToCalculateFrom): ?float
    {
        $commission = null;

        $rules->each(function (AdvancedAccountingRuleDto $rule) use ($booking, $bookingConceptToCalculateFrom, &$commission) {
            if ($this->rentalIdIsInCollection($booking->rental_id, $rule->rentals)) {
                $commission = $booking->$bookingConceptToCalculateFrom * $rule->value / 100;

                return false; // To exit the each()
            }
        });

        return $commission;
    }

    private function getCommissionFromFeeRules(Collection $rules, Booking $booking): ?float
    {
        $feesCommission = null;

        $rules->each(function (AdvancedAccountingRuleDto $rule) use ($booking, &$feesCommission) {
            $booking->bookingFees->each(function (BookingFee $fee) use ($rule, $booking, &$feesCommission) {
                if (CheckFeeOrTaxNameMatchesRuleAction::run($fee, $rule->string, $rule->contains) && $this->rentalIdIsInCollection($booking->rental_id, $rule->rentals)) {
                    $feesCommission += $fee->getTotalPrice() * $rule->value / 100;
                }
            });
        });

        return $feesCommission;
    }

    private function getCommissionFromTaxRules(Collection $rules, Booking $booking): ?float
    {
        $taxesCommission = null;

        $rules->each(function (AdvancedAccountingRuleDto $rule) use ($booking, &$taxesCommission) {
            $booking->bookingTaxes->each(function (BookingTax $tax) use ($rule, $booking, &$taxesCommission) {
                if (CheckFeeOrTaxNameMatchesRuleAction::run($tax, $rule->string, $rule->contains) && $this->rentalIdIsInCollection($booking->rental_id, $rule->rentals)) {
                    if ($tax->tax_included_in_price) {
                        // If user adds a tax, which is already included in the rental price, he/she wishes to deduct it from the commission amount.
                        $taxesCommission -= $tax->amount * $rule->value / 100;
                    } else {
                        // If user adds a tax which is not included in the rental price, is because wants to add it in the calculation.
                        $taxesCommission += $tax->amount * $rule->value / 100;
                    }
                }
            });
        });

        return $taxesCommission;
    }

    private function rentalIdIsInCollection(int $rentalId, Collection $rentals): bool
    {
        return $rentals->isEmpty() || $rentals->contains($rentalId);
        // Note: If the rentals array of a rule is empty, it means "for all rentals"
    }
}
