<?php

namespace App\Actions\Accounting\ExportSettlements;

use App\Exports\MultiSheetExcelExporter;
use App\Models\Team;
use App\Support\SaveExcel;
use Exception;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Maatwebsite\Excel\Excel;

abstract class ExcelSettlementExporterAction
{
    use AsAction;

    abstract public function handle(Team $team, array $expandedSettlement, string $formatter, string $directory);

    /**
     * @throws Exception
     */
    protected function export(Team $team, MultiSheetExcelExporter $exporter, string $directory): void
    {
        $name = Str::slug($this->title());
        $saver = new SaveExcel(SaveExcel::FULL_PATH);
        $saver->handle($team, $exporter, $name, Excel::XLS, $directory);
    }

    abstract public function title(): string;
}
