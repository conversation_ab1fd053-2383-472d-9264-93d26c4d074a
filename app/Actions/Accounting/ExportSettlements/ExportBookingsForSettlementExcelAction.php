<?php

namespace App\Actions\Accounting\ExportSettlements;

use App\Exports\MultiSheetExcelExporter;
use App\Exports\Sheets\Accounting\BookingsSheet;
use App\Models\Team;
use Illuminate\Support\Arr;

class ExportBookingsForSettlementExcelAction extends ExcelSettlementExporterAction
{
    public function handle(Team $team, array $expandedSettlement, string $formatter, string $directory)
    {
        $excelExporter = new MultiSheetExcelExporter();

        $excelExporter->addSheet(
            new BookingsSheet(Arr::get($expandedSettlement, 'bookings') ?? [], $formatter),
        );

        $this->export($team, $excelExporter, $directory);
    }

    public function title(): string
    {
        return 'Bookings';
    }
}
