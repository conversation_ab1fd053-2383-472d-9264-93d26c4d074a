<?php

namespace App\Actions\Accounting\Settlements;

use App\Actions\Auth\VerifyRequestResourceIsFromSameTeamAction;
use App\DTO\Accounting\SettlementRecipientDto;
use App\Events\StatementDownloadedEvent;
use App\Events\StatementEmailedEvent;
use App\Models\Settlement;
use App\Models\Team;
use App\Notifications\SettlementNotification;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class EmailOrDownloadSettlementAction
{
    use AsAction;

    public function asController(Team $team, Settlement $settlement, SettlementRecipientDto $settlementRecipient, Request $request): Response
    {
        VerifyRequestResourceIsFromSameTeamAction::run($team, $settlement);

        $user = $request->user();
        $settlementRecipient->user_id = $user->id;
        $settlementRecipient->user = $user;

        $action = Str::of($request->path())->afterLast('/');
        $this->handle($action, $team, $settlement, $settlementRecipient);

        return response('');
    }

    public function handle(string $action, Team $team, Settlement $settlement, SettlementRecipientDto $settlementRecipient): void
    {
        $notification = (new SettlementNotification($team, $settlement))->locale($settlementRecipient->language);
        $settlementRecipient->notify($notification);

        if ($action == 'email') {
            event(new StatementEmailedEvent($team, $settlement));
        } else {
            event(new StatementDownloadedEvent($team, $settlement));
        }
    }
}
