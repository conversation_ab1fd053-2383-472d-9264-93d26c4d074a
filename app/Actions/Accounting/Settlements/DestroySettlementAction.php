<?php

namespace App\Actions\Accounting\Settlements;

use App\Actions\Auth\VerifyRequestResourceIsFromSameTeamAction;
use App\Events\StatementDeletedEvent;
use App\Models\Settlement;
use App\Models\Team;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;

class DestroySettlementAction
{
    use AsAction;

    public function asController(Team $team, Settlement $settlement): Response
    {
        VerifyRequestResourceIsFromSameTeamAction::run($team, $settlement);

        $this->handle($team, $settlement);

        return response('');
    }

    public function handle(Team $team, Settlement $settlement): void
    {
        event(new StatementDeletedEvent($team, $settlement));

        // FIXME: Why are we force deleting this information?
        $settlement->forceDelete();
    }
}
