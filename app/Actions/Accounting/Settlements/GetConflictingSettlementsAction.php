<?php

namespace App\Actions\Accounting\Settlements;

use App\Models\Settlement;
use App\Query\SettlementQuery;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConflictingSettlementsAction
{
    use AsAction;

    /**
     * The objective of this action is to return any conflicting settlement already created in the DB with the parameters given.
     * Normally this is called before saving a new settlement to validate if it can be saved.
     */
    public function handle(int $team_id, array $rentals, string $currency, Carbon $start, Carbon $end, bool $onCheckOut, ?int $settlementId = null): Collection
    {
        return Settlement::query()
            ->select(['id', 'rentals', 'name'])
            ->onTeam($team_id)
            ->whereCurrency($currency)
            ->where('start', '<=', $end)
            ->where('end', '>=', $start)
            ->where('on_check_out', $onCheckOut)
            ->when($settlementId,
                fn (SettlementQuery $query, int $settlementId) => $query->where('id', '<>', $settlementId)
            )->get()
            ->filter(function (Settlement $settlement) use ($rentals) {
                return count(array_intersect($rentals, $settlement->rentals)) > 0;
            });
    }
}
