<?php

namespace App\Actions\Accounting\Payouts\PartialPayouts;

use App\Events\PayoutMarkedPaidEvent;
use App\Http\Resources\PayoutResource;
use App\Models\PartialPayout;
use App\Models\Payout;
use App\Models\PayoutDetail;
use App\Models\Settlement;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;

class MarkPayoutAsPaidAction
{
    use AsAction;

    public ?string $errorMessage = null;

    public ?int $errorCode = null;

    public function asController(Team $team, Settlement $settlement, Payout $payout, Request $request): PayoutResource
    {
        abort_unless($team->id == $payout->team_id && $team->id == $settlement->team_id, 401);

        $success = $this->handle($team, $payout);
        abort_if(! $success, $this->errorCode, $this->errorMessage);

        // Note: Updated relationships are queried in the PayoutResource
        return new PayoutResource($payout);
    }

    public function asMultiple(Team $team, Request $request): Response
    {
        $request->validate([
            'payouts' => 'required|array',
        ]);

        $payouts = Payout::query()
            ->onTeam($team)
            ->whereIn('id', $request->input('payouts'))
            ->get();

        abort_if($payouts->isEmpty(), 404); // In case someone is putting payout ids from another team

        foreach ($payouts as $payout) {
            $this->handle($team, $payout);
        }

        return response('OK', 200);
    }

    public function handle(Team $team, Payout $payout): bool
    {
        $paid = PartialPayout::wherePayoutId($payout->id)
            ->sum('amount') ?? 0.0;

        $value = PayoutDetail::wherePayoutId($payout->id)
            ->select('value')
            ->sum('value') ?? 0.0;

        if (($value >= 0 && $value <= $paid) || ($value < 0 && $value >= $paid)) {
            $this->errorMessage = 'All has been paid already';
            $this->errorCode = 422;

            return false;
        }

        $partial = new PartialPayout(
            [
                'team_id' => $team->id,
                'settlement_id' => $payout->settlement_id,
                'payout_id' => $payout->id,
                'amount' => $value - $paid,
                'paid_at' => now(),
            ]
        );

        $partial->save();

        event(new PayoutMarkedPaidEvent($team, $payout, $partial));

        return true;
    }
}
