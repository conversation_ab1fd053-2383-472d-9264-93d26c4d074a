<?php

namespace App\Actions\Accounting\Payouts;

use App\Http\Resources\PayoutListingResource;
use App\Models\Payout;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetAllPayoutsAction
{
    use AsAction;

    public function asController(Team $team, Request $request): AnonymousResourceCollection
    {
        $filter = $request->input('filter');
        $payee = (int) $request->input('recipient', 0);
        $query = $this->handle($team, $filter, $payee, forPagination: true);
        $payouts = $query->paginate(50);

        return PayoutListingResource::collection($payouts);
    }

    public function handle(Team $team, ?string $filter, int $payee, bool $forPagination = false): Collection|Builder
    {
        $query = Payout::query()
            ->where('payouts.team_id', '=', $team->id)
            ->has('settlement')
            ->when($payee != 0 && $payee != null, fn (Builder $query) => $query->where('payouts.payee_id', '=', $payee))
            ->when($filter, function (Builder $query, $filter) use ($team) {
                $search = str_replace('*', '%', $filter);
                if (! empty($search)) {
                    $search = '%'.$search.'%';
                }
                $query
                    ->whereIn('settlement_id', fn ($query) => $query
                        ->select('id')->from('settlements')
                        ->where('team_id', '=', $team->id)
                        ->where('name', 'like', $search))
                    ->orWhereIn('payee_id', fn ($query) => $query
                        ->select('id')
                        ->from('payees')
                        ->where('team_id', '=', $team->id)
                        ->where('name', 'like', $search));
            })
            ->select('payouts.*')
            ->when(request()->boolean('unpaid_only'), fn (Builder $query) => $query
                ->withSum('partialPayouts', 'amount')
                ->withSum('details', 'value')
                ->havingRaw('IFNULL(ROUND(`partial_payouts_sum_amount`, 2), 0) <> IFNULL(ROUND(`details_sum_value`, 2), 0)')
            )
            ->join('settlements', 'settlements.id', '=', 'payouts.settlement_id')
            ->orderByDesc('settlements.start')
            ->orderByDesc('settlements.id')
            ->orderByDesc('id')
            ->with(['settlement', 'payee', 'invoices', 'partialPayouts', 'details']);

        if ($forPagination) {
            return $query;
        } else {
            return $query->get();
        }
    }
}
