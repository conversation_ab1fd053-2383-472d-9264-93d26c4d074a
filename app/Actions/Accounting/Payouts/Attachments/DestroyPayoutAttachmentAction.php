<?php

namespace App\Actions\Accounting\Payouts\Attachments;

use App\Models\Payout;
use App\Models\PayoutAttachment;
use App\Models\Settlement;
use App\Models\Team;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;

class DestroyPayoutAttachmentAction
{
    use AsAction;

    public function asController(Team $team, Settlement $settlement, Payout $payout, PayoutAttachment $attachment): Response
    {
        $this->handle($attachment);

        return response('');
    }

    public function handle(PayoutAttachment $attachment): void
    {
        $attachment->delete();
    }
}
