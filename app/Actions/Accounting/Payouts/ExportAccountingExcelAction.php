<?php

namespace App\Actions\Accounting\Payouts;

use App\Exports\Sheets\Accounting\CompletePayoutSheet;
use App\Exports\Sheets\Accounting\InvoicesSheet;
use App\Exports\Sheets\Accounting\SimplifiedPayoutSheet;
use App\Models\Team;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ExportAccountingExcelAction
{
    use AsAction;

    public function asController(Team $team, ActionRequest $request): Response|BinaryFileResponse
    {
        $type = $request->input('type', 'payouts-details');
        abort_unless(in_array($type, ['payouts', 'payouts-details', 'invoices']), 400);

        $from = Carbon::parse($request->input('from'))->startOfDay();
        $to = Carbon::parse($request->input('to'))->endOfDay();

        return $this->handle($team, $type, $from, $to);
    }

    public function handle(Team $team, string $type, Carbon $from, Carbon $to): Response|BinaryFileResponse
    {
        if ($type === 'payouts') {
            return (new SimplifiedPayoutSheet($team, $from, $to))->download('data.xlsx');
        }
        if ($type === 'invoices') {
            return (new InvoicesSheet($team, $from, $to))->download('data.xlsx');
        }

        return (new CompletePayoutSheet($team, $from, $to))->download('data.xlsx');
    }
}
