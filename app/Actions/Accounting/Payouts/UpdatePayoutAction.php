<?php

namespace App\Actions\Accounting\Payouts;

use App\DTO\Accounting\PayoutDetailDto;
use App\Http\Resources\PayoutResource;
use App\Models\Payout;
use App\Models\PayoutDetail;
use App\Models\Settlement;
use App\Models\Team;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\Optional;

class UpdatePayoutAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'name' => ['string', 'max:255'],
        ];
    }

    public function asController(Team $team, Settlement $settlement, Payout $payout, Request $request): PayoutResource
    {
        $request->merge(['team_id' => $team->id, 'settlement_id' => $settlement->id]);

        $payout = $this->handle($payout, $request->input());

        // Note: Updated relationships are queried in the PayoutResource
        return new PayoutResource($payout);
    }

    public function handle(Payout $payout, array $input): Payout
    {
        $payout->update($input);
        $payout->save();

        $details = collect($input['details']);
        PayoutDetail::query()
            ->where('payout_id', $payout->id)
            ->whereNotIn('id', $details->pluck('id')->filter())
            ->delete();

        $details = PayoutDetailDto::collection($details); // Not done before because I can't use the "pluck()" method

        $details->each(function (PayoutDetailDto $detail) use ($payout) {
            $detail->payout_id = $payout->id;

            if (is_int($detail->id)) { // If the id exists (int and not Optional object), it means we have to update
                $detail->updated_at = Optional::create(); // Make sure we let Laravel update the updated_at column
                PayoutDetail::query()
                    ->where('id', $detail->id)
                    ->update($detail->toArray());
            } else { // If no id, just create new
                PayoutDetail::create($detail->toArray());
            }
        });

        return $payout;
    }
}
