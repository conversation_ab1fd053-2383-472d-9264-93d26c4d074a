<?php

namespace App\Actions\Accounting\Payouts;

use App\Http\Resources\PayoutListingResource;
use App\Models\Payee;
use App\Models\Payout;
use App\Models\Settlement;
use App\Models\Team;
use App\Query\PayoutQuery;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class GetSettlementPayoutsAction
{
    use AsAction;

    public function asController(Team $team, Settlement $settlement): AnonymousResourceCollection
    {
        $query = $this->handle($team, $settlement, forPagination: true);
        $payouts = $query->paginate(50);

        return PayoutListingResource::collection($payouts);
    }

    public function handle(Team $team, Settlement $settlement, bool $forPagination = false): Builder|Collection|PayoutQuery
    {
        $query = Payout::query()
            ->whereSettlementId($settlement->id)
            ->where('payouts.team_id', '=', $team->id);
        $filter = request('filter', '');

        if (! empty($filter)) {
            $search = str_replace('*', '%', $filter);

            if (! empty($search)) {
                $search = '%'.$search.'%';
            }

            $settlements = Settlement::whereTeamId($team->id)
                ->where('name', 'LIKE', $search)
                ->select('id')
                ->pluck('id');

            $recipients = Payee::whereTeamId($team->id)
                ->where('name', 'LIKE', $search)
                ->select('id')
                ->pluck('id');

            if (($settlements != null) || ($recipients != null)) {
                $query->where(function ($q) use (&$settlements, &$recipients) {
                    /* @var Builder $q */
                    return $q->whereIn('payouts.settlement_id', $settlements)
                        ->orWhereIn('payouts.payee_id', $recipients);
                });
            }
        }

        $query = $query
            ->join('settlements', 'settlements.id', '=', 'payouts.settlement_id')
            ->select(
                [
                    'payouts.*',
                    'settlements.start',
                ]
            )
            ->orderBy('settlements.start', 'DESC')
            ->orderBy('settlements.name', 'ASC')
            ->orderBy('payouts.id', 'ASC');

        if ($forPagination) {
            return $query;
        } else {
            return $query->get();
        }
    }
}
