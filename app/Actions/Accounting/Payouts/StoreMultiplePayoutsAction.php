<?php

namespace App\Actions\Accounting\Payouts;

use App\Http\Requests\Payout\PayoutRequest;
use App\Models\Settlement;
use App\Models\Team;
use App\Rules\PayoutValidationRules;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreMultiplePayoutsAction
{
    use AsAction;

    public function asController(Request $request, Team $team, Settlement $settlement): Response
    {
        abort_unless($settlement->team_id == $team->id, 403);

        foreach ($request->input('data') as $payoutData) {
            $payoutRequest = PayoutRequest::createFrom(resolve(Request::class)->replace($payoutData));
            $payoutRequest->validate(PayoutValidationRules::getRules());

            StorePayoutAction::run(
                payoutDto: $payoutRequest->toPayoutDto(),
                payoutDetailsCollection: $payoutRequest->toPayoutDetailsCollection(),
                team: $team);
        }

        return response('OK', 200);
    }
}
