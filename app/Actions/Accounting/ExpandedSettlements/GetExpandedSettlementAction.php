<?php

namespace App\Actions\Accounting\ExpandedSettlements;

use App\Actions\Accounting\AdvancedSettings\HandleAdvancedSettingsModificationsAction;
use App\DTO\Settlements\ExpandedSettlementDTO;
use App\Models\Settlement;
use App\Models\Team;
use App\Query\SettlementQuery;
use Lorisleiva\Actions\Concerns\AsAction;

/**
 * This action gets a settlement model as it is saved in the DB and transforms it into an expanded settlement, calculating everything needed.
 */
class GetExpandedSettlementAction
{
    use AsAction;

    private ExpandedSettlementDTO $settlementDTO;

    public function handle(Settlement|int $settlement, Team $team, bool $isSimple = false): ExpandedSettlementDTO
    {
        // If we don't get a settlement, it's a good idea to fetch it with all dependencies needed.
        if (is_int($settlement)) {
            $settlement = Settlement::query()
                ->whereId($settlement)
                ->withAuthor()
                ->withSlimRentals($team->id)
                ->withPayoutDetails() // This loads only settlement_id from payouts table
                ->when(! $isSimple, fn (SettlementQuery $query) => $query->withPayoutsAndPayees())
                ->firstOrFail();
        }

        // Construct the basic version of the DTO:
        $this->settlementDTO = ExpandedSettlementDTO::fromSettlementModel($settlement)
            ->setIsSimple($isSimple);

        // Implement the basic stuff common in both the simple and full versions of a settlement
        $this->getSettlementAuthorAndRentalsInfo($settlement)
            ->getSettlementPayoutsAndPayeesInfo($settlement, $isSimple);

        // Implement attributes for the Settlement Full version if needed
        if (! $isSimple) {
            // Let's first get the bookings of the settlement
            $this->getSettlementBookingsInfo($settlement, $team);

            // Now, if we have to do any modifications, it's time to do them before we calculate totals
            $this->modifyWithAdvancedSettings($team);

            // Finally, once we have all the information we need and modified as needed,
            // let's calculate the values needed by the frontend to display information in tables
            $this->calculateExpandedSettlementValues($team);
        }

        return $this->settlementDTO;
    }

    private function getSettlementAuthorAndRentalsInfo(Settlement $settlement): self
    {
        $this->settlementDTO->author = $settlement->author?->name ?? 'Nobody';
        $this->settlementDTO->slim_rentals = $settlement->settlementSlimRentals;

        return $this;
    }

    private function getSettlementPayoutsAndPayeesInfo(Settlement $settlement, bool $simple): self
    {
        $this->settlementDTO = GetSettlementPayoutsAndPayeesAction::run($this->settlementDTO, $settlement, $simple);

        return $this;
    }

    private function getSettlementBookingsInfo(Settlement $settlement, Team $team): self
    {
        $this->settlementDTO = GetSettlementBookingsAction::run($this->settlementDTO, $settlement, $team);

        return $this;
    }

    private function modifyWithAdvancedSettings(Team $team): self
    {
        $this->settlementDTO = HandleAdvancedSettingsModificationsAction::run($this->settlementDTO, $team);

        return $this;
    }

    private function calculateExpandedSettlementValues(Team $team): self
    {
        $this->settlementDTO = CalculateSettlementExpandedValuesAction::run($this->settlementDTO, $team);

        return $this;
    }
}
