<?php

namespace App\Actions\Accounting\ExpandedSettlements;

use App\DTO\Settlements\ExpandedSettlementDTO;
use App\Enum\PayoutDetailOriginalTypeEnum;
use App\Models\Payout;
use App\Models\Settlement;
use Illuminate\Database\Eloquent\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

/**
 * This action calculates the values for the payouts and payees of a settlement.
 */
class GetSettlementPayoutsAndPayeesAction
{
    use AsAction;

    public function handle(ExpandedSettlementDTO $attributesDTO, Settlement $settlement, bool $simple): ExpandedSettlementDTO
    {
        $payoutDetailsByType = $settlement->payoutDetails
            ->groupBy('original_type')
            ->map
            ->sum('value')
            ->toArray();

        $attributesDTO->payouts_value = array_sum($payoutDetailsByType);
        $attributesDTO->bookings_value = data_get($payoutDetailsByType, PayoutDetailOriginalTypeEnum::BOOKING->value, 0.00);
        $attributesDTO->fees_value = data_get($payoutDetailsByType, PayoutDetailOriginalTypeEnum::FEE->value, 0.00);
        $attributesDTO->taxes_value = data_get($payoutDetailsByType, PayoutDetailOriginalTypeEnum::TAX->value, 0.00);
        $attributesDTO->others_value = data_get($payoutDetailsByType, PayoutDetailOriginalTypeEnum::OTHER->value, 0.00);
        $attributesDTO->expenses_value = data_get($payoutDetailsByType, PayoutDetailOriginalTypeEnum::EXPENSE->value, 0.00);

        if (! $simple) {
            /** @var $payouts Collection */
            $payouts = $settlement->loadMissing('payouts.payee')->payouts;
            $attributesDTO->payouts = $payouts->count();

            $attributesDTO->payee_emails = $payouts
                ->map(fn (Payout $p) => $p->payee->email)
                ->filter(fn (string $mail) => ! empty($mail))
                ->unique()
                ->values();
        }

        return $attributesDTO;
    }
}
