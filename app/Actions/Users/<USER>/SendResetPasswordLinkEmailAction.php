<?php

namespace App\Actions\Users\Password;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Password;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsController;

class SendResetPasswordLinkEmailAction
{
    use AsController;

    public function rules(): array
    {
        return [
            'email' => ['required', 'email'],
        ];
    }

    public function asController(ActionRequest $request): JsonResponse
    {
        $response = Password::sendResetLink(
            $request->only('email')
        );
        $status = $response === Password::RESET_LINK_SENT ? 200 : 400;

        return response()->json(['status' => $response], $status);
    }
}
