<?php

namespace App\Actions\Users\Invitations;

use App\Http\Resources\InvitationResource;
use App\Models\Invitation;
use Lorisleiva\Actions\Concerns\AsAction;

class GetMailedInvitationAction
{
    use AsAction;

    public function asController(string $invitation): InvitationResource
    {
        $model = $this->handle($invitation);
        if ($model == null) {
            abort(404);
        }

        if ($model->isExpired()) {
            $model->delete();

            abort(404);
        }

        return new InvitationResource($model);
    }

    public function handle(string $invitation): ?Invitation
    {
        return Invitation::query()
            ->with(['team', 'user'])
            ->where('token', $invitation)
            ->first();
    }
}
