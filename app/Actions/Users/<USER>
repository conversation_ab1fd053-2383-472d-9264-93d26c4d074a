<?php

namespace App\Actions\Users;

use App\Models\Team;
use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsAction;

class TouchUsersForActiveTeamsAction
{
    use AsAction;

    public string $commandSignature = 'ninja:touch-users-for-all-active-teams';

    public function asCommand(Command $command): void
    {
        $this->handle();
    }

    public function handle(): void
    {
        Team::query()->activeTeams()->get()->each(fn (Team $team) => $team->users()->touch());
    }
}
