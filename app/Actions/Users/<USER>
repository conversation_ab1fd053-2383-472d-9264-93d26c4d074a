<?php

namespace App\Actions\Users;

use App\Actions\Auth\VerifyRequestUserAndAuthUserAreSameAction;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Hash;
use Lorisleiva\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class UpdateUserPasswordAction
{
    use AsAction;

    public function authorize(ActionRequest $request)
    {
        return VerifyRequestUserAndAuthUserAreSameAction::run($request->route('id'));
    }

    public function rules(): array
    {
        return [
            'current' => ['required', 'current_password'],
            'new' => ['required', 'string', 'max:200'],
        ];
    }

    public function asController(ActionRequest $request, int $id): UserResource
    {
        $this->handle($request->input('new'));

        return GetApiUserAction::run();
    }

    public function handle(string $newPassword): void
    {
        $user = GetApiUserAction::run(wantsJson: false);

        $user->password = Hash::make($newPassword);

        $user->save();
    }

    public function getValidationMessages(): array
    {
        return [
            'current.current_password' => 'The given password does not match our records.',
        ];
    }
}
