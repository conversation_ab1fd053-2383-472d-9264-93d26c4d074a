<?php

namespace App\Actions\Users;

use App\Events\UserUpdatedEvent;
use App\Models\User;
use Lorisle<PERSON>\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class SaveUserLocaleAction
{
    use AsAction;

    protected const DEFAULT_LOCALE = 'en_US';

    public function asController(ActionRequest $request, User $user)
    {
        $this->handle($user, $request->input('locale', self::DEFAULT_LOCALE));

        return GetApiUserAction::run();
    }

    public function handle(User $user, string $locale): void
    {
        abort_unless(in_array($locale, config('ninja.long_locales')), 404);

        $user->locale = $locale;
        $user->save();
        event(new UserUpdatedEvent($user));
    }
}
