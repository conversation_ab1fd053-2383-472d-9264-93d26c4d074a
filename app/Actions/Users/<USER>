<?php

namespace App\Actions\Users;

use App\DTO\UserSettingsData;
use App\Http\Resources\UserResource;
use App\Models\User;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateUserSettingsAction
{
    use AsAction;

    public function asController(ActionRequest $request, User $user, UserSettingsData $data): UserResource
    {
        $this->handle($user, $data);

        return GetApiUserAction::run();
    }

    public function handle(User $user, UserSettingsData $data): void
    {
        $settings = $user->getSettings();

        $settings->fill($data->toArray());
        $settings->save();
    }
}
