<?php

namespace App\Actions\Users;

use App\Models\Team;
use Exception;
use Illuminate\Console\Command;
use Lorisleiva\Actions\Concerns\AsAction;

class TouchTeamUsersAction
{
    use AsAction;

    public string $commandSignature = 'ninja:touch-team-users {team}';

    public function asCommand(Command $command): int
    {
        $team = Team::findOrFail($command->argument('team'));
        $this->handle($team, self::class);

        return $command::SUCCESS;
    }

    public function asListener(mixed $event): void
    {
        if (property_exists($event, 'team')) {
            $team = $event->team;
        } elseif (property_exists($event, 'user')) {
            $team = $event->user->team;
        } else {
            throw new Exception('Could not process UserOrTeamUpdatedListenerAction');
        }
        $class = get_class($event);
        $this->handle($team, $class);
    }

    public function handle(Team $team, string $class = ''): void
    {
        pnLog('[TouchTeamUsersAction] Touching all team users for '.$class, $team);
        $team->users()->touch();
    }
}
