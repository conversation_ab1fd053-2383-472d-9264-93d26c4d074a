<?php

namespace App\Actions\Users;

use App\Actions\Teams\TeamMembers\AddTeamMemberAction;
use App\Enum\TeamRolesEnum;
use App\Models\Invitation;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class RegisterUserAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'invitation' => ['required', 'string'],
            'name' => ['required', 'string'],
            'email' => ['required', 'email', 'unique:users,email'],
            'team_id' => ['required'],
            'password' => ['required', 'min:5', 'confirmed'],
            'terms' => ['required', 'accepted'],
            'user_language' => ['nullable', 'string', 'max:5', 'min:5'],
        ];
    }

    public function asController(ActionRequest $request): JsonResponse
    {
        $token = $request->input('invitation');
        $email = $request->input('email');
        $password = $request->input('password');
        $name = $request->input('name');
        $teamId = $request->input('team_id');
        $locale = $request->input('user_language');

        $invitation = Invitation::query()->whereToken($token)->firstOrFail();
        if ($invitation->team_id != $teamId) {
            abort(400, 'Invalid team');
        }

        $this->handle($name, $email, $password, $invitation->role, $teamId, rentals: $invitation->rentals, locale: $locale);

        $invitation->delete();

        return response()->json();
    }

    public function handle(
        string $name,
        string $email,
        string $password,
        ?TeamRolesEnum $role = null,
        ?int $teamId = null,
        ?string $phoneCountry = null,
        ?string $phone = null,
        ?array $rentals = null,
        ?string $locale = null,
        int|string|null $externalId = null,
        ?int $providerId = null,
    ): User {
        $role ??= TeamRolesEnum::MEMBER;
        $user = User::forceCreate(collect([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'current_team_id' => $teamId,
            'ninja_role' => $role,
            'country_code' => $phoneCountry,
            'phone' => $phone,
            'external_id' => $externalId,
            'provider_id' => $providerId,
        ])->when($locale, fn (Collection $collection) => $collection->put('locale', $locale))
            ->toArray()
        );

        if (! is_null($teamId)) {
            $team = Team::query()->find($teamId);

            if ($rentals !== null) {
                AssignRentalsToUserAction::run($team, $user, $rentals);
            }

            AddTeamMemberAction::run($team, $user, $role);
        }

        return $user;
    }
}
