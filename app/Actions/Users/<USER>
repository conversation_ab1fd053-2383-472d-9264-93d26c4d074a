<?php

namespace App\Actions\Users;

use App\Enum\UserSettingsEnum;
use App\Models\RentalNinjaTeam;
use App\Models\User;
use Lorisleiva\Actions\Concerns\AsAction;

class ShouldNotifyUserAction
{
    use AsAction;

    /**
     * Checks if a user can or can't be notified about something.
     * Execute the action and return a result.
     */
    public function handle(int|User $user, UserSettingsEnum $type): bool
    {
        if ($user instanceof User) {
            $instance = $user;
        } else {
            $instance = User::find($user);
        }

        if (isLocal() && RentalNinjaTeam::novaAccessEmails()->doesntContain($instance->email)) {
            return false;
        }
        $settings = $instance->getSettings();

        // This takes the proper attribute from the settings model or returns true if it doesn't exist
        return $settings->{$type->value} ?? true;
    }
}
