<?php

namespace App\Actions\Crm;

use App\Exports\CompaniesExport;
use App\Models\Team;
use App\Support\SaveExcel;
use Illuminate\Database\Eloquent\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Maatwebsite\Excel\Excel as ExcelExport;

class GetCompaniesExcelExportUrl
{
    use AsAction;

    public function handle(Team $team, Collection $companies): string
    {
        $companies->load(['manager', 'channelManager', 'demoer', 'onboarder', 'team']);

        return SaveExcel::url($team, new CompaniesExport($companies), 'crm/companies', ExcelExport::CSV);
    }
}
