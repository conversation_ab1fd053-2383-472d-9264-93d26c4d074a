<?php

namespace App\Actions\Bookings\Comments;

use App\DataProviders\ApiConnectors\Interfaces\BookingCommentsApi;
use App\Models\Booking;
use App\Models\RentalNinjaTeam;
use App\Models\Team;
use App\Notifications\Internal\RentalNinjaTeamSlackNotification;
use App\Notifications\NinjaProviderPushErrorNotification;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteBookingCommentToProviderAction
{
    use AsAction;

    public int $jobTries = 8;

    public int $jobMaxExceptions = 2;

    public function getJobBackoff(): int
    {
        return 30;
    }

    public function asJob(Team $team, int $bookingId, int $commentId): void
    {
        $booking = Booking::getBookingModel($team, $bookingId);
        $this->handle($team, $booking, $commentId);
    }

    public function handle(Team $team, Booking $booking, int $CommentId): bool
    {
        $api = $booking->getProviderConnector();
        if ($api instanceof BookingCommentsApi) {
            return $api->deleteComment($CommentId);
        }

        return false;
    }

    public function jobFailed(Throwable $e, Team $team, int $bookingId, int $commentId): void
    {
        pnLog('[ASYNC PUSH] Delete Comment Failed', $team);
        report($e);

        if (! $team->config()->isWhiteLabel()) {
            $booking = Booking::getBookingModel($team, $bookingId);
            $type = NinjaProviderPushErrorNotification::DELETE_COMMENT;
            $notification = new NinjaProviderPushErrorNotification($type, $booking);
            $team->owner->notify($notification);
        }

        RentalNinjaTeam::slackNotification(
            "[ASYNC PUSH] Deleting a comment FAILED for team $team->id in booking $bookingId, comment id $commentId",
            RentalNinjaTeamSlackNotification::WARNING
        );
    }
}
