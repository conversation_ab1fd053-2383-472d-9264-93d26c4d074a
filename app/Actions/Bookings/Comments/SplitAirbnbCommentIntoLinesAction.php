<?php

namespace App\Actions\Bookings\Comments;

use App\DTO\Bookings\BookingCommentLineDto;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

/**
 * This action gets an airbnb comment and separates it into lines to be able to fetch information from it. Let me show one comment example here:
 * Rental price: €392.00
 * Cleaning fee: €83.00
 * Paid by customer (before traveler fees): €486.76
 * Airbnb to pay back: €413.75
 * Airbnb already paid back: €0.00
 * Airbnb commission: €73.01
 * Damage deposit: €150.00
 * Confirmation code: HMD2JDAZZR
 * Occupancy tax paid to host: €0.00
 * Transient occupancy tax paid by Airbnb: €0.00
 * Guest requested to check in at 15 and check out at 10.
 */
class SplitAirbnbCommentIntoLinesAction
{
    use AsAction;

    /**
     * Warning: we assume that when calling this method, you are passing the airbnb $comment.
     */
    public function handle(string $comment): DataCollection
    {
        $collection = BookingCommentLineDto::collection([]);

        foreach (explode("\n", $comment) as $line) {
            if (empty($line)) {
                continue;
            }

            $dots = strrpos($line, ':');

            if (is_int($dots) && $dots >= 0) {
                $left = substr($line, 0, $dots);
                $right = substr($line, $dots + 2);
            } else {
                $left = $line;
                $right = '';
            }

            $collection[] = new BookingCommentLineDto($left, $right);
        }

        return $collection;
    }
}
