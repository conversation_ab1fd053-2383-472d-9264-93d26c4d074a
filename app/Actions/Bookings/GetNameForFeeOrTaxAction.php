<?php

namespace App\Actions\Bookings;

use App\Models\Team;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;

class GetNameForFeeOrTaxAction
{
    use AsAction;

    /**
     * Execute the action and return a result.
     */
    public function handle(string|array $payload, Team $team)
    {
        // If this is already an string -non json-, returns it.
        if (is_string($payload) && ! $this->isJson($payload)) {
            return $payload;
        }

        // Convert the payload to an array
        if (is_array($payload)) {
            $collection = collect($payload);
        } elseif ($this->isJson($payload)) {
            $collection = collect(json_decode($payload, true));
        } else {
            throw new Exception('Unknown fee/tax object');
        }
        // Get the lang to choose from in the json
        $lang = $team->getTeamLocale();

        // Return the desired language value from the json:
        if ($collection->isEmpty()) {
            return 'Not Defined';
        } // Try to return the requested value.
        elseif ($collection->has($lang) && ! empty($collection->get($lang))) {
            return $collection->get($lang);
        } // Else if the requested language is not english and english exists, return english
        elseif ($collection->has('en') && ! empty($collection->get('en'))) {
            return $collection->get('en');
        } else {
            return $collection->first();
        }
    }

    private function isJson($string): bool
    {
        $decoded = json_decode($string);

        return json_last_error() == JSON_ERROR_NONE && ! is_int($decoded); // Should not be an int the returned decoded otherwise array_key_exists($lang, $array) below blows up
    }
}
