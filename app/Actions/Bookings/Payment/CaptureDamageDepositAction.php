<?php

namespace App\Actions\Bookings\Payment;

use App\Actions\Auth\VerifyUserHasAccessToBookingAction;
use App\Actions\Bookings\GetBookingWithRelationsAction;
use App\Http\Resources\BookingResource;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Team;
use Illuminate\Http\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CaptureDamageDepositAction
{
    use AsAction;

    private ?string $failureReason = null;

    private ?string $failureCode = null;

    public function rules(): array
    {
        return ['amount' => ['nullable', 'integer']];
    }

    public function asController(Team $team, Booking $booking, BookingPayment $bookingPayment, ActionRequest $request): BookingResource|Response
    {
        VerifyUserHasAccessToBookingAction::run($booking);
        abort_unless(! $bookingPayment->locked, 400, "Payment is locked, can't be modified.");
        abort_unless($team->gatewayActivated($booking->rental_id) && $team->config()->ninjaPaymentGatewayEnabled(), 403, 'Payment gateway is not active for this Rental.');
        abort_unless(! $bookingPayment->isCanceled() && $bookingPayment->isStripeHold(), 403, 'Payment is already canceled or is not a Stripe Hold.');

        $success = $this->handle($team, $booking, $bookingPayment, $request->input('amount'));

        if ($success) {
            return GetBookingWithRelationsAction::make()->asController($team, $booking->id);
        } else {
            return response($this->failureReason, $this->failureCode);
        }
    }

    public function handle(Team $team, Booking $booking, BookingPayment $payment, ?int $centsAmount = null): bool
    {
        $result = $team->stripeConnect()->captureDamageDeposit($booking, $payment, $centsAmount);
        // The webhook will do the rest.

        if (is_string($result)) {
            $this->failureReason = $result;
            $this->failureCode = 400;

            return false;
        } else {
            return true;
        }
    }
}
