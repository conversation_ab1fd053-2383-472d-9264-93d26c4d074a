<?php

namespace App\Actions\Bookings\Payment;

use App\DTO\Bookings\PaymentData;
use App\Events\PaymentCreatedEvent;
use App\Events\PaymentRefundedEvent;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

/**
 * This action adds a payment to a Smoobu Booking and
 * then it makes sures that the data in the Smoobu API is correct.
 *
 *
 * Class CreateSmoobuPaymentAction
 */
class AddBookingPaymentInDbAction
{
    use AsAction;

    /**
     * @throws Throwable
     */
    public function handle(Team $team, Booking $booking, PaymentData $data): BookingPayment
    {
        $payment = new BookingPayment();

        $payment->provider_id = $booking->provider_id;
        $payment->team_id = $team->id;
        $payment->amount_in_cents = $data->amountInCents;
        $payment->currency = $data->currency;
        $payment->kind = $data->kind;
        $payment->stripe_payment_intent_id = $data->stripePaymentIntentId;
        $payment->hold_until = $data->holdUntil;
        $payment->stripe_captured = $data->stripeCaptured;
        $payment->stripe_cents_fee = $data->stripeCentsFee;
        $payment->stripe_fee_currency = $data->stripeFeeCurrency;
        $payment->notes = $data->notes;
        $payment->booking_id = $booking->id;
        $payment->paid_at = $data->paidAt;
        $payment->author_id = $data->authorId;
        $payment->locked = false;
        $payment->email = $data->email;
        $payment->saveOrFail();

        // Update booking
        $totalPaid = BookingPayment::query()
            ->onTeam($team->id)
            ->onBooking($booking)
            ->whereNotCancelled()
            ->sum('amount_in_cents') / 100;

        $booking->paid_amount = $totalPaid;
        $booking->save();

        if ($data->amountInCents >= 0) {
            event(new PaymentCreatedEvent($team, $payment->id));
        } else {
            event(new PaymentRefundedEvent($team, $payment->id));
        }

        return $payment;
    }
}
