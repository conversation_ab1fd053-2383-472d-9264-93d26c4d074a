<?php

namespace App\Actions\Bookings\Payment;

use App\DataProviders\ApiConnectors\Interfaces\PaymentsApi;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\RentalNinjaTeam;
use App\Models\Team;
use App\Notifications\Internal\RentalNinjaTeamSlackNotification;
use App\Notifications\NinjaProviderPushErrorNotification;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CancelBookingPaymentToProviderAction
{
    use AsAction;

    public int $jobTries = 8;

    public int $jobMaxExceptions = 2;

    public function getJobBackoff(): int
    {
        return 30;
    }

    public function asJob(Team $team, int $bookingId, BookingPayment $payment): void
    {
        $booking = Booking::getBookingModel($team, $bookingId);

        $this->handle($booking, $payment);
    }

    public function jobFailed(Throwable $e, Team $team, int $bookingId, BookingPayment $payment): void
    {
        pnLog('[ASYNC PUSH] Cancel Payment Failed', $team);
        report($e);

        $booking = Booking::getBookingModel($team, $bookingId);

        // In case it's a stripe payment, we can't cancel the payment because money has been already refunded to customer!
        if (empty($payment->stripe_payment_intent_id)) {
            // Payment cannot be canceled in the provider. Restoring it.
            $payment->canceled_at = null;
            $payment->save();

            $totalPaid = BookingPayment::query()
                ->onTeam($payment->team_id)
                ->onBooking($bookingId)
                ->whereNotCancelled()
                ->sum('amount_in_cents') / 100;

            $booking->paid_amount = $totalPaid;
            $booking->save();
        }

        if (! $booking->team->config()->isWhiteLabel()) {
            $type = NinjaProviderPushErrorNotification::CANCEL_PAYMENT;
            if (! empty($payment->stripe_payment_intent_id)) {
                $appName = $team->config()->rnAppName();
                $provider = $booking->provider()->fullName();
                $content = "WARNING: funds were already returned to the guest. Thus, we have kept the payment in $appName but we couldn't update $provider";
            }
            $notification = new NinjaProviderPushErrorNotification($type, $booking, isset($content) ? $content : null);
            if ($payment->author) {
                $payment->author->notify($notification);
            } else {
                $booking->team->owner->notify($notification);
            }
        }

        RentalNinjaTeam::slackNotification(
            "[ASYNC PUSH] Cancelling a payment FAILED for team $booking->team_id in booking $booking->id",
            RentalNinjaTeamSlackNotification::WARNING
        );
    }

    public function handle(Booking $booking, BookingPayment $model): void
    {
        $api = $booking->getProviderConnector();
        if ($api instanceof PaymentsApi) {
            $api->deletePayment($booking, $model);
        }
    }
}
