<?php

namespace App\Actions\Bookings\Payment;

use App\Actions\Alerts\UpdateBookingPaymentAlertsAction;
use App\Actions\Auth\VerifyUserHasAccessToBookingAction;
use App\Actions\Bookings\GetBookingWithRelationsAction;
use App\Actions\Providers\Generic\ShouldPostToProviderInLocalEnvAction;
use App\DataProviders\ApiConnectors\Interfaces\PaymentsApi;
use App\DTO\Bookings\PaymentData;
use App\Http\Resources\BookingResource;
use App\Models\Booking;
use App\Models\Team;
use Auth;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class AddBookingPaymentAction
{
    use AsAction;

    /**
     * @throws Exception
     */
    public function asController(Team $team, Booking $booking, Request $request): BookingResource|JsonResponse
    {
        VerifyUserHasAccessToBookingAction::run($booking);

        $paymentData = PaymentData::from([
            'amountInCents' => ninjaIntval($request->input('amount_in_cents')),
            'currency' => $booking->currency,
            'kind' => $request->input('kind'),
            'paidAt' => Carbon::parse($request->input('paid_at'))
                ->setTimezone('UTC'),
            'notes' => $request->input('notes', ''),
            'authorId' => Auth::user()->id,
        ]);

        $result = $this->handle(team: $team, booking: $booking, paymentData: $paymentData);

        if (! $result) {
            return response()->json(['message' => 'Something Went wrong'], 404);
        }

        return GetBookingWithRelationsAction::make()
            ->asController($team, $booking);
    }

    /**
     * @throws Exception
     */
    public function handle(Team $team, Booking $booking, PaymentData $paymentData): bool
    {
        // Abort if the price inputted is bigger than what's actually left to be paid or less than what we can return
        $leftToPayInCents = ninjaIntval(100 * $booking->getLeftToPayTotal());
        $maxToRefundInCents = -ninjaIntval(100 * $booking->paid_amount);
        // Note: amountInCents could be negative if it's a refund
        if ($paymentData->amountInCents > $leftToPayInCents || $paymentData->amountInCents < $maxToRefundInCents) {
            pnLog("[AddBookingPaymentAction] Payment amount is invalid. Amount: $paymentData->amountInCents, Left to pay: $leftToPayInCents, Max to refund: $maxToRefundInCents");

            return false;
        }

        $paymentData->paidAt->subMinutes(10);

        // Save in DB first
        $payment = AddBookingPaymentInDbAction::run($team, $booking, $paymentData);
        nLog("[AddBookingPaymentAction] Payment added to DB. Payment ID: $payment->id");

        // Then, update provider: TODO UPDATE
        $api = $booking->getProviderConnector();
        AddBookingPaymentToProviderAction::dispatchIf(
            $api instanceof PaymentsApi && ShouldPostToProviderInLocalEnvAction::run(),
            $team,
            $booking->id,
            $payment);

        // Update the alerts for the fresh booking
        UpdateBookingPaymentAlertsAction::run($booking->fresh(), $paymentData->authorId ?? 0);
        nLog("[AddBookingPaymentAction] Alerts updated for booking ID: $booking->id");

        return true;
    }

    public function rules(): array
    {
        return [
            'amount_in_cents' => ['required'],
            'kind' => ['required', 'string'],
            'paid_at' => ['required'],
        ];
    }
}
