<?php

namespace App\Actions\Bookings\Sources;

use App\Models\Booking;
use Lorisleiva\Actions\Concerns\AsAction;

/**
 * Use this class to get the channel to pay back amount. Here, we should divide by each channel, where an action will return the amount. Each
 * action should consider the different ways we get the information of bookings from the same channel per each Channel Manager.
 */
class GetChannelToPayBackAmountAction
{
    use AsAction;

    public function handle(Booking $booking): ?float
    {
        // Divide by channels:

        // Airbnb
        if ($booking->sourceNameContainsAirbnb()) {
            return CalculateAirbnbPayBackValuesAction::run($booking)?->airbnb_to_pay_back;
        }

        // Other sources
        return null; //This means we don't have enough info to get this value
    }
}
