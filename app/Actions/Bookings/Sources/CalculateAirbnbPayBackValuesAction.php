<?php

namespace App\Actions\Bookings\Sources;

use App\Actions\Bookings\Comments\SplitAirbnbCommentIntoLinesAction;
use App\Actions\Bookings\Pricing\ExtractPriceFromCommentLineAction;
use App\DTO\Bookings\AirbnbPayBackValuesDto;
use App\DTO\Bookings\BookingCommentLineDto;
use App\Models\Booking;
use Lorisleiva\Actions\Concerns\AsAction;

/**
 * This class should be used to get the airbnb to pay back and
 * already paid back values for each of our integrated channel managers,
 * as each manages this info differently.
 */
class CalculateAirbnbPayBackValuesAction
{
    use AsAction;

    public function handle(Booking $booking): ?AirbnbPayBackValuesDto
    {
        // Ensure first that the Channel Manager is providing us with enough info to get this
        if (! IsAirbnbWithMonetaryCommentsAction::run($booking)) {
            return null;
        }

        $lines = SplitAirbnbCommentIntoLinesAction::run($booking->comments->first()->content);

        $airbnb_to_pay_back = 0.0;
        $airbnb_already_paid_back = 0.0;

        /** @var BookingCommentLineDto $line */
        foreach ($lines as $line) {
            switch ($line->left) {
                case 'Airbnb to pay back':
                    $airbnb_to_pay_back = ExtractPriceFromCommentLineAction::run($line->right);
                    break;
                case 'Airbnb already paid back':
                    $airbnb_already_paid_back = ExtractPriceFromCommentLineAction::run($line->right);
                    break;
            }
        }

        return new AirbnbPayBackValuesDto($airbnb_to_pay_back, $airbnb_already_paid_back);
    }
}
