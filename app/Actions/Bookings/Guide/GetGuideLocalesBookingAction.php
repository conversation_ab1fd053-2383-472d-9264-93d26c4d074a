<?php

namespace App\Actions\Bookings\Guide;

use App\Models\Booking;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class GetGuideLocalesBookingAction
{
    use AsAction;

    public function handle(Team $team, Booking $booking): array
    {
        if (! $team->canUseGuestsApp()) {
            return [];
        }

        if (! $booking->guestApplicationRentalSettings->guest_app) {
            return [];
        }

        $guide = $booking->guestApplicationRentalSettings;

        if (empty($guide->guide)) {
            return [];
        }

        $languages = [];

        foreach ($guide->guide as $impl) {
            $languages[] = $impl['language'];
        }

        if (empty($languages)) {
            return [];
        }

        return $languages;
    }
}
