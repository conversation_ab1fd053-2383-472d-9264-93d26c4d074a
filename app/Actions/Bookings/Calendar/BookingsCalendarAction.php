<?php

namespace App\Actions\Bookings\Calendar;

use App\Actions\Rentals\GetRentalsFromFilterAction;
use App\Models\Booking;
use App\Models\Team;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class BookingsCalendarAction
{
    use AsAction;

    public function prepareForValidation(ActionRequest $request): void
    {
        $request->merge([
            'user_id' => auth()->user()->id,
        ]);
    }

    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'year' => 'required|integer',
            'month' => 'required|integer|min:1|max:12',
            'rentals' => 'required|array',
            'divider' => 'required|integer|min:1|max:12',
            'sources' => 'nullable|array',
            'bookingTags' => 'nullable|array',
        ];
    }

    public function asController(ActionRequest $request)
    {
        $request->validated();
        $user = auth()->user();
        $rentals = GetRentalsFromFilterAction::run();
        $date = Carbon::create($request->input('year'), $request->input('month'))->startOfMonth();
        $sources = $request->input('sources');
        $bookingTags = $request->input('bookingTags');

        return $this->handle(
            team: $user->team,
            rentals: $rentals,
            date: $date,
            divider: $request->input('divider', 1),
            sources: $sources,
            bookingTags: $bookingTags,
        );
    }

    public function handle(Team $team, array $rentals, Carbon $date, int $divider = 1, ?array $sources = null, ?array $bookingTags = null)
    {
        return [
            'team_id' => auth()->user()->current_team_id,
            'from' => $date->copy()->startOfMonth()->toDateString(),
            'to' => $date->copy()->endOfMonth()->toDateString(),
            'bookings' => $this->bookings($team, $rentals, $date, $sources, $bookingTags)->count(),
            'data' => $this->bookingsToReturn(
                team: $team,
                rentals: $rentals,
                date: $date,
                divider: $divider,
                sources: $sources,
                bookingTags: $bookingTags,
            ),
        ];
    }

    private function bookings(Team $team, array $rentals, Carbon $date, ?array $sources = null, ?array $bookingTags = null): Collection
    {
        return once(fn () => Booking::query()
            ->onTeam($team)
            ->filledBetween($date->copy()->startOfMonth(), $date->copy()->endOfMonth())
            ->whereIn('rental_id', $rentals)
            ->when(! empty($sources),
                fn ($query) => $query->whereIn('booking.source_id', $sources)
            )
            ->whereTagsIn($bookingTags)
            ->with(['source', 'client'])
            ->select([
                'id',
                'rental_id',
                'client_id',
                'team_id',
                'start_at',
                'end_at',
                'adults',
                'children',
                'reference',
                'source_id',
                'status',
                'final_price',
                'final_rental_price',
                'paid_amount',
                'provider_id',
                rawQueryBookingCheckInTime($this->defaultCheckInTime($team)), // We are skipping here the rental default ci time but this only affects a few pixels
                rawQueryBookingCheckOutTime($this->defaultCheckOutTime($team)), // We are skipping here the rental default co time but this only affects a few pixels
            ])
            ->oldest('start_at')
            ->orderBy('rental_id')
            ->get()
        );
    }

    private function defaultCheckInTime(Team $team): string
    {
        return once(fn () => $team->defaultArrivalTime());
    }

    private function defaultCheckOutTime(Team $team): string
    {
        return once(fn () => $team->defaultDepartureTime());
    }

    private function bookingsToReturn(Team $team, array $rentals, Carbon $date, int $divider = 1, ?array $sources = null, ?array $bookingTags = null): Collection
    {
        $firstDayOfMonth = $date->copy()->startOfMonth();
        $lastDayOfMonth = $date->copy()->endOfMonth();

        $bookings = $this->bookings(
            team: $team,
            rentals: $rentals,
            date: $date,
            sources: $sources,
            bookingTags: $bookingTags,
        );

        $used = collect();
        $dates = collect($this->dates($date));

        $tr = $dates->map(
            function (Carbon $date) use ($divider, $lastDayOfMonth, $firstDayOfMonth, $bookings, &$used) {
                $startOfDay = $date->copy()->startOfDay()->timestamp;
                $endOfDay = $date->copy()->endOfDay()->timestamp;

                $validBookings = $bookings
                    ->where('end_at', '>=', $startOfDay)
                    ->where('start_at', '<=', $endOfDay)
                    ->whereNotIn('id', $used);

                if ($validBookings->isEmpty()) {
                    return [];
                }

                $ids = $validBookings->pluck('id');
                $used = $used->push($ids)->flatten()->filter();

                return $validBookings
                    ->map(
                        fn (Booking $booking) => GetBookingDataForCalendarAction::run(
                            booking: $booking,
                            date: $date,
                            firstDayOfMonth: $firstDayOfMonth,
                            lastDayOfMonth: $lastDayOfMonth,
                            divider: $divider
                        ))
                    ->values()
                    ->toArray();
            }
        );

        return $tr->flatten(1)->sortBy('date');
    }

    private function dates(Carbon $date): array
    {
        return once(fn () => $date->startOfMonth()->daysUntil($date->copy()->endOfMonth())->toArray());
    }
}
