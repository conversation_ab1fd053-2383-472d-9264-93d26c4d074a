<?php

namespace App\Actions\Bookings\Fees;

use App\Actions\Auth\VerifyUserHasAccessToBookingAction;
use App\Actions\Bookings\GetBookingWithRelationsAction;
use App\DTO\Bookings\BookingDto;
use App\Http\Resources\BookingResource;
use App\Models\Booking;
use App\Models\BookingFee;
use App\Models\BookingTax;
use App\Models\Team;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;

class EditFeeBookingByIdAction
{
    use AsAction;

    public function asController(Team $team, Booking $booking, BookingFee $fee): BookingResource
    {
        VerifyUserHasAccessToBookingAction::run($booking->id);

        $booking = Booking::getBookingModel($team, $booking->id);

        // Get the request body with the comment in it.
        $amount = request('amount');
        $times_booked = request('times_booked');

        // Check if the comment is valid
        if (empty($amount)) {
            abort(422, 'Missing amount.');
        }

        // Check if the comment is valid
        if (empty($times_booked)) {
            abort(422, 'Missing times_booked.');
        }

        $current_price = $fee->price * $fee->times_booked;

        $difference = ($amount * $times_booked) - $current_price;

        //  nothing to update.
        if ($difference == 0) {
            return GetBookingWithRelationsAction::make()->asController($team, $booking->id);
        }

        $request = request();
        $booking = $this->handle($booking, $team, $fee, $request);

        if (empty($booking)) {
            abort(500, 'Bad Response from BookingSync');
        }

        // Return the updated booking.
        return GetBookingWithRelationsAction::make()->asController($team, $booking);
    }

    public function handle(Booking $booking, Team $team, BookingFee $fee, Request $request): ?Booking
    {
        $amount = $request->get('amount');
        $times_booked = $request->get('times_booked');
        $current_price = $fee->price * $fee->times_booked;
        $difference = ($amount * $times_booked) - $current_price;
        $amount = number_format($amount, 2, '.', '');

        $requester = $booking->getProviderConnector();

        $other_fees = BookingFee::whereBookingId($booking->id)
            ->where('team_id', '=', $team->id)
            ->get();
        $fees = collect();
        $fees->add([
            'id' => $fee->id,
            'times_booked' => $times_booked,
            'price' => $amount,
        ]);
        foreach ($other_fees as $other_fee) {
            if ($other_fee->id == $fee->id) {
                continue;
            }
            $fees->add([
                'id' => $other_fee->id,
                'times_booked' => $other_fee->times_booked,
                'price' => number_format($other_fee->price, 2, '.', ''),
            ]);
        }

        $initial_final_price = $booking->final_price;
        $updated_final_price = $initial_final_price + $difference;
        $final_price = number_format($updated_final_price, 2, '.', '');

        $taxes = collect();
        $found_taxes = BookingTax::whereBookingId($booking->id)
            ->whereTeamId($team->id)
            ->get();
        foreach ($found_taxes as $other_tax) {
            $taxes->add([
                'id' => $other_tax->id,
                'amount' => number_format($other_tax->amount, 2, '.', ''),
                'included' => $other_tax->tax_included_in_price,
            ]);
        }

        $booking_data = BookingDto::create($team->id, $booking->id)
            ->setFinalPrice($final_price)
            ->setInitialPrice(number_format($booking->initial_price, 2, '.', ''))
            ->setDiscount($booking->discount)
            ->setBookingFees($fees)
            ->setBookingTaxes($taxes);

        $result = $requester->updateBookingByModel($booking, $booking_data);

        if (empty($result)) {
            return null;
        }

        return $booking;
    }
}
