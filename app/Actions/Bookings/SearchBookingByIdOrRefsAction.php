<?php

namespace App\Actions\Bookings;

use App\Http\Resources\SlimBookingResource;
use App\Models\Booking;
use App\Models\Team;
use App\Query\BookingQuery;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class SearchBookingByIdOrRefsAction
{
    use AsAction;

    public array $rules = [
        'search' => ['required', 'string'],
    ];

    public function asController(Team $team, ActionRequest $request): AnonymousResourceCollection
    {
        $search = $request->input('search');
        $user = $request->user();
        $rentals = $user->getRentalsForUser();

        $bookings = $this->handle($team, $search, $rentals);

        return SlimBookingResource::collection($bookings->paginate());
    }

    public function handle(Team $team, string $search, array $rentals): BookingQuery
    {
        return Booking::query()
            ->withoutGlobalScopes()
            ->onTeam($team)
            ->onRentals($rentals)
            ->where(function (BookingQuery $query) use ($search) {
                $query->where('id', 'like', "$search%")
                    ->orWhere('reference', 'like', "$search%")
                    ->orWhere('channel_id', 'like', "$search%");
            });
    }
}
