<?php

namespace App\Actions\Bookings\Actions;

use App\Actions\Auth\VerifyUserHasAccessToBookingAction;
use App\Actions\Bookings\GetBookingWithRelationsAction;
use App\DataProviders\ApiConnectors\Interfaces\ManageRequestsApi;
use App\Http\Resources\BookingResource;
use App\Models\Booking;
use App\Models\Team;
use App\Models\User;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class AcceptBookingRequestAction
{
    use AsAction;

    public function asController(ActionRequest $request, Team $team, Booking $booking): BookingResource
    {
        VerifyUserHasAccessToBookingAction::run($booking);
        abort_unless($booking->canBeAccepted(), 400, 'Booking cannot be accepted');

        $booking = $this->handle($team, $booking, $request->user());

        return GetBookingWithRelationsAction::make()->asController($team, $booking);
    }

    public function handle(Team $team, Booking $booking, ?User $user = null): Booking
    {
        pnLog("[AcceptBookingRequestAction] Accepting booking: $booking->id by $user->id", $team);

        $connector = $booking->getProviderConnector();
        if (! $connector instanceof ManageRequestsApi) {
            abort(400, 'Provider does not support managing requests');
        }

        $success = $connector->acceptRequest($booking);

        abort_unless($success, 400, 'Failed to accept booking request');

        return $booking;
    }
}
