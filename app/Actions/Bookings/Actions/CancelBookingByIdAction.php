<?php

namespace App\Actions\Bookings\Actions;

use App\Actions\Auth\VerifyUserHasAccessToBookingAction;
use App\Events\Booking\BookingCancelledEvent;
use App\Models\Booking;
use App\Models\Team;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Http\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CancelBookingByIdAction
{
    use AsAction;

    public string $commandSignature = 'ninja:cancel-booking-by-id {team} {booking}';

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->permissionFor('can_manage_bookings') || $request->user()->permissionFor('block_dates');
    }

    public function asCommand(Command $command): void
    {
        $teamId = intval($command->argument('team'));
        $team = Team::find($teamId);
        $bookingId = intval($command->argument('booking'));
        $booking = Booking::getBookingModel($teamId, $bookingId);

        $this->handle($booking, $team);
        $booking->getProviderConnector()?->updateBooking($booking->external_id);
    }

    public function asController(ActionRequest $request, Team $team, Booking $booking): Response
    {
        VerifyUserHasAccessToBookingAction::run($booking);
        $this->checkPermissions($request, $booking);

        $deleted = $this->handle($booking, $team, $request->user());

        if ($deleted) {
            $booking->getProviderConnector()?->updateBooking($booking->external_id);

            return response('1', 200);
        }

        return response('0', 200);
    }

    public function handle(Booking $booking, Team $team, ?User $user = null): bool
    {
        $result = $booking->getProviderConnector()->deleteBookingById($booking);

        if ($result) {
            event(new BookingCancelledEvent($team, $booking->id, $user));
        }

        return $result;
    }

    protected function checkPermissions(ActionRequest $request, Booking $booking): void
    {
        $canManage = $request->user()->permissionFor('can_manage_bookings');
        $canBlockDates = $request->user()->permissionFor('block_dates');
        $isBlock = $booking->status->isUnavailable();

        abort_if(! $isBlock && ! $canManage, 400, 'You do not have permission to cancel bookings.');
        abort_if($isBlock && ! $canManage && ! $canBlockDates, 400, 'You do not have permission to cancel bookings.');
    }
}
