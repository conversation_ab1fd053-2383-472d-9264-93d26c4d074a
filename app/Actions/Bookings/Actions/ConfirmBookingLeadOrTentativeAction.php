<?php

namespace App\Actions\Bookings\Actions;

use App\Actions\Auth\VerifyUserHasAccessToBookingAction;
use App\Actions\Bookings\GetBookingWithRelationsAction;
use App\DataProviders\Providers\NoProvider;
use App\Enum\BookingStatusEnum;
use App\Events\Booking\BookingConfirmedEvent;
use App\Http\Resources\BookingResource;
use App\Models\Booking;
use App\Models\Team;
use App\Models\User;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class ConfirmBookingLeadOrTentativeAction
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->permissionFor('can_manage_bookings');
    }

    public function asController(ActionRequest $request, Team $team, Booking $booking): BookingResource
    {
        VerifyUserHasAccessToBookingAction::run($booking);
        abort_if($booking->status !== BookingStatusEnum::LEAD && $booking->status !== BookingStatusEnum::TENTATIVE, 403, 'Booking is not in a lead or tentative status');
        abort_if($booking->provider_id !== NoProvider::ID, 403, 'Only internal bookings can be confirmed');

        $booking = $this->handle($team, $booking, $request->user());

        return GetBookingWithRelationsAction::make()->asController($team, $booking);
    }

    public function handle(Team $team, Booking $booking, ?User $user = null): Booking
    {
        $booking->status = BookingStatusEnum::BOOKED;
        $booking->save();

        event(new BookingConfirmedEvent($team, $booking->id, $user));

        return $booking;
    }
}
