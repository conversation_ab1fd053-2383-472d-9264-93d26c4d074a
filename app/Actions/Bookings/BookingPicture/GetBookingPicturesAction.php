<?php

namespace App\Actions\Bookings\BookingPicture;

use App\Http\Resources\PictureResource;
use App\Models\Booking;
use App\Models\NinjaPicture;
use App\Models\Team;
use App\Query\NinjaPictureQuery;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Lorisleiva\Actions\Concerns\AsAction;

class GetBookingPicturesAction
{
    use AsAction;

    protected const ITEMS_PER_PAGE = 500;

    public function asController(Team $team, Booking $booking): LengthAwarePaginator
    {
        return $this->handle($team, $booking, request()->user()->permissionFor('view_detailed_payment_info'));
    }

    public function handle(Team $team, Booking $booking, bool $withPaymentPictures = true): LengthAwarePaginator
    {
        return NinjaPicture::query()
            ->hasTeam($team)
            ->hasBooking($booking)
            ->when(! $withPaymentPictures, fn (NinjaPictureQuery $query) => $query->whereNull('payment_id'))
            ->with('author')
            ->latest()
            ->paginate(self::ITEMS_PER_PAGE);
    }

    public function jsonResponse(LengthAwarePaginator $pictures): AnonymousResourceCollection
    {
        return PictureResource::collection($pictures);
    }
}
