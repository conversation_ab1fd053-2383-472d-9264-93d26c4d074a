<?php

namespace App\Notifications;

use App\Models\Booking;
use App\Models\Rental;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackAttachment;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class CommentCreatedNotification extends Notification implements ShouldQueue
{
    use NinjaNotification;
    use Queueable;

    public $booking;

    public $rental;

    public $content;

    public $author;

    /**
     * Create a new notification instance.
     */
    public function __construct(Booking $booking, Rental $rental, $content, $author)
    {
        $this->booking = $booking;
        $this->rental = $rental;
        $this->content = $content;
        $this->author = $author;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['slack'];
    }

    public function toSlack($notifiable): SlackMessage
    {
        $booking = $this->booking;
        $rental = $this->rental;
        $author = $this->author !== null && User::whereId($this->author)
            ->exists() ? ucwords(User::whereId($this->author)
            ->select('name')
            ->first()->name) : $booking->source_public_name;
        $from = Carbon::createFromTimestampUTC($booking->start_at)
            ->toFormattedDateString();
        $to = Carbon::createFromTimestampUTC($booking->end_at)
            ->toFormattedDateString();

        $title = __('messages.new_comment_created.title', ['rental' => $rental->name, 'from' => $from, 'to' => $to]);
        $body = $this->content;
        $config = $this->config($notifiable);

        return $this
            ->newSlackMessage($notifiable)
            ->content("$title\n$body")
            ->attachment(fn (SlackAttachment $attachment) => $attachment
                ->title($rental->name, $config->rnAppTargetDomain(config('ninja.booking_view')."/$booking->id"))
                ->fields([
                    'User' => $author,
                    'From' => $from,
                    'To' => $to,
                ]));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [];
    }
}
