<?php

namespace App\Notifications;

use App\Exceptions\NinjaNotifiableNotDeclaredException;
use App\Flavors\NinjaFlavor;
use App\Notifications\Messages\NinjaMailMessage;
use App\Traits\NinjaNotifiable;
use Exception;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Support\Arr;
use Throwable;

trait NinjaNotification
{
    public $tries = 1;

    public $maxExceptions = 1;

    /**
     * @param  $notifiable  : can't have Class type, provider config checks if the given class has NinjaNotifiable trait
     *
     * @throws NinjaNotifiableNotDeclaredException
     */
    public function newSlackMessage($notifiable): SlackMessage
    {
        $config = $this->config($notifiable);

        return (new SlackMessage)
            ->from($config->rnAppName())
            ->image(a($config->rnAppLogo()));
    }

    /**
     * @throws NinjaNotifiableNotDeclaredException
     */
    public function config($notifiable): NinjaFlavor
    {
        if (Arr::has(class_uses($notifiable), NinjaNotifiable::class)) {
            return $notifiable->config();
        }
        throw new NinjaNotifiableNotDeclaredException("Configuration not declared on $notifiable NinjaNotifiable");
    }

    /**
     * Handle a job failure.
     *
     *
     * @throws Exception
     */
    public function failed(Throwable $exception): void
    {
        report($exception);
    }

    /**
     * @throws NinjaNotifiableNotDeclaredException
     */
    public function newMailMessage($notifiable): NinjaMailMessage
    {
        return NinjaMailMessage::fromNinjaNotifiable($this->config($notifiable));
    }
}
