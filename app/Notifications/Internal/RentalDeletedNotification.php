<?php

namespace App\Notifications\Internal;

use App\Events\RentalDeletedEvent;
use App\Models\Rental;
use App\Models\RentalNinjaTeam;
use App\Notifications\NinjaNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;

class RentalDeletedNotification extends Notification implements ShouldQueue
{
    use NinjaNotification;
    use Queueable;

    /** @var RentalDeletedEvent */
    public $event;

    /**
     * Create a new notification instance.
     */
    public function __construct(RentalDeletedEvent $event)
    {
        $this->event = $event;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['slack'];
    }

    public function toSlack($notifiable): SlackMessage
    {
        $team = $this->event->team;
        $rentalId = is_iterable($this->event->rentalId) ? $this->event->rentalId[0] : $this->event->rentalId;
        $rental = Rental::getRentalModel($team, $rentalId);
        $provider = $rental->provider();
        $config = $team->config();
        $teamName = strtoupper($team->name);
        $providerName = $provider->fullName();

        return (new SlackMessage())
            ->from($config->rnAppName())
            ->image($provider->providerLogo()) // Can't use NinjaSlackMessage trait because we are using the provider's logo here, not the product logo
            ->to(RentalNinjaTeam::channel())
            ->content("The $teamName team is ⬇️ one rental in $providerName. It has $team->rentals.");
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [];
    }
}
