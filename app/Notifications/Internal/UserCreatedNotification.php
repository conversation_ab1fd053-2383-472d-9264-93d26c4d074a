<?php

namespace App\Notifications\Internal;

use App;
use App\Models\RentalNinjaTeam;
use App\Models\User;
use App\Notifications\NinjaNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class UserCreatedNotification extends Notification implements ShouldQueue
{
    use NinjaNotification;
    use Queueable;

    public $user;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['slack'];
    }

    public function toSlack($notifiable): SlackMessage
    {
        $env = App::environment();
        $provider = Str::title($this->user->provider()->fullName());

        return (new SlackMessage())
            ->from("Rental Ninja [$env]")
            ->image($this->user->provider()->providerLogo())
            ->to(RentalNinjaTeam::channel())
            ->content("New user {$this->user->name} on $provider team {$this->user?->team?->name} :smiley:");
    }
}
