<?php

namespace App\Notifications;

use App\Actions\Support\Currencies\FormatCurrencyAction;
use App\Exceptions\NinjaNotifiableNotDeclaredException;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Rental;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\SlackAttachment;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class PaymentCreatedNotification extends Notification
{
    use NinjaNotification;
    use Queueable;

    public $rental;

    public $booking;

    public BookingPayment $payment;

    public function __construct(Booking $booking, Rental $rental, BookingPayment $payment)
    {
        $this->booking = $booking;
        $this->rental = $rental;
        $this->payment = $payment;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['slack'];
    }

    /**
     * @throws NinjaNotifiableNotDeclaredException
     */
    public function toSlack($notifiable): SlackMessage
    {
        $booking = $this->booking;
        $rental = $this->rental;
        $payment = $this->payment;

        $author = ucwords($payment->author?->name ?? '');

        $formatted_payment = FormatCurrencyAction::run($payment->currency, $payment->amount_in_cents / 100);

        $body = strlen($author) > 0 ? __('messages.alert.payment_received.author', [
            'author' => $author,
            'amount' => $formatted_payment,
            'rental' => $rental->name,
        ]) : __('messages.alert.payment_received.no_author', [
            'amount' => $formatted_payment,
            'rental' => $rental->name,
        ]);

        $config = $this->config($notifiable);

        return $this
            ->newSlackMessage($notifiable)
            ->content($body)
            ->success()
            ->attachment(fn (SlackAttachment $attachment) => $attachment
                ->title($rental->name, $config->rnAppTargetDomain(config('ninja.booking_view')."/$booking->id"))
                ->fields([
                    'Kind' => $payment->kind,
                    'Value' => $formatted_payment,
                    'From' => Carbon::createFromTimestamp($booking->start_at)->toFormattedDateString(),
                    'To' => Carbon::createFromTimestamp($booking->end_at)->toFormattedDateString(),
                ]));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [];
    }
}
