<?php

namespace App\Notifications;

use App\Models\UserNotificationToken;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\InteractsWithQueue;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;
use Psr\Log\LogLevel;

class NinjaFcmNotification extends Notification implements ShouldQueue
{
    use InteractsWithQueue, NinjaNotification, Queueable;

    public ?string $title;

    public ?string $body;

    public ?string $type;

    public ?array $data;

    public ?string $image;

    public UserNotificationToken $notifiable;

    /**
     * Create a new notification instance.
     */
    public function __construct(UserNotificationToken $notifiable, string $title, string $body, string $type, ?array $data = [], ?string $image = null)
    {
        $this->title = $title;
        $this->body = $body;
        $this->type = $type;
        $this->data = $data;
        $this->image = $image;
        $this->notifiable = $notifiable;

        // FCM Sometimes reply with a: "NotificationChannels\Fcm\Exceptions\CouldNotSendNotification: The service is currently unavailable." exception.
        $this->tries = 2;
        $this->maxExceptions = 2;
        $this->onQueue('notifications');
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return [FcmChannel::class];
    }

    /**
     * Determine if the notification should be sent.
     *
     * @param  mixed  $notifiable
     */
    public function shouldSend($notifiable, string $channel): bool
    {
        if (app()->isLocal()) {
            return false;
        }

        return true;
    }

    /**
     * @throws Exception
     */
    public function toFcm(UserNotificationToken $notifiable): FcmMessage
    {
        // Source: https://github.com/laravel-notification-channels/fcm?tab=readme-ov-file + official website documentation
        $notification = FcmNotification::create()
            ->image($this->image)
            ->title($this->title)
            ->body($this->body);

        $data = array_merge(
            $this->data,
            UserNotificationToken::type($this->type),
            [
                'title' => $this->title,
                'body' => $this->body,
            ]
        );
        $data = array_map('strval', $data);

        $message = FcmMessage::create()
            ->data($data)
            ->custom([
                'android' => UserNotificationToken::android(
                    $this->title,
                    $this->body,
                    $this->type.'_android',
                    $this->image,
                    // Icon can be added here, but works for Android only right now (Dec '23). Must be a pointer to a local image in the device.
                    // https://firebase.google.com/docs/cloud-messaging/send-message?authuser=0#example-notification-message-with-platform-specific-delivery-options
                    //https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages?authuser=0#androidnotification
                    // It will override the default one (if any) stated in the Android manifest: https://firebase.google.com/docs/cloud-messaging/android/receive
                    // If no default in the Android Manifest, the App's one with white background is used.
                ),
                'apns' => UserNotificationToken::apns(
                    $this->title,
                    $this->body,
                    $this->type.'_ios',
                    $this->image,
                ),
            ])
            ->notification($notification);

        nLog('Sending Notification to user', $notifiable->user->current_team_id, LogLevel::INFO, $message->toArray());

        return $message;
    }
}
