<?php

namespace App\Notifications;

use App\Actions\Accounting\Settlements\GetDownloadUrlSettlementAction;
use App\DTO\Accounting\SettlementRecipientDto;
use App\Models\Settlement;
use App\Models\Team;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;
use Throwable;

class SettlementNotification extends Notification implements ShouldQueue
{
    use NinjaNotification;
    use Queueable;

    public Settlement $settlement;

    public Team $team;

    public function __construct(Team $team, Settlement $settlement)
    {
        $this->team = $team;
        $this->settlement = $settlement;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     *
     * @throws Throwable
     */
    public function toMail(SettlementRecipientDto $notifiable): MailMessage
    {
        // Make sure that we have the downloadable content available
        $url = GetDownloadUrlSettlementAction::run(
            request(),
            $this->team,
            $this->settlement,
            $notifiable->language);

        // Note: the language passed there will be the language for the runtime and therefore, the language for all the documents generated and this email as well.
        $config = $this->config($notifiable);

        return $this
            ->newMailMessage($notifiable)
            ->success()
            ->from($config->mailFromAddress(), $this->team->name)
            ->subject(__('accounting_pdfs.email.statement_subject', ['name' => $this->settlement->name]))
            ->when($notifiable->send_copy_to_me, function (MailMessage $message) use ($notifiable) {
                $message->cc($notifiable->user()->email, $notifiable->user()->name);
            })
            ->line(new HtmlString(nl2br(e($notifiable->content))))
            ->action(__('accounting_pdfs.email.statement_button'), $url)
            ->line(__('messages.email.note'))
            ->salutation(ucwords($notifiable->user()->name).' @ '.ucwords($this->team->name));
    }
}
