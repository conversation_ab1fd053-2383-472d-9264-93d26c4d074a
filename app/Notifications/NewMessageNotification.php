<?php

namespace App\Notifications;

use App\Exceptions\NinjaNotifiableNotDeclaredException;
use App\Models\Message;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\SlackAttachment;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class NewMessageNotification extends Notification
{
    use NinjaNotification;
    use Queueable;

    public function __construct(
        public Message $message,
    ) {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['slack'];
    }

    /**
     * @throws NinjaNotifiableNotDeclaredException
     */
    public function toSlack($notifiable): SlackMessage
    {
        $message = $this->message;
        $message->load('booking.rental');
        $booking = $message->booking;

        $title = __('messages.new_message.title');
        $body = Str::limit($message->message, 300);
        $config = $this->config($notifiable);

        return $this
            ->newSlackMessage($notifiable)
            ->content("$title\n$body")
            ->success()
            ->attachment(fn (SlackAttachment $attachment) => $attachment
                ->title($booking->rental->name, $config->rnAppTargetDomain(config('ninja.inbox_view')."/$message->booking_id"))
                ->fields([
                    'Sender' => $message->sender_name,
                    'Source' => $booking->source_public_name,
                    'From' => $booking->getCarbonCheckInTime()->toFormattedDateString(),
                    'To' => $booking->getCarbonCheckOutTime()->toFormattedDateString(),
                ]));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [];
    }
}
