<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\UserNotificationToken;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class ChatMessageNotification extends Notification implements ShouldQueue
{
    use NinjaNotification;
    use Queueable;

    public ?string $senderName;

    public ?string $text;

    public ?array $data;

    public User $sender;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $sender, string $senderName, string $text, array $data)
    {
        $this->sender = $sender;
        $this->senderName = $senderName;
        $this->text = $text;
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return [FcmChannel::class];
    }

    public function toFcm($notifiable): FcmMessage
    {
        $fcmN = FcmNotification::create()
            ->image($this->sender->photo_url)
            ->title($this->senderName)
            ->body($this->text);

        $data = array_merge(
            $this->data,
            UserNotificationToken::type(UserNotificationToken::TYPE_CHAT_MESSAGE),
            [
                'title' => $this->senderName,
                'body' => $this->text,
            ]
        );
        $data = array_map('strval', $data);

        return FcmMessage::create()
            ->data($data)
            ->custom([
                'android' => UserNotificationToken::android(
                    $this->senderName,
                    $this->text,
                    'chat_notification_android',
                    $this->sender->photo_url
                ),
                'apns' => UserNotificationToken::apns(
                    $this->senderName,
                    $this->text,
                    'chat_notification_ios',
                    $this->sender->photo_url
                ),
            ])
            ->notification($fcmN);
    }
}
