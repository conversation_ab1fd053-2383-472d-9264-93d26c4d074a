<?php

namespace App\Exceptions;

use Exception;

class NinjaBookingException extends Exception
{
    public const UNAVAILABLE = 'unavailable';
    public const MINIMUM_STAY = 'minimum_stay';
    public const MAX_STAY = 'max_stay';
    public const CHANGEOVER = 'changeover';

    public string $type;
    public ?int $extra;

    public function __construct(
        string $type,
        ?int $extra = null,
        ?string $message = null // If you want to add an extra message
    ) {
        $this->type = $type;
        $this->extra = $extra;

        parent::__construct($message); // To be used without previous error: you must pass a $message and the $context. Otherwise just use a regular Exception
    }
}
