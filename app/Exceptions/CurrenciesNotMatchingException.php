<?php

namespace App\Exceptions;

use Exception;

class CurrenciesNotMatchingException extends Exception
{
    public function __construct(
        public int $teamId,
        public int $bookingId,
        public ?string $bookingCurrency = null,
        public ?string $paymentCurrency = null,
        public ?float $amount = null,
    ) {
        $message = 'The booking currency does not match with the currency indicated in the comments OR the charge amount is negative';
        parent::__construct($message, 500);
    }

    public function context(): array
    {
        return [
            'team_id' => $this->teamId,
            'booking_id' => $this->bookingId,
            'booking_currency' => $this->bookingCurrency,
            'payment_currency' => $this->paymentCurrency,
            'amount' => $this->amount,
        ];
    }
}
