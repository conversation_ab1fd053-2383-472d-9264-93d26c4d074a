<?php

namespace App\Exceptions;

use Exception;
use Throwable;

class RoleNotFoundException extends Exception
{
    public function __construct(string $message = '', int $code = 0, ?Throwable $previous = null)
    {
        if (empty($message)) {
            $message = "User role not found. We don't have that user role available. There may be a typo.";
        }
        parent::__construct($message, $code, $previous);
    }
}
