<?php

namespace App\Livewire;

use App\DTO\Website\WebsiteSlimRentalData;
use App\Models\Currency;
use App\Models\Team;
use App\Support\WebsiteLocaleHelper;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class PropertyGridCard extends Component
{
    public $rental;
    public $start;
    public $end;
    public $guests;
    public $markup;

    public function render(): View
    {
        return view('livewire.property-grid-card');
    }

    public function getRentalUrl(WebsiteSlimRentalData $rental): string
    {
        $params = '';
        if ($this->start && $this->end) {
            $params .= '?start='.$this->start;
            $params .= '&end='.$this->end;
        }
        if ($this->guests) {
            $params .= '&guests='.$this->guests;
        }
        $team = \Route::current()->parameter('team');
        $localeUrl = WebsiteLocaleHelper::getFromSession();
        if ($team != null && $team instanceof Team) {
            return $localeUrl.$team->id.'/property/'.$rental->id.$params;
        }

        return $localeUrl.'property/'.$rental->id.$params;
    }

    public function getAmount(int $value, $currency): string
    {
        $decimal = Currency::find(strtolower($currency))->decimal_digits;
        $symbol = Currency::find(strtolower($currency))->symbol;
        if ($decimal == 0) {
            $value = intval($value / 100)."  $symbol";
        } else {
            $value = number_format(round($value / 100, 2), 2)."  $symbol";
        }

        return $value;
    }
}
