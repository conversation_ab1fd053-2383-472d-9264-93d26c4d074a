<?php

namespace App\Livewire\Setups;

use App\Models\SetupCompany;
use App\Models\SetupCredential;
use Livewire\Attributes\On;
use Livewire\Component;

class SetupCredentialModal extends Component
{
    public $showModal = false;
    public $editingCredentialId = null;
    public $setupCompanyId = null;
    public $credentialTypes = [];
    public $passwordTypeField = 'password';
    public $passwordNotEmpty = false;

    public $credential = [
        'type' => '',
        'service' => '',
        'name' => '',
        'username' => '',
        'password' => '',
    ];

    public function rules(): array
    {
        return [
            'credential.type' => 'required|string|in:'.implode(',', SetupCredential::TYPES),
            'credential.name' => 'required|string|max:255',
            'credential.user' => 'required|string|max:255',
            'credential.password' => 'required|string|max:255',
        ];
    }

    public function mount($setupCompanyId): void
    {
        $this->setupCompanyId = $setupCompanyId;
        $this->credentialTypes = collect(SetupCredential::TYPES)
            ->map(function ($type) {
                return ['id' => $type, 'name' => ucfirst(str_replace('_', ' ', $type))];
            })
            ->toArray();
    }

    #[On('openCredentialModal')]
    public function openCredentialModal($credentialId = null, $airbnbHostId = null): void
    {
        $this->editingCredentialId = $credentialId;

        if ($credentialId) {
            $credential = SetupCredential::findOrFail($credentialId);
            $this->passwordNotEmpty = ! empty($credential->password);

            $this->credential = [
                'type' => $credential->type,
                'name' => $credential->service,
                'user' => $credential->username,
                'password' => $credential->password,
            ];
        } elseif ($airbnbHostId) {
            pnLog("[SetupCredentialModal] Trying to fetch credential for airbnb host $airbnbHostId which was not found in the database");
            $credential = SetupCredential::query()
                ->where('setup_company_id', $this->setupCompanyId)
                ->where('type', 'ota')
                ->where('service', '=', $airbnbHostId)
                ->first();
            $this->credential = [
                'type' => $credential?->type ?? 'ota',
                'name' => $credential?->service ?? $airbnbHostId,
                'user' => $credential?->username ?? '',
                'password' => $credential?->password ?? '',
            ];
        } else {
            $this->credential = [
                'type' => '',
                'name' => '',
                'user' => '',
                'password' => '',
            ];
        }

        $this->showModal = true;
    }

    public function showPassword(): void
    {
        if (\App\Models\RentalNinjaTeam::userHasNovaAccess(request()->user()) && ! is_null($this->editingCredentialId)) {
            $this->credential['password'] = decrypt($this->credential['password']);
            $this->passwordTypeField = 'text';
        }
    }

    public function saveCredential()
    {
        $this->validate();

        $setupCompany = SetupCompany::findOrFail($this->setupCompanyId);

        $setupCompany->setupCredentials()->updateOrCreate(['id' => $this->editingCredentialId],
            [
                'type' => $this->credential['type'],
                'service' => $this->credential['name'],
                'username' => $this->credential['user'],
                'password' => $this->credential['password'],
            ]
        );

        $this->showModal = false;
        $this->reset('credential', 'editingCredentialId', 'passwordNotEmpty');
        $this->dispatch('credentialSaved');
    }

    public function render()
    {
        return view('livewire.setups.setup-credential-modal');
    }
}
