<?php

namespace App\Livewire\Setups;

use App\Models\SetupCompany;
use App\Models\SetupRental;
use Closure;
use DB;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;

class SetupRentalsTab extends Component
{
    use WithFileUploads;

    #[Validate]
    public $rows = [];
    public $loginHash;
    public $setupCompany;
    public $allRentals;

    public function mount(SetupCompany $company)
    {
        $this->setupCompany = $company;
        $this->allRentals = $this->setupCompany->setupRentals;

        $unlockedRentals =
            $this->setupCompany->setupRentals->where('locked', false);

        if ($unlockedRentals->isNotEmpty()) {
            $this->rows = $unlockedRentals->map(function (SetupRental $rental, int $index) {
                return [
                    'rowIndex' => $index + 1,
                    'id' => $rental->id,
                    'name' => $rental->name,
                    'airbnb_id' => $rental->airbnb_id,
                    'booking_url' => $rental->booking_url,
                    'booking_legal_entity_id' => $rental->booking_legal_entity_id,
                    'booking_hotel_id' => $rental->booking_hotel_id,
                    'booking_room_id' => $rental->booking_room_id,
                    'from_database' => true,
                    'is_editable' => $rental->locked != true, // Add this line
                ];
            })
                ->toArray();
        } else {
            $this->rows = [
                ['rowIndex' => 1, 'id' => null, 'name' => '', 'airbnb_id' => '', 'booking_url' => '', 'booking_legal_entity_id' => '', 'booking_hotel_id' => '', 'booking_room_id' => '', 'from_database' => false, 'is_editable' => true],
            ];
        }
    }

    public function updatedRows(): void
    {
        $this->validate();
    }

    public function addRow()
    {
        $this->rows[] = [
            'rowIndex' => count($this->rows) + 1,
            'id' => null,
            'name' => '',
            'airbnb_id' => '',
            'booking_url' => '',
            'booking_legal_entity_id' => '',
            'booking_hotel_id' => '',
            'booking_room_id' => '',
            'from_database' => false,
            'is_editable' => true,
        ];
    }

    public function removeRow($index): void
    {
        if (! $this->rows[$index]['from_database']) {
            unset($this->rows[$index]);
            $this->rows = array_values($this->rows); // Re-index the array
        }
    }

    public function lockRentalRow($index): void
    {
        // Lock the setup_rental
        $rental = SetupRental::find($this->rows[$index]['id']);
        if (is_null($rental)) {
            return;
        }
        $rental->lockRental();

        unset($this->rows[$index]);
        $this->rows = array_values($this->rows); // Re-index the array
    }

    public function deleteSetupRentalRow($index): void
    {
        // Lock the setup_rental
        $rental = SetupRental::find($this->rows[$index]['id']);
        if ($rental) {
            $rental->delete();
            unset($this->rows[$index]);
            $this->rows = array_values($this->rows); // Re-index the array
        }
    }

    public function save()
    {
        if (empty($this->rows)) {
            session()->flash('error', 'No data to save. Please add at least one row.');

            return;
        }

        // if any rentals on all rentals contains the same airbnb_id or booking_url or booking_roomid as the new rows, throw an error
        $existingAirbnbIds = $this->allRentals->pluck('airbnb_id')
            ->filter()
            ->toArray();
        $existingBookingRoomIds = $this->allRentals->pluck('booking_room_id')
            ->filter()
            ->toArray();
        $hasError = false;

        foreach ($this->rows as $row) {
            // If id is set, it's an existing record, so skip validation
            if (isset($row['id'])) {
                continue;
            }
            if (in_array($row['airbnb_id'], $existingAirbnbIds)) {
                session()->flash('error', 'The airbnb id has already been taken: '.$row['airbnb_id']);
                $hasError = true;
            }
            if (in_array($row['booking_room_id'], $existingBookingRoomIds)) {
                session()->flash('error', 'The booking room id has already been taken: '.$row['booking_room_id']);
                $hasError = true;
            }
        }

        if ($hasError) {
            return;
        }

        $this->validate();

        try {
            DB::transaction(function () {
                foreach ($this->rows as $row) {
                    $row['booking_url'] = Str::before($row['booking_url'], '?');

                    if (! $row['is_editable']) {
                        continue; // Skip non-editable rows
                    }
                    $data = [
                        'name' => $row['name'],
                        'airbnb_id' => ! empty($row['airbnb_id']) ? (int) $row['airbnb_id'] : null,
                        'booking_url' => ! empty($row['booking_url']) ? $row['booking_url'] : null,
                        'booking_legal_entity_id' => ! empty($row['booking_legal_entity_id']) ? (int) $row['booking_legal_entity_id'] : null,
                        'booking_hotel_id' => ! empty($row['booking_hotel_id']) ? (int) $row['booking_hotel_id'] : null,
                        'booking_room_id' => ! empty($row['booking_room_id']) ? (int) $row['booking_room_id'] : null,
                    ];

                    if (isset($row['id'])) {
                        // Update existing record
                        SetupRental::where('id', $row['id'])
                            ->where('setup_company_id', $this->setupCompany->id)
                            ->update($data);
                    } // Try to avoid duplicates.
                    elseif (SetupRental::where('name', $data['name'])
                        ->where('team_id', $this->setupCompany->team_id)
                        ->exists()) {
                        SetupRental::where('name', $data['name'])
                            ->where('team_id', $this->setupCompany->team_id)
                            ->update($data);
                    } else {
                        // Create new record
                        $data['setup_company_id'] = $this->setupCompany->id;
                        $data['team_id'] = $this->setupCompany->team_id;
                        SetupRental::forceCreate($data);
                    }
                }

                // Delete rentals that were removed from the table
                // $existingIds = collect($this->rows)->pluck('id')->filter()->toArray();
                // OnboardingRental::where('setup_company_id', $this->setupCompany->id)
                //   ->whereNotIn('id', $existingIds)
                //   ->delete();
            });

            // Queue URL validation job
            // dispatch(new ValidateBookingUrls($this->rows));

            session()->flash('message', 'Data saved successfully! URL validation is in progress.');
            $this->reloadPage();
        } catch (Exception $e) {
            report($e);
            pnLog('[SetupRentalTab] Error saving setup rentals: '.$e->getMessage());
            session()->flash('error', 'An error occurred while saving. Please try again or contact support.');
        }
    }

    public function reloadPage(): void
    {
        $this->dispatch('reloadPage');
    }

    public function render(): View
    {
        return view('livewire.setups.setup-rentals-tab', ['canSave' => ! empty($this->rows)]);
    }

    protected function rules(): array
    {
        return [
            'rows.*.airbnb_id' => [
                'nullable',
                'integer',
                $this->uniqueInArray('airbnb_id'),
            ],
            'rows.*.name' => [
                'string',
                'required',
                'max:255',
            ],
            'rows.*.booking_url' => [
                'url',
                'nullable',
                function ($attribute, $value, $fail) {
                    if (strpos($value, 'https://www.booking.com/hotel/') !== 0) {
                        $fail('The booking URL must start with "https://www.booking.com/hotel/".');
                    }
                },
            ],
            'rows.*.booking_legal_entity_id' => [
                'integer',
                'nullable',
                'max:999999999',
                'required_with:rows.*.booking_url,rows.*.booking_room_id,rows.*.booking_hotel_id',
            ],
            'rows.*.booking_hotel_id' => [
                'integer',
                'nullable',
                'max:999999999',
                'required_with:rows.*.booking_url,rows.*.booking_room_id,rows.*.booking_legal_entity_id',
            ],
            'rows.*.booking_room_id' => [
                'integer',
                'nullable',
                'max:99999999999',
                $this->uniqueInArray('booking_room_id'),
                function ($attribute, $value, $fail) {
                    $index = explode('.', $attribute)[1];
                    $hotelId = $this->rows[$index]['booking_hotel_id'];
                    if ($hotelId && ! str_starts_with((string) $value, (string) $hotelId)) {
                        $fail("The booking room ID must start with the hotel ID ($hotelId).");
                    }
                    // if length of booking room id is equal to hotelId length + 1
                    if ($hotelId && strlen($value) <= strlen($hotelId)) {
                        $fail('The booking room ID must be longer than the hotel ID.');
                    }
                },
            ],
        ];
    }

    protected function uniqueInArray($field): Closure
    {
        return function ($attribute, $value, $fail) use ($field) {
            $count = collect($this->rows)
                ->where($field, $value)
                ->count();
            if ($count > 1) {
                $fail("The $field has already been taken.");
            }
        };
    }

    private function getHttpResponseCode($url): string
    {
        $headers = get_headers($url);

        return substr($headers[0], 9, 3);
    }
}
