<?php

namespace App\Livewire\Setups;

use App\Models\ChannelManager\LanguagesCM;
use App\Models\Currency;
use App\Models\SetupCompany;
use App\Models\SetupCredential;
use Livewire\Attributes\On;
use Livewire\Component;

class SetupServicesAndMarkupsTab extends Component
{
    public SetupCompany $company;
    public $airbnb_markup = '';
    public $booking_markup = '';
    public $currency = '';
    public $locale = '';
    public $credentials = [];
    // Select options
    public $credentialTypes = [];
    public $currencyOptions = [];
    public $localeOptions = [];

    public $newCredential = [
        'name' => '',
        'user' => '',
        'password' => '',
    ];

    public function rules()
    {
        return [
            'credentials.*.type' => 'required|string|in:'.implode(',', SetupCredential::TYPES),
            'credentials.*.name' => 'required|string|max:255',
            'credentials.*.user' => 'required|string|max:255',
            'credentials.*.password' => 'required|string|max:255',
            'newCredential.name' => 'required|string|max:255',
            'newCredential.user' => 'required|string|max:255',
            'newCredential.password' => 'required|string|max:255',
        ];
    }

    #[On('credentialSaved')]
    public function refresh()
    {
        $this->credentials = $this->company->setupCredentials->map(function ($credential) {
            return [
                'id' => $credential->id,
                'type' => $credential->type,
                'name' => $credential->service,
                'user' => $credential->username,
                'password' => $credential->password,
            ];
        })->toArray();
    }

    public function mount(SetupCompany $company)
    {
        $this->company = $company;

        $this->airbnb_markup = $company->airbnb_markup;
        $this->booking_markup = $company->booking_markup;
        $this->currency = $company->currency;
        $this->locale = $company->locale;
        $this->credentialTypes = collect(SetupCredential::TYPES)->map(function ($type) {
            return ['id' => $type, 'name' => $type];
        })->toArray();

        $this->credentials = $company->setupCredentials->map(function ($credential) {
            return [
                'id' => $credential->id,
                'type' => $credential->type,
                'name' => $credential->service,
                'user' => $credential->username,
                'password' => $credential->password,
            ];
        })->toArray();

        $this->currencyOptions = Currency::all()->map(fn (Currency $c) => ['id' => $c->id, 'name' => $c->name])->toArray();
        $this->localeOptions = LanguagesCM::all()->map(fn (LanguagesCM $c) => ['id' => $c->code, 'name' => $c->name])->toArray();
    }

    public function getFillOrUpdateLabel($credential)
    {
        return (! $credential['user'] || ! $credential['password']) ? 'Fill' : 'Update';
    }

    public function addCredential()
    {
        $data = $this->validate([
            'newCredential.type' => 'required|string|max:255',
            'newCredential.name' => 'required|string|max:255',
            'newCredential.user' => 'required|string|max:255',
            'newCredential.password' => 'required|string|max:255',
        ]);

        $data = $data['newCredential'];
        /** @var SetupCredential $model */
        $model = $this->company->setupCredentials()->create([
            'type' => $data['type'],
            'service' => $data['name'],
            'username' => $data['user'],
            'password' => $data['password'],
        ]);

        $this->credentials[] = array_merge(['id' => $model->id], $this->newCredential);
        $this->newCredential = ['type' => 'OTA', 'name' => '', 'user' => '', 'password' => ''];
    }

    public function removeCredential($index)
    {
        $this->company->setupCredentials()->find($this->credentials[$index]['id'])->delete();
        unset($this->credentials[$index]);
        $this->credentials = array_values($this->credentials);
    }

    public function openCredentialModal($credentialId = null)
    {
        $this->dispatch('openCredentialModal', $credentialId);
    }

    public function saveMarkup()
    {
        $data = $this->validate([
            'airbnb_markup' => 'required|numeric',
            'booking_markup' => 'required|numeric',
            'currency' => 'required|string|max:3',
            'locale' => 'required|string|max:2',
        ]);

        $this->company->fill($data)->save();

        // Here you would typically save the markup to your database
        // For this example, we'll just show a success message
        session()->flash('message', 'Account information saved successfully.');
    }

    public function render()
    {
        return view('livewire.setups.setup-services-and-markups-tab');
    }
}
