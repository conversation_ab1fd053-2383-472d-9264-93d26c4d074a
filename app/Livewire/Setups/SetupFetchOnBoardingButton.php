<?php

namespace App\Livewire\Setups;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;
use Livewire\Component;
use Spatie\LaravelMarkdown\MarkdownRenderer;

class SetupFetchOnBoardingButton extends Component
{
    public $setupId;
    public $onboardingId;
    public $markdownContent = '';
    public $contentUrl = '';
    public $title = '';
    public $showOverview = false;
    public $showInfo = false;
    public $hash;
    public $classes = '';

    public function mount($setupId, $onboardingId, $title, $showOverview = false, $showInfo = false, $hash)
    {
        $this->setupId = $setupId;
        $this->title = $title;
        $this->onboardingId = $onboardingId;
        $this->showOverview = $showOverview;
        $this->showInfo = $showInfo;
        $this->hash = $hash;

        $this->generateContentUrl();

        if ($this->showOverview) {
            $this->fetchMarkdown();
        }

        if ($this->onboardingId && $this->showInfo) {
            $this->fetchMarkdown();
        }
    }

    public function generateContentUrl()
    {
        if ($this->onboardingId) {
            $this->contentUrl = '/setup/'.$this->hash.'/'.$this->onboardingId.'/info';
        } else {
            $this->contentUrl = '/setup/'.$this->hash.'/overview';
        }
    }

    public function fetchMarkdown()
    {
        $this->markdownContent = '';

        if (! $this->setupId && ! $this->onboardingId) {
            $this->addError('markdown', 'Invalid setup or onboarding ID.');

            return;
        }

        $path = "setups/{$this->setupId}/listings_summary.md";

        if ($this->setupId && $this->onboardingId) {
            $path = "setups/{$this->setupId}/{$this->onboardingId}/onboarding.md";
        }

        if (Storage::disk('rn-setups')->exists($path)) {
            $markdown = Storage::disk('rn-setups')->get($path);

            // Use Spatie's MarkdownRenderer
            $renderer = app(MarkdownRenderer::class);
            $this->markdownContent = $renderer->toHtml($markdown);
        } else {
            $this->addError('markdown', 'Markdown file not found.');
        }
    }

    public function render()
    {
        if ($this->showOverview || $this->showInfo) {
            return View::make('markdown-content', ['content' => $this->markdownContent]);
        }

        return view('livewire.setups.setup-fetch-on-boarding-button');
    }
}
