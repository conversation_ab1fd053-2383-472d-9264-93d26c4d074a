<?php

namespace App\Livewire;

use App\DTO\Website\WebsiteConfigData;
use App\Support\WebsiteLocaleHelper;
use Artesaos\SEOTools\Traits\SEOTools as SEOToolsTrait;
use Livewire\Component;
use Str;

class WebHeader extends Component
{
    use SEOToolsTrait;

    public string $logo;
    public string $websiteSeoTitle;
    public string $localeUrl = '';
    public string $currentUrl = '';
    public string $currentDomain = '';
    public bool $isPreviewMode = false;
    public bool $showMobileMenu = false;
    public bool $isDirectStays = false;
    public array $locations = [];

    public function mount(): void
    {
        $distributionWebsite = request()->input('distribution');
        abort_if(is_null($distributionWebsite), 404, 'Website not found');

        $config = WebsiteConfigData::from($distributionWebsite);
        $this->localeUrl = WebsiteLocaleHelper::getFromSession();
        $this->logo = $config->logo;
        $this->websiteSeoTitle = $config->websiteSeoTitle;
        $websiteSeoKeywords = $config->keywords;
        $this->seo()->metatags()->setKeywords($websiteSeoKeywords);
        $this->seo()->setCanonical($config->currentUrl);
        $this->currentUrl = $config->currentUrl;
        $this->currentDomain = $config->domain;
        $this->isPreviewMode = $config->isPreviewMode;
        $this->isDirectStays = $config->isDirectStays;
        $locations = $config->locations;
        $localeUrl = $this->localeUrl;
        // url locations
        $locations = array_map(function ($location) use ($localeUrl, $config) {
            $currentDomain = $config->domain;
            $locationSlug = Str::slug($location);
            $url = "https://$currentDomain{$localeUrl}destinations/{$locationSlug}";

            return [
                'id' => strtolower($location),
                'name' => ucfirst($location),
                'url' => $url,
            ];
        }, $locations);
        $this->locations = $locations;
    }

    public function render()
    {
        return view('livewire.web-header')->title($this->websiteSeoTitle);
    }

    public function toggleMobileMenu(): void
    {
        $this->showMobileMenu = ! $this->showMobileMenu;
    }
}
