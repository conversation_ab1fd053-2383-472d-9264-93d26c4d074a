<?php

namespace App\Livewire;

use App\DTO\Website\WebsiteConfigData;
use Livewire\Component;

class WebsiteContactPage extends Component
{
    public WebsiteConfigData $distributionWebsite;

    public function mount()
    {
        $distributionWebsite = request()->input('distribution');
        $this->distributionWebsite = WebsiteConfigData::from($distributionWebsite);
    }

    public function render()
    {
        return view('livewire.website-contact-page');
    }
}
