<?php

namespace App\Livewire;

use App\DTO\Website\WebsiteRentalData;
use App\DTO\Website\WebsiteRentalPictureData;
use App\Enum\ImageTypeEnum;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Enumerable;
use Livewire\Component;

class PropertyMainHeroImages extends Component
{
    public WebsiteRentalData $rental;

    // rental pictures collection
    public WebsiteRentalPictureData $mainImage;
    public Enumerable $webRentalPictures;

    public function mount(WebsiteRentalData $rental): void
    {
        // TODO use imgix library to generate image url with src set
        $this->rental = $rental;

        $this->webRentalPictures = $rental->pictures;
        $this->mainImage = $rental->pictures->where('imageType', ImageTypeEnum::mainImage)->first() ?? $rental->pictures->first();

        if (count($this->webRentalPictures) < 2) {
            throw new Exception('Please add images to your rental');
        }
    }

    public function render(): View|string
    {
        $photosCount = $this->webRentalPictures->count();
        if ($photosCount < 3) {
            return '<div>Please add at least 3 images to your rental</div>';
        }

        return view('livewire.property-main-hero-images');
    }
}
