<?php

namespace App\Livewire;

use App\Actions\Rentals\GetAvailableRentalAndQuoteForWebsiteAction;
use App\DTO\Website\WebsiteConfigData;
use App\Rules\StartAndEndDateRule;
use App\Traits\Livewire\WithRange;
use Artesaos\SEOTools\Traits\SEOTools as SEOToolsTrait;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request as HttpRequest;
use Illuminate\Support\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;
use Str;

class PropertyWebsitePage extends Component
{
    use WithRange;
    use SEOToolsTrait;
    use WithPagination;

    public string $range;
    #[Url(history: true)]
    public string $start = '';
    #[Url(history: true)]
    public string $end = '';
    #[Url(history: true)]
    public string $location = 'all';
    #[Url(history: true)]
    public int $guests = 1;
    public int $teamId;
    #[Url]
    public bool $cancel;
    #[Url]
    public bool $success;

    public string $headline = '';
    public string $mainImage = '';
    public string $mainImageTitle = '';
    public string $mainImageDescription = '';
    public string $locale = 'en';
    public WebsiteConfigData $websiteConfig;
    public array $locations = [];
    public bool $hideLocation = false;
    #[Url]
    public int $page = 1;

    public function mount(?string $city, HttpRequest $request): void
    {
        $imgixParams = '?compress=auto&quality=97&fm=auto&ar=16:9&fit=crop&crop=entropy&w=1440';

        $this->websiteConfig = WebsiteConfigData::from($request->input('distribution'));
        $this->locations = $this->websiteConfig->locations;

        $this->headline = $this->websiteConfig->headline;
        $this->mainImage = $this->websiteConfig->mainImage.$imgixParams;
        $this->mainImageTitle = $this->websiteConfig->mainImageTitle;
        $this->mainImageDescription = $this->websiteConfig->mainImageDescription;

        $this->setMetaData();
    }

    private function setMetaData(): void
    {
        $title = $this->websiteConfig->websiteSeoTitle ?? $this->headline;
        $description = $this->websiteConfig->websiteSeoDescription ?? Str::limit($this->headline, 160);

        $this->seo()->setTitle($title);
        $this->seo()->setDescription($description);

        // OpenGraph
        $this->seo()->opengraph()->setTitle($title);
        $this->seo()->opengraph()->setDescription($description);
        $this->seo()->opengraph()->setUrl(url()->current());
        $this->seo()->opengraph()->addImage($this->mainImage, ['width' => 1200, 'height' => 630]);
        $this->seo()->opengraph()->setType('website');

        // Twitter Card
        $this->seo()->twitter()->setTitle($title);
        $this->seo()->twitter()->setDescription($description);
        $this->seo()->twitter()->setImage($this->mainImage);
    }

    #[Computed]
    public function now(): string
    {
        return Carbon::now()->format('Y-m-d');
    }

    #[Computed]
    public function rentals()
    {
        $start = null;
        $end = null;
        $guests = $this->guests;
        if ($this->start && $this->end) {
            $start = Carbon::parse($this->start);
            $end = Carbon::parse($this->end);
        }

        $websiteConfig = $this->websiteConfig;

        $rentals = collect($websiteConfig->rentalsWithLocation);
        $websiteConfig->rentals = $rentals->keys()->toArray();
        if ($this->location !== 'all') {
            $websiteConfig->rentals = $rentals->filter(function ($loc) {
                return Str::slug(strtolower($loc)) === strtolower($this->location);
            })->keys()->toArray();
        }

        return GetAvailableRentalAndQuoteForWebsiteAction::run($websiteConfig, $start, $end, $guests, 15);
    }

    public function rules(): array
    {
        return [
            'range' => ['required', 'string', new StartAndEndDateRule()],
        ];
    }

    #[On('updateSearch')]
    public function updateSearch(string $range, int $selectedGuests, string $selectedLocation)
    {
        $range = $this->getRange($range);
        if ($range) {
            $start = $range['start'];
            $end = $range['end'];

            $this->start = $start;
            $this->end = $end;
        }
        $this->guests = $selectedGuests;
        $this->location = $selectedLocation;
        $this->dispatch('scrollTo', ['element' => '#rentals']);
    }

    #[On('resetSearch')]
    public function resetSearch()
    {
        $this->start = '';
        $this->end = '';
        $this->guests = 1;
        $this->location = 'all';
    }

    public function render(): View
    {
        return view('livewire.property-website-page', [
            'allRentals' => $this->rentals(),
            'markup' => $this->websiteConfig->markup,
        ]);
    }
}
