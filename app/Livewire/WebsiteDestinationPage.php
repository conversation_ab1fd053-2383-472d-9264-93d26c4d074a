<?php

namespace App\Livewire;

use App\DTO\Website\WebsiteConfigData;
use Illuminate\Http\Request as HttpRequest;

class WebsiteDestinationPage extends PropertyWebsitePage
{
    public string $city;

    public function mount(?string $city, HttpRequest $request): void
    {
        $imgixParams = '?compress=auto&quality=97&fm=auto&ar=16:9&fit=crop&crop=entropy&w=1440';

        $this->websiteConfig = WebsiteConfigData::from($request->input('distribution'));
        $this->locations = $this->websiteConfig->locations;

        $cityNonSlug = str_replace('-', ' ', $city);
        $cityHeadline = ucwords($cityNonSlug);
        $this->headline = __('booking_page.rentals_in').' '.$cityHeadline;
        $this->mainImage = $this->websiteConfig->mainImage.$imgixParams;
        $this->mainImageTitle = $this->websiteConfig->mainImageTitle;
        $this->mainImageDescription = $this->websiteConfig->mainImageDescription;

        $websiteSeoDescription = $this->headline.$this->websiteConfig->websiteSeoDescription;
        $this->city = $city;
        $this->location = $city;
        $this->hideLocation = true;

        $this->seo()->setTitle($this->headline);
        $this->seo()->setDescription($websiteSeoDescription);
    }
}
