<?php

namespace App\Livewire;

use App\DTO\Website\WebsiteConfigData;
use Illuminate\Contracts\View\View;
use League\CommonMark\CommonMarkConverter;
use Livewire\Component;
use Zoon\CommonMark\Ext\YouTubeIframe\YouTubeIframeExtension;

class WebsiteAboutPage extends Component
{
    public $aboutUs = '';

    public function mount(): void
    {
        $distributionWebsite = request()->input('distribution');
        abort_if(is_null($distributionWebsite), 404, 'Website not found');

        $config = WebsiteConfigData::from($distributionWebsite);
        $aboutUs = $config->about;

        $converter = new CommonMarkConverter([
            'youtube_iframe' => [
                'width' => '640',
                'height' => '480',
                'allow_full_screen' => true,
            ],
        ]);

        $converter->getEnvironment()->addExtension(new YouTubeIframeExtension());
        $c = $converter->convert($aboutUs)->getContent();

        $this->aboutUs = $c;
    }

    public function render(): View
    {
        return view('livewire.website-about-page');
    }
}
