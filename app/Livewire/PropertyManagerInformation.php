<?php

namespace App\Livewire;

use App\DTO\Website\WebsitePropertyManagerData;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class PropertyManagerInformation extends Component
{
    public WebsitePropertyManagerData $manager;

    public function mount(WebsitePropertyManagerData $manager)
    {
        $this->manager = $manager;
    }

    public function render(): View
    {
        return view('livewire.property-manager-information');
    }
}
