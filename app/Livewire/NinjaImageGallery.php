<?php

namespace App\Livewire;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class NinjaImageGallery extends Component
{
    public string $uuid;

    public Collection $images;
    public ?bool $withArrows = false;
    public ?bool $withIndicators = false;
    public ?bool $withLightbox = true;
    public ?string $class = null;
    public array $bulletLinks = [];
    public array $bulletLinksTitle = [];
    public ?string $link = null;

    public function mount(Collection $images, bool $withArrows = false, bool $withIndicators = false, bool $withLightbox = true, ?string $class = null, $link = null)
    {
        $this->uuid = 'ninja-gallery'.md5(serialize($this));
        $this->images = $images;
        $this->withArrows = $withArrows;
        $this->withIndicators = $withIndicators;
        $this->withLightbox = $withLightbox;
        $this->class = $class;
        $this->link = $link;

        if (! $withLightbox) {
            // create bullet links to use inside the slider
            foreach ($images as $image) {
                $this->bulletLinks[] = '#'.md5($image->urlGrid);
                $this->bulletLinksTitle[] = $image->type;
            }
        }
    }

    public function render(): View|Closure|string
    {
        return <<<'HTML'
                        <div
                            x-data="{
                                init() {
                                    const lightbox = new PhotoSwipeLightbox({
                                        gallery: '#gallery-{{ $uuid }}',
                                        children: 'a',
                                        showHideAnimationType: 'fade',
                                        pswpModule: PhotoSwipe,
                                    });

                                    lightbox.init();
                                }
                            }"
                        >
                            <div class="relative">
                                <div id="gallery-{{ $this->uuid }}" class="pswp-gallery pswp-gallery--single-column carousel {{ $this->class }}" loading="lazy">
                                    @foreach($images as $image)
                                        @if($withLightbox)
                                            <a
                                                class="carousel-item"
                                                href="{{ $image->urlBig }}"
                                                title="{{ $image->description }}"
                                                ata-pswp-width="{{ $image->width }}"
                                                data-pswp-height="{{ $image->height }}"
                                                data-pswp-srcset="{{ $image->urlBig }}&w=200 200w,{{ $image->urlBig }}&w=400 400w, {{ $image->urlBig }}&w=800 800w, {{ $image->urlBig }}&w=1200 1200w"
                                                target="_blank"
                                            >
                                                <img
                                                    src="{{ $image->urlBig }}&w=120"
                                                    id="{{ md5($image->urlBig) }}"
                                                    title="{{ $image->description }}"
                                                    alt="{{ $image->type }}"
                                                />
                                            </a>
                                        @else
                                            <img
                                                src="{{ $image->urlGrid }}"
                                                alt="{{ $image->description }}"
                                                title="{{ $image->type }}"
                                                id="{{ md5($image->urlGrid) }}"
                                                class="object-cover hover:opacity-70"
                                                onClick="window.location.href = '{{ $link }}'"
                                                loading="lazy"
                                            />
                                        @endif
                                    @endforeach
                                </div>
                                <div class="absolute right-0 z-20 overflow-y-scroll text-center bottom-2" wire:scroll style="left:0">
                                    @foreach($bulletLinks as $link)
                                        <a type="button" href="{{ $link }}" class="radio_button_checked" style="color:lightgray" title="{{ $bulletLinksTitle[$loop->index] }}" onclick="event.stopPropagation();">
                                            <span class="text-sm material-symbols-outlined notranslate">radio_button_checked</span>
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                            <style>
                                .carousel-item {
                                    max-width: 100px;
                                }
                                @media (max-width: 767px) {
                                    .carousel-item {
                                        max-width: 75px;
                                    }
                                }
                                .pswp img {
                                    max-width: none;
                                    object-fit: contain;
                                }
                            </style>
                        </div>
                HTML;
    }
}
