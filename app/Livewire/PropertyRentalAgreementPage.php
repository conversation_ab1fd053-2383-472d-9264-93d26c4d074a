<?php

namespace App\Livewire;

use App\DTO\Website\WebsiteRentalData;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Http\Request as HttpRequest;
use Livewire\Component;
use Str;

class PropertyRentalAgreementPage extends Component
{
    public string $rentalAgreement;
    public string $propertyName;

    public function mount(HttpRequest $request, string $rentalId)
    {
        $team = Team::find($request->input('distribution')['teamId']);
        $rental = Rental::getRentalModel($team, (int) $rentalId);
        $rental = WebsiteRentalData::from($rental);
        $this->rentalAgreement = Str::markdown($rental->rentalAgreement);
        $this->propertyName = $rental->rentalName;
    }

    public function render()
    {
        return view('livewire.property-rental-agreement-page');
    }
}
