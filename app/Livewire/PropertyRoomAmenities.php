<?php

namespace App\Livewire;

use App\DTO\Website\WebsiteConfigData;
use App\DTO\Website\WebsiteRentalData;
use App\Models\Amenity;
use App\Models\Rental;
use Livewire\Component;
use Lorisleiva\Actions\ActionRequest;

class PropertyRoomAmenities extends Component
{
    public array $rentalAmenities;
    public ?string $rentalSurface;
    public string $rentalSurfaceUnit;
    public array $roomAmenities;
    public array $allAmenities = [];
    public $expanded = false;

    public function mount(int $rentalId, ActionRequest $request)
    {
        $this->rentalId = $rentalId;
        $distribution = WebsiteConfigData::from($request->input('distribution'));
        $this->teamId = $distribution->teamId;
        $rentalModel = Rental::getRentalModel($this->teamId, (int) $rentalId);
        $rental = WebsiteRentalData::fromModel($rentalModel);
        $this->rentalSurface = $rental->surface;
        $this->rentalSurfaceUnit = $rental->surfaceUnit;

        $ra = collect($rental->roomAmenities);
        $amIds = $ra->pluck('room_id')->merge($ra->pluck('amenities.*.amenity_id')->flatten()->filter())->unique();
        $allAmenities = Amenity::query()->whereIn('external_id', $amIds)->get();
        $this->rentalAmenities = $rental->amenities;

        $ra = $ra->map(fn (array $room) => [
            'icon' => $allAmenities->firstWhere('external_id', $room['room_id'])->icon,
            'key' => $allAmenities->firstWhere('external_id', $room['room_id'])->key,
            'amenities' => collect($room['amenities'])->map(function (array $amenity) use ($allAmenities) {
                $am = $allAmenities->firstWhere('external_id', $amenity['amenity_id']);
                // add $am to $this->allAmenities
                $this->allAmenities[] = $am;

                return [
                    'icon' => $am->icon,
                    'key' => $am->key,
                    'count' => $amenity['count'],
                ];
            }),
        ]);

        $this->roomAmenities = $ra->toArray();
    }

    public function render()
    {
        return view('livewire.property-room-amenities');
    }

    public function toggleExpand()
    {
        $this->expanded = ! $this->expanded;
    }
}
