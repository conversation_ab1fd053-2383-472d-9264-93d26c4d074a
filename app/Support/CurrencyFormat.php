<?php

namespace App\Support;

use App\Models\Currency;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class CurrencyFormat extends NumberFormat
{
    const FORMAT_CURRENCY_GBP_SIMPLE = '"£"#,##0.00_-';

    const FORMAT_CURRENCY_GBP = '£#,##0_-';

    const DEFAULT_FORMAT = '###0.00_-';

    public static function fromCodeToFormat(string $code, bool $simple = true): string
    {
        switch ($code) {
            case 'USD':
                return $simple ? self::FORMAT_CURRENCY_USD_INTEGER : self::FORMAT_CURRENCY_USD;
            case 'EUR':
                return $simple ? self::FORMAT_CURRENCY_EUR_INTEGER : self::FORMAT_CURRENCY_EUR;
            case 'GBP':
                return $simple ? self::FORMAT_CURRENCY_GBP_SIMPLE : self::FORMAT_CURRENCY_GBP;
            default:
                return self::getDefaultFormat($code);
        }
    }

    private static function getDefaultFormat(string $code): string
    {
        $x = Currency::whereCodeAlphabetic(Str::lower($code))->firstOrFail()->symbol;

        return "$x#,##0.00_-";
    }
}
