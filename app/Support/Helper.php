<?php

use App\Models\Team;
use Illuminate\Database\Query\Expression;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Psr\Log\LogLevel;

function apiDateFromTimestamp($date): ?string
{
    return $date != null && $date != 0
        ? Carbon::createFromTimestampUTC($date)->toIso8601ZuluString()
        : null;
}

/**
 * @return null
 */
function apiDateFromCarbon(?Carbon $date = null)
{
    return $date?->toIso8601ZuluString();
}

function safe_json_object(array $object): mixed
{
    return empty($object) ? json_decode('{}') : $object;
}

function str_slug($title, $separator = '-', $language = 'en'): string
{
    return Str::slug($title, $separator, $language);
}

function starts_with($haystack, $needles): bool
{
    return Str::startsWith($haystack, $needles);
}

function ninja_str_contains($haystack, $needles): bool
{
    return Str::contains($haystack, $needles);
}

/**
 * This is important.
 *
 * This function takes care of seeing if we have an asset url env variable. If found, put everything correctly...
 *
 * @return string
 */
function a(string $url)
{
    $url = Str::startsWith($url, '/')
        ? Str::replaceFirst('/', '', $url)
        : $url;

    return ! empty(getenv('ASSET_URL')) ? getenv('ASSET_URL').'/'.$url : '/'.$url;
}

/**
 * Returns the base asset url for the current environment.
 *
 * @return string
 *
 * @noinspection PhpUnused
 */
function baseAssetUrl()
{
    if (isLocal()) {
        return getenv('APP_URL').'/';
    }

    return ! empty(getenv('ASSET_URL')) ? getenv('ASSET_URL').'/' : '/';
}

/**
 * @return HtmlString|string
 *
 * @throws Exception
 */
function m(string $url)
{
    if (isLocal()) {
        return mix($url);
    }

    return a($url);
}

function isLocal()
{
    return app()->environment('local');
}

function isProduction()
{
    return app()->environment('production');
}

function isNotProduction()
{
    return ! app()->environment('production');
}

function isStaging()
{
    return app()->environment('staging');
}

function isNotStaging()
{
    return ! app()->environment('staging');
}

function nLogException(
    string $string,
    Exception $exception,
    Team|int|null $team = null,
    array $data = [],
    string $level = LogLevel::ERROR)
{
    nLog(
        $string,
        $team,
        $level,
        array_merge(
            [
                'exception-message' => $exception->getMessage(),
                'exception-trace' => $exception->getTrace(),
            ],
            $data
        )
    );
}

function pnLog(
    string $string,
    Team|int|null $team = null,
    ?string $level = LogLevel::INFO,
    array|Collection|null $context = [],
    ?array $envs = [],
): void {
    if (isLocal()) {
        $extraChannels = null;
    } else {
        $extraChannels = 'papertrail';
    }
    nLog($string, $team, $level, $context, $envs, $extraChannels);
}

function nLog(
    string $string,
    Team|int|null $team = null,
    ?string $level = LogLevel::INFO,
    array|Collection|null $context = [],
    ?array $envs = [],
    array|string|null $extraChannels = null
): void {
    // IF the level of this log is debug, and the app isn't in debug mode, return
    if ($level == LogLevel::DEBUG && ! config('app.debug')) {
        return;
    }
    if ($context instanceof Collection) {
        $context = $context->toArray();
    }

    // Only assign or the given envs.
    if (! empty($envs) && ! app()->environment($envs)) {
        return;
    }
    if ($team == null) {
        $teamId = 0;
    } elseif ($team instanceof Team) {
        $teamId = $team->id;
    } elseif (is_numeric($team)) {
        $teamId = intval($team);
    }

    $url = Str::replaceFirst(config('app.url'), '', request()->url());

    // DO NOT LOG THE URLS THAT CONTAIN....
    if (Str::contains($url, config('ninja.log_exceptions'))) {
        return;
    }

    $cli = app()->runningInConsole() || strlen($url) > 0
        ? ''
        : '[CLI]';
    $method = '';
    if (request()->method() !== 'GET') {
        $method = $cli !== '[CLI]'
            ? '['.request()->method().']'
            : '';
    }

    if (isset($teamId) && $teamId != 0) {
        $context = array_merge($context, ['team' => $teamId]);
    }

    $x = "$cli$method";
    if (! empty($x)) {
        $x = "$x: ";
    }

    if (is_null($extraChannels)) {
        $channels = [config('logging.default')];
    } elseif (is_array($extraChannels)) {
        $channels = array_merge([config('logging.default')], $extraChannels);
    } else {
        $channels = [config('logging.default'), $extraChannels];
    }

    foreach ($channels as $channel) {
        switch ($level) {
            case LogLevel::EMERGENCY:
                Log::channel($channel)->emergency("$x $string", $context);
                break;
            case LogLevel::ALERT:
                Log::channel($channel)->alert("$x $string", $context);
                break;
            case LogLevel::CRITICAL:
                Log::channel($channel)->critical("$x $string", $context);
                break;
            case LogLevel::ERROR:
                Log::channel($channel)->error("$x $string", $context);
                break;
            case LogLevel::WARNING:
                Log::channel($channel)->warning("$x $string", $context);
                break;
            case LogLevel::NOTICE:
                Log::channel($channel)->notice("$x $string", $context);
                break;
            case LogLevel::INFO:
                Log::channel($channel)->info("$x $string", $context);
                break;
            case LogLevel::DEBUG:
                Log::channel($channel)->debug("$x $string", $context);
                break;
        }
    }
}

function rawQueryBookingCheckInTime(string $default_ci_time): Expression
{
    return DB::raw("IF(booking.expected_checkin_time IS NULL or booking.expected_checkin_time = '', '$default_ci_time', booking.expected_checkin_time) as expected_checkin_time");
}

function rawQueryBookingCheckOutTime(string $default_co_time): Expression
{
    return DB::raw(
        "IF(booking.expected_checkout_time IS NULL or booking.expected_checkout_time = '', '$default_co_time', booking.expected_checkout_time) as expected_checkout_time"
    );
}

function getCleanClassName(mixed $class): string
{
    return Str::afterLast(get_class($class), '\\');
}

if (! function_exists('is_http_call')) {
    function is_http_call()
    {
        return app()->runningInConsole() === false;
    }
}

/** php has some problems when trying to get the int value of a float.
 * For instance -13514.0, when converted to int with intval() results to -13513.
 * This is due to the way php represents floats in binary. Use this method to avoid this problem.
 * Curiously, this happened only when retreiving the input from the request $request->input('amount_in_cents'), but not when trying to reproduce it
 * by passing -13514.0 to intval(). Curious...
 */
function ninjaIntval(float $float): int
{
    return intval(round($float, 0));
}

function containsHtml(string $string): bool
{
    return $string != strip_tags($string);
}

function normalizeString(string $string): Illuminate\Support\Stringable
{
    return Str::of($string)->trim()->lower()->ascii();
}
