<?php

namespace App\Support;

use App\Models\CarbonPeriod;
use DateInterval;

/**
 * Created by IntelliJ IDEA.
 * User: polbatllo
 * Date: 23/11/2017
 * Time: 12:06.
 */
class RangeHelper
{
    const RANGE_CURRENT_WEEK = 'current_week';

    const RANGE_CURRENT_MONTH = 'current_month';

    const RANGE_CURRENT_YEAR = 'current_year';

    const RANGE_CURRENT_QUARTER = 'current_trimester';

    const RANGE_LAST_7_DAYS = 'last_7_days';

    const RANGE_LAST_WEEK = 'last_week';

    const RANGE_LAST_30_DAYS = 'last_30_days';

    const RANGE_LAST_MONTH = 'last_month';

    const RANGE_LAST_60_DAYS = 'last_60_days';

    const RANGE_LAST_90_DAYS = 'last_90_days';

    const RANGE_LAST_180_DAYS = 'last_180_days';

    const RANGE_LAST_365_DAYS = 'last_365_days';

    const RANGE_LAST_YEAR = 'last_year';

    const RANGE_TWO_YEARS_AGO = 'two_years_ago';

    const ALWAYS = 'always';

    const TODAY = 'today';

    public static function getPeriod(string $range): CarbonPeriod
    {
        switch ($range) {
            case self::TODAY:
                return new CarbonPeriod(today()->startOfDay(), today()->endOfDay());
            case self::ALWAYS:
                return new CarbonPeriod(now()->subYears(100), now()->addYears(100));
                // from closest monday to today.
            case self::RANGE_CURRENT_WEEK:
                return new CarbonPeriod(today()->startOfWeek(), today()->endOfWeek());

                // from first day of current month to current date
            case self::RANGE_CURRENT_MONTH:
                return new CarbonPeriod(today()->startOfMonth(), today()->endOfMonth());

                // from 1 January of current year to current date
            case self::RANGE_CURRENT_YEAR:
                return new CarbonPeriod(today()->startOfYear(), today()->endOfYear());

                // Current trimester
            case self::RANGE_CURRENT_QUARTER:
                return new CarbonPeriod(today()->startOfQuarter(), today()->endOfQuarter());
                // from 7 days ago to today.
            case self::RANGE_LAST_7_DAYS:
                return self::lastXDays(7);

                // last full week
            case self::RANGE_LAST_WEEK:
                return CarbonPeriod::lastWeek();

                // from 30 days ago to today
            case self::RANGE_LAST_30_DAYS:
                return self::lastXDays(30);

                // last full month
            case self::RANGE_LAST_MONTH:
                return CarbonPeriod::lastMonth();

                // from 60 days ago to today
            case self::RANGE_LAST_60_DAYS:
                return self::lastXDays(60);

                // from 90 days ago to today
            case self::RANGE_LAST_90_DAYS:
                return self::lastXDays(90);

                // from 180 days ago to today
            case self::RANGE_LAST_180_DAYS:
                return self::lastXDays(180);

                // from 365 days ago to today
            case self::RANGE_LAST_365_DAYS:
                return self::lastXDays(365);

                // last full year
            case self::RANGE_LAST_YEAR:
                $start = today()->startOfYear()->subDays(1)->startOfYear();
                $end = $start->copy()->endOfYear();

                return new CarbonPeriod($start, $end);

                // two years ago.
            case self::RANGE_TWO_YEARS_AGO:
                $start = today()->startOfYear()->subDays(1)->startOfYear()->subDays(1)->startOfYear();
                $end = $start->copy()->endOfYear();

                return new CarbonPeriod($start, $end);
        }

        return CarbonPeriod::thisDay();
    }

    private static function lastXDays($days): CarbonPeriod
    {
        $start = today()->subDays($days);
        $end = today()->endOfDay();

        return new CarbonPeriod($start, $end);
    }

    public static function getInterval($range): DateInterval
    {
        switch ($range) {
            // from closest monday to today.
            case self::RANGE_CURRENT_WEEK:
            case self::RANGE_LAST_MONTH:
            case self::RANGE_CURRENT_MONTH:
            case self::RANGE_LAST_7_DAYS:
            case self::RANGE_LAST_WEEK:
            case self::RANGE_LAST_30_DAYS:
            case self::RANGE_CURRENT_QUARTER:
            case self::RANGE_LAST_60_DAYS:
            case self::RANGE_LAST_90_DAYS:
                return new DateInterval('P1D');
                // from 1 January of current year to current date
            case self::RANGE_CURRENT_YEAR:
            case self::RANGE_LAST_365_DAYS:
            case self::RANGE_LAST_YEAR:
            case self::RANGE_TWO_YEARS_AGO:
            case self::RANGE_LAST_180_DAYS:
                return new DateInterval('P1M');
        }

        return new DateInterval('H:I:S');
    }

    public static function getFormatter($range)
    {
        switch ($range) {
            // from closest monday to today.
            case self::RANGE_CURRENT_MONTH:
            case self::RANGE_CURRENT_WEEK:
            case self::RANGE_LAST_MONTH:
            case self::RANGE_LAST_7_DAYS:
            case self::RANGE_LAST_WEEK:
            case self::RANGE_LAST_30_DAYS:
            case self::RANGE_CURRENT_QUARTER:
            case self::RANGE_LAST_60_DAYS:
            case self::RANGE_LAST_90_DAYS:
                return 'D d M';
                // from 1 January of current year to current date
            case self::RANGE_CURRENT_YEAR:
            case self::RANGE_LAST_YEAR:
            case self::RANGE_TWO_YEARS_AGO:
                return 'M Y';
            case self::RANGE_LAST_180_DAYS:
            case self::RANGE_LAST_365_DAYS:
                return 'd/M';
        }

        return "d ([ .\t-])* m";
    }
}
