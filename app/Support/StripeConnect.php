<?php

namespace App\Support;

use App\DTO\Bookings\BookingFeeData;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\PaymentGatewayAccount;
use App\Models\RentalFee;
use App\Models\Team;
use Exception;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Laravel\Cashier\Cashier;
use Stripe\Exception\InvalidRequestException;
use Stripe\PaymentIntent;
use Stripe\StripeClient;

class StripeConnect
{
    const HOLD_DAYS_AFTER_CHECK_OUT = 3;
    const MAX_STRIPE_HOLD_DAYS = 7;
    // With the above configuration of days for the Holds, this means holds are returned between 3 to 1 days after check-out

    public function __construct(
        public Team $team,
    ) {
    }

    private function stripeClient(array $options = []): StripeClient
    {
        $options = array_merge([
            'client_id' => config('services.stripe.client_id'),
        ], $options);

        return Cashier::stripe($options);
    }

    public function onboardingFormUrl(PaymentGatewayAccount $paymentGatewayAccount): string
    {
        // Follow this instructions: https://stripe.com/docs/connect/oauth-standard-accounts
        // Parameter details: https://stripe.com/docs/connect/oauth-reference
        $stripe = $this->stripeClient();
        $state = $paymentGatewayAccount->id;

        return $stripe->getConnectBase().'/oauth/authorize?response_type=code&client_id='.$stripe->getClientId().'&scope=read_write'."&state=$state".'&redirect_uri='.$this->team->config()->rnBackendTargetDomain(route(name: 'connectOauth', absolute: false));
    }

    public function completeOauth(string $code): string
    {
        $response = $this->stripeClient()->oauth->token(['grant_type' => 'authorization_code', 'code' => $code])->getLastResponse()->json;

        return $response['stripe_user_id'];
    }

    public function deauthorize(PaymentGatewayAccount $paymentGatewayAccount): bool
    {
        try {
            $stripe = $this->stripeClient();
            $stripe->oauth->deauthorize(['client_id' => $stripe->getClientId(), 'stripe_user_id' => $paymentGatewayAccount->gateway_account_id]);

            return true;
        } catch (Exception $exception) {
            report($exception);

            return false;
        }
    }

    public function checkChargesEnabled(PaymentGatewayAccount $paymentGatewayAccount): bool
    {
        return $this->stripeClient()->accounts->retrieve($paymentGatewayAccount->gateway_account_id)->getLastResponse()->json['charges_enabled'];
    }

    public function getCheckoutSessionUrl(Booking $booking, int $centsAmount, string $productDescription, string $successUrl, ?string $cancelUrl = null, array $metadata = [], array $extra = []): string
    {
        $rentalName = $booking->rental->name;
        $city = ! empty($booking->rental->city) ? $booking->rental->city : $rentalName;
        $checkinDate = Carbon::createFromTimestamp($booking->start_at)->toDateString();
        $extra = collect($extra);

        // https://stripe.com/docs/api/checkout/sessions/create
        return $this->stripeClient()->checkout->sessions->create(collect([
            'mode' => 'payment',
            'line_items' => [
                [
                    'price_data' => [
                        'currency' => strtolower($booking->currency),
                        'unit_amount' => $centsAmount,
                        'tax_behavior' => 'inclusive', // Do not charge TAX on top
                        'product_data' => [
                            'name' => __('messages.reservation_details_guest', ['city' => $city, 'on' => $checkinDate]),
                            'description' => $productDescription,
                        ],
                    ],
                    'quantity' => 1,
                ],
            ],
            'customer_creation' => 'always',
            'currency' => strtolower($booking->currency), // https://www.iso.org/iso-4217-currency-codes.html
            'success_url' => $successUrl,
            'metadata' => $metadata,
            //TODO: we should put a privacy policy when collecting data from the guest: specially for direct bookings and downpayment email: https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-consent_collection-terms_of_service
        ])->when($cancelUrl, fn (Collection $collection, string $value) => $collection->put('cancel_url', $value))
            ->when($extra->isNotEmpty(), fn (Collection $collection) => $collection->merge($extra))
            ->toArray(),
            ['stripe_account' => $this->team->findPaymentGateway($booking->rental_id)->gateway_account_id] // I think this substitutes the on_behalf_of parameter, as it was not working when I added both.
        )->url;
    }

    public function getBookingProductDescription(Booking $booking, bool $isDamageDeposit = false, bool $shouldCreateTask = false): string
    {
        $rentalName = $booking->rental->name;
        $checkinDate = Carbon::createFromTimestamp($booking->start_at)->toDateString();

        $productDescription = __('messages.payment_gateway.charge_description', ['rentalName' => $rentalName, 'date' => $checkinDate, 'guests' => $booking->getPax(), 'team' => $this->team->name]);

        if ($isDamageDeposit) {
            return __('messages.payment_gateway.charge_description.hold').' '.$productDescription.' '.__('messages.payment_gateway.charge_description.hold-return');
        } elseif ($shouldCreateTask) { // If task should be created, means the charge (not hold) contains the DD.
            return $productDescription.' '.__('messages.payment_gateway.charge_description.charged-deposit');
        } else {
            return $productDescription;
        }
    }

    public function getDownpaymentProductDescription(Booking $booking): string
    {
        $rentalName = $booking->rental->name;
        $checkinDate = Carbon::createFromTimestamp($booking->start_at)->toDateString();

        return __('messages.payment_gateway.charge_description.downpayment', ['rentalName' => $rentalName, 'date' => $checkinDate, 'guests' => $booking->getPax(), 'team' => $this->team->name]);
    }

    public function getUpsaleProductDescription(EloquentCollection $rentalFees, string $locale): string
    {
        $buyingList = $rentalFees->map(fn (RentalFee $rentalFee) => $rentalFee->getName($locale) ?? '')->implode(', ');

        return __('messages.payment_gateway.charge_description.upsales', ['buyingList' => $buyingList]);
    }

    public function getExistingBookingMetadata(Booking $booking, bool $isDamageDeposit = false, bool $shouldCreateTask = false): array
    {
        $metadata = [
            'booking_reference' => $booking->reference,
            'is_normal_payment' => true,
            'should_create_task' => ! $isDamageDeposit && $shouldCreateTask, //In the cases were we are charging the DD instead of holding it, create a reminder for the PM
        ];
        if ($isDamageDeposit) {
            // Add hold_until to identify this is a hold, and know when it should be released
            $metadata['hold_until'] = now()->addDays(self::MAX_STRIPE_HOLD_DAYS)->toDateTimeString(); // Holds will be returned X days after payment

            /* OLD: to limit the number of days after checkout, use this logic:
            $bookingLength = GetBookingLengthDaysAction::run($booking);
            $desiredDdReturn = now()->addDays($bookingLength + self::HOLD_DAYS_AFTER_CHECK_OUT);
            $maxDdHold = now()->addDays(self::MAX_STRIPE_HOLD_DAYS);
            $metadata['hold_until'] = ($desiredDdReturn->isAfter($maxDdHold) ? $maxDdHold : $desiredDdReturn)->toDateTimeString();*/
        }

        return $metadata;
    }

    public function getBookingEngineMetadata(Booking $booking): array
    {
        return [
            'booking_reference' => $booking->reference,
            'is_booking_engine' => true,
        ];
    }

    public function getExtraParamsForPreAuthorization(): array
    {
        return [
            'payment_intent_data' => ['capture_method' => 'manual'], // Pre-authorization
            'payment_method_types' => ['card'], // Allow only cards for holds. Other types not available.
            //'payment_method_options' => ['card' => ['request_extended_authorization' => 'if_available']], TODO: to be used for extended authorizations
        ];
    }

    public function getUpsalesPurchaseMetadata(Booking $booking, Collection $addons): array
    {
        return [
            'booking_reference' => $booking->reference,
            'ninja_upsells' => $addons->map(fn (BookingFeeData $bookingFeeData) => [
                'fee_id' => $bookingFeeData->feeId,
                'price' => (string) round($bookingFeeData->price, 2),
                'times_booked' => $bookingFeeData->timesBooked,
            ])->values(),
        ];
    }

    /**
     * Returns a string in case of error, false in case of http request success but refund still not processed by stripe. True if OK.
     */
    public function refundPayment(Booking $booking, BookingPayment $payment): bool|string
    {
        try {
            $refund = $this->stripeClient()->refunds->create([
                'payment_intent' => $payment->stripe_payment_intent_id, //'amount' => , // Use this to partially refund a payment. Development for partial refunds is not done, it can only be done from stripe and doesn't pay off to be done here.
                //'refund_application_fee' => true, This is in case we start charging application fees https://stripe.com/docs/connect/direct-charges#issuing-refunds
            ],
                ['stripe_account' => $this->team->findPaymentGateway($booking->rental_id)->gateway_account_id],
            );
        } catch (InvalidRequestException $e) {
            // Do not report in case the problem is that it has been already refunded
            if (! Str::contains($e->getMessage(), 'has already been refunded')) {
                report($e);
            }

            return $e->getMessage();
        }

        return match ($refund->status) {
            'pending' => false,
            'succeeded', 'successful' => true,
            default => "Refund status: $refund->status",
        };
    }

    public function returnDamageDeposit(Booking $booking, BookingPayment $payment): bool|string
    {
        if ($payment->hold_until === null) {
            return false; // We must ensure this is a hold. Otherwise stripe will fail because intent's status must be requires_capture
        }

        return $this->cancelPaymentIntent($booking, $payment->stripe_payment_intent_id);
    }

    /** To be used for situations like returning Damage Deposits or holds done from the Booking Engine. */
    public function cancelPaymentIntent(Booking $booking, string $paymentIntentId): bool
    {
        try {
            $paymentIntent = $this->stripeClient()->paymentIntents->cancel(
                $paymentIntentId,
                opts: ['stripe_account' => $this->team->findPaymentGateway($booking->rental_id)->gateway_account_id]
            );
        } catch (Exception $e) {
            return $e->getMessage();
        }

        return $paymentIntent->status === 'canceled';
    }

    public function captureDamageDeposit(Booking $booking, BookingPayment $payment, ?int $centsAmount = null): bool|string
    {
        if ($payment->hold_until === null) {
            return false; // We must ensure this is a hold. Otherwise stripe will fail because intent's status must be requires_capture
        }

        if ($centsAmount) {
            $amount = $centsAmount > $payment->amount_in_cents ? $payment->amount_in_cents : $centsAmount;
            $params = ['amount_to_capture' => $amount];
            // Partially capturing automatically releases the remaining amount
        } else {
            $params = [];
        }

        try {
            return $this->capturePaymentIntentHold($booking, $payment->stripe_payment_intent_id, $params);
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    public function capturePaymentIntentHold(Booking $booking, string $paymentIntentId, $params = []): string|bool
    {
        $paymentIntent = $this->stripeClient()->paymentIntents->capture(
            id: $paymentIntentId,
            params: $params,
            opts: ['stripe_account' => $this->team->findPaymentGateway($booking->rental_id)->gateway_account_id]
        );

        return $paymentIntent->status == 'succeeded' ? true : "Stripe didn't process the capture of the payment intent hold correctly. Check Stripe.";
    }

    public function getPaymentIntent(Booking $booking, string $paymentIntentId): PaymentIntent
    {
        return $this->stripeClient()->paymentIntents->retrieve(
            $paymentIntentId,
            ['expand' => ['latest_charge.balance_transaction']], // To be able to get stripe fee
            ['stripe_account' => $this->team->findPaymentGateway($booking->rental_id)->gateway_account_id],
        );
    }
}
