<?php

namespace App\Support;

use App\DataProviders\ProviderSync;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Spatie\ResponseCache\CacheProfiles\BaseCacheProfile;
use Symfony\Component\HttpFoundation\Response;

class NinjaCacheProfiles extends BaseCacheProfile
{
    public function shouldCacheRequest(Request $request): bool
    {
        if ($request->ajax() && $request->isMethod('get')) {
            $user = Auth::user();

            if ($user != null && $user->current_team_id != null) {
                if (ProviderSync::isInSync($user->current_team_id)) {
                    return false;
                }

                return true;
            }

            return false;
        }

        if ($request->ajax() && $request->isMethod('post') && Str::contains($request->url(), 'stats')) {
            $user = Auth::user();

            if ($user != null && $user->current_team_id != null) {
                if (ProviderSync::isInSync($user->current_team_id)) {
                    return false;
                }

                return true;
            }

            return false;
        }

        if ($this->isRunningInConsole()) {
            return false;
        }

        return $request->isMethod('get');
    }

    public function shouldCacheResponse(Response $response): bool
    {
        return $response->isSuccessful() || $response->isRedirection();
    }
}
