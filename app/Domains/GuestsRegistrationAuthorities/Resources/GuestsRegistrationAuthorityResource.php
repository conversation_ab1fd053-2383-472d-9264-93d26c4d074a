<?php

namespace App\Domains\GuestsRegistrationAuthorities\Resources;

use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin GuestsRegistrationAuthority
 */
class GuestsRegistrationAuthorityResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'rental_id' => $this->rental_id,
            'authority' => $this->authority,
            'sources' => $this->sources,
            'username' => $this->username,
            'has_password' => ! empty($this->password), // never return the password through the API
            ...$this->extra_rental_info,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
