<?php

namespace App\Domains\GuestsRegistrationAuthorities\Api;

use App\Actions\Support\Files\NinjaZipper;
use App\Actions\Support\Files\XmlToArrayAction;
use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Domains\GuestsRegistrationAuthorities\Enums\SesHospedajesRequestTypeEnum;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use App\Domains\GuestsRegistrationAuthorities\Models\SesHospedajesCommunication;
use App\Exceptions\NinjaAddContextException;
use App\Exceptions\NinjaNotImplementedException;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Country;
use App\Models\Team;
use Arr;
use Exception;
use Http;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection as BaseCollection;
use Spatie\ArrayToXml\ArrayToXml;
use Str;

class SesHospedajesApi implements GuestsRegistrationAuthoritiesApiInterface
{
    const AUTHORITY_TYPE = GuestsRegistrationAuthorityEnum::SesHospedajes;

    private string $url;

    public function __construct()
    {
        //$this->url = 'https://hospedajes.ses.mir.es/hospedajes-web/ws/v1/comunicacion'; // TODO
        $this->url = 'https://hospedajes.pre-ses.mir.es/hospedajes-web/ws/v1/comunicacion'; // Test url
    }

    private function makeRequest(GuestsRegistrationAuthority $authority, array $body, ?SesHospedajesRequestTypeEnum $requestType = null): BaseCollection
    {
        $headers = [
            'authorization' => 'Basic '.base64_encode("{$authority->username}:{$authority->password}"),
            // Note: This uses APP_KEY env. If you try this in local dev with a production database, the decrypted password will be wrong
            'content-type' => 'text/xml; charset=utf-8', // Very important as api returns 500 if this is not like this
            //'accept' => 'application/xml', I think this header gives an error
        ];

        $responseBody = Http::withHeaders($headers)
            ->withOptions(['verify' => isProduction() ? base_path(config('services.ses_hospedajes.certificate_path')) : false]) // Disable SSL verification for non prod envs
            ->retry(2, 500)
            ->send('POST', $this->url, ['body' => $this->arrayToXml($body, $requestType, true)])
            ->body();

        return $this->parseXmlResponseBody($responseBody, $body, $authority);
    }

    public function communicateNewBookings(Collection|Booking $bookings): BaseCollection
    {
        if ($bookings instanceof Collection) {
            throw new NinjaNotImplementedException("[SES Hospedajes] We don't accept communication of multiple Bookings at once in SesHospedajesApi. New Booking.");
            // Note: this is because when using consultaLote, it's difficult to map their returned codigo_comunicacion with each SesHospedajesComunication model of the "lote"
        }
        $booking = $bookings;
        $authority = $booking->rental->guestRegistrationAuthorities->firstWhere('authority', self::AUTHORITY_TYPE);
        $team = $booking->team;

        $phone = $booking->client->getPhoneNumbersCollection()->first();
        $email = $booking->client->getPrimaryEmail(booking: $booking);

        if (empty($phone) && empty($email)) {
            report(new Exception("[SES Hospedajes] Booking id {$booking->id} from team {$team->id} has no phone nor email and this should not happen when reporting to ses hospedajes."));
        }

        $persona = collect([
            'rol' => 'TI',
            'nombre' => $booking->client->firstname ?? '-',
            'apellido1' => empty($booking->client->lastname) ? '-' : $booking->client->lastname,
        ])->when($phone, fn (BaseCollection $persona) => $persona->put('telefono', $phone))
            ->when($email, fn (BaseCollection $persona) => $persona->put('correo', $email));

        $solicitud = ['comunicacion' => [
            'establecimiento' => [
                'codigo' => $authority->extra_rental_info['codigo_establecimiento'],
            ],
            'contrato' => $this->getBloqueContrato($booking),
            'persona' => $persona->toArray(),
        ]];

        $requestType = SesHospedajesRequestTypeEnum::ReservaHospedaje;
        $body = $this->getCommunicationRequestBody($team, $authority, $requestType, ['solicitud' => $solicitud]);

        return $this->makeRequest($authority, $body, $requestType);
    }

    public function communicatePreCheckInForms(Collection|Booking $bookings): BaseCollection
    {
        if ($bookings instanceof Collection) {
            throw new NinjaNotImplementedException("[SES Hospedajes] We don't accept communication of multiple Bookings at once in SesHospedajesApi. PCIF.");
            // Note: this is because when using consultaLote, it's difficult to map their returned codigo_comunicacion with each SesHospedajesComunication model of the "lote"
        }
        /** @var Booking $booking */
        $booking = $bookings;
        $authority = $booking->rental->guestRegistrationAuthorities->firstWhere('authority', self::AUTHORITY_TYPE);
        $team = $booking->team;
        $passports = $booking->preCheckInForm->passports;

        if ($passports->count() != $booking->getPax()) {
            // TODO: still communicate and create alert? or just create alert?
        }

        $guests = [];
        foreach ($passports as $passport) {
            $mayorDeEdad = Carbon::parse($passport->birth_date)->addYears(18)->isPast();

            if (empty($passport->email) && empty($passport->phone)) {
                report(new Exception("[SES Hospedajes] Passport id $passport->id has no phone nor email and this should not happen when reporting to ses hospedajes."));
            }

            $email = $passport->email;
            $phone = transform($passport->phone, fn ($phone) => '+'.$passport->phone_prefix.$phone);

            // Note: Fields MUST be in the order defined in the documentation:
            $guest = collect([
                'rol' => 'VI',
                'nombre' => $passport->first_name,
                'apellido1' => 'Alvarado', //$passport->last_name, //TODO divide if needed
                'apellido2' => 'Carbajo', // Todo: divide last_name if document type is NIF
            ])->when($mayorDeEdad, function (BaseCollection $guest) use ($passport) {
                $guest->put('tipoDocumento', 'NIF')//->put('tipoDocumento', $passport->document_type) TODO transform using enum
                    ->put('numeroDocumento', $passport->country_specific_id) // TODO OR document_id if passport
                    ->put('soporteDocumento', $passport->document_id); // TODO: compulsory if NIF or NIE
            })->put('fechaNacimiento', Carbon::parse($passport->birth_date)->format('Y-m-d'))
            ->put('direccion', [
                'direccion' => $passport->address, // TODO: if > 100 use direccionComplementaria
                'codigoMunicipio' => '08019', // TODO use codigoMunicipio() de RentalLegalDetails
                //'nombreMunicipio' => $passport->city, // TODO
                'codigoPostal' => $passport->zip,
                'pais' => strtoupper(Country::firstWhere('id', strtolower($passport->address_country_code))->code_3),
            ])->when($phone, fn (BaseCollection $persona) => $persona->put('telefono', $phone))
            ->when($email, fn (BaseCollection $persona) => $persona->put('correo', $email));
            //->put('parentesco', $passport->relationship); // TODO

            $guests[] = $guest->toArray();
        }

        $solicitud = [
            'codigoEstablecimiento' => $authority->extra_rental_info['codigo_establecimiento'],
            'comunicacion' => [
                'contrato' => $this->getBloqueContrato($booking),
                'persona' => $guests,
            ],
        ];

        $requestType = SesHospedajesRequestTypeEnum::ParteViajeros;
        $body = $this->getCommunicationRequestBody($team, $authority, $requestType, ['solicitud' => $solicitud]);

        return $this->makeRequest($authority, $body, $requestType);
    }

    public function communicateBookingCancellations(Collection|Booking $bookings): Collection
    {
        if ($bookings instanceof Booking) {
            $bookings = new Collection([$bookings]);
        }

        $team = $bookings->first()->team;
        $comunicaciones = $bookings
            ->flatMap(fn (Booking $booking) => $booking->sesHospedajesCommunications->whereNotNull('codigo_comunicacion'));

        $solicitud = [
            'comunicaciones' => [
                'anul:codigoComunicacion' => $comunicaciones->pluck('codigo_comunicacion')->toArray(),
            ],
        ];

        $authority = $bookings->first()->rental->guestRegistrationAuthorities->firstWhere('authority', self::AUTHORITY_TYPE);

        $requestType = SesHospedajesRequestTypeEnum::Anulacion;
        $body = $this->getCommunicationRequestBody($team, $authority, $requestType, $solicitud);

        $this->makeRequest($authority, $body, $requestType);

        return $comunicaciones; // The deleted communications
    }

    /** We expect to have here a Collection of communications from the same rental */
    public function consultaLotes(Collection $communications): BaseCollection
    {
        $solicitud = [
            'lotes' => [
                'con:lote' => $communications->pluck('lote')->toArray(),
            ],
        ];

        /** @var SesHospedajesCommunication $firstCom */
        $firstCom = $communications->first();
        $team = $firstCom->team;
        $authority = $firstCom->rental->guestRegistrationAuthorities->firstWhere('authority', self::AUTHORITY_TYPE);

        $requestType = SesHospedajesRequestTypeEnum::Consulta;
        $body = $this->getCommunicationRequestBody($team, $authority, $requestType, $solicitud);

        return $this->makeRequest($authority, $body, $requestType);
    }

    public function validateAuthority(GuestsRegistrationAuthority $authority): void
    {
        if (empty($authority->extra_rental_info['codigo_establecimiento']) || empty($authority->extra_rental_info['codigo_arrendador'])) {
            throw new Exception("Ses Hospedajes: we shouldn't allow codigo_establecimiento or codigo_arrendador to be empty in a Ses Hospedajes authority");
        }
    }

    public function consultaCatalogo(GuestsRegistrationAuthority $authority, string $tabla): BaseCollection
    {
        $body = ['catalogoRequest' => ['peticion' => ['catalogo' => $tabla]]];

        return $this->makeRequest($authority, $body);
    }

    /** PRIVATE METHODS */
    private function getBloqueContrato(Booking $booking): array
    {
        return [
            'referencia' => $booking->reference,
            'fechaContrato' => Carbon::createFromTimestampUTC($booking->created_at)->format('Y-m-d'),
            'fechaEntrada' => $booking->getCarbonCheckInTime()->format('Y-m-d\TH:i:s'),
            'fechaSalida' => $booking->getCarbonCheckOutTime()->format('Y-m-d\TH:i:s'),
            'numPersonas' => $booking->getPax(),
            'pago' => $this->getBloquePago($booking),
        ];
    }

    private function getBloquePago(Booking $booking): array
    {
        /** @var BookingPayment $payment */
        $payment = $booking->bookingPayments->sortBy('paid_at')->first();

        if ($payment) {
            $kind = match ($payment->kind) {
                'cash', 'cheque', 'travel-cheque' => 'EFECT',
                'credit-card' => 'TARJT',
                'wiretransfer' => 'TRANS',
                'credits' => 'OTRO',
                'paypal', 'online' => 'PLATF',
                default => 'PLATF',
            };

            return [
                'tipoPago' => $kind,
                'fechaPago' => $payment->paid_at?->format('Y-m-d') ?? $payment->created_at->format('Y-m-d'),
            ];
        }

        return [
            // Reality is that Check-in App (competitor) lets the user select a type of payment for all communications
            'tipoPago' => 'TARJT', // All direct bookings are done with credit cards
            'fechaPago' => Carbon::createFromTimestampUTC($booking->created_at)->format('Y-m-d'),
        ];
    }

    private function getCommunicationRequestBody(Team $team, GuestsRegistrationAuthority $authority, SesHospedajesRequestTypeEnum $requestType, array $solicitud): array
    {
        $this->validateAuthority($authority);

        $cabecera = [
            'codigoArrendador' => $authority->extra_rental_info['codigo_arrendador'],
            'aplicacion' => $team->config()->rnAppName(),
            'tipoOperacion' => $requestType->getTipoOperacion(),
        ];

        $tipoComunicacion = $requestType->getTipoComunicacion();
        if ($tipoComunicacion) {
            $cabecera['tipoComunicacion'] = $tipoComunicacion;
        }

        return ['comunicacionRequest' => [
            'peticion' => [
                'cabecera' => $cabecera,
                'solicitud' => $this->compressAndEncodeArray($solicitud, $requestType),
            ],
        ]];
    }

    private function arrayToXml(array $payload, ?SesHospedajesRequestTypeEnum $requestType = null, bool $addEnvelope = false): string
    {
        if ($addEnvelope) {
            // If Envelope, this is the top level xml
            $root = [
                'rootElementName' => 'soapenv:Envelope',
                '_attributes' => [
                    'xmlns:soapenv' => 'http://schemas.xmlsoap.org/soap/envelope/',
                    'xmlns:com' => 'http://www.soap.servicios.hospedajes.mir.es/comunicacion',
                ],
            ];
            $body = [
                'soapenv:Header' => [],
                'soapenv:Body' => [
                    'com:'.array_key_first($payload) => $payload[array_key_first($payload)],
                ],
            ];
        } else {
            // If not envelope, this is the compressed XML
            $root = [
                'rootElementName' => $requestType->getCompressedXmlRootElementName($payload),
                '_attributes' => [
                    $requestType->getCompressedXmlRootElementAttribute() => $requestType->getXsdFileRoute(),
                ],
            ];
            $body = $requestType->getCompressedXmlBody($payload);
        }

        return (new ArrayToXml($body, $root))->dropXmlDeclaration()->toXml();
    }

    private function compressAndEncodeArray(array $payload, SesHospedajesRequestTypeEnum $requestType): string
    {
        $xml = $this->arrayToXml($payload, $requestType);

        $zip = NinjaZipper::create(Str::random().'.zip');
        $zip->addString('solicitud.xml', $xml);

        return base64_encode($zip->getCompressedContents());

        /* Alternatively, use this code which may be more performant: uses a php temp stream, which uses memory up to a limit (default value)
        * and if overflows, it uses the disk instead. But we need to test if this works in prod.
        $zip = new ZipArchive();
        $tempStream = fopen('php://temp', 'r+b');
        // Temporary stream that can spill to disk: default is to use memory up to a default limit when it then uses disk

        if ($tempStream && $zip->open($tempStream, ZipArchive::CREATE) === true) {
            $zip->addFromString('body-request', $xml);
            $zip->close();

            rewind($tempStream); // Go back to the beginning of the stream
            $compressedData = stream_get_contents($tempStream); // Read the content
            fclose($tempStream); // Close and clean up the stream

            // Encode the compressed content
            return base64_encode($compressedData);
        } else {
            echo "Failed to create ZIP archive.";
        }*/
    }

    private function parseXmlResponseBody(string $responseBody, array $requestBody, GuestsRegistrationAuthority $authority): BaseCollection
    {
        libxml_use_internal_errors(true);
        $xmlObject = simplexml_load_string($responseBody);

        if ($xmlObject === false) {
            // Must be a json
            return  collect(json_decode($responseBody, true));
        } else {
            $array = XmlToArrayAction::run($xmlObject);

            $code = Arr::get($array, 'Envelope.SOAP-ENV:Body.ns3:comunicacionResponse.respuesta.codigo',
                Arr::get($array, 'Envelope.SOAP-ENV:Body.ns3:catalogoResponse.resultado.codigo'));
            $description = Arr::get($array, 'Envelope.SOAP-ENV:Body.ns3:comunicacionResponse.respuesta.descripcion');
            if ($code !== '0' && ! Str::contains($description, 'Ya existe un lote creado con el mismo fichero para esa entidad.')) {
                throw new NinjaAddContextException(
                    team: $authority->team_id,
                    context: ['response_body' => $array, 'request_body' => $requestBody],
                    message: '[SES Hospedajes] Error response received from Ses Hospedajes. Their response: '.$description,
                );
            }

            return collect(collect($array)->first());
        }
    }
}
