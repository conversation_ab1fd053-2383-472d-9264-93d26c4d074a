<?php

namespace App\Domains\GuestsRegistrationAuthorities\Actions;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Domains\GuestsRegistrationAuthorities\Data\GuestsRegistrationAuthorityData;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use App\Domains\GuestsRegistrationAuthorities\Resources\GuestsRegistrationAuthorityResource;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreGuestsRegistrationAuthorityAction
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return VerifyUserHasAccessToRentalsAction::hasAccess($request->user(), $request->route('teamRental'));
    }

    public function asController(Team $team, Rental $teamRental, GuestsRegistrationAuthorityData $data, ActionRequest $request): GuestsRegistrationAuthorityResource
    {
        $guestsRegistrationAuthority = $this->handle($data);

        return new GuestsRegistrationAuthorityResource($guestsRegistrationAuthority);
    }

    public function handle(GuestsRegistrationAuthorityData $data): GuestsRegistrationAuthority
    {
        return GuestsRegistrationAuthority::create($data->toArray());
    }
}
