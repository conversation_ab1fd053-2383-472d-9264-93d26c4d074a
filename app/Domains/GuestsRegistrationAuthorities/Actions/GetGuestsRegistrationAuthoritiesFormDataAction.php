<?php

namespace App\Domains\GuestsRegistrationAuthorities\Actions;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\LaravelData\DataCollection;

class GetGuestsRegistrationAuthoritiesFormDataAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'authority' => ['required', 'string'],
        ];
    }

    public function authorize(ActionRequest $request): bool
    {
        return VerifyUserHasAccessToRentalsAction::hasAccess($request->user(), $request->route('teamRental'));
    }

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): DataCollection
    {
        $authority = $request->input('authority');

        $exists = GuestsRegistrationAuthority::query()
            ->where('team_id', $team->id)
            ->where('rental_id', $teamRental->id)
            ->where('authority', $authority)
            ->exists();

        return GuestsRegistrationAuthorityEnum::tryFrom($authority)
            ->getService()
            ->getFormFields(! $exists);
    }
}
