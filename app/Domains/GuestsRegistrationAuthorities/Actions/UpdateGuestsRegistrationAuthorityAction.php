<?php

namespace App\Domains\GuestsRegistrationAuthorities\Actions;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Domains\GuestsRegistrationAuthorities\Data\GuestsRegistrationAuthorityData;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use App\Domains\GuestsRegistrationAuthorities\Resources\GuestsRegistrationAuthorityResource;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateGuestsRegistrationAuthorityAction
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return VerifyUserHasAccessToRentalsAction::hasAccess($request->user(), $request->route('teamRental'));
    }

    public function asController(Team $team, Rental $teamRental, GuestsRegistrationAuthority $guestsRegistrationAuthority, GuestsRegistrationAuthorityData $data, ActionRequest $request): GuestsRegistrationAuthorityResource
    {
        abort_unless($team->id === $guestsRegistrationAuthority->team_id, 403);

        $updatedAuthority = $this->handle($guestsRegistrationAuthority, $data);

        return new GuestsRegistrationAuthorityResource($updatedAuthority);
    }

    public function handle(GuestsRegistrationAuthority $guestsRegistrationAuthority, GuestsRegistrationAuthorityData $data): GuestsRegistrationAuthority
    {
        $guestsRegistrationAuthority->update($data->toArray());

        return $guestsRegistrationAuthority;
    }
}
