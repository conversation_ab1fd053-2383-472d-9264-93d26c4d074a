<?php

namespace App\Domains\GuestsRegistrationAuthorities\Actions;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class DestroyGuestsRegistrationAuthorityAction
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return VerifyUserHasAccessToRentalsAction::hasAccess($request->user(), $request->route('teamRental'));
    }

    public function asController(Team $team, Rental $teamRental, GuestsRegistrationAuthority $guestsRegistrationAuthority): AnonymousResourceCollection
    {
        abort_unless($team->id === $guestsRegistrationAuthority->team_id, 403);
        $this->handle($guestsRegistrationAuthority);

        return IndexGuestsRegistrationAuthoritiesAction::make()->asController($team, $teamRental);
    }

    public function handle(GuestsRegistrationAuthority $guestsRegistrationAuthority): bool
    {
        return $guestsRegistrationAuthority->delete();
    }
}
