<?php

namespace App\Domains\GuestsRegistrationAuthorities\Actions;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use App\Domains\GuestsRegistrationAuthorities\Resources\GuestsRegistrationAuthorityResource;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetGuestsRegistrationAuthoritiesAction
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return VerifyUserHasAccessToRentalsAction::hasAccess($request->user(), $request->route('teamRental'));
    }

    public function asController(Team $team, Rental $teamRental, GuestsRegistrationAuthority $guestsRegistrationAuthority): GuestsRegistrationAuthorityResource
    {
        abort_unless($team->id === $guestsRegistrationAuthority->team_id, 403);

        return new GuestsRegistrationAuthorityResource($guestsRegistrationAuthority);
    }
}
