<?php

namespace App\Domains\GuestsRegistrationAuthorities\Actions\SesHospedajes;

use App\Domains\GuestsRegistrationAuthorities\Actions\ShouldReportBookingsToAuthoritiesAction;
use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Domains\GuestsRegistrationAuthorities\Models\SesHospedajesCommunication;
use Lorisleiva\Actions\Concerns\AsAction;

class SesHospedajesConsultaLotesAction
{
    use AsAction;

    public function asJob(SesHospedajesCommunication $communication): void
    {
        $communication->load($this->loadRelations());
        $this->handle($communication);
    }

    public function handle(SesHospedajesCommunication $communication): void
    {
        // Despite this should not be triggered unless we are in production, double check here
        if (! ShouldReportBookingsToAuthoritiesAction::run($communication->team_id)) {
            return;
        }

        GuestsRegistrationAuthorityEnum::SesHospedajes->getService()->consultaLotesPendientes($communication);
    }

    public static function loadRelations(): array
    {
        return ['team', 'rental.guestRegistrationAuthorities'];
    }
}
