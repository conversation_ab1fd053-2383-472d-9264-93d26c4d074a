<?php

namespace App\Domains\GuestsRegistrationAuthorities\Actions;

use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Models\Booking;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class ReportCheckInsToAuthoritiesAction
{
    use AsAction;

    /**
     * @param  Collection<array>  $bookings  We expect $bookings to be a Collection containing bookings in the form of [id, team_id]
     */
    public function asJob(Collection $bookings, GuestsRegistrationAuthorityEnum $authority): void
    {
        $bookings = Booking::query()
            ->loadCollection($bookings)
            ->with($this->loadRelationships())
            ->get();

        $this->handle($bookings, $authority);
    }

    public function handle(EloquentCollection $bookings, ?GuestsRegistrationAuthorityEnum $authority = null): bool
    {
        $bookings = $bookings->filter(fn (Booking $booking) => ShouldReportBookingsToAuthoritiesAction::run($booking->team_id));

        // If an authority is passed, we should run only that authority
        if ($authority) {
            $authority->getService()->reportAtCheckInTime($bookings);
        } else {
            // We should get here for a single booking only, thus, we can run per each booking. Else, we should group by authority
            $bookings->each(function (Booking $booking) {
                foreach ($booking->rental->guestRegistrationAuthorities as $authority) {
                    $authority->getService()->reportAtCheckInTime($booking);
                }
            });
        }

        return true;
    }

    public static function loadRelationships(): array
    {
        return array_merge(['rental.guestRegistrationAuthorities', 'preCheckInForm'], GuestsRegistrationAuthorityEnum::communicationBookingRelationships());
    }
}
