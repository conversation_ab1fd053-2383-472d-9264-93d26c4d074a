<?php

namespace App\Domains\GuestsRegistrationAuthorities\Actions;

use App\Actions\Auth\VerifyUserHasAccessToRentalsAction;
use App\Domains\GuestsRegistrationAuthorities\Resources\GuestsRegistrationAuthorityResource;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class IndexGuestsRegistrationAuthoritiesAction
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return VerifyUserHasAccessToRentalsAction::hasAccess($request->user(), $request->route('teamRental'));
    }

    public function asController(Team $team, Rental $teamRental): AnonymousResourceCollection
    {
        return GuestsRegistrationAuthorityResource::collection($this->handle($team, $teamRental));
    }

    public function handle(Team $team, Rental $rental): Collection
    {
        return $team->guestsRegistrationAuthorities()
            ->where('rental_id', $rental->id)
            ->get();
    }
}
