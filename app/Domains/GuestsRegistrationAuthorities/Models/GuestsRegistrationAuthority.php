<?php

namespace App\Domains\GuestsRegistrationAuthorities\Models;

use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Domains\GuestsRegistrationAuthorities\Services\GuestsRegistrationAbstractService;
use App\Models\NinjaProviderModel;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\GuestsRegistrationAuthority.
 *
 * @property int $id
 * @property int $team_id
 * @property int $rental_id
 * @property GuestsRegistrationAuthorityEnum $authority
 * @property array|null $sources
 * @property string|null $username
 * @property string|null $password
 * @property array|null $extra_rental_info
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Rental $rental
 * @property-read \App\Models\Team $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority query()
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority whereAuthority($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority whereExtraRentalInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority whereRentalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority whereSources($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GuestsRegistrationAuthority whereUsername($value)
 *
 * @mixin \Eloquent
 */
class GuestsRegistrationAuthority extends NinjaProviderModel
{
    use HasFactory;

    protected $table = 'guests_registration_authorities';

    protected $fillable = [
        'team_id',
        'rental_id',
        'authority',
        'sources',
        'username',
        'password',
        'extra_rental_info',
    ];

    protected $casts = [
        'authority' => GuestsRegistrationAuthorityEnum::class,
        'sources' => 'array',
        'extra_rental_info' => 'array',
        'password' => 'encrypted',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id']);
    }

    public function getService(): GuestsRegistrationAbstractService
    {
        return $this->authority->getService();
    }
}
