<?php

namespace App\Domains\GuestsRegistrationAuthorities\Models;

use App\Domains\GuestsRegistrationAuthorities\Enums\CommunicationTypeEnum;
use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

interface AuthorityCommunicationInterface
{
    public function getAuthority(): GuestsRegistrationAuthorityEnum;

    public function getCommunicationType(): CommunicationTypeEnum;

    public function team(): BelongsTo; // All must have team id

    public function booking(): BelongsTo; // All must have booking id

    public function rental(): BelongsTo; // All must have rental id
}
