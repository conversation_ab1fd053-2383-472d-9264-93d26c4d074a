<?php

namespace App\Domains\GuestsRegistrationAuthorities\Models;

use App\Domains\GuestsRegistrationAuthorities\Enums\CommunicationTypeEnum;
use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Domains\GuestsRegistrationAuthorities\Enums\SesHospedajesCodigoEstadoEnum;
use App\Domains\GuestsRegistrationAuthorities\Enums\SesHospedajesRequestTypeEnum;
use App\Models\Booking;
use App\Models\NinjaProviderModel;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\SesHospedajesCommunication.
 *
 * @property int $id
 * @property int $team_id
 * @property int $rental_id
 * @property int $booking_id
 * @property string|null $lote
 * @property string|null $codigo_comunicacion
 * @property SesHospedajesRequestTypeEnum $tipo_comunicacion
 * @property SesHospedajesCodigoEstadoEnum|null $codigo_estado
 * @property string|null $descripcion_estado
 * @property string|null $tipo_error
 * @property string|null $descripcion_error
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Booking $booking
 * @property-read \App\Models\Booking $rental
 * @property-read \App\Models\Team $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication query()
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereCodigoComunicacion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereCodigoEstado($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereDescripcionError($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereDescripcionEstado($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereLote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereRentalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereTipoComunicacion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereTipoError($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SesHospedajesCommunication whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class SesHospedajesCommunication extends NinjaProviderModel implements AuthorityCommunicationInterface
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'ses_hospedajes_communications';

    protected $guarded = ['id'];

    protected $casts = [
        'tipo_comunicacion' => SesHospedajesRequestTypeEnum::class,
        'codigo_estado' => SesHospedajesCodigoEstadoEnum::class,
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class, ['booking_id', 'team_id'], ['id', 'team_id'])->withoutGlobalScopes();
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id']);
    }

    public function getAuthority(): GuestsRegistrationAuthorityEnum
    {
        return GuestsRegistrationAuthorityEnum::SesHospedajes;
    }

    public function getCommunicationType(): CommunicationTypeEnum
    {
        return match ($this->tipo_comunicacion) {
            SesHospedajesRequestTypeEnum::ReservaHospedaje => CommunicationTypeEnum::atBookingCreation,
            SesHospedajesRequestTypeEnum::ParteViajeros => CommunicationTypeEnum::atCheckInTime,
        };
    }
}
