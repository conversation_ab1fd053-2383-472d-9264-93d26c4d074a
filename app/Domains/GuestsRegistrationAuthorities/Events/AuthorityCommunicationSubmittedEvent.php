<?php

namespace App\Domains\GuestsRegistrationAuthorities\Events;

use App\Contracts\CreatesProviderEvents;
use App\Domains\GuestsRegistrationAuthorities\Models\AuthorityCommunicationInterface;
use App\Models\ProviderEvent;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AuthorityCommunicationSubmittedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public AuthorityCommunicationInterface $communication,
    ) {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->communication->team_id,
            'rental_id' => $this->communication->rental_id,
            'type' => ProviderEvent::AUTHORITY_COMMUNICATION_SUBMITTED,
            'booking_id' => $this->communication->booking_id,
            'related_model_type' => $this->communication->getMorphClass(),
            'related_model_id' => $this->communication->id,
            'data' => [
                'authority' => $this->communication->getAuthority(),
                'communication_type' => $this->communication->getCommunicationType(),
            ],
        ]);
    }
}
