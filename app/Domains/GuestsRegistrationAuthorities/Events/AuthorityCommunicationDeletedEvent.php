<?php

namespace App\Domains\GuestsRegistrationAuthorities\Events;

use App\Contracts\CreatesProviderEvents;
use App\Domains\GuestsRegistrationAuthorities\Enums\CommunicationTypeEnum;
use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Models\ProviderEvent;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AuthorityCommunicationDeletedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public int $teamId,
        public int $bookingId,
        public int $rentalId,
        public string $reletedModelType,
        public int $relatedModelId,
        public GuestsRegistrationAuthorityEnum $authority,
        public CommunicationTypeEnum $communicationType,
    ) {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->teamId,
            'rental_id' => $this->rentalId,
            'type' => ProviderEvent::AUTHORITY_COMMUNICATION_CANCELED,
            'booking_id' => $this->bookingId,
            'related_model_type' => $this->reletedModelType,
            'related_model_id' => $this->relatedModelId,
            'data' => [
                'authority' => $this->authority,
                'communication_type' => $this->communicationType,
            ],
        ]);
    }
}
