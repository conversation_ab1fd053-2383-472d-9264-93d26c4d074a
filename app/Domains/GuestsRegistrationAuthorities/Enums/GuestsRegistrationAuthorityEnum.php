<?php

namespace App\Domains\GuestsRegistrationAuthorities\Enums;

use App;
use App\Domains\GuestsRegistrationAuthorities\Services\GuestsRegistrationAbstractService;
use App\Domains\GuestsRegistrationAuthorities\Services\SesHospedajesService;

enum GuestsRegistrationAuthorityEnum: string
{
    case SesHospedajes = 'ses-hospedajes';

    public function getService(): GuestsRegistrationAbstractService
    {
        return match ($this) {
            self::SesHospedajes => App::make(SesHospedajesService::class),
        };
    }

    // List here the booking.php relationships for the communication models
    public static function communicationBookingRelationships(): array
    {
        return ['sesHospedajesCommunications'];
    }
}
