<?php

namespace App\Domains\GuestsRegistrationAuthorities\Enums;

enum SesHospedajesCodigoEstadoEnum: int
{
    case SinErrores = 1;
    case ErroresCabecera = 2;
    case ErrorInesperado = 3;
    case EnProceso = 4;
    case Pendiente = 5;
    case TramitadoConErrores = 6;

    public function getDescription(): string
    {
        return match ($this) {
            self::SinErrores => 'Lote tramitado sin errores',
            self::ErroresCabecera => 'Lote con errores en la cabecera o formato de la solicitud',
            self::ErrorInesperado => 'Error inesperado',
            self::EnProceso => 'En proceso',
            self::Pendiente => 'Pendiente',
            self::TramitadoConErrores => 'Lote tramitado con errores en algunas comunicaciones',
        };
    }

    /** For this statuses, we should report again after solving issues. */
    public function mustReportAgain(): bool
    {
        return match ($this) {
            self::SinErrores, self::EnProceso, self::Pendiente => false,
            self::ErroresCabecera, self::Error<PERSON>nes<PERSON><PERSON>, self::TramitadoConErrores => true,
        };
    }

    public function mustBeConsulted(): bool
    {
        return match ($this) {
            self::EnProceso, self::Pendiente => true,
            default => false,
        };
    }
}
