<?php

namespace App\Domains\GuestsRegistrationAuthorities\Enums;

enum SesHospedajesRequestTypeEnum: string
{
    case ParteViajeros = 'PV';
    case ReservaHospedaje = 'RH';
    case Consulta = 'C';
    case Anulacion = 'B';

    public function getXsdFileRoute(): string
    {
        return match ($this) {
            self::ParteViajeros => 'http://www.neg.hospedajes.mir.es/altaParteHospedaje',
            self::ReservaHospedaje => 'http://www.neg.hospedajes.mir.es/altaReservaHospedaje',
            self::Consulta => 'http://www.neg.hospedajes.mir.es/consultarComunicacion',
            self::Anulacion => 'http://www.neg.hospedajes.mir.es/anularComunicacion',
        };
    }

    public function getTipoOperacion(): string
    {
        return match ($this) {
            self::ParteViajeros, self::ReservaHospedaje => 'A',
            self::Consulta => self::Consulta->value,
            self::Anulacion => self::Anulacion->value,
        };
    }

    public function getTipoComunicacion(): ?string
    {
        return match ($this) {
            self::ParteViajeros => self::ParteViajeros->value,
            self::ReservaHospedaje => self::ReservaHospedaje->value,
            self::Consulta, self::Anulacion => null,
        };
    }

    public function getCompressedXmlRootElementName(array $payload): string
    {
        return match ($this) {
            self::ParteViajeros, self::ReservaHospedaje => 'ns2:peticion',
            self::Consulta => 'con:'.array_key_first($payload),
            self::Anulacion => 'anul:'.array_key_first($payload),
        };
    }

    public function getCompressedXmlRootElementAttribute(): string
    {
        return match ($this) {
            self::ParteViajeros, self::ReservaHospedaje => 'xmlns:ns2',
            self::Consulta => 'xmlns:con',
            self::Anulacion => 'xmlns:anul',
        };
    }

    public function getCompressedXmlBody(array $payload): array
    {
        return match ($this) {
            self::ParteViajeros, self::ReservaHospedaje => $payload,
            self::Consulta, self::Anulacion => $payload[array_key_first($payload)],
        };
    }
}
