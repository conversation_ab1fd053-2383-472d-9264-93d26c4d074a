<?php

namespace App\Domains\GuestsRegistrationAuthorities\Data;

use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Spatie\LaravelData\Attributes\FromRouteParameterProperty;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\Validation\ArrayType;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;
use Spatie\LaravelData\Support\Validation\ValidationContext;

#[MapName(SnakeCaseMapper::class)]
class GuestsRegistrationAuthorityData extends Data
{
    public function __construct(
        #[FromRouteParameterProperty('guestsRegistrationAuthority', 'id')]
        public int|Optional $id,
        #[FromRouteParameterProperty('team', 'id')]
        public int $teamId,
        #[FromRouteParameterProperty('teamRental', 'id')]
        public int $rentalId,
        #[Required, StringType]
        public GuestsRegistrationAuthorityEnum $authority,
        #[Nullable, ArrayType]
        public ?array $sources,

        // Note: Validation rules of the following attributes must all be at the Authority level to avoid overriding of the rules here
        public string|Optional|null $username,
        public string|Optional|null $password,
        public array|Optional|null $extraRentalInfo,
    ) {
    }

    public static function rules(ValidationContext $context): array
    {
        $payload = $context->payload;
        $id = array_key_exists('id', $payload) ? $payload['id'] : null;
        $exists = GuestsRegistrationAuthority::query()
            ->when($id, fn (Builder $query) => $query->where('id', '<>', $id))
            ->where('team_id', $payload['teamId'])
            ->where('rental_id', $payload['rentalId'])
            ->where('authority', $payload['authority'])
            ->exists();

        abort_if($exists, 400, "This Authority is already assigned to this rental. You can't have the same Authority multiple times for the same Rental.");

        return array_merge(
            GuestsRegistrationAuthorityEnum::tryFrom($payload['authority'])->getService()->authorityValidationRules(request()->isMethod('post')),
            ['team_id' => 'integer', 'rental_id' => 'integer'] // These rules are here because for some reason, fails adding a 'required' to the rules (default without these rules here)
        );
    }

    public static function fromRequest(Request $request): static
    {
        $authority = GuestsRegistrationAuthorityEnum::tryFrom($request->input('authority'));

        return new self(
            id: $request->route('guestsRegistrationAuthority')?->id ?? new Optional(),
            teamId: $request->route('team')->id,
            rentalId: $request->route('teamRental')->id,
            authority: $authority,
            sources: $request->input('sources'),
            username: $request->input('username'),
            password: $request->input('password') ?? new Optional(), // For PUT, I don't want to update with null the DB
            extraRentalInfo: $authority->getService()->getExtraRentalInfoFromPayload($request->input()),
        );
    }
}
