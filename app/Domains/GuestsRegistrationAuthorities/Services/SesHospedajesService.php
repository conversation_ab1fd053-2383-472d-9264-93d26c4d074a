<?php

namespace App\Domains\GuestsRegistrationAuthorities\Services;

use App\Domains\GuestsRegistrationAuthorities\Actions\SesHospedajes\SesHospedajesConsultaLotesAction;
use App\Domains\GuestsRegistrationAuthorities\Actions\SesHospedajes\SesHospedajesSyncBookingInformationAction;
use App\Domains\GuestsRegistrationAuthorities\Api\SesHospedajesApi;
use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Domains\GuestsRegistrationAuthorities\Enums\SesHospedajesCodigoEstadoEnum;
use App\Domains\GuestsRegistrationAuthorities\Enums\SesHospedajesRequestTypeEnum;
use App\Domains\GuestsRegistrationAuthorities\Events\AuthorityCommunicationDeletedEvent;
use App\Domains\GuestsRegistrationAuthorities\Events\AuthorityCommunicationSubmittedEvent;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use App\Domains\GuestsRegistrationAuthorities\Models\SesHospedajesCommunication;
use App\DTO\Support\FrontendComponentData;
use App\Enum\FrontendFormDataTypeEnum;
use App\Events\Booking\BookingAbstractEvent;
use App\Models\Booking;
use Arr;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Spatie\LaravelData\DataCollection;

class SesHospedajesService extends GuestsRegistrationAbstractService
{
    const AUTHORITY_TYPE = GuestsRegistrationAuthorityEnum::SesHospedajes;

    public function __construct(
        private SesHospedajesApi $api = new SesHospedajesApi(),
    ) {
    }

    public function dispatchSync(): void
    {
        SesHospedajesSyncBookingInformationAction::dispatch();
    }

    public function reportAtBookingCreation(Collection|Booking $bookings): bool
    {
        $this->ensureCollection($bookings, self::AUTHORITY_TYPE)
             ->where(fn (Booking $booking) => $booking->client_id !== null) // Validation: ensure we have a client. The rest of the information is always present in our bookings
             ->each(function (Booking $booking) {
                 // Note: We have finally decided to do 1 request per booking as there is no way to map multiple "comunicaciones" of a "lote" to each in consultaLotes()

                 // Do not communicate bookings already communicated
                 if ($booking->sesHospedajesCommunications
                      ->where('tipo_comunicacion', SesHospedajesRequestTypeEnum::ReservaHospedaje)
                      ->where(function (SesHospedajesCommunication $communication) {
                          return $communication->codigo_estado === null || ! $communication->codigo_estado->mustReportAgain();
                      })
                      ->isNotEmpty()) {
                     return;
                 }

                 try {
                     $response = $this->api->communicateNewBookings($booking);
                     $lote = Arr::get($response, 'SOAP-ENV:Body.ns3:comunicacionResponse.respuesta.lote');
                     if ($lote === null) {
                         throw new Exception('[SES Hospedajes] lote in Reserva de Hospedaje is null!');
                     }

                     $com = SesHospedajesCommunication::create([
                         'team_id' => $booking->team_id,
                         'rental_id' => $booking->rental_id,
                         'booking_id' => $booking->id,
                         'lote' => $lote, // 1 lote -> 1 comunicacion -> 1 booking
                         'tipo_comunicacion' => SesHospedajesRequestTypeEnum::ReservaHospedaje,
                     ]);

                     event(new AuthorityCommunicationSubmittedEvent($com));

                     SesHospedajesConsultaLotesAction::dispatch($com)->delay(60 * 5); // In 5 min will be resolved?
                 } catch (Exception $e) {
                     // Report and continue with other bookings:
                     report($e);
                 }
             });

        return true;
    }

    public function reportAtCheckInTime(Collection|Booking $bookings): bool
    {
        $this->ensureCollection($bookings, self::AUTHORITY_TYPE)
            ->where(fn (Booking $booking) => $booking->client_id !== null && $booking->preCheckInForm?->completed)
            ->each(function (Booking $booking) {
                // Note: We have finally decided to do 1 request per booking as there is no way to map multiple "comunicaciones" of a "lote" to each in consultaLotes()

                // Do not communicate bookings already communicated
                if ($booking->sesHospedajesCommunications
                    ->where('tipo_comunicacion', SesHospedajesRequestTypeEnum::ParteViajeros)
                    ->where(function (SesHospedajesCommunication $communication) {
                        return $communication->codigo_estado === null || ! $communication->codigo_estado->mustReportAgain();
                    })
                    ->isNotEmpty()) {
                    return;
                }

                try {
                    $response = $this->api->communicatePreCheckInForms($booking);
                    $lote = Arr::get($response, 'SOAP-ENV:Body.ns3:comunicacionResponse.respuesta.lote');
                    if ($lote === null) {
                        throw new Exception('[SES Hospedajes] lote in Parte Viajeros is null!');
                    }

                    $com = SesHospedajesCommunication::create([
                        'team_id' => $booking->team_id,
                        'rental_id' => $booking->rental_id,
                        'booking_id' => $booking->id,
                        'lote' => $lote, // 1 lote -> 1 comunicacion -> 1 booking
                        'tipo_comunicacion' => SesHospedajesRequestTypeEnum::ParteViajeros,
                    ]);

                    event(new AuthorityCommunicationSubmittedEvent($com));

                    SesHospedajesConsultaLotesAction::dispatch($com)->delay(60 * 5); // In 5 min will be resolved?
                } catch (Exception $e) {
                    // Report and continue with other bookings:
                    report($e);
                }
            });

        return true;
    }

    public function reportAtBookingCancellation(Collection|Booking $bookings): bool
    {
        $bookings = $this->ensureCollection($bookings, self::AUTHORITY_TYPE, false); // Do not filter by source, we want to cancel all existing reports

        $communicationsModifiedFlag = false;
        $bookings->each(function (Booking $booking) use (&$communicationsModifiedFlag) {
            // If we have communications which should be resolved, try to get the codigo_comunicacion
            $booking->sesHospedajesCommunications
                ->where(fn (SesHospedajesCommunication $com) => $com->codigo_estado === null || $com->codigo_estado->mustBeConsulted())
                ->whenNotEmpty(function (Collection $communications) use (&$communicationsModifiedFlag) {
                    $this->consultaLotesPendientes($communications);
                    $communicationsModifiedFlag = true;
                });
        });

        if ($communicationsModifiedFlag) {
            $bookings->load('sesHospedajesCommunications');
        }

        $bookings->where(fn (Booking $booking) => $booking->sesHospedajesCommunications->whereNotNull('codigo_comunicacion')->isNotEmpty())
            ->groupBy(['team_id', 'rental_id']) // Group by team and rental and call the api for each one
            ->each(function (Collection $teamRentals) {
                $teamRentals->each(function (Collection $rentalBookings) {
                    $rentalBookings->chunk(100)->each(function (Collection $bookings) {
                        try {
                            $deletedComms = $this->api->communicateBookingCancellations($bookings);

                            // We will only get here if response is OK:
                            $deletedComms
                                ->each(function (SesHospedajesCommunication $com) {
                                    $com->delete();
                                    rescue(fn () => new AuthorityCommunicationDeletedEvent(
                                        $com->team_id,
                                        $com->booking_id,
                                        $com->rental_id,
                                        $com->getMorphClass(),
                                        $com->id,
                                        $com->getAuthority(),
                                        $com->getCommunicationType(),
                                    ));
                                });
                        } catch (Exception $e) {
                            report($e);
                        }
                    });
                });
            });

        return true;
    }

    public function reportAtBookingModification(Booking $booking, BookingAbstractEvent $event): bool
    {
        // Ses Hospedajes asks for check-in time. Thus, we should update including when the event is CheckInOutTimeModifiedEvent

        // 1- Cancel communications

        // 2- Re-send communications
        return true;
    }

    public function consultaLotesPendientes(Collection|SesHospedajesCommunication $communications): bool
    {
        if ($communications instanceof SesHospedajesCommunication) {
            $communications = new Collection([$communications]);
        }

        $communications = $communications->filter(function (SesHospedajesCommunication $communication) {
            return $communication->codigo_estado === null || $communication->codigo_estado->mustBeConsulted();
        });

        if ($communications->isEmpty()) {
            return false;
        }

        try {
            $response = $this->api->consultaLotes($communications);
            foreach (Arr::get($response, 'SOAP-ENV:Body.ns3:comunicacionResponse') as $key => $resultado) {
                if ($key != 'resultado') {
                    continue;
                }
                /** @var SesHospedajesCommunication $communication */
                $communication = $communications->firstWhere('lote', $resultado['lote']);
                $communication->codigo_estado = SesHospedajesCodigoEstadoEnum::tryFrom(Arr::get($resultado, 'codigoEstado'));
                $communication->descripcion_estado = Arr::get($resultado, 'descEstado');
                $resultadoComunicacion = Arr::get($resultado, 'resultadoComunicaciones.resultadoComunicacion');
                if ($resultadoComunicacion) {
                    if (Arr::get($resultadoComunicacion, 'anulada')) {
                        $communication->delete();
                    }
                    $codigoComunicacion = Arr::get($resultadoComunicacion, 'codigoComunicacion');
                    if ($codigoComunicacion !== null) {
                        $communication->codigo_comunicacion = $codigoComunicacion; // This means correctly processed
                    } else {
                        $communication->tipo_error = Arr::get($resultadoComunicacion, 'tipoError');
                        $communication->descripcion_error = Arr::get($resultadoComunicacion, 'error');
                    }
                }

                $communication->save();
            }
        } catch (Exception $e) {
            report($e);
        }

        return true;
    }

    public function consultaCatalogo(string $tabla): array
    {
        $authority = GuestsRegistrationAuthority::first();
        $response = $this->api->consultaCatalogo($authority, $tabla);

        return Arr::get($response, 'SOAP-ENV:Body.ns3:catalogoResponse.respuesta.resultado');
    }

    public function getFormFields(bool $creatingNew): DataCollection
    {
        return FrontendComponentData::collection([
            FrontendComponentData::from([
                'key' => 'username',
                'type' => FrontendFormDataTypeEnum::string,
                'name' => 'Usuario Servicio Web',
                'description' => __('ninja.registration_authorities.form.ses_hospedajes.username.description'),
                'is_required' => true,
            ]),
            FrontendComponentData::from([
                'key' => 'password',
                'type' => FrontendFormDataTypeEnum::password,
                'name' => 'Contraseña Servicio Web',
                'description' => __('ninja.registration_authorities.form.ses_hospedajes.password.description'),
                'is_required' => $creatingNew,
            ]),
            FrontendComponentData::from([
                'key' => 'codigo_arrendador',
                'type' => FrontendFormDataTypeEnum::string,
                'name' => 'Código de Arrendador',
                'description' => __('ninja.registration_authorities.form.ses_hospedajes.codigo_arrendador.description'),
                'is_required' => true,
            ]),
            FrontendComponentData::from([
                'key' => 'codigo_establecimiento',
                'type' => FrontendFormDataTypeEnum::string,
                'name' => 'Código de Establecimiento',
                'description' => __('ninja.registration_authorities.form.ses_hospedajes.codigo_establecimiento.description'),
                'is_required' => true,
            ]),
        ]);
    }

    public function authorityValidationRules(bool $creatingNew): array
    {
        return [
            'username' => ['string', 'required'],
            'password' => $creatingNew ? ['required', 'string'] : ['sometimes', 'required', 'string'],
            'codigo_arrendador' => ['string', 'required'],
            'codigo_establecimiento' => ['string', 'required'],
        ];
    }

    public function getExtraRentalInfoFromPayload(array $payload): array
    {
        return [
            'codigo_arrendador' => $payload['codigo_arrendador'],
            'codigo_establecimiento' => $payload['codigo_establecimiento'],
        ];
    }
}
