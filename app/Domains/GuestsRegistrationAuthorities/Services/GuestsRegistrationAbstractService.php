<?php

namespace App\Domains\GuestsRegistrationAuthorities\Services;

use App\Domains\GuestsRegistrationAuthorities\Enums\GuestsRegistrationAuthorityEnum;
use App\Domains\GuestsRegistrationAuthorities\Models\GuestsRegistrationAuthority;
use App\Events\Booking\BookingAbstractEvent;
use App\Models\Booking;
use Illuminate\Database\Eloquent\Collection;
use Spatie\LaravelData\DataCollection;

abstract class GuestsRegistrationAbstractService
{
    /** Each service implementation will decide:
     * - if we need to do something for each method here or not (may not be required by the authority).
     * - if we can send multiple bookings at once
     * - if we need to group them by rental or not
     * - filter out the sources for which we don't want to send anything.
     */
    abstract public function dispatchSync(): void;

    /** Report at the booking creation time. If we have to do something, implement it here. */
    abstract public function reportAtBookingCreation(Collection|Booking $bookings): bool;

    /** Cancel booking reports whenever a booking is canceled. In case we don't have to for an authority, leave it blank*/
    abstract public function reportAtBookingCancellation(Collection|Booking $bookings): bool;

    /** Report anything that needs to be reported at the check-in time. Leave blank if the authority does not require anything at this moment */
    abstract public function reportAtCheckInTime(Collection|Booking $bookings): bool;

    /** Make any modifications to communications already sent. Use the event to decide what to do */
    abstract public function reportAtBookingModification(Booking $booking, BookingAbstractEvent $event): bool;

    /** The form fields required to create the Authority */
    abstract public function getFormFields(bool $creatingNew): DataCollection;

    /** The validation rules required to create the Authority */
    abstract public function authorityValidationRules(bool $creatingNew): array;

    /** How to convert the payload from the frontend to the extra_rental_info array */
    abstract public function getExtraRentalInfoFromPayload(array $payload): array;

    public function ensureCollection(Collection|Booking $bookings, GuestsRegistrationAuthorityEnum $authorityType, bool $filterBySource = true): Collection
    {
        if ($bookings instanceof Booking) {
            $bookings = new Collection([$bookings]);
        }

        // Ensure the bookings are from the desired authority
        return $bookings->where(function (Booking $booking) use ($authorityType, $filterBySource) {
            /** @var GuestsRegistrationAuthority $authority */
            $authority = $booking->rental->guestRegistrationAuthorities->firstWhere('authority', $authorityType);

            return $authority !== null
                && (! $filterBySource || empty($authority->sources) || in_array($booking->source_id, $authority->sources));
        });
    }
}
