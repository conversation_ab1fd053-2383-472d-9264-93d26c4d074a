<?php

namespace App\Domains\Automations\Resources;

use App\Domains\Automations\Models\TriggerActionLog;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TriggerActionLog
 */
class TriggerActionLogResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'trigger_action_id' => $this->trigger_action_id,
            'booking_id' => $this->booking_id,
            'rental_id' => $this->rental_id,
            'content' => $this->content,
            'run_at' => $this->run_at,
        ];
    }
}
