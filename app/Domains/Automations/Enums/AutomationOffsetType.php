<?php

namespace App\Domains\Automations\Enums;

enum AutomationOffsetType: string
{
    case CHECK_IN_OFFSET = 'check_in_offset';
    case CHECK_OUT_OFFSET = 'check_out_offset';
    case CREATED_OFFSET = 'created_offset';

    public function isCheckOut(): bool
    {
        return $this == self::CHECK_OUT_OFFSET;
    }

    public function isCheckIn(): bool
    {
        return $this == self::CHECK_IN_OFFSET;
    }

    public static function toKeyTranslationArray(): array
    {
        return array_map(
            fn (self $type) => [
                'key' => $type->value,
                'translation' => __('automations.offset_types.'.$type->value),
            ],
            self::cases()
        );
    }
}
