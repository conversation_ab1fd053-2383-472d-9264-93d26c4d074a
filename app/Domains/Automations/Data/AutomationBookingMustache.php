<?php

/** @noinspection PhpMethodNamingConventionInspection */

namespace App\Domains\Automations\Data;

use App\Actions\Guests\GetGuestsPortalAutologinAction;
use App\Models\Booking;
use App\Models\Currency;

class AutomationBookingMustache
{
    public function __construct(public Booking $booking)
    {
    }

    public function booking_reference(): string
    {
        return $this->booking->reference;
    }

    public function client_name(): string
    {
        return $this->booking->client->firstname;
    }

    public function client_fullname(): string
    {
        return $this->booking->client->fullname;
    }

    public function check_in_date(): string
    {
        return $this->booking->getStartAt()->format('Y-m-d');
    }

    public function check_out_date(): string
    {
        return $this->booking->getEndAt()->format('Y-m-d');
    }

    public function check_in_time(): string
    {
        return $this->booking->getStartAt()->format('H:i');
    }

    public function check_out_time(): string
    {
        return $this->booking->getEndAt()->format('H:i');
    }

    public function booking_final_price(): string
    {
        return $this->formatPrice($this->booking->final_price);
    }

    public function booking_deposit_price(): string
    {
        return $this->formatPrice($this->booking->damage_deposit);
    }

    public function booking_paid_amount(): string
    {
        return $this->formatPrice($this->booking->paid_amount);
    }

    public function booking_missing_payment(): string
    {
        return $this->formatPrice($this->booking->final_price - $this->booking->paid_amount);
    }

    public function booking_guest_portal_url(): string
    {
        return GetGuestsPortalAutologinAction::run($this->booking->team, $this->booking);
    }

    public function rental_public_name(): string
    {
        return $this->booking->rental->channel_name;
    }

    public function rental_name(): string
    {
        return $this->booking->rental->name;
    }

    public function rental_city(): string
    {
        return $this->booking->rental->city;
    }

    public function rental_country(): string
    {
        return $this->booking->rental->country_code;
    }

    public function rental_address(): string
    {
        return $this->booking->rental->address1.' '.$this->booking->rental->address2;
    }

    public function rental_zip(): string
    {
        return $this->booking->rental->zip;
    }

    public function team_name(): string
    {
        return $this->booking->team->name;
    }

    public function rental_contact_name(): string
    {
        return $this->booking->rental->contact_name;
    }

    public function rental_contact_email(): string
    {
        return $this->booking->rental->contact_email;
    }

    public function rental_contact_phone(): string
    {
        return $this->booking->rental->contact_phone;
    }

    protected function formatPrice(float $value): string
    {
        $currency = once(fn () => Currency::find(strtolower($this->booking->currency))->symbol);

        return $value.' '.$currency;
    }
}
