<?php

namespace App\Domains\Automations\Actions\Core;

use App\Domains\Automations\Enums\AutomationOffsetType;
use App\Domains\Automations\Enums\BookingPaidStatusOptions;
use App\Domains\Automations\Models\Automation;
use App\Domains\Automations\Models\BookingAutomationLog;
use App\Models\Booking;
use App\Models\BookingTag;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class RunAutomationForBookingAction
{
    use AsAction;

    public string $commandSignature = 'ninja:run-automations {teamId} {automationId} {bookingId}';
    public string $commandDescription = 'Run a single automation';

    public string $jobQueue = 'automations';
    public int $jobTries = 1;
    public int $jobMaxExceptions = 1;

    public function asCommand(Command $command): int
    {
        $teamId = $command->argument('teamId');
        $automationId = $command->argument('automationId');
        $bookingId = $command->argument('bookingId');
        $team = Team::find($teamId);
        $automation = $team->automations()->find($automationId);
        $booking = Booking::getBookingModel($team, $bookingId);

        $this->handle($team, $automation, $booking);

        return $command::SUCCESS;
    }

    public function handle(Team $team, Automation $automation, Booking $booking): void
    {
        // Double check filters
        if (! $this->checkFilters($automation, $booking)) {
            return;
        }
        // Check the trigger
        $trigger = $this->checkTrigger($automation, $booking);
        if ($trigger === false) {
            return;
        }

        // Register automation run
        BookingAutomationLog::registerRun($automation, $booking, $trigger);

        // If we are here, it means the booking matches the automation conditions
        $automation->triggerActions->each(function ($action) use ($team, $booking) {
            RunTriggerActionForBookingAction::run($team, $action, $booking);
        });
    }

    private function checkTrigger(Automation $a, Booking $b): bool|int
    {
        $runs = $b->automationLogs()
            ->where('automation_id', $a->id)
            ->get();

        // Event based: Event based automations are triggered by the event itself
        if ($a->trigger_type->isEventBased()) {
            return true;
        }

        // Triggered by time: Check if the booking is within the trigger time
        $attempt = $runs->count() + 1;
        $daysDifferent = match ($attempt) {
            1 => $a->trigger_days_diff,
            2 => $a->trigger_2nd_attempt_days_diff,
            3 => $a->trigger_3rd_attempt_days_diff,
            default => null,
        };

        if (is_null($daysDifferent)) {
            return false;
        }
        $column = match ($a->trigger_offset) {
            AutomationOffsetType::CHECK_IN_OFFSET => 'start_at',
            AutomationOffsetType::CHECK_OUT_OFFSET => 'end_at',
            AutomationOffsetType::CREATED_OFFSET => 'created_at',
            default => throw new \InvalidArgumentException('Invalid trigger type'),
        };
        if (Carbon::parse($b->{$column})->shiftTimezone($b->rental->timezone)->isBefore(now()->addDays($daysDifferent))) {
            return false;
        }

        return $attempt;
    }

    private function checkFilters(Automation $a, Booking $b): bool
    {
        // 1. Booking status
        if ($a->booking_status && $b->status != $a->booking_status) {
            return false;
        }

        // 2. Source filter
        if ($a->source_filter->isInclude() && ! in_array($b->source_id, $a->source_ids)) {
            return false;
        }
        if ($a->source_filter->isExclude() && in_array($b->source_id, $a->source_ids)) {
            return false;
        }

        // 3. Rental filter
        if ($a->rental_filter->isInclude() && ! in_array($b->rental_id, $a->rental_ids)) {
            return false;
        }
        if ($a->rental_filter->isExclude() && in_array($b->rental_id, $a->rental_ids)) {
            return false;
        }

        // 4. Pre check-in status
        if ($a->pre_check_in_status && $b->preCheckInForm->completed != $a->pre_check_in_status) {
            return false;
        }

        // 5. Tags filter
        if ($a->tags_filter->isInclude() && ! $b->tagsCollection()->contains(fn (BookingTag $tag) => in_array($tag->id, $a->tags_ids))) {
            return false;
        }
        if ($a->tags_filter->isExclude() && $b->tagsCollection()->contains(fn (BookingTag $tag) => in_array($tag->id, $a->tags_ids))) {
            return false;
        }

        // 6. Paid filter
        if ($a->paid_filter->isInclude() || $a->paid_filter->isExclude()) {
            $th = match ($a->paid_threshold) {
                BookingPaidStatusOptions::FULLY_PAID => $b->final_price + $b->damage_deposit - 0.01,
                BookingPaidStatusOptions::BOOKING_PAID => $b->final_price - 0.01,
                BookingPaidStatusOptions::RENTAL_PRICE_PAID => $b->final_rental_price - 0.01,
                BookingPaidStatusOptions::DOWNPAYMENT_PAID => $b->downpayment - 0.01,
            };
            if ($a->paid_filter->isInclude()) {
                $pass = $th <= $b->paid_amount;
            } else {
                $pass = $th >= $b->paid_amount;
            }
            if (! $pass) {
                return false;
            }
        }
        // 7. Note contains
        if ($a->note_contains && ! Str::of($b->notes)->contains($a->note_contains)) {
            return false;
        }

        return true;
    }
}
