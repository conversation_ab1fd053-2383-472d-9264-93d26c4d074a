<?php

namespace App\Domains\Automations\Actions\API;

use App\Domains\Automations\Data\TriggerActionData;
use App\Domains\Automations\Models\Automation;
use App\Domains\Automations\Models\TriggerAction;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class AttachTriggerActionAction
{
    use AsAction;

    public function asController(Team $team, Automation $automation, TriggerActionData $data): TriggerActionData
    {
        return TriggerActionData::from($this->handle($team, $automation, $data));
    }

    public function handle(Team $team, Automation $automation, TriggerActionData $data): TriggerAction
    {
        return $automation->triggerActions()->create($data->toArray());
    }
}
