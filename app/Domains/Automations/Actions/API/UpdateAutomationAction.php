<?php

namespace App\Domains\Automations\Actions\API;

use App\Domains\Automations\Data\AutomationData;
use App\Domains\Automations\Models\Automation;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsController;

class UpdateAutomationAction
{
    use AsController;

    public function asController(Team $team, Automation $automation, AutomationData $data): AutomationData
    {
        $automation = $this->handle($automation, $data);

        return AutomationData::from($automation);
    }

    public function handle(Automation $automation, AutomationData $data): Automation
    {
        $automation->update($data->toArray());

        return $automation;
    }
}
