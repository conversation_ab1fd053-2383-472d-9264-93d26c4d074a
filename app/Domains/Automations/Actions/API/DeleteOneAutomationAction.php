<?php

namespace App\Domains\Automations\Actions\API;

use App\Domains\Automations\Models\Automation;
use App\Models\Team;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\Concerns\AsController;

class DeleteOneAutomationAction
{
    use AsController;

    public function asController(Team $team, Automation $automation): JsonResponse
    {
        $status = $this->handle($automation);

        return response()->json([], $status ? 204 : 404);
    }

    private function handle(Automation $automation): bool
    {
        return $automation->delete();
    }
}
