<?php

namespace App\Domains\Automations\Actions\API;

use App\Domains\Automations\Models\TriggerActionLog;
use App\Domains\Automations\Resources\TriggerActionLogResource;
use App\Models\Team;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Concerns\AsController;

class FetchTriggerActionLogsAction
{
    use AsController;
    use AsAction;

    public function asController(Team $team, ActionRequest $request): \Illuminate\Http\Resources\Json\AnonymousResourceCollection
    {
        // Get query parameters
        $triggerActionId = $request->input('trigger_action_id');
        $bookingId = $request->input('booking_id');
        $rentalId = $request->input('rental_id');
        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : null;
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : null;

        $logs = TriggerActionLog::query()
            ->where('team_id', $team->id)
            ->when($triggerActionId, fn ($q) => $q->where('trigger_action_id', $triggerActionId))
            ->when($bookingId, fn ($q) => $q->where('booking_id', $bookingId))
            ->when($rentalId, fn ($q) => $q->where('rental_id', $rentalId))
            ->when($startDate, fn ($q) => $q->where('run_at', '>=', $startDate))
            ->when($endDate, fn ($q) => $q->where('run_at', '<=', $endDate))
            ->orderBy('run_at', 'desc')
            ->paginate();

        return TriggerActionLogResource::collection($logs);
    }
}
