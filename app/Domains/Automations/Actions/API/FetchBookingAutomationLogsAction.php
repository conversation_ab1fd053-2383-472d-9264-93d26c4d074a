<?php

namespace App\Domains\Automations\Actions\API;

use App\Domains\Automations\Models\BookingAutomationLog;
use App\Domains\Automations\Resources\BookingAutomationLogResource;
use App\Models\Team;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsController;

class FetchBookingAutomationLogsAction
{
    use AsController;

    public function asController(Team $team, ActionRequest $request): AnonymousResourceCollection
    {
        // Get query parameters
        $automationId = $request->input('automation_id');
        $bookingId = $request->input('booking_id');
        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : null;
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : null;

        $logs = BookingAutomationLog::query()
            ->where('team_id', $team->id)
            ->when($automationId, fn ($q) => $q->where('automation_id', $automationId))
            ->when($bookingId, fn ($q) => $q->where('booking_id', $bookingId))
            ->when($startDate, fn ($q) => $q->where('run_at', '>=', $startDate))
            ->when($endDate, fn ($q) => $q->where('run_at', '<=', $endDate))
            ->orderBy('run_at', 'desc')
            ->paginate();

        return BookingAutomationLogResource::collection($logs);
    }
}
