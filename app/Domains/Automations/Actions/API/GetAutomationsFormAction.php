<?php

namespace App\Domains\Automations\Actions\API;

use App\Domains\Automations\Enums\AutomationFilterType;
use App\Domains\Automations\Enums\AutomationOffsetType;
use App\Domains\Automations\Enums\AutomationTriggerType;
use App\Domains\Automations\Enums\BookingPaidStatusOptions;
use App\Domains\Automations\Enums\BookingPlaceholderEnum;
use App\Domains\Automations\Enums\TriggerActionType;
use App\Enum\BookingStatusEnum;
use App\Http\Resources\BookingTagResource;
use App\Http\Resources\SourceResource;
use App\Models\ProviderEvent;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsController;

class GetAutomationsFormAction
{
    use AsController;

    public function asController(Team $team, ActionRequest $request): array
    {
        $user = $request->user();

        return [
            'booking_status' => collect(BookingStatusEnum::PUBLIC_STATES)->map(
                fn (BookingStatusEnum $status) => [
                    'key' => $status->value,
                    'translation' => $status->getStatusPublicName(),
                ]
            ),
            'sources' => SourceResource::collection($team->sources),
            'rental_ids' => $user->getRentalsForUser(), // Rental ids
            'tags' => BookingTagResource::collection($team->bookingTags),
            'automation_filter_type' => AutomationFilterType::toKeyTranslationArray(),
            'automation_offset_type' => AutomationOffsetType::toKeyTranslationArray(),
            'automations_trigger_types' => AutomationTriggerType::toKeyTranslationArray(),
            'booking_paid_status_options' => BookingPaidStatusOptions::toKeyTranslationArray(),
            'booking_placeholder_enum' => BookingPlaceholderEnum::toKeyTranslationArray(), //Add description
            'events' => [
                ['key' => ProviderEvent::BOOKING_CREATED, 'translation' => __('automations.events.booking-created')],
                ['key' => ProviderEvent::BOOKING_DATES_UPDATED, 'translation' => __('automations.events.booking-dates-updated')],
                ['key' => ProviderEvent::BOOKING_CANCELLED, 'translation' => __('automations.events.booking-cancelled')],
                ['key' => ProviderEvent::BOOKING_TENTATIVE, 'translation' => __('automations.events.booking-tentative')],
                ['key' => ProviderEvent::BOOKING_BLOCK, 'translation' => __('automations.events.block-created')],
                ['key' => ProviderEvent::LEAD_CREATED, 'translation' => __('automations.events.lead-created')],
                ['key' => ProviderEvent::BOOKING_REQUESTED, 'translation' => __('automations.events.booking-requested')],
                ['key' => ProviderEvent::BOOKING_REJECTED, 'translation' => __('automations.events.booking-rejected')],
                ['key' => ProviderEvent::PAYMENT_CREATED, 'translation' => __('automations.events.payment-created')],
                ['key' => ProviderEvent::PAYMENT_CANCELED, 'translation' => __('automations.events.payment-canceled')],
                ['key' => ProviderEvent::PAYMENT_REFUNDED, 'translation' => __('automations.events.payment-refunded')],
                ['key' => ProviderEvent::CHECK_IN_OUT_TIME_MODIFIED, 'translation' => __('automations.events.check-in-out-time-modified')],
                ['key' => ProviderEvent::UPSALE_PURCHASED, 'translation' => __('automations.events.upsale-purchased')],
            ],
            'trigger_actions_types' => TriggerActionType::toKeyTranslationArray(),
            'trigger_actions_dependencies' => TriggerActionType::toDependenciesArray(),
        ];
    }
}
