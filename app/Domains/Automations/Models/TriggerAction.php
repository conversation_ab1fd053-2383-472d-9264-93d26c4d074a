<?php

namespace App\Domains\Automations\Models;

use App\Domains\Automations\Data\MultiLanguageTextWithParamsData;
use App\Domains\Automations\Enums\AutomationFilterType;
use App\Domains\Automations\Enums\TriggerActionType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Domains\Automations\Models\TriggerAction.
 *
 * @property int $id
 * @property int $team_id
 * @property int $automation_id
 * @property TriggerActionType $action_type
 * @property \Spatie\LaravelData\Contracts\BaseData|null $content
 * @property string|null $destination
 * @property AutomationFilterType|null $user_filter
 * @property array|null $user_ids
 * @property array|null $tags
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Domains\Automations\Models\Automation $automation
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction query()
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereActionType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereAutomationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereDestination($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereTags($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereUserFilter($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TriggerAction whereUserIds($value)
 *
 * @mixin \Eloquent
 */
class TriggerAction extends Model
{
    protected $fillable = [
        'team_id',
        'automation_id',
        'action_type',
        'content',
        'user_filter',
        'user_ids',
        'destination',
        'tags',
    ];

    protected $casts = [
        'action_type' => TriggerActionType::class,
        'content' => MultiLanguageTextWithParamsData::class,
        'user_filter' => AutomationFilterType::class,
        'user_ids' => 'array',
        'tags' => 'array',
    ];

    public function automation(): BelongsTo
    {
        return $this->belongsTo(Automation::class);
    }
}
