<?php

namespace App\Domains\HomeAutomation\Mail;

use App\Domains\HomeAutomation\Actions\GetAllBookingDoorKeyCodesAction;
use App\Mail\GuestNotifications\NinjaGuestNotificationEmail;
use App\Models\Booking;
use App\Models\Team;
use Exception;

class SmartlockAuthorisationEmail extends NinjaGuestNotificationEmail
{
    /**
     * Create a new message instance.
     */
    public function __construct(
        public Team $team,
        public int $bookingId,
        public bool $forcePlain = false,
    ) {
        parent::__construct($forcePlain);
    }

    /**
     * Build the message.
     *
     * @throws Exception
     */
    public function build(): static
    {
        $booking = Booking::getBookingModel($this->team, $this->bookingId, ['client', 'rental', 'smartlockAuthorisations.homeAutomationDevice']);
        $client = $booking->client;
        $photo = ninja_str_contains($this->team->photo_url, 'avatar') ? null : $this->team->imgixPhoto();
        $teamName = ucwords(strtolower($this->team->name));
        $contactEmail = $this->getReplyToAddress($this->team, $booking->rental, $booking);

        $rental = $booking->rental;
        $doorInstructions = $rental->door_instructions;
        $subject = __('messages.home_automation.smartlock_authorisation_email.subject', [
            'city' => ! empty($rental->city) ? $rental->city : $rental->name,
            'on' => $booking->getCheckInDay(),
        ]);

        return $this
            ->subject($subject)
            ->from($this->getMailFromAddress($this->team, $booking->rental, $booking), $teamName)
            ->replyTo($contactEmail, $teamName)
            ->view(! $this->forcePlain ? 'mail.smartlock.smartlock_authorisation' : null)
            ->text('mail.smartlock.smartlock_authorisation_plain')
            ->with([
                'sub' => $subject,
                'team' => $this->team,
                'booking' => $booking,
                'clientName' => $client->getClientNameForEmail(),
                'photo' => $photo,
                'authorisations' => GetAllBookingDoorKeyCodesAction::run($booking),
                'contactEmail' => $contactEmail,
                'doorInstructions' => $doorInstructions,
            ]);
    }
}
