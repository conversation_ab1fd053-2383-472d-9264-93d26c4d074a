<?php

namespace App\Domains\HomeAutomation\Actions;

use App\Domains\HomeAutomation\Events\SmartlockAuthorisationCreatedEvent;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Enum\AlertTypeEnum;
use App\Events\AlertCreatedEvent;
use App\Models\Alert;
use App\Models\Booking;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateSmartlockBookingAlertsAction implements ShouldQueue
{
    use AsAction;

    /**
     * @param  Collection<array>  $bookingsData
     *                                           We expect $bookingsData to be a Collection containing bookings in the form of [id, team_id]
     */
    public function asJob(Collection $bookingsData): void
    {
        // We mainly expect here to create alerts, as they should be live deleted when a new auth is
        Booking::query()
            ->loadCollection($bookingsData)
            ->with($this->loadRelationships())
            ->get()
            ->each(fn (Booking $booking) => rescue(fn () => $this->handle($booking)));
    }

    public function asListener(SmartlockAuthorisationCreatedEvent $event): void
    {
        $booking = $event->auth->booking;

        if ($booking) {
            $booking->loadMissing($this->loadRelationships());
            $this->handle($booking, false);
            // Note: If more than one auth is created, we don't want to create alerts for the rest of auths when the first auth is created
            // and the rest are about to come via webhook (race conditions)
        }
    }

    public function handle(Booking $booking, bool $shouldCreateAlerts = true): void
    {
        $missingAuthDevices = $booking->getRentalSmartlocks()->pluck('id')
                ->diff($booking->smartlockAuthorisations->pluck('home_automation_device_id')->unique());

        $alerts = $booking->alerts->where('alert_type', AlertTypeEnum::MISSING_AUTHORISATIONS);

        if ($missingAuthDevices->isEmpty() && $alerts->isNotEmpty()) {
            $alerts->each->delete();
        } elseif (
            $missingAuthDevices->isNotEmpty() &&
            $booking->getDaysLeftForCheckIn() < ManageBookingSmartlockAuthorisationsAction::AUTH_CREATION_ADVANCE_DAYS // Use same criteria as SlimBookingResource
        ) {
            $missingAlerts = $missingAuthDevices->diff($alerts->pluck('alertable_id'));
            if ($shouldCreateAlerts && $missingAlerts->isNotEmpty()) {
                $missingAlerts->each(function (int $deviceId) use ($booking) {
                    $alert = Alert::create([
                        'team_id' => $booking->team_id,
                        'rental_id' => $booking->rental_id,
                        'booking_id' => $booking->id,
                        'alertable_type' => HomeAutomationDevice::class,
                        'alertable_id' => $deviceId,
                        'alert_type' => AlertTypeEnum::MISSING_AUTHORISATIONS,
                        'real_value' => $booking->getRentalSmartlocks()->firstWhere('id', $deviceId)->name,
                    ]);
                    event(new AlertCreatedEvent($alert));
                });
            }

            $alertsToDelete = $alerts->pluck('alertable_id')->diff($missingAuthDevices);
            if ($alertsToDelete->isNotEmpty()) {
                $alertsToDelete->each->delete();
            }
        }
    }

    private function loadRelationships(): array
    {
        return ['rental.homeAutomationDeviceRentals.homeAutomationDevice', 'smartlockAuthorisations', 'alerts'];
    }
}
