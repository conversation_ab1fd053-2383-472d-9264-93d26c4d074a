<?php

namespace App\Domains\HomeAutomation\Actions;

use App\Domains\HomeAutomation\Enums\HomeAutomationDeviceTypeEnum;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Domains\HomeAutomation\Models\SmartlockAuthorisation;
use App\Domains\HomeAutomation\Resources\SmartlockAuthorisationResource;
use App\Domains\HomeAutomation\Services\SmartLocksServiceInterface;
use App\Models\Team;
use App\Models\User;
use Auth;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CreatePermanentSmartlockAuthorisationAction
{
    use AsAction;

    private ?string $error = null;

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:20'],
            'user_id' => ['required', 'int'],
            'code' => ['nullable', 'string'],
            'from' => ['required', 'date'],
            'until' => ['required', 'date', 'after:from'],
        ];
    }

    /* We have decided to not allow users to add the code in the frontend. Uncomment this if we allow it
     * public function afterValidator(Validator $validator, ActionRequest $request): void
    {
        $existingCodes = SmartlockAuthorisation::query()
            ->where('home_automation_device_id', $request->homeAutomationDevice->id)
            ->whereNotNull('code')
            ->pluck('code');

        if ($existingCodes->contains($request->input('code'))) {
            $validator->errors()->add('existing_code', __('messages.home_automation.devices.errors.existing_code'));
        }
    }*/

    public function asController(Team $team, HomeAutomationDevice $homeAutomationDevice, ActionRequest $request): SmartlockAuthorisationResource
    {
        abort_unless(CheckUserHasAccessToDeviceAction::run(Auth::user(), $homeAutomationDevice), 401, "You don't have access to the device.");
        abort_unless($homeAutomationDevice->type === HomeAutomationDeviceTypeEnum::SMART_LOCK, 400, "This device is not a Smart Lock, thus can't generate authorisations.");
        abort_unless($homeAutomationDevice->getService() instanceof SmartLocksServiceInterface, 400, 'This device has no access to generate authorisations.');
        abort_unless($homeAutomationDevice->getService()->canGeneratePermanentCodes(), 400, "This device can't generate permanent authorisations.");
        abort_unless($homeAutomationDevice->online, 400, "This device is offline. You can't create an authorisation for this device."); // Frontend should prevent this

        $name = $request->input('name');
        $code = $request->input('code');
        $from = Carbon::parse($request->input('from'));
        $until = transform($request->input('until'), fn ($until) => Carbon::parse($until));
        $user = User::find($request->input('user_id'));

        abort_unless($user && $user->current_team_id === $team->id, 404, 'User not found.');
        abort_if($code && ! $homeAutomationDevice->has_keypad, 400, "You are passing a code while this device doesn't have any keypad thus can't create codes.");
        $validationError = $homeAutomationDevice->getService()->validatePermanentCode($name, $code);
        if ($validationError) {
            abort(400, $validationError);
        }

        $auth = $this->handle($homeAutomationDevice, $user, $name, $from, $until, $code);
        abort_unless($auth !== null, 400, $this->error);

        // If the device is offline, inform the frontend that the authorisation couldn't be created
        sleep(3);
        $homeAutomationDevice->sync();
        if (! $homeAutomationDevice->refresh()->online) {
            $auth->delete();
            abort(400, __('messages.home_automation.authorisations.errors.offline'));
        }

        // Refresh it to see if the webhook has arrived and load the user
        $auth = SmartlockAuthorisation::query()
            ->with(['user', 'homeAutomationDevice']) // This will load the user in the resource to have his avatar
            ->where('id', $auth->id)
            ->first();

        return new SmartlockAuthorisationResource($auth);
    }

    public function handle(HomeAutomationDevice $device, User $user, string $name, Carbon $from, ?Carbon $until = null, ?string $code = null): ?SmartlockAuthorisation
    {
        if (! ShouldUseHomeAutomationApiAction::run($device->team_id)) {
            $this->error = "This environment can't use home automation api";

            return null;
        }

        if (! $device->online) {
            return null;
        }

        $device->loadMissing(['smartlockAuthorisations', 'homeAutomationAccount']);

        try {
            return $device->getService()->createSmartLockPermanentAccess($device, $user, $name, $from, $until, $code);
            // Note: auth created event is not triggered here because we have to wait until is mapped (means confirmed by provider). See webhook flow
        } catch (\Exception $e) {
            $this->error = $e->getMessage();

            return null;
        }
    }
}
