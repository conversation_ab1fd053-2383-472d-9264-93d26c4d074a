<?php

namespace App\Domains\HomeAutomation\Actions;

use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Domains\HomeAutomation\Resources\HomeAutomationDeviceResource;
use App\Models\Team;
use Lorisleiva\Actions\Concerns\AsAction;

class GetSingleHomeAutomationDeviceAction
{
    use AsAction;

    public function asController(Team $team, HomeAutomationDevice $homeAutomationDevice): HomeAutomationDeviceResource
    {
        return new HomeAutomationDeviceResource($this->handle($homeAutomationDevice));
    }

    public function handle(HomeAutomationDevice $homeAutomationDevice): HomeAutomationDevice
    {
        return $homeAutomationDevice
            ->load(['homeAutomationDeviceRentals', 'smartlockAuthorisations.user', 'smartlockAuthorisations.homeAutomationDevice']);
    }
}
