<?php

namespace App\Domains\HomeAutomation\Actions;

use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Models\User;
use Lorisleiva\Actions\Concerns\AsAction;

class CheckUserHasAccessToDeviceAction
{
    use AsAction;

    public function handle(User $user, HomeAutomationDevice $device): bool
    {
        // A device may be placed in the main entrance of a building, shared among different rentals.
        // Thus, user only needs access to one of the rentals of the device to access it
        // OR
        // You have permission to manage devices regardless of the rental is is paired to
        // (useful also if device is not yet paired, you should be able to open/close the door)
        return collect($user->getRentalsForUser())
                ->intersect($device->homeAutomationDeviceRentals->pluck('rental_id'))
                ->count() > 0
            || $user->permissionFor('manage_home_automation_settings');
    }
}
