<?php

namespace App\Domains\HomeAutomation\Actions;

use App\Domains\HomeAutomation\Enums\HomeAutomationProviderEnum;
use App\DTO\Team\PriceData;
use App\Models\Team;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetHomeAutomationPricingAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'provider' => 'required|string',
        ];
    }

    public function asController(Team $team, ActionRequest $request): JsonResponse
    {
        /** @var PriceData $price */
        $price = HomeAutomationProviderEnum::getPricing(HomeAutomationProviderEnum::tryFrom($request->input('provider')))
            ->filter(fn (PriceData $price) => $price->yearly === $team->isYearlySubscription())
            ->first();

        return response()->json(['data' => $price->only('id', 'yearly', 'pricing')]);
    }
}
