<?php

namespace App\Domains\HomeAutomation\Actions;

use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Events\Booking\BookingDatesUpdatedEvent;
use App\Events\Booking\CheckInOutTimeModifiedEvent;
use App\Models\Booking;
use App\Models\Team;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Lorisleiva\Actions\Concerns\AsAction;

class ModifyBookingAuthorisationsTimesAction implements ShouldBeUnique
{
    use AsAction;

    public int $jobUniqueFor = 60;

    public function getJobUniqueId(Team $team, int $bookingId): int
    {
        return intval($team->id.$bookingId); // We are using 64 bits system thus number size should be ok. Alternatively convert to string
    }

    public function asListener(BookingDatesUpdatedEvent|CheckInOutTimeModifiedEvent $event): void
    {
        $team = $event->team;
        $booking = Booking::getBookingModel($team, $event->bookingId, ['smartlockAuthorisations', 'rental.homeAutomationDeviceRentals.homeAutomationDevice']);

        $auths = $booking->smartlockAuthorisations;
        if ($auths->count() === 0 && ($booking->getDaysLeftForCheckIn() > ManageBookingSmartlockAuthorisationsAction::AUTH_CREATION_ADVANCE_DAYS || $booking->isPassed())) {
            return;
        }

        if ($booking->getRentalSmartlocks()->filter(fn (HomeAutomationDevice $device) => $device->online)->isEmpty()) {
            return;
        }

        // We should get here only if we have auths to modify or auths to create
        // For now, we don't want to delay the dispatch as we want that, when a user changes the CI or CO of a booking, immediately the auths are modified
        if ($auths->count() === 0) {
            CreateSmartlockBookingAuthorisationAction::dispatch(collect([['id' => $booking->id, 'team_id' => $booking->team_id]]));
            // Note: do not force as the booking may have been postponed by mistake and then when re-moved back to the original date, we don't want to create new auths
        } else {
            self::dispatch($team, $booking->id);
        }
    }

    public function asJob(Team $team, int $bookingId): void
    {
        $booking = Booking::getBookingModel($team, $bookingId, ['smartlockAuthorisations.homeAutomationDevice.homeAutomationAccount']);
        $this->handle($booking);
    }

    /** Should be ONLY USED for situations where we already have Authorisations */
    public function handle(Booking $booking): void
    {
        if (! ShouldUseHomeAutomationApiAction::run($booking->team_id)) {
            return;
        }

        $booking->loadMissing(['smartlockAuthorisations']);
        if ($booking->smartlockAuthorisations->isEmpty()) {
            return;
        }

        // We may find a booking to which we just need to modify the authorisations, or we need to cancel them because they are postponed
        if ($booking->getDaysLeftForCheckIn() > ManageBookingSmartlockAuthorisationsAction::AUTH_CREATION_ADVANCE_DAYS || $booking->isPassed()) {
            // We need to delete authorisations
            foreach ($booking->smartlockAuthorisations as $auth) {
                rescue(fn () => DeleteSmartlockAuthorisationAction::run($auth->homeAutomationDevice, $auth));
            }
        } else {
            // Calculate from and until (we consider all current authorisations will have devices with the same timezone):
            [$from, $until] = $booking->getSmartlockFromUntil($booking->smartlockAuthorisations->first()->homeAutomationDevice);

            foreach ($booking->smartlockAuthorisations as $authorisation) {
                // Check if times don't match
                if ($authorisation->from->diffInMinutes($from) > 10 || $authorisation->until->diffInMinutes($until) > 10) {
                    $device = $authorisation->homeAutomationDevice;
                    rescue(fn () => ModifySmartlockAuthorisationAction::run($device, $authorisation, $from, $until, $booking)); // Continue to the rest of auths if this fails
                }
            }
        }
    }
}
