<?php

namespace App\Domains\HomeAutomation\Actions;

use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Models\Team;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\Concerns\AsAction;

class DeleteHomeAutomationDeviceAction
{
    use AsAction;

    public function asController(Team $team, HomeAutomationDevice $homeAutomationDevice): JsonResponse
    {
        $this->handle($homeAutomationDevice);

        return response()->json();
    }

    public function handle(HomeAutomationDevice $homeAutomationDevice): bool
    {
        if (! ShouldUseHomeAutomationApiAction::run($homeAutomationDevice->team_id)) {
            return false;
        }

        return $homeAutomationDevice->getService()->removeDevice($homeAutomationDevice->homeAutomationAccount, $homeAutomationDevice, true);
    }
}
