<?php

namespace App\Domains\HomeAutomation\Events;

use App\Contracts\CreatesProviderEvents;
use App\Domains\HomeAutomation\Enums\HomeAutomationDeviceTypeEnum;
use App\Domains\HomeAutomation\Models\HomeAutomationAccount;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Models\ProviderEvent;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class HomeAutomationDeviceDeletedEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public HomeAutomationAccount $account,
        public int $id,
        public HomeAutomationDeviceTypeEnum $type,
        public string $name,
    ) {
        pnLog("[Home Automation] Device deleted in account {$this->account->id} of type {$this->type->value} and name {$this->name}", $this->account->team_id);
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->account->team_id,
            'type' => ProviderEvent::DEVICE_DELETED,
            'related_model_type' => HomeAutomationDevice::class,
            'related_model_id' => $this->id,
            'data' => [
                'home_automation_account_id' => $this->account->id,
                'provider' => $this->account->provider,
                'type' => $this->type,
                'name' => $this->name,
            ],
        ]);
    }
}
