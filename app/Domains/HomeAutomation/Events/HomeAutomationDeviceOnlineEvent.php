<?php

namespace App\Domains\HomeAutomation\Events;

use App\Contracts\CreatesProviderEvents;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Models\ProviderEvent;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class HomeAutomationDeviceOnlineEvent implements CreatesProviderEvents
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public HomeAutomationDevice $device,
    ) {
    }

    public function createProviderEvent(): ?ProviderEvent
    {
        return ProviderEvent::create([
            'team_id' => $this->device->team_id,
            'type' => ProviderEvent::DEVICE_ONLINE,
            'related_model_type' => HomeAutomationDevice::class,
            'related_model_id' => $this->device->id,
            'data' => [
                'provider' => $this->device->provider,
                'type' => $this->device->type,
                'name' => $this->device->name,
            ],
        ]);
    }
}
