<?php

namespace App\Domains\HomeAutomation\Api;

use App\Domains\HomeAutomation\Models\HomeAutomationAccount;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use Illuminate\Http\Client\Response;

interface SmartLocksApiInterface
{
    public function getDeviceAuthorisations(HomeAutomationAccount $account, HomeAutomationDevice $device): Response;

    public function openDoor(HomeAutomationDevice $device): Response;

    public function lockDoor(HomeAutomationDevice $device): Response;
}
