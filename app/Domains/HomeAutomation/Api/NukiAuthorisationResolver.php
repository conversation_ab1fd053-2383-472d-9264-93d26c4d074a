<?php

namespace App\Domains\HomeAutomation\Api;

use App\Domains\HomeAutomation\Enums\HomeAutomationProviderEnum;
use App\Domains\HomeAutomation\Events\SmartlockAuthorisationCreatedEvent;
use App\Domains\HomeAutomation\Events\SmartlockAuthorisationDeletedEvent;
use App\Domains\HomeAutomation\Models\HomeAutomationAccount;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Domains\HomeAutomation\Models\SmartlockAuthorisation;
use App\Exceptions\NinjaNotImplementedException;
use Arr;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class NukiAuthorisationResolver extends HomeAutomationResolver
{
    public bool $isMapped = false;

    /** @param HomeAutomationDevice $device should never be null */
    public function callback(Collection $response, HomeAutomationAccount $account, ?HomeAutomationDevice $device = null, bool $singleId = true): bool
    {
        if (! $device) {
            throw new NinjaNotImplementedException('We must have a device here');
        }

        // We only sync Authorisations to users or keypad codes:
        $response = $response->filter(fn (array $data) => Arr::get($data, 'accountUserId') || Arr::get($data, 'code'));

        if ($response->isEmpty()) {
            return true;
        }

        $databaseAuths = SmartlockAuthorisation::withTrashed()
            ->where('home_automation_device_id', $device->id)
            ->get();

        if (! $singleId) {
            // Delete not received auths if we are syncing all auths
            $responseIds = $response->pluck('id');
            $toDelete = $databaseAuths
                ->filter(fn (SmartlockAuthorisation $auth) => $auth->deleted_at === null && $responseIds->doesntcontain($auth->external_id) && $auth->external_id !== null);

            if ($toDelete->isNotEmpty()) {
                SmartlockAuthorisation::query()
                    ->whereIn('id', $toDelete->pluck('id'))
                    ->whereNotNull('external_id') // Non mapped authorisations are removed in a different flow, not depending on when the device is synced
                    ->delete();

                $toDelete->each(fn (SmartlockAuthorisation $auth) => $this->triggerDeletedEvent($auth));
            }
        }

        /** @var array $data */
        foreach ($response as $data) {
            $auth = $this->getAuthorisation($account, $databaseAuths, $data, $singleId);
            $isNewAuth = $auth->id === null;

            // Manage the "enabled" value correctly
            $enabled = Arr::get($data, 'enabled', true);
            $isRestored = ! $isNewAuth && $enabled && $auth->trashed();
            if (($isNewAuth && ! $enabled) || (! $isNewAuth && $auth->trashed() && ! $enabled)) {
                continue; // We don't want to continue to avoid triggering the created event + save useless data
            } elseif (! $isNewAuth && ! $enabled && ! $auth->trashed()) {
                $auth->delete();
                $this->triggerDeletedEvent($auth);
                continue;
            } elseif ($isRestored) {
                $auth->restore();
            }

            $auth->home_automation_device_id = $device->id;
            $auth->name = Arr::get($data, 'name') ?? 'No name';
            $auth->code = Arr::get($data, 'code'); // Auto converts to string if not null. If you transform null to string, returns empty string
            $auth->external_user_id = Arr::get($data, 'accountUserId'); // Auto converts to string if not null. If you transform null to string, returns empty string
            $auth->from = transform(Arr::get($data, 'allowedFromDate'), fn ($from) => Carbon::parse($from));
            $auth->until = transform(Arr::get($data, 'allowedUntilDate'), fn ($until) => Carbon::parse($until));
            $auth->save();

            if ($isNewAuth || $isRestored || $this->isMapped) {
                event(new SmartlockAuthorisationCreatedEvent($auth));
            }
        }

        return true;
    }

    private function getAuthorisation(HomeAutomationAccount $account, EloquentCollection $databaseAuths, array $authData, bool $isWebhook): SmartlockAuthorisation
    {
        $externalId = $authData['id'];

        $auth = $databaseAuths->firstWhere('external_id', $externalId);
        if ($auth) {
            return $auth;
        }

        // If it's a webhook from a new code created, try to match it with an existing code
        $code = Arr::get($authData, 'code');
        $externalUserId = Arr::get($authData, 'accountUserId');
        $from = transform(Arr::get($authData, 'allowedFromDate'), fn ($from) => Carbon::parse($from));
        $until = transform(Arr::get($authData, 'allowedUntilDate'), fn ($until) => Carbon::parse($until));
        if ($isWebhook && ($code || ($externalUserId && $from && $until))) {
            /** @var SmartlockAuthorisation $match */
            $match = $databaseAuths->whereNull('external_id')
                ->filter(function (SmartlockAuthorisation $auth) use ($code, $externalUserId, $from, $until) {
                    // 1- Only 1 same code per device is permitted, must be the same auth
                    // 2- Or if is the same userId with the same dates, must be the same auth
                    return $auth->code == (string) $code ||
                        ($auth->external_user_id == (string) $externalUserId && $auth->from->isSameDay($from) && $auth->until->isSameDay($until) && $auth->booking_id !== null);
                })
                ->first();

            if ($match) {
                $match->external_id = $externalId;
                $this->isMapped = true;

                return $match;
            }
        }

        $auth = new SmartlockAuthorisation();
        $auth->team_id = $account->team_id;
        $auth->provider = HomeAutomationProviderEnum::NUKI;
        $auth->external_id = $externalId;

        return $auth;
    }

    private function triggerDeletedEvent(SmartlockAuthorisation $auth): void
    {
        event(new SmartlockAuthorisationDeletedEvent($auth->id, $auth->home_automation_device_id, $auth->team_id, $auth->name, $auth->provider, $auth->booking_id));
    }
}
