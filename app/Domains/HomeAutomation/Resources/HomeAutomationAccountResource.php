<?php

namespace App\Domains\HomeAutomation\Resources;

use App\Domains\HomeAutomation\Models\HomeAutomationAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin HomeAutomationAccount
 */
class HomeAutomationAccountResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'provider' => $this->provider,
            'external_id' => $this->external_id,
            'name' => $this->name,
            'devices_count' => $this->home_automation_devices_count,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
