<?php

namespace App\Domains\HomeAutomation\Services;

use App\Domains\HomeAutomation\Models\HomeAutomationAccount;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Models\Team;

/** This service is a Singleton. Thus, avoid any constructor parameter to be passed which is not permanent,
 * as it is constructed once only per execution.
 */
interface HomeAutomationServiceInterface
{
    public function startConnection(Team $team);

    public function syncAccount(HomeAutomationAccount $account);

    public function removeAccount(HomeAutomationAccount $account): bool;

    public function syncDevices(HomeAutomationAccount $account);

    public function syncDevice(HomeAutomationAccount $account, string $externalId);

    public function removeDevice(HomeAutomationAccount $account, HomeAutomationDevice $device, bool $fromApi = false): bool;
}
