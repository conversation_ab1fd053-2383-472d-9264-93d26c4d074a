<?php

namespace App\Domains\HomeAutomation\Services;

use App\Domains\HomeAutomation\Actions\Nuki\InformNukiAboutSignupOrCancelationAction;
use App\Domains\HomeAutomation\Api\NukiAccountResolver;
use App\Domains\HomeAutomation\Api\NukiApi;
use App\Domains\HomeAutomation\Api\NukiAuthorisationResolver;
use App\Domains\HomeAutomation\Api\NukiDevicesResolver;
use App\Domains\HomeAutomation\Enums\HomeAutomationProviderEnum;
use App\Domains\HomeAutomation\Events\HomeAutomationDeviceDeletedEvent;
use App\Domains\HomeAutomation\Events\SmartlockAuthorisationDeletedEvent;
use App\Domains\HomeAutomation\Models\HomeAutomationAccount;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Domains\HomeAutomation\Models\SmartlockAuthorisation;
use App\Exceptions\NinjaAddContextException;
use App\Models\Booking;
use App\Models\Team;
use App\Models\User;
use Exception;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Str;

/** Class is readonly to avoid adding properties to the class, as this is a Singleton retrieved from the container */
readonly class NukiService implements HomeAutomationServiceInterface, SmartLocksServiceInterface
{
    public function __construct(
        private NukiApi $api = new NukiApi(),
    ) {
    }

    public function startConnection(Team $team): string
    {
        pnLog("[Nuki] Starting Nuki oauth for team $team->id.", $team);

        $baseUrl = $this->api->url;
        $clientId = urlencode(config('services.nuki.client_id'));
        $redirectUri = urlencode($team->config()->rnBackendTargetDomain(route(name: 'nukiOauth', absolute: false)));
        $scopes = urlencode('account smartlock smartlock.action smartlock.auth smartlock.config smartlock.log notification webhook.central');
        $state = urlencode("fghjklsd$team->id"); // Nuki requires minimum 8 characters long

        return $baseUrl."/oauth/authorize?response_type=code&client_id=$clientId&redirect_uri=$redirectUri&scope=$scopes&state=$state";
        /*
         * Note: to register Decentral Webhooks, register the scope: webhook.decentral (instead of webhook.central) above.
         * Then, make a PUT https://api.nuki.io/api/decentralWebhook/ with payload:
         * "webhookUrl": "https://didac-rn.eu.ngrok.io/home-automation/nuki/webhook"
         * "webhookFeatures": [list of webhooks]
         * The response will contain the webhook secret (but for local dev no need it).
         * Check the API pdf documentation for full details
         */
    }

    public function completeOauth(Team $team, string $code): ?HomeAutomationAccount
    {
        pnLog("[Nuki] Exchanging oauth code by token for team $team->id.", $team);

        $response = $this->api->completeOauth($team, $code);

        if (! $response->ok()) {
            throw new NinjaAddContextException(team: $team, message: 'Error while completing Nuki Oauth.');
        }

        $response = $response->collect();

        /** @var HomeAutomationAccount $account */
        $account = $team->homeAutomationAccounts()->create([ // I need to save it to the DB because to syncAccount we need id
            'provider' => HomeAutomationProviderEnum::NUKI,
            'access_token' => $response['access_token'],
            'refresh_token' => $response['refresh_token'],
            'token_expires_at' => now()->addSeconds($response['expires_in']),
        ]);

        // Sync the account to get the external_id which will be used to verify if the account was already created, and to notify Nuki
        try {
            $this->syncAccount($account);
        } catch (UniqueConstraintViolationException $e) {
            // We may find an exception of unique index when trying to save an external_id that already exists
            pnLog("[Nuki] Team $team->id is trying to connect an account that already exists.", $team);

            // Find out if the other account is active or deleted and we need to recover it.
            $externalAccountId = $this->api->getAccount($account)->collect()->get('accountId');
            $existingAccount = HomeAutomationAccount::query()
                ->withTrashed()
                ->where('external_id', $externalAccountId)
                ->where('provider', HomeAutomationProviderEnum::NUKI)
                ->first();

            if ($existingAccount && $existingAccount->trashed()) {
                pnLog("[Nuki] Team $team->id is trying to connect an account that was deleted. We restore it.", $team);
                $existingAccount->restore();
                $existingAccount->team_id = $team->id;
                $existingAccount->access_token = $account->access_token;
                $existingAccount->refresh_token = $account->refresh_token;
                $existingAccount->token_expires_at = $account->token_expires_at;
                $existingAccount->save();

                $account->forceDelete();
                $account = $existingAccount;
                $this->syncAccount($account);
            } else {
                pnLog("[Nuki] Team $team->id is trying to connect an account that is active.", $team);
                $account->forceDelete();

                return null;
            }
        }

        $account = $account->refresh();

        pnLog("[Nuki] Oauth completed for team $team->id.", $team);

        return $account;
    }

    public function syncAccount(HomeAutomationAccount $account): true
    {
        pnLog("[Nuki] Starting account $account->id sync.", team: $account->team_id);

        $data = $this->api->getAccount($account)->collect();

        return NukiAccountResolver::resolve($data, $account);
    }

    public function removeAccount(HomeAutomationAccount $account): bool
    {
        pnLog("[Nuki] Removing account $account->id.", team: $account->team_id);

        foreach ($account->homeAutomationDevices as $device) {
            $this->removeDevice($account, $device);
        }

        $result = $account->delete();

        pnLog("[Nuki] Account $account->id removed.", team: $account->team_id);

        InformNukiAboutSignupOrCancelationAction::dispatchIf(
            $result && ! in_array($account->team_id, config('ninja.testing_team_ids')),
            'Rental Ninja: Nuki account disconnected',
            "Hi!\n\nThis is the Rental Ninja team. Unfortunately, the account with id $account->external_id and name $account->name has been disconnected from Rental Ninja. Can you please remove that account and all it's devices from the list of connected accounts and our next invoice?\n\nThank you!\nBest regards,\nRental Ninja team",
        );

        return $result;
    }

    public function syncDevices(HomeAutomationAccount $account): true
    {
        pnLog("[Nuki] Starting devices sync for $account->id.", team: $account->team_id);

        $data = $this->api->getDevices($account)->collect();

        return NukiDevicesResolver::resolve($data, $account, false);
    }

    public function syncDevice(HomeAutomationAccount $account, string $externalId): true
    {
        pnLog("[Nuki] Starting device sync for account $account->id for device with external id: $externalId.", team: $account->team_id);

        $data = $this->api->getDevice($account, $externalId)->collect()->toArray();

        $data = collect([$data]);

        return NukiDevicesResolver::resolve($data, $account, true);
    }

    public function removeDevice(HomeAutomationAccount $account, HomeAutomationDevice $device, bool $fromApi = false): bool
    {
        pnLog("[Nuki] Removing device $device->id from account $account->id.", team: $account->team_id);

        if ($device->delete()) { // We delete the same way we do when a webhook tells us the device has been deleted
            event(new HomeAutomationDeviceDeletedEvent($account, $device->id, $device->type, $device->name));

            if ($fromApi) {
                $response = $this->api->removeDevice($account, $device->external_id);

                if (! $response->successful()) {
                    // Re-sync the account to get back the device
                    $this->syncDevices($account);
                }
            }
        }

        return true;
    }

    public function syncAuthorisations(HomeAutomationAccount $account)
    {
        pnLog("[Nuki] Starting authorisations sync for account $account->id.", team: $account->team_id);

        // Sync by device to avoid payload size issues. A device can have up to 200 authorisations (new dev gen) + could have more user authorisations
        // Also because Nuki auths have the "enabled" property, which we map with our soft deleted. Thus, in the resolver, we need to load deleted auths too.
        // Thus, we would need to load all auths for all devices at once if we don't sync just by device.
        foreach ($account->homeAutomationDevices as $device) {
            $data = $this->api->getDeviceAuthorisations($account, $device)->collect();

            return NukiAuthorisationResolver::resolve($data, $account, $device, false);
        }

        return true;
    }

    public function openDoor(HomeAutomationDevice $device): bool
    {
        pnLog("[Nuki] Opening door for device $device->id.", team: $device->team_id);

        return $this->api->openDoor($device)->successful();
    }

    public function lockDoor(HomeAutomationDevice $device): bool
    {
        pnLog("[Nuki] Locking door for device $device->id.", team: $device->team_id);

        return $this->api->lockDoor($device)->successful();
    }

    /** $email is only used to indicate to which email you wish to send the invitation code (in case you don't want to use the standard communication channel)
     * for the App authorisations only (email must be sent upon receiving webhook as we need the webhook's code to send the email).
     */
    public function createSmartLockBookingAccess(Booking $booking, Collection $devices, ?string $email = null): Collection
    {
        // Filter out devices not online, as auth creation will fail:
        $devices = $devices
            ->filter(fn (HomeAutomationDevice $device) => $device->provider === HomeAutomationProviderEnum::NUKI && $device->online);

        /** @var Collection<HomeAutomationDevice> $keyPadDevices */
        $keyPadDevices = $devices->filter(fn (HomeAutomationDevice $device) => $device->has_keypad);
        /** @var Collection<HomeAutomationDevice> $noKeyPadDevices */
        $noKeyPadDevices = $devices->filter(fn (HomeAutomationDevice $device) => ! $device->has_keypad);

        // Let's clean any authorisation from this booking which is not yet mapped to nuki.
        // If there is a problem when creating it in nuki, we will save them thus we won't allow the user to attempt again manually. We must delete them to let him try again.
        // We understand this is not triggered so often so we delete an authorisation who's webhook is pending.
        SmartlockAuthorisation::query()
         ->where('team_id', $booking->team_id)
         ->where('booking_id', $booking->id)
         ->whereNull('external_id')
         ->delete();

        $auths = collect();
        if ($keyPadDevices->isNotEmpty()) {
            pnLog("[Nuki] Creating codes for devices with id {$keyPadDevices->pluck('id')->toStringList()} for booking id $booking->id.", team: $booking->team_id);
            $auths = $auths->merge($this->createTemporaryKeypadCodes($keyPadDevices, $booking));
        }

        if ($noKeyPadDevices->isNotEmpty()) {
            pnLog("[Nuki] Granting user access for devices with id {$keyPadDevices->pluck('id')->toStringList()} for booking id $booking->id.", team: $booking->team_id);
            $auths = $auths->merge($this->createTemporaryUserAccesses($noKeyPadDevices, $booking, $email));
        }

        return $auths;
    }

    private function createTemporaryKeypadCodes(Collection $keyPadDevices, Booking $booking): Collection
    {
        $codes = collect();
        $keyPadDevices->groupBy('home_automation_account_id')->each(function (Collection $devices) use ($booking, &$codes) {
            $existingCodes = SmartlockAuthorisation::query()
               ->whereIn('home_automation_device_id', $devices->pluck('id'))
               ->whereNotNull('code')
               ->get();

            // Ensure we only generate new codes for devices without an Auth for that booking
            $bookingExistingCodes = $existingCodes->where('team_id', $booking->team_id)
                ->where('booking_id', $booking->id);

            $devices = $devices->whereNotIn('id', $bookingExistingCodes->pluck('home_automation_device_id')->unique());

            if ($devices->isNotEmpty()) {
                $name = $this->getBookingAuthName($booking, true); // Max 20 chars
                $code = $this->generateUniqueCode($existingCodes->whereIn('home_automation_device_id', $devices->pluck('id'))->pluck('code'));
                [$from, $until] = $booking->getSmartlockFromUntil($devices->first()); // We assume all devices will have the same timezone if they are mapped to the same rental

                // 1st create the auth in our DB: the webhook will match it + here we will be able to return the auth to the frontend.
                $newCodes = $devices->map(function (HomeAutomationDevice $device) use ($booking, $name, $code, $from, $until) {
                    $auth = new SmartlockAuthorisation();
                    $auth->team_id = $booking->team_id;
                    $auth->provider = HomeAutomationProviderEnum::NUKI;
                    $auth->home_automation_device_id = $device->id;
                    $auth->booking_id = $booking->id;
                    $auth->name = $name;
                    $auth->code = (string) $code;
                    $auth->from = $from;
                    $auth->until = $until;
                    $auth->save();

                    return $auth;
                });

                // 2nd api call
                /** @var Response $response */
                $response = rescue(fn () => $this->api->createKeypadCode(
                    account: $devices->first()->homeAutomationAccount,
                    name: $name,
                    code: $code,
                    externalIds: $devices->pluck('external_id'),
                    fromDate: $from,
                    untilDate: $until,
                )); // Response is empty. Should be handled from webhook.
                // 3rd If the device is offline, the response is exactly the same: empty. But no webhook is received.
                // I think whenever is back online we will receive the webhook and we will be able to map the auth,
                // and if not, we delete the ones not sync with the provider within the next command run of the generation of auths

                if (! $response || ! $response->successful()) {
                    $newCodes->each(fn (SmartlockAuthorisation $auth) => $auth->delete());

                    return; // We want to continue iterating but stop here in this item
                }

                $codes = $codes->merge($newCodes);
            }
        });

        return $codes;
    }

    /** $email is only used to indicate to which email you wish to send the invitation code (in case you don't want to use the standard communication channel)
     * for the App authorisations only (email must be sent upon receiving webhook as we need the webhook's code to send the email).
     */
    private function createTemporaryUserAccesses(Collection $noKeyPadDevices, Booking $booking, ?string $email): Collection
    {
        $auths = collect();
        $noKeyPadDevices->groupBy('home_automation_account_id')->each(function (Collection $devices) use ($booking, $email, &$auths) {
            $account = $devices->first()->homeAutomationAccount;

            // Split between devices with no auth for this booking and the ones they have, so we just need to re-send the invitation code
            $existingAuths = $booking->smartlockAuthorisations
                ->whereIn('home_automation_device_id', $devices->pluck('id'));

            $devicesWithoutAuth = $devices->filter(fn (HomeAutomationDevice $device) => $existingAuths->pluck('home_automation_device_id')->unique()->doesntContain($device->id));
            if ($devicesWithoutAuth->isNotEmpty()) {
                $user = $this->getNukiUser($account, $booking);

                // Save first to the database:
                $name = $this->getBookingAuthName($booking); // Max 32 chars
                $nukiUserId = (string) $user['accountUserId'];
                [$from, $until] = $booking->getSmartlockFromUntil($devicesWithoutAuth->first()); // We assume all devices will have the same timezone if they are mapped to the same rental

                $newAuths = $devicesWithoutAuth->map(function (HomeAutomationDevice $device) use ($booking, $name, $nukiUserId, $from, $until, $email) {
                    $auth = new SmartlockAuthorisation();
                    $auth->team_id = $booking->team_id;
                    $auth->provider = HomeAutomationProviderEnum::NUKI;
                    $auth->home_automation_device_id = $device->id;
                    $auth->booking_id = $booking->id;
                    $auth->name = $name;
                    $auth->external_user_id = $nukiUserId;
                    $auth->from = $from;
                    $auth->until = $until;
                    $auth->metadata = ['request_id' => '']; // We need this otherwise the update query below doesn't work
                    if ($email) {
                        $auth->metadata = array_merge($auth->metadata, ['email' => $email]);
                    }
                    $auth->save();

                    return $auth;
                });

                // Make API call
                $response = rescue(fn () => $this->api->createUserAuthorisation(
                    account: $account,
                    name: $name,
                    nukiUserId: $nukiUserId,
                    externalIds: $devicesWithoutAuth->pluck('external_id'),
                    fromDate: $from,
                    untilDate: $until,
                ));

                if (! $response || ! $response->successful()) {
                    $newAuths->each(fn (SmartlockAuthorisation $auth) => $auth->delete());

                    return; // We want to continue iterating but stop here in this item
                }

                SmartlockAuthorisation::query()
                    ->whereIn('id', $newAuths->pluck('id'))
                    ->update(['metadata->request_id' => $response->collect()['requestId']]); // Important: we can't retreive and save later as we could have a race condition with the webhook

                $auths = $auths->merge($newAuths);
            }
        });

        return $auths;
    }

    private function getNukiUser(HomeAutomationAccount $account, Booking $booking): array
    {
        // Check first if we find a user with booking's email:
        // Note 1: email is not important because Nuki won't send any email to this address, it will be us, nor the user doesn't need to create an account with this email
        $domain = Str::of($booking->team->config()->rnAppName())->lower()->replace(' ', '-')->replace(['@', '.', '?'], '')->toString();
        $reference = strtolower($booking->reference); // Nuki doesn't like capital letters
        $userEmail = "$reference@$domain.com";
        // Note 2: I just want a unique email address per booking. Nuki sends an email here only when access is revoked, but we don't want that.

        $user = $this->api->findUser($account, $userEmail)->collect()->first(); // Array

        // Create a Nuki user if it doesn't exist
        if (! $user) {
            $name = "Booking $booking->reference";
            $user = $this->api->createUser($account, $userEmail, $name, $booking->client->getLocale())
                ->collect()
                ->toArray(); // Note: This doesn't send any email to the user
        }

        return $user;
    }

    private function getBookingAuthName(Booking $booking, bool $short = false): string
    {
        if ($short) {
            $ci = $booking->getCarbonCheckInTime();
            $co = $booking->getCarbonCheckOutTime();

            return 'Bk:'.$ci->month.'/'.$ci->day.'->'.$co->month.'/'.$co->day; // Max 20 chars
        } else {
            return 'Bk:'.$booking->getCheckInDay().'->'.$booking->getCheckOutDay(); // Max 32 chars
        }
    }

    public function updateAuthorisationAccessTimes(HomeAutomationDevice $device, SmartlockAuthorisation $auth, Carbon $from, Carbon $until, ?Booking $booking = null): ?SmartlockAuthorisation
    {
        pnLog("[Nuki] Updating authorisation with id $auth->id from device $device->id to new times.", team: $device->team_id);

        if ($booking && $auth->isBookingAuth()) {
            $auth->name = $this->getBookingAuthName($booking, ! $auth->isAppAuth());
        }

        $auth->from = $from;
        $auth->until = $until;
        $response = $this->api->modifyAuthorisationFromUntil($device->homeAutomationAccount, $device, $auth);

        if ($response->successful()) {
            $auth->save();
        } else {
            return null;
        }

        return $auth;
    }

    public function createSmartLockPermanentAccess(HomeAutomationDevice $device, User $user, string $name, Carbon $from, ?Carbon $until = null, ?string $code = null): ?SmartlockAuthorisation
    {
        pnLog("[Nuki] Creating permanent authorisation for device $device->id for user with id $user->id.", team: $device->team_id);

        if (Str::length($name) > 20) {
            throw new Exception('Trying to generate an authorisation with a name longer than 20 characters, which is the maximum');
        }

        $from = $from->shiftTimezone($device->timezone) // We understand the time given by the PM is located in the timezone of the device
        ->setTimezone('UTC'); // We need to provide the time in UTC
        $until = transform($until, fn ($until) => $until->shiftTimezone($device->timezone)->setTimezone('UTC'));
        $account = $device->homeAutomationAccount;

        $auth = new SmartlockAuthorisation();
        $auth->team_id = $device->team_id;
        $auth->provider = HomeAutomationProviderEnum::NUKI;
        $auth->home_automation_device_id = $device->id;
        $auth->name = $name;
        $auth->from = $from;
        $auth->until = $until;
        $auth->user_id = $user->id;

        if ($device->has_keypad) {
            if (! $code) {
                $existingCodes = $device->smartlockAuthorisations
                    ->whereNotNull('code')
                    ->pluck('code');
                $code = $this->generateUniqueCode($existingCodes);
            }
            $auth->code = $code;
        } else {
            $externalUser = $this->api->findUser($account, $user->email)->collect()->first(); // Array
            if (! $externalUser) {
                $externalUser = $this->api->createUser($account, $user->email, $user->name, $user->short_locale)
                    ->collect()
                    ->toArray(); // Note: This doesn't send any email to the user
            }
            $auth->external_user_id = (string) $externalUser['accountUserId'];
            $auth->metadata = ['request_id' => '']; // We need this otherwise the update query below doesn't work
        }

        $auth->save();

        if ($device->has_keypad) {
            $response = rescue(fn () => $this->api->createKeypadCode(
                account: $account,
                name: $name,
                code: $code,
                externalIds: collect([$device->external_id]),
                fromDate: $from,
                untilDate: $until,
            )); // Response is empty
        } else {
            $response = rescue(fn () => $this->api->createUserAuthorisation(
                account: $account,
                name: $name,
                nukiUserId: $auth->external_user_id,
                externalIds: collect([$device->external_id]),
                fromDate: $from,
                untilDate: $until,
            ));
        }

        if (! $response || ! $response->successful()) {
            $auth->delete();

            return null;
        }

        if (! $device->has_keypad) {
            SmartlockAuthorisation::query()
                ->where('id', $auth->id)
                ->update(['metadata->request_id' => $response->collect()['requestId']]); // Important: we can't retreive and save later as we could have a race condition with the webhook
        }

        return $auth;
    }

    /** Nuki rules for codes are:
     * - 6 numeric digits
     * - must not contain 0
     * - must not start by 12
     * - must be unique by device.
     *
     * @param  Collection<string>  $existingCodes
     */
    private function generateUniqueCode(Collection $existingCodes): int
    {
        do {
            $code = Str::of(mt_rand(100000, 999999))
                   ->replace('0', (string) rand(1, 9));
        } while ($code->startsWith('12') || $existingCodes->contains($code->toString()));

        return (int) $code->toString();
    }

    public function validatePermanentCode(string $name, ?string $code): ?string
    {
        if (Str::length($name) > 20) {
            return "Name is too long, can't contain more than 20 characters.";
        }

        if ($code && ! $this->validCode($code)) {
            return __('messages.home_automation.devices.errors.nuki.invalid_code');
        }

        return null;
    }

    private function validCode(string $code): bool
    {
        $code = Str::of($code);

        return ! $code->startsWith('12') && ! $code->contains('0') && $code->length() === 6 && ! $code->contains('.') && is_numeric($code->toString());
    }

    public function canGeneratePermanentCodes(): bool
    {
        return true;
    }

    public function removeAuthorisation(HomeAutomationAccount $account, HomeAutomationDevice $device, SmartlockAuthorisation $authorisation): bool
    {
        // TODO: should we first try to ping the device to see if is online and only try if is so? Otherwise, if booking is postponed, auths are not deleted in RN
        pnLog("[Nuki] Removing authorisation with id $authorisation->id of device $device->id from account $account->id.", team: $account->team_id);

        $result = $this->api->removeAuthorisation($account, $device, $authorisation);
        // If the device is offline, we will receive a 200 and Nuki should try to remove the Auth within the next 24 hours, so continue to delete it anyway

        if ($result) {
            // If the device is offline, do not proceed. Deleted authorisations won't be picked by the ManageBookingAuthorisationsAction, thus won't be removed when back online.
            sleep(2);
            $device->sync();
            if (! $device->refresh()->online) {
                return false;
            }

            $externalUserId = $authorisation->external_user_id;
            $authorisation->delete();
            event(new SmartlockAuthorisationDeletedEvent($authorisation->id, $authorisation->home_automation_device_id, $authorisation->team_id, $authorisation->name, $authorisation->provider, $authorisation->booking_id));

            // If is a user authorization, in case there are no more authorisations for this user, remove it as well
            if ($externalUserId) {
                $accountDevices = $account->homeAutomationDevices()->select('id')->pluck('id');
                $leftAuths = SmartlockAuthorisation::query()
                    ->whereIn('home_automation_device_id', $accountDevices)
                    ->where('external_user_id', $externalUserId)
                    ->count();

                if ($leftAuths === 0) {
                    // Check Nuki doesn't have any other type of Auth for this user (we just sync some types of auths):
                    if ($this->api->getAuthorisations($account, $externalUserId)->collect()->count() === 0) {
                        $this->api->removeUser($account, $externalUserId);
                    }
                }
            }
        }

        return true;
    }
}
