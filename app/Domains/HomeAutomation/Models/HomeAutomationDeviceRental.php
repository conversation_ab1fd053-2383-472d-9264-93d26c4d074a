<?php

namespace App\Domains\HomeAutomation\Models;

use App\Models\NinjaProviderModel;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Domains\HomeAutomation\Models\HomeAutomationDeviceRental.
 *
 * @property int $id
 * @property int $team_id
 * @property int $home_automation_device_id
 * @property int $rental_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Domains\HomeAutomation\Models\HomeAutomationDevice $homeAutomationDevice
 * @property-read Rental $rental
 * @property-read Team $team
 *
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental query()
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental whereHomeAutomationDeviceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental whereRentalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|HomeAutomationDeviceRental withoutTrashed()
 *
 * @mixin \Eloquent
 */
class HomeAutomationDeviceRental extends NinjaProviderModel
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'home_automation_device_rental';

    protected $fillable = [
        'team_id',
        'rental_id',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id']);
    }

    public function homeAutomationDevice(): BelongsTo
    {
        return $this->belongsTo(HomeAutomationDevice::class);
    }
}
