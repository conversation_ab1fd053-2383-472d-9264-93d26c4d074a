<?php

namespace App\Domains\HomeAutomation\Models;

use App\Domains\HomeAutomation\Enums\HomeAutomationProviderEnum;
use App\Domains\HomeAutomation\Services\HomeAutomationServiceInterface;
use App\Models\Booking;
use App\Models\NinjaProviderModel;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * App\Domains\HomeAutomation\Models\SmartlockAuthorisation.
 *
 * @property int $id
 * @property int $team_id
 * @property HomeAutomationProviderEnum $provider nuki, igloohome,...
 * @property string|null $external_id
 * @property int $home_automation_device_id
 * @property int|null $booking_id If null, should be a permanent code.
 * @property string $name
 * @property string|null $code
 * @property string|null $external_user_id In case you can open the door via a provider App, the authorised user.
 * @property int|null $user_id
 * @property Carbon|null $from
 * @property Carbon|null $until
 * @property bool $emailed
 * @property array|null $metadata
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read Booking|null $booking
 * @property-read \App\Domains\HomeAutomation\Models\HomeAutomationDevice $homeAutomationDevice
 * @property-read Team $team
 * @property-read User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation query()
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereEmailed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereExternalUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereHomeAutomationDeviceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereUntil($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|SmartlockAuthorisation withoutTrashed()
 *
 * @mixin \Eloquent
 */
class SmartlockAuthorisation extends NinjaProviderModel
{
    use SoftDeletes;

    protected $fillable = [
    ];

    protected $casts = [
        'provider' => HomeAutomationProviderEnum::class,
        'from' => 'datetime',
        'until' => 'datetime',
        'emailed' => 'boolean',
        'metadata' => 'array',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function homeAutomationDevice(): BelongsTo
    {
        return $this->belongsTo(HomeAutomationDevice::class);
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class, ['booking_id', 'team_id'], ['id', 'team_id'])->withoutGlobalScopes();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function getService(): HomeAutomationServiceInterface
    {
        return $this->provider->getService();
    }

    public function isAppAuth(): bool
    {
        return $this->external_user_id !== null;
    }

    public function isBookingAuth(): bool
    {
        return $this->booking_id !== null;
    }

    public function getLocalFrom(): Carbon
    {
        return $this->from->setTimezone($this->homeAutomationDevice->timezone);
    }

    public function getLocalUntil(): Carbon
    {
        return $this->until->setTimezone($this->homeAutomationDevice->timezone);
    }

    public function getDeviceName(): ?string
    {
        // Find the device name if any. If the auth is "fake" thus, Rental level door_code or Booking level, use rental's door_public_name
        return $this->homeAutomationDevice?->public_name ?? $this->booking?->rental->door_public_name; // public_name will already have as fallback the device name
    }
}
