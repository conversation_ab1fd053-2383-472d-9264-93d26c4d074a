<?php

namespace App\Domains\HomeAutomation\Enums;

use App;
use App\Domains\HomeAutomation\Actions\Nuki\SyncNukiAccountAction;
use App\Domains\HomeAutomation\Models\HomeAutomationAccount;
use App\Domains\HomeAutomation\Services\HomeAutomationServiceInterface;
use App\Domains\HomeAutomation\Services\NukiService;
use App\Domains\HomeAutomation\Services\SmartLocksServiceInterface;
use App\DTO\Team\PriceData;
use App\DTO\Team\ProductData;
use Spatie\LaravelData\DataCollection;

enum HomeAutomationProviderEnum: string
{
    case NUKI = 'nuki';
    //case IGLOOHOME = 'igloohome';

    public function getService(): HomeAutomationServiceInterface|SmartLocksServiceInterface
    {
        return match ($this) {
            self::NUKI => App::make(NukiService::class),
            //self::IGLOOHOME => App::make(IgloohomeService::class),
        };
    }

    public function allowsAccountNameModification(): bool
    {
        return match ($this) {
            self::NUKI => false, // We sync it
            //self::IGLOOHOME => ,
        };
    }

    public function dispatchSync(HomeAutomationAccount $account): bool
    {
        switch ($this) {
            case self::NUKI:
                SyncNukiAccountAction::dispatch($account);

                return true;
                break;
                /*case self::IGLOOHOME:
                    SyncIgloohomeAccountAction::dispatch($account);
                    return true;
                    break;*/
            default:
                return false;
                break;
        }
    }

    public function getBillableProduct(): ProductData
    {
        return match ($this) {
            self::NUKI => ProductData::findProduct('nuki'),
            //self::IGLOOHOME => ProductData::findProduct(),
        };
    }

    public function getExcludedBillableExternalTypes(): array
    {
        return match ($this) {
            self::NUKI => ['1', '2', '3'],
            //self::IGLOOHOME => [],
        };
    }

    /**
     * @return DataCollection<PriceData>
     */
    public static function getPricing(?self $provider = null): DataCollection
    {
        $priceIds = collect();

        if ($provider) {
            $product = ProductData::findProduct($provider->value);

            $priceIds = $priceIds->merge($product->yearlyPrices->where('default', true)->pluck('id')
                ->merge($product->monthlyPrices->where('default', true)->pluck('id')));
        } else {
            $providers = collect(self::cases())->map(fn (self $case) => $case->value);

            foreach ($providers as $provider) {
                $product = ProductData::findProduct($provider);

                $priceIds = $priceIds->merge($product->yearlyPrices->where('default', true)->pluck('id')
                    ->merge($product->monthlyPrices->where('default', true)->pluck('id')));
            }
        }

        return PriceData::allPrices()->filter(fn (PriceData $price) => $priceIds->contains($price->id));
    }

    public function getAppDownloadUrl(?string $code = null): string
    {
        return match ($this) {
            self::NUKI => "https://nuki.io/en-uk/invite/$code",
        };
    }

    public function getExternalTypeName(string $externalType): string
    {
        switch ($this) {
            case self::NUKI:
                return match ($externalType) {
                    '0' => 'Keyturner',
                    '1' => 'Box',
                    '2' => 'Opener',
                    '3' => 'Smartdoor',
                    '4' => 'Smartlock',
                };
                break;
        }
    }
}
