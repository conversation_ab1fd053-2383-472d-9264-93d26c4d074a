<?php

namespace App\Domains\Hostboost\Actions;

use App\Models\SetupAirbnbHost;
use App\Models\SetupRental;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class FetchScrapeAction
{
    use AsAction;

    public const SIGNATURE = 'ninja:fetch-scrap-for-setup-rentals';

    public string $commandSignature = 'ninja:fetch-scrap-for-setup-rentals {sRentals?}';

    public function asCommand(Command $command): int
    {
        $sRentalId = $command->argument('sRentals');
        SetupRental::query()
            ->when($sRentalId, fn ($q) => $q->where('id', $sRentalId))
            ->whereNotNull('scrape_id')
            ->whereNotIn('scrape_status', ['completed', 'failed'])
            ->get()
            ->each(fn (SetupRental $sRental) => $this->handle($sRental));

        return $command::SUCCESS;
    }

    public function handle(SetupRental $sRental): void
    {
        if (! $sRental->locked) {
            $sRental->scrape_id = null;
            $sRental->scrape_status = null;
            $sRental->save();
        }

        $response = GetScrapeFileAction::run($sRental);

        $status = data_get($response, 'status');
        $sRental->scrape_status = $status;

        if ($status != 'completed') {
            $sRental->save();

            return;
        }
        $setupCompany = $sRental->setupCompany;

        $completedAt = data_get($response, 'completed_at') ?? Carbon::now();
        $sRental->fetched = true;
        $sRental->fetched_at ??= $completedAt;
        $sRental->last_fetched_at = $completedAt;
        $sRental->airbnb_name = data_get($response, 'public_name');

        $hosts = array_merge(
            [data_get($response, 'host')],
            data_get($response, 'co_hosts', [])
        );

        $hostIds = [];
        // Ensure hosts are in the setup
        foreach ($hosts as $host) {
            /** @var SetupAirbnbHost $host */
            $host = $setupCompany->setupAirbnbHosts()->updateOrCreate([
                'name' => $host['name'],
                'airbnb_user_id' => $host['airbnb_user_id'],
            ]);
            $hostIds[] = $host->id;
        }
        $sRental->airbnb_hosts = $hostIds;
        if (count($hostIds) == 1) {
            $sRental->airbnb_owner = $hostIds[0];
        }

        $sRental->save();
    }
}
