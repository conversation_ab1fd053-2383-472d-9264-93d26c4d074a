<?php

namespace App\Domains\ManageBookings\Data;

use App\Domains\ManageBookings\Actions\CheckAvailabilityForBookingAction;
use App\Models\Rental;
use App\Models\RentalDailyDetails;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Transformers\DateTimeInterfaceTransformer;

class DatesBookingFormData extends Data
{
    public function __construct(
        // Configuration
        public bool $visible = false,
        public bool $editable = true,

        public int $fieldId = 1,
        public bool $showAllDates = false,
        // Fields/Validation
        public ?array $availableDates = [],
        public ?array $checkInDays = [],
        public ?array $checkOutDays = [],
        // Values
        #[WithTransformer(DateTimeInterfaceTransformer::class, format: 'Y-m-d')]
        public ?Carbon $originalCheckInDate = null,
        #[WithTransformer(DateTimeInterfaceTransformer::class, format: 'Y-m-d')]
        public ?Carbon $originalCheckOutDate = null,
        #[WithTransformer(DateTimeInterfaceTransformer::class, format: 'Y-m-d')]
        public ?Carbon $currentCheckInDate = null,
        #[WithTransformer(DateTimeInterfaceTransformer::class, format: 'Y-m-d')]
        public ?Carbon $currentCheckOutDate = null,
        public ?Carbon $newCheckInDate = null,
        public ?Carbon $newCheckOutDate = null,
        // Actions
        public bool $reset = false,
        // Errors
        public ?array $errors = [],
    ) {
    }

    public function isUpdated(): bool
    {
        return
            $this->reset ||
            ! is_null($this->newCheckInDate) && (! $this->currentCheckInDate?->isSameDay($this->newCheckInDate)) ||
            ! is_null($this->newCheckOutDate) && (! $this->currentCheckOutDate?->isSameDay($this->newCheckOutDate));
    }

    public function checkInDate(): ?Carbon
    {
        return $this->reset ? null : $this->newCheckInDate ?? $this->currentCheckInDate;
    }

    public function checkOutDate(): ?Carbon
    {
        return  $this->reset ? null : $this->newCheckOutDate ?? $this->currentCheckOutDate;
    }

    public function length(): int
    {
        return $this->checkInDate()->diffInDays($this->checkOutDate()->startOfDay());
    }

    public function refreshDatesForm(Rental $rental, BookingFormData $form): void
    {
        $isBlock = $form->type->currentValue?->isUnavailable() ?? false;

        $this->availableDates = $rental->dailyDetails()
            ->whereIn('booked', [RentalDailyDetails::EMPTY, RentalDailyDetails::PREPARATION])
            ->where('date', '>=', Carbon::today()->subMonth())
            ->pluck('date')
            ->map(fn ($date) => $date->toDateString())
            ->when($form->dates->originalCheckInDate && $form->dates->originalCheckOutDate,
                fn (Collection $dates) => $dates->merge(
                    collect($form->dates->originalCheckInDate->toPeriod($form->dates->originalCheckOutDate))
                        ->map(fn (Carbon $date) => $date->toDateString())
                )->sort()
            )
            ->toArray();

        $this->fieldId += 1;
        $this->showAllDates = false;

        $startAt = $this->checkInDate();
        $endAt = $this->checkOutDate();

        if ($endAt) {
            // If the booking exists, we must omit those dates
            $lastDateToCheck = $this->originalCheckInDate ?? $endAt;
            $lastDateToCheck = $lastDateToCheck->copy()->startOfDay();
            /** @var RentalDailyDetails $firstUnavailable */
            $firstUnavailable = $rental->dailyDetails()
                ->where('date', '<', $lastDateToCheck)
                ->whereIn('booked', [RentalDailyDetails::BOOKED, RentalDailyDetails::BLOCK, RentalDailyDetails::OVERBOOKING])
                ->orderByDesc('date')
                ->first();
            $from = $firstUnavailable?->date->copy()->addDay();

            $this->checkInDays = [
                'from' => $from?->toDateString(),
                'to' => $endAt->copy()->subDay()->toDateString(),
            ];
        } else {
            $this->checkInDays = null;
        }
        if ($startAt) {
            // If the booking exists, we must omit those dates
            $firstDateToCheck = $this->originalCheckOutDate ?? $startAt;

            /** @var RentalDailyDetails $lastUnavailable */
            $lastUnavailable = $rental->dailyDetails()
                ->where('date', '>=', $firstDateToCheck)
                ->whereIn('booked', [RentalDailyDetails::BOOKED, RentalDailyDetails::BLOCK, RentalDailyDetails::OVERBOOKING])
                ->orderBy('date')
                ->first();
            $firstCheckOutOption = $startAt->copy()->addDay();
            $lastCheckOutOption = $lastUnavailable?->date->copy() ?? Carbon::now()->addMonths($rental->bookable_months);
            if ($lastCheckOutOption->isBefore($firstCheckOutOption)) {
                $this->checkOutDays = [];
            } else {
                $this->checkOutDays = [
                    'from' => $firstCheckOutOption->toDateString(),
                    'to' => $lastCheckOutOption->toDateString(),
                ];
            }
        } else {
            $this->checkOutDays = null;
        }

        if ($endAt && $startAt && $endAt->isAfter($startAt) && ! $isBlock) {
            // Availability check
            $check = CheckAvailabilityForBookingAction::run($rental, $startAt, $endAt, $this->originalCheckInDate, $this->originalCheckOutDate);

            $this->errors = $check->getErrorsArray();
        } else {
            $this->errors = [];
        }
    }
}
