<?php

namespace App\Domains\ManageBookings\Data;

use App\Models\Rental;
use App\Models\RentalDailyDetails;
use App\Models\Team;
use App\Models\User;
use App\Query\RentalQuery;
use Illuminate\Database\Eloquent\Builder;
use Spatie\LaravelData\Data;

class RentalBookingFormData extends Data
{
    public function __construct(
        // Configuration
        public bool $visible = false,
        public bool $editable = true,
        public int $fieldId = 1,
        // Fields/Validation
        public ?array $options = [],
        public bool $sameLocation = false,
        // Values
        public ?int $originalValue = null,
        public ?int $currentValue = null,
        public ?int $newValue = null,
    ) {
    }

    public function isUpdated(): bool
    {
        return ! is_null($this->newValue) && $this->newValue !== $this->currentValue;
    }

    public function getRentalId(): ?int
    {
        return $this->newValue ?? $this->currentValue;
    }

    public function refreshRentalForm(Team $team, BookingFormData $form, User $user): void
    {
        $numGuests = $form->guestCount->numGuests();
        $startAt = $form->dates->checkInDate();
        $endAt = $form->dates->checkOutDate();
        $originalRental = $this->originalValue ? Rental::getRentalModel($team, $this->originalValue) : null;

        $filterByLocation = null;
        if ($this->sameLocation && $originalRental) {
            $filterByLocation = $originalRental->city;
        }

        $userRental = $user->getRentalsForUser();

        $this->options = $team->teamRentals()
            ->whereIn('id', $userRental)
            ->when(! is_null($numGuests),
                fn (Builder $query) => $query->where('sleeps_max', '>=', $numGuests)
            )
            // By availability
            ->where(fn (Builder $query) => $query
                ->when($startAt && $endAt, fn (Builder $query) => $query
                    ->whereDoesntHave('dailyDetails', function ($query) use ($startAt, $endAt) {
                        $query
                            ->where('date', '>=', $startAt)
                            ->where('date', '<', $endAt)
                            ->whereIn('booked', [RentalDailyDetails::BOOKED, RentalDailyDetails::OVERBOOKING, RentalDailyDetails::BLOCK]);
                    }))
                ->when($this->originalValue, fn (RentalQuery $q) => $q->orWhere('id', '=', $this->originalValue))
            )
            ->when(! $form->type->currentValue->isUnavailable(), fn (RentalQuery $q) => $q->where('is_rental_ninja', true))
            ->when($originalRental, fn (RentalQuery $q) => $q->where('provider_id', $originalRental->provider_id))

            ->when($filterByLocation, fn (RentalQuery $q) => $q->where('city', $filterByLocation))
            ->get(['name', 'id'])
            ->map(fn (Rental $r) => ['id' => $r->id, 'name' => $r->name])
            ->sortBy('name')

            ->values()
            ->toArray();
        $this->fieldId += 1;
        $this->visible = true;
    }
}
