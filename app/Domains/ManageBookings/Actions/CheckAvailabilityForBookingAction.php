<?php

namespace App\Domains\ManageBookings\Actions;

use App\Domains\ManageBookings\Data\AvailabilityCheckData;
use App\Models\Rental;
use App\Models\RentalDailyDetails;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

/**
 * Class StoreBookingAction.
 *
 * @throw NinjaBookingException
 */
class CheckAvailabilityForBookingAction
{
    use AsAction;

    public function handle(Rental $rental, Carbon $checkIn, Carbon $checkOut, ?Carbon $currentCheckIn = null, ?Carbon $currentCheckOut = null): AvailabilityCheckData
    {
        $check = AvailabilityCheckData::create();
        // Availability check
        $bookingLength = $checkOut->diffInDays($checkIn->copy()->startOfDay());
        $period = $rental->getDetailsForPeriod($checkIn, $checkOut);
        $bookingPeriod = $period->slice(0, $bookingLength);
        /** @var RentalDailyDetails $checkInDay */
        $checkInDay = $period->first();
        /** @var RentalDailyDetails $checkOutDay */
        $checkOutDay = $period->last();
        // Format current check-in and check-out dates
        $currentCheckOut = $currentCheckOut?->copy()?->subDay()?->startOfDay();
        $currentCheckIn = $currentCheckIn?->copy()?->startOfDay();

        if ($bookingLength < $checkInDay->min_stay) {
            $check->setMinStayError();
        }
        if (! is_null($rental->max_stay) && $bookingLength > $rental->max_stay) {
            $check->setMaxStayError();
        }
        if (! $checkInDay->changeover->canCheckIn()) {
            $check->setChangeoverInError();
        }
        if (! $checkOutDay->changeover->canCheckOut()) {
            $check->setChangeoverOutError();
        }
        $updatedPeriod = $bookingPeriod->when($currentCheckIn && $currentCheckOut,
            fn (Collection $period) => $period->filter(
                fn (RentalDailyDetails $item) => ! $item->date->between($currentCheckIn, $currentCheckOut)
            ));
        if ($updatedPeriod->contains(fn ($item) => in_array($item['booked'], [RentalDailyDetails::BOOKED, RentalDailyDetails::OVERBOOKING, RentalDailyDetails::BLOCK]))) {
            $check->setBookedError();
        }
        if ($updatedPeriod->contains('booked', RentalDailyDetails::PREPARATION)) {
            // TODO: Edit booking that overlaps its own preparation
            $check->setPreparationError();
        }
        if ($checkOutDay->date->isPast()) {
            $check->setPastError();
        }

        return $check;
    }
}
