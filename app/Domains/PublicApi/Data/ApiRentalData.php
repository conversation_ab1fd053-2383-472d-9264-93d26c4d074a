<?php

namespace App\Domains\PublicApi\Data;

use App\Enum\PricingModelEnum;
use App\Models\Rental;
use App\Transformers\DoNotWrapCollectionTransformer;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

class ApiRentalData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public string $currency,
        public int $min_price,
        public int $max_price,
        public int $min_stay,
        public int $available_window,
        public string $pricing_model,
        #[WithTransformer(DoNotWrapCollectionTransformer::class)]
        #[DataCollectionOf(ApiRentalFeeData::class)]
        public DataCollection $fees,
    ) {
    }

    public static function fromModel(Rental $rental): self
    {
        return new self(
            id: $rental->id,
            name: $rental->name,
            currency: $rental->currency,
            min_price: $rental->min_price,
            max_price: $rental->max_price,
            min_stay: $rental->min_stay,
            available_window: $rental->bookable_months,
            pricing_model: self::getPricingModelPublicName($rental->pricing_model),
            fees: ApiRentalFeeData::collection($rental->rentalFees),
        );
    }

    // This cab be moved to a caster/transformer is used in multiple places
    private static function getPricingModelPublicName(PricingModelEnum $pricingModel): string
    {
        return match ($pricingModel) {
            PricingModelEnum::season => 'season',
            PricingModelEnum::external => 'external',
            PricingModelEnum::smartPricing => 'smart_pricing',
            PricingModelEnum::api => 'api',
            PricingModelEnum::manual => 'manual',
            default => 'other',
        };
    }
}
