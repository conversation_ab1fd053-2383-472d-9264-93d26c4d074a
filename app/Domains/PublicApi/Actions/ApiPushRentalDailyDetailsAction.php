<?php

namespace App\Domains\PublicApi\Actions;

use App\Actions\Rentals\DailyDetails\Support\RentalDailyDetailsBatchUpdater;
use App\Domains\PublicApi\Data\ApiRentalDailyData;
use App\Enum\PricingModelEnum;
use App\Events\RentalPricingModelChangedEvent;
use App\Models\Rental;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsController;

class ApiPushRentalDailyDetailsAction
{
    use AsController;

    public function asController(Team $team, Rental $teamRental, ActionRequest $request): \Illuminate\Http\JsonResponse
    {
        if ($teamRental->pricing_model != PricingModelEnum::api) {
            $teamRental->pricing_model = PricingModelEnum::api;
            $teamRental->save();
            event(new RentalPricingModelChangedEvent($team, $teamRental->id, data: ['pricing_model' => PricingModelEnum::api]));
        }

        $data = ApiRentalDailyData::collection($request->input('data'));

        $batch = RentalDailyDetailsBatchUpdater::forApi($teamRental);
        $data->toCollection()->each(
            fn (ApiRentalDailyData $dailyData) => $batch->addApi($dailyData)
        );
        $batch->save();

        return response()->json(['message' => 'Rental Daily Details Updated']);
    }
}
