<?php

namespace App\Domains\PublicApi\Actions;

use App\Domains\PublicApi\Data\ApiSlimRentalData;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsController;
use Spatie\LaravelData\DataCollection;

class ApiGetRentalsAction
{
    use AsController;

    public function asController(Team $team, ActionRequest $request): DataCollection
    {
        return  ApiSlimRentalData::collection($team->teamRentals);
    }
}
