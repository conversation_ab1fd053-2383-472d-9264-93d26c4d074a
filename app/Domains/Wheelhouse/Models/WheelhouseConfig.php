<?php

namespace App\Domains\Wheelhouse\Models;

use App\Domains\Wheelhouse\Data\WhConfigData;
use App\Domains\Wheelhouse\Enums\WhPreferencesEnum;
use App\Models\NinjaProviderModel;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Domains\Wheelhouse\Models\WheelhouseConfig.
 *
 * @property int $id
 * @property int $team_id
 * @property int $rental_id
 * @property bool $active
 * @property int|null $wh_manual_base_rate
 * @property WhPreferencesEnum $wh_base_price_method
 * @property WhPreferencesEnum $wh_weekend_method
 * @property WhPreferencesEnum $wh_seasonality_method
 * @property WhPreferencesEnum $wh_last_minute_method
 * @property WhPreferencesEnum $wh_far_future_method
 * @property int|null $wh_far_future_days
 * @property float|null $wh_far_future_percent
 * @property int|null $wh_far_future_min_stay
 * @property float|null $wh_internal_markup
 * @property int|null $wh_recommended_base_rate
 * @property int|null $wh_aggressive_base_rate
 * @property int|null $wh_conservative_base_rate
 * @property int $wh_location_available
 * @property int $wh_successful
 * @property Carbon|null $wh_last_push
 * @property Carbon|null $wh_last_sync
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Rental $rental
 * @property-read Team $team
 *
 * @method static Builder|WheelhouseConfig newModelQuery()
 * @method static Builder|WheelhouseConfig newQuery()
 * @method static Builder|WheelhouseConfig query()
 * @method static Builder|WheelhouseConfig whereActive($value)
 * @method static Builder|WheelhouseConfig whereCreatedAt($value)
 * @method static Builder|WheelhouseConfig whereId($value)
 * @method static Builder|WheelhouseConfig whereRentalId($value)
 * @method static Builder|WheelhouseConfig whereTeamId($value)
 * @method static Builder|WheelhouseConfig whereUpdatedAt($value)
 * @method static Builder|WheelhouseConfig whereWhAggressiveBaseRate($value)
 * @method static Builder|WheelhouseConfig whereWhBasePriceMethod($value)
 * @method static Builder|WheelhouseConfig whereWhConservativeBaseRate($value)
 * @method static Builder|WheelhouseConfig whereWhFarFutureDays($value)
 * @method static Builder|WheelhouseConfig whereWhFarFutureMethod($value)
 * @method static Builder|WheelhouseConfig whereWhFarFutureMinStay($value)
 * @method static Builder|WheelhouseConfig whereWhFarFuturePercent($value)
 * @method static Builder|WheelhouseConfig whereWhInternalMarkup($value)
 * @method static Builder|WheelhouseConfig whereWhLastMinuteMethod($value)
 * @method static Builder|WheelhouseConfig whereWhLastPush($value)
 * @method static Builder|WheelhouseConfig whereWhLastSync($value)
 * @method static Builder|WheelhouseConfig whereWhLocationAvailable($value)
 * @method static Builder|WheelhouseConfig whereWhManualBaseRate($value)
 * @method static Builder|WheelhouseConfig whereWhRecommendedBaseRate($value)
 * @method static Builder|WheelhouseConfig whereWhSeasonalityMethod($value)
 * @method static Builder|WheelhouseConfig whereWhSuccessful($value)
 * @method static Builder|WheelhouseConfig whereWhWeekendMethod($value)
 *
 * @mixin \Eloquent
 */
class WheelhouseConfig extends NinjaProviderModel
{
    protected $fillable = [
        'active',
        'wh_manual_base_rate',
        'wh_base_price_method',
        'wh_weekend_method',
        'wh_seasonality_method',
        'wh_last_minute_method',
        'wh_far_future_method',
        'wh_internal_markup',
        'wh_manual_base_rate',
        'wh_recommended_base_rate',
        'wh_aggressive_base_rate',
        'wh_conservative_base_rate',
        'wh_last_push',
        'wh_last_sync',
    ];

    protected $casts = [
        'active' => 'boolean',
        'wh_base_price_method' => WhPreferencesEnum::class,
        'wh_weekend_method' => WhPreferencesEnum::class,
        'wh_seasonality_method' => WhPreferencesEnum::class,
        'wh_last_minute_method' => WhPreferencesEnum::class,
        'wh_far_future_method' => WhPreferencesEnum::class,
        'wh_last_push' => 'datetime',
        'wh_last_sync' => 'datetime',
    ];

    // Model methods
    public function getMarkupFactor(): float
    {
        return 1 + ($this->wh_internal_markup / 100);
    }

    public function getReverseMarkupFactor(): float
    {
        return  1 / (1 + ($this->wh_internal_markup / 100));
    }

    public function updateFromWhConfigData(?WhConfigData $data): void
    {
        if (is_null($data)) {
            return;
        }
        // Check manual base rate
        if ($data->whBasePriceMethod == WhPreferencesEnum::CUSTOM && empty($data->whManualBaseRate)) {
            $data->whBasePriceMethod = WhPreferencesEnum::AGGRESSIVE;
        }
        $this->wh_base_price_method = $data->whBasePriceMethod;
        $this->wh_weekend_method = $data->whWeekendMethod;
        $this->wh_seasonality_method = $data->whSeasonalityMethod;
        $this->wh_last_minute_method = $data->whLastMinuteMethod;
        $this->wh_far_future_method = $data->whFarFutureMethod;

        if ($this->wh_base_price_method == WhPreferencesEnum::CUSTOM) {
            $this->wh_manual_base_rate = $data->whManualBaseRate;
        } else {
            $this->wh_manual_base_rate = null;
        }

        if ($this->wh_far_future_method == WhPreferencesEnum::CUSTOM) {
            $this->wh_far_future_days = $data->whFarFutureDays;
            $this->wh_far_future_percent = $data->whFarFuturePercent;
            $this->wh_far_future_min_stay = $data->whFarFutureMinStay;
        } else {
            $this->wh_far_future_days = null;
            $this->wh_far_future_percent = null;
            $this->wh_far_future_min_stay = null;
        }
    }

    // Relations
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class, ['rental_id', 'team_id'], ['id', 'team_id']);
    }
}
