<?php

namespace App\Domains\Wheelhouse\Actions;

use App\Actions\Providers\Generic\HandleProviderWebhookAction;
use App\Actions\Providers\Generic\IncreaseWebhookInvocationCountAction;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class HandleWheelhouseWebhookAction extends HandleProviderWebhookAction
{
    use AsAction;

    public function rules(): array
    {
        return [
            'event' => 'required|string',
            'user_initiated' => 'required|bool',
            'external_user_id' => 'sometimes|string',
            'listing_id' => 'sometimes|string',
        ];
    }

    public function asController(ActionRequest $request): JsonResponse
    {
        $event = $request->get('event');
        $listingId = $request->get('listing_id');

        $rentalId = $teamId = null;
        if (! is_null($listingId)) {
            $rentalId = Str::of($listingId)->afterLast('_')->toInteger();
            $teamId = Str::of($listingId)->beforeLast('_')->afterLast('_')->toInteger();
        }

        pnLog("[Wheelhouse] Webhook received for $event for rental $rentalId", $teamId);

        // We have implemented only two events for now
        if ($event != 'listing_sync_requested' || $event != 'listing_recommendations_updated') {
            return response()->json(['success' => 'OK']);
        }

        $this->handle($event, $teamId, $rentalId);

        return response()->json(['success' => 'OK']);
    }

    /** @noinspection PhpUnused */
    public function handle(string $event, int $teamId, int $rentalId): void
    {
        $team = Team::findOrFail($teamId);
        $rental = Rental::getRentalModel($team, $rentalId);

        if ($event == 'listing_sync_requested') {
            SyncRentalWithWhAction::dispatch($team, $rental->id);
        } elseif ($event == 'listing_recommendations_updated') {
            SyncRatesFromWheelhouseAction::dispatch($team, $rental->id);
        }

        IncreaseWebhookInvocationCountAction::run($team);
    }
}
