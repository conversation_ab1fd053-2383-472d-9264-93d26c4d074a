<?php

namespace App\Domains\Wheelhouse\Actions;

use App\Domains\Wheelhouse\Api\Wheelhouse;
use App\Enum\PricingModelEnum;
use App\Events\Booking\BookingCancelledEvent;
use App\Events\Booking\BookingConfirmedEvent;
use App\Models\Booking;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;

class NotifyWhBookingAction implements ShouldBeUniqueUntilProcessing
{
    use AsAction;

    public string $commandSignature = 'ninja-wheelhouse:notify-booking {team} {rental} {booking}';

    public int $jobMaxExceptions = 2;
    public int $jobBackoff = 120;

    public string $jobQueue = 'priority-sync';
    public int $jobUniqueFor = 90;

    public function getJobUniqueId(Team $team, ?int $bookingId = null): string
    {
        return "WH_{$team->id}_{$bookingId}";
    }

    public function configureJob(JobDecorator $job): void
    {
        $job->delay(60);
    }

    public function asListener(BookingConfirmedEvent|BookingCancelledEvent $event): void
    {
        $team = $event->team;
        $bookingId = $event->bookingId;
        $booking = Booking::getBookingModel($team, $bookingId);
        $rental = $booking->rental;

        // Not active rentals (with access to previous pricing) are synced once a day
        if ($rental->pricing_model != PricingModelEnum::smartPricing) {
            return;
        }

        $nightlyPriceThreshold = max($rental->min_price / 100 / 2, $rental->base_rate / 100 / 10);
        if (is_null($booking->currency) || $booking->final_price / $booking->nights < $nightlyPriceThreshold) {
            return;
        }

        self::dispatch($team, $rental->id, $bookingId);
    }

    public function asJob(Team $team, ?int $rentalId = null, ?int $bookingId = null): void
    {
        $rental = $rentalId ? Rental::getRentalModel($team, $rentalId) : null;
        $booking = $bookingId ? Booking::getBookingModel($team, $bookingId) : null;

        $this->handle($team, $rental, $booking);
    }

    public function asCommand(Command $command): int
    {
        $teamId = $command->argument('team');
        $bookingId = $command->argument('booking');
        $rentalId = $command->argument('rental');

        $team = Team::find($teamId);
        $booking = Booking::getBookingModel($team, $bookingId);
        $rental = Rental::getRentalModel($team, $rentalId);
        $this->handle($team, $rental, $booking);

        return $command::SUCCESS;
    }

    public function handle(Team $team, Rental $rental, Booking $booking): void
    {
        $api = Wheelhouse::getApi($team);

        $api->updateSingleReservation($rental, $booking);
        $api->updateListingCalendar($rental);
    }
}
