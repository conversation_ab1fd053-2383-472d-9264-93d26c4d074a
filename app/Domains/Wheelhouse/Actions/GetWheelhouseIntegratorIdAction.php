<?php

namespace App\Domains\Wheelhouse\Actions;

use App\Models\Booking;
use App\Models\Rental;
use App\Models\Team;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;

class GetWheelhouseIntegratorIdAction
{
    use AsAction;

    public function handle(Team|Rental|Booking $model): string
    {
        $prefix = 'rn_';
        if ($model instanceof Rental) {
            return $prefix.$model->team_id.'_'.$model->id;
        }
        if ($model instanceof Booking) {
            return $prefix.$model->team_id.'_'.$model->id;
        }
        if ($model instanceof Team) {
            return $prefix.$model->id;
        }
        throw new Exception('Invalid model type');
    }
}
