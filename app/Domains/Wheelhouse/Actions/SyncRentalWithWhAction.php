<?php

namespace App\Domains\Wheelhouse\Actions;

use App\Domains\Wheelhouse\Api\Wheelhouse;
use App\Domains\Wheelhouse\Models\WheelhouseConfig;
use App\Enum\PricingModelEnum;
use App\Models\Rental;
use App\Models\RentalNinjaTeam;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;

class SyncRentalWithWhAction implements ShouldBeUniqueUntilProcessing
{
    use AsAction;

    public const SIGNATURE = 'ninja-wheelhouse:sync-rental';
    public string $commandSignature = 'ninja-wheelhouse:sync-rental {team} {rental}';

    public int $jobMaxExceptions = 2;
    public int $jobBackoff = 120;

    public string $jobQueue = 'cron-sync';
    public int $jobUniqueFor = 120;

    public function getJobUniqueId(Team $team, int $rentalId): string
    {
        return "team_{$team->id}_rental_$rentalId";
    }

    public function asCommand(Command $command): int
    {
        $teamId = $command->argument('team');
        $rentalId = $command->argument('rental');
        $team = Team::find($teamId);
        $rental = Rental::getRentalModel($team, $rentalId);

        $currentSetting = $rental->pricing_model;
        $rental->pricing_model = PricingModelEnum::season;

        $this->handle($team, $rental);

        $rental->pricing_model = $currentSetting;
        $rental->save();

        return $command::SUCCESS;
    }

    public function asJob(Team $team, int $rentalId): void
    {
        $rental = Rental::getRentalModel($team, $rentalId);

        $this->handle($team, $rental);
    }

    public function handle(Team $team, Rental $rental, bool $force = false): void
    {
        pnLog("[SyncRentalWithWhAction] Start with rental $rental->id", $team);
        $config = $rental->wheelhouseConfig;

        if ($this->rentalNotReady($rental)) {
            pnLog("[SyncRentalWithWhAction] Rental not ready: $rental->id", $team);
            $config->active = false;
            $config->wh_last_push = now();
            $config->save();

            return;
        }
        $api = Wheelhouse::getApi($team);
        $api->registerUser();

        if (! $config->wh_location_available) {
            $config->wh_location_available = $api->checkLocationCoverage($rental);
            if (! $config->wh_location_available) {
                $this->notifyNewLocation($rental, $team);

                return;
            }
        }

        UpdateWhiteLabelMarkupAction::run($rental);

        $api->updateListing($rental);
        $api->updateListingCalendar($rental);
        $api->updateReservations($rental);
        if ($force) {
            $api->setAllListingPreferences($rental);
        }
        $api->setListingMinMaxBaseAndDiscounts($rental);

        $this->getBaseRates($api, $rental, $config);
        $this->checkStatus($api, $rental, $config);

        $config->wh_last_push = now();
        $config->wh_successful = true;
        $config->save();
        pnLog("[SyncRentalWithWhAction] End with rental $rental->id", $team);
    }

    private function getBaseRates(Wheelhouse $api, Rental $rental, WheelhouseConfig $config): void
    {
        $rates = $api->getBasePriceRecommendation($rental);
        $config->wh_recommended_base_rate = $rates['recommended'];
        $config->wh_conservative_base_rate = $rates['conservative'];
        $config->wh_aggressive_base_rate = $rates['aggressive'];
        $config->save();
    }

    private function rentalNotReady(Rental $rental): bool
    {
        return is_null($rental->country_code) ||
            is_null($rental->zip) ||
            is_null($rental->lat) || is_null($rental->lng) ||
            is_null($rental->base_rate) ||
            is_null($rental->min_stay) ||
            empty($rental->currency) ||
            empty($rental->sleeps);
    }

    protected function notifyNewLocation(Rental $rental, Team $team): void
    {
        pnLog("[SyncRentalWithWhAction] Location not available: $rental->id", $team);

        Cache::remember(
            key: "wh_location_not_available_$rental->id",
            ttl: now()->addWeek(),
            callback:  function () use ($rental) {
                RentalNinjaTeam::slackNotification(
                    instance: "Market request for Rental Ninja:\n- Country: $rental->country_code\n- Postal code: $rental->zip\n- Coordinates: ($rental->lat, $rental->lng) (lat, long)",
                    channel: '#out-of-market-market-requests_rental_ninja_whitelabel'
                );

                return true;
            });
    }

    private function checkStatus(Wheelhouse $api, Rental $rental, ?WheelhouseConfig $config): void
    {
        if (is_null($config->active)) {
            $config->active = $api->checkListingStatus($rental);
            $config->save();
        }
        if ($rental->pricing_model == PricingModelEnum::smartPricing && ! $config->active) {
            $api->enableListing($rental);
        }
        if ($rental->pricing_model != PricingModelEnum::smartPricing && $config->active) {
            $api->disableListing($rental);
        }
    }
}
