<?php

namespace App\Domains\Wheelhouse\Actions\Backup;

use App\Domains\Wheelhouse\Api\Wheelhouse;
use App\Models\Rental;
use App\Models\RentalDailyDetails;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;

class WhBackupUpdateCustomRatesAction
{
    use AsAction;

    public string $commandSignature = 'ninja-wheelhouse:backup-update-custom-rates {team?} {rental}';

    public int $jobMaxExceptions = 2;
    public int $jobBackoff = 120;

    public string $jobQueue = 'cron-sync';
    public int $jobUniqueFor = 300;

    public function asJob(Team $t, int $rentalId, Collection $customRates): void
    {
        $rental = Rental::getRentalModel($t, $rentalId);

        $this->handle($t, $rental, $customRates);
    }

    public function asCommand(Command $command): int
    {
        $teamId = $command->argument('team');
        $rentalId = $command->argument('rental');

        $team = Team::findOrFail($teamId);
        $rental = Rental::getRentalModel($team, $rentalId);

        $collection = $rental->dailyDetails()
            ->where('date', '>=', Carbon::now()->startOfDay())
            ->where('date', '<=', Carbon::now()->addMonths($rental->bookable_months))
            ->whereNotNull('manual_price_in_cents')
            ->get()
            ->map(fn (RentalDailyDetails $rds) => [
                'date' => $rds->date,
                'price_in_cents' => $rds->manual_price_in_cents,
            ]);

        $this->handle($team, $rental, $collection);

        return $command::SUCCESS;
    }

    /**
     * @param  Team  $team
     * @param  Rental  $rental
     * @param  Collection  $manualRates  [date, price_in_cents]
     * @return void
     */
    public function handle(Team $team, Rental $rental, Collection $manualRates): void
    {
        $api = Wheelhouse::getApi($team);
        $api->setCustomRates($rental, $manualRates);
    }
}
