<?php

namespace App\Domains\Wheelhouse\Actions\Backup;

use App\Domains\Wheelhouse\Api\Wheelhouse;
use App\Enum\PricingModelEnum;
use App\Events\RentalDeletedEvent;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;

class WhBackupDeleteListingsAction implements ShouldBeUniqueUntilProcessing
{
    use AsAction;

    public string $commandSignature = 'ninja-wheelhouse:backup-delete-listings {team?} {rental?}';

    public int $jobMaxExceptions = 2;
    public int $jobBackoff = 120;

    public string $jobQueue = 'cron-sync';
    public int $jobUniqueFor = 300;

    public function getJobUniqueId(Team $team, ?int $rentalId = null): string
    {
        return "WH_{$team->id}_{$rentalId}";
    }

    public function configureJob(JobDecorator $job): void
    {
        $job->delay(300);
    }

    public function asListener(RentalDeletedEvent $event): void
    {
        $team = $event->team;
        $rentalsIds = $event->rentalId;

        collect($rentalsIds)
            ->map(fn ($rentalId) => Rental::getRentalModel($team, $rentalId))
            ->filter(fn ($rental) => $rental->pricing_model == PricingModelEnum::smartPricing)
            ->each(fn ($rental) => self::dispatch($team, $rental->id));
    }

    public function asJob(Team $t, ?int $rentalId = null): void
    {
        $rental = $rentalId ? Rental::getRentalModel($t, $rentalId) : null;

        $this->handle($t, $rental);
    }

    public function asCommand(Command $command): int
    {
        $teamId = $command->argument('team');
        if ($teamId) {
            $team = Team::find($command->argument('team'));
            $rentalId = $command->argument('rental');
            $rental = $rentalId ? Rental::getRentalModel($team, $rentalId) : null;

            $this->handle($team, $rental);
        } else {
            Team::activeTeams()
                ->whereRelation('teamRentals', fn ($query) => $query->where('is_rental_ninja', true)->withTrashed()
                )
                ->get()
                ->each(fn ($team) => self::dispatch($team));
        }

        return $command::SUCCESS;
    }

    public function handle(Team $team, ?Rental $rental = null): void
    {
        $api = Wheelhouse::getApi($team);

        if ($rental) {
            $rentals = collect([$rental]);
        } else {
            $rentals = $team->teamRentals()->onlyTrashed()->where('is_rental_ninja', '=', true)->get();
        }
        $rentals->each(fn (Rental $rental) => $api->deleteListing($rental));
    }
}
