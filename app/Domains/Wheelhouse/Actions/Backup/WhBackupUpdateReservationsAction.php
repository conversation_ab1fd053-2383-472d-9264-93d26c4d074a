<?php

namespace App\Domains\Wheelhouse\Actions\Backup;

use App\Domains\Wheelhouse\Api\Wheelhouse;
use App\Enum\PricingModelEnum;
use App\Events\Booking\BookingCancelledEvent;
use App\Events\Booking\BookingConfirmedEvent;
use App\Models\Booking;
use App\Models\Rental;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;

class WhBackupUpdateReservationsAction implements ShouldBeUniqueUntilProcessing
{
    use AsAction;

    public string $commandSignature = 'ninja-wheelhouse:backup-update-reservations {team?} {rental?} {booking?}';

    public int $jobMaxExceptions = 2;
    public int $jobBackoff = 120;

    public string $jobQueue = 'cron-sync';
    public int $jobUniqueFor = 300;

    public function getJobUniqueId(Team $team, ?int $bookingId = null): string
    {
        return "WH_{$team->id}_{$bookingId}";
    }

    public function configureJob(JobDecorator $job): void
    {
        $job->delay(300);
    }

    public function asListener(BookingConfirmedEvent|BookingCancelledEvent $event): void
    {
        $team = $event->team;
        $bookingId = $event->bookingId;
        $booking = Booking::getBookingModel($team, $bookingId);
        $rental = $booking->rental;

        if ($rental->pricing_model != PricingModelEnum::smartPricing) {
            return;
        }

        self::dispatch($team, $rental->id, $bookingId);
    }

    public function asJob(Team $team, ?int $rentalId = null, ?int $bookingId = null): void
    {
        $rental = $rentalId ? Rental::getRentalModel($team, $rentalId) : null;
        $booking = $bookingId ? Booking::getBookingModel($team, $bookingId) : null;

        $this->handle($team, $rental, $booking);
    }

    public function asCommand(Command $command): int
    {
        $teamId = $command->argument('team');
        if ($teamId) {
            $team = Team::find($command->argument('team'));
            $bookingId = $command->argument('booking');
            $rentalId = $command->argument('rental');
            $booking = $bookingId ? Booking::getBookingModel($team, $bookingId) : null;
            $rental = $rentalId ? Rental::getRentalModel($team, $rentalId) : null;

            $this->handle($team, $rental, $booking);
        } else {
            Team::activeTeams()
                ->whereRelation('teamRentals', 'pricing_model', '=', PricingModelEnum::smartPricing)
                ->get()
                ->each(fn ($team) => self::dispatch($team));
        }

        return $command::SUCCESS;
    }

    public function handle(Team $team, ?Rental $rental = null, ?Booking $booking = null): void
    {
        $api = Wheelhouse::getApi($team);
        if ($rental && $booking) {
            $api->updateSingleReservation($rental, $booking);

            return;
        }
        if ($rental) {
            $api->updateReservations($rental);

            return;
        }
        $team->teamRentals
            ->filter(fn ($rental) => $rental->pricing_model == PricingModelEnum::smartPricing)
            ->each(fn ($rental) => $api->updateReservations($rental));
    }
}
