<?php

namespace App\Domains\Wheelhouse\Data;

use App\Domains\Wheelhouse\Enums\WhPreferencesEnum;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Optional;

#[MapName(SnakeCaseMapper::class)]
class WhConfigData extends Data
{
    public function __construct(
        public int|Optional|null $whManualBaseRate,
        public WhPreferencesEnum $whBasePriceMethod,
        public WhPreferencesEnum $whWeekendMethod,
        public WhPreferencesEnum $whSeasonalityMethod,
        public WhPreferencesEnum $whLastMinuteMethod,
        public WhPreferencesEnum $whFarFutureMethod,
        public ?int $whFarFutureDays,
        public ?int $whFarFuturePercent,
        public ?int $whFarFutureMinStay,
    ) {
    }

    public function getConfigKey(): string
    {
        return $this->whBasePriceMethod->value.'_'.$this->whWeekendMethod->value.'_'.$this->whSeasonalityMethod->value.'_'.$this->whLastMinuteMethod->value.'_'.$this->whManualBaseRate;
    }
}
