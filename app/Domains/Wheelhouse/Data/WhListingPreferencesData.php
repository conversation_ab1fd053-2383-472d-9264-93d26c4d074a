<?php

namespace App\Domains\Wheelhouse\Data;

use App\Enum\PricingModelEnum;
use App\Models\Rental;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class WhListingPreferencesData extends Data
{
    public function __construct(
        public int $numIncludedGuests,
        public int $numMaxGuests,
        #[DataCollectionOf(WhFeeData::class)]
        public DataCollection $fees,

        public bool $automaticRatePostingEnabled,
        public float $weeklyDiscountPct,
        public float $monthlyDiscountPct,
        public int $minimumStay,
    ) {
    }

    public static function fromModel(Rental $r): WhListingPreferencesData
    {
        $automationEnabled = ($r->pricing_model == PricingModelEnum::smartPricing) && $r->syncRates() && $r->team_id != 5;

        return new self(
            numIncludedGuests: $r->sleeps,
            numMaxGuests: $r->sleeps_max,
            fees: WhFeeData::collection($r->rentalFees),
            automaticRatePostingEnabled: $automationEnabled,
            weeklyDiscountPct: 0,
            monthlyDiscountPct: 0,
            minimumStay: $r->min_stay,
        );
    }
}
