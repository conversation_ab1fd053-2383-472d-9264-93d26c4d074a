<?php

namespace App\Domains\Stripe\Actions;

use App\Models\Country;
use App\Models\RentalNinjaTeam;
use App\Models\User;
use App\Notifications\Internal\RentalNinjaTeamMailNotification;
use Laravel\Cashier\Cashier;
use Lorisleiva\Actions\Concerns\AsAction;
use Str;

class SaveVerifiedTeamOwnersPhoneAction
{
    use AsAction;

    public function asJob(int $userId, string $stripeVerificationSessionId): void
    {
        $this->handle($userId, $stripeVerificationSessionId);
    }

    public function handle(int $userId, string $stripeVerificationSessionId): void
    {
        $session = Cashier::stripe()->identity->verificationSessions->retrieve(
            $stripeVerificationSessionId,
            ['expand' => ['verified_outputs']]
        );
        $phone = $session->verified_outputs?->phone ?? '';

        if (! empty($phone) && Str::startsWith($phone, '+')) {
            $phoneNumber = Str::substr($phone, 1); // remove the '+' sign

            $number = '';
            $countryCode = '';
            Country::query()
                ->select(['id', 'calling_code'])
                ->get()
                ->sortByDesc(fn (Country $country) => strlen($country->calling_code))
                ->each(function (Country $country) use ($phoneNumber, &$countryCode, &$number) {
                    if (Str::startsWith($phoneNumber, $country->calling_code)) {
                        $countryCode = $country->id;
                        $number = Str::substr($phoneNumber, strlen($country->calling_code));

                        return false;
                    }
                });

            if (! empty($number) && ! empty($countryCode)) {
                $user = User::find($userId);
                $user->phone = $number;
                $user->country_code = $countryCode;
                $user->save();
            } else {
                $this->reportPhoneNotParsed($userId, $phone);

                return;
            }
        } else {
            $this->reportPhoneNotParsed($userId, $phone);

            return;
        }
    }

    private function reportPhoneNotParsed(int $userId, string $phone): void
    {
        $rentalNinja = RentalNinjaTeam::getInstance();
        $rentalNinja->email = RentalNinjaTeam::TECH_EMAIL;
        $message = "We received the following phone number and we couldn't parse it to our phone format: $phone. User id: $userId.";
        $rentalNinja->notify(new RentalNinjaTeamMailNotification(
            "[WARNING] We couldn't save a verified phone number to the team owner",
            $message,
        ));
    }
}
