<?php

namespace App\Exports;

use App\Models\Rental;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PayoutExport implements FromView, ShouldAutoSize, WithStrictNullComparison
{
    /** @var array */
    private $payout_resource;

    /** @var array */
    private $rentals_list;

    public function __construct(array $payout_resource)
    {
        $this->payout_resource = $payout_resource;

        // Get a rentals list with key rental_id and value rental_name. It will be sent to the view to be able to seek for the rental name
        $this->rentals_list = Rental::query()
            ->onTeam($payout_resource['team_id'])
            ->withTrashed() //Payout may contain deleted rentals
            ->whereIn('id',
                array_filter($payout_resource['rentals']->toArray(),
                    fn ($rental_id) => ! empty($rental_id) && $rental_id != 0)
            )
            ->pluck('name', 'id')
            ->toArray();
        // Note: We need to get the rentals ids from payout because a payout may contain bookings which are not anymore in the settlement
        // (the user may have updated the settlement without updating payouts, and this gives an error)
    }

    public function view(): View
    {
        // Note: This method will be called in the Job, when creating the excel by Excel::...
        // Therefore, no problem if here we need to handle here complicated stuff:

        return view('ninja.excel.payout.summary', [
            'payout' => $this->payout_resource,
            'rentals' => $this->rentals_list,
        ]);
    }
}
