<?php

namespace App\Exports\Sheets\Accounting;

use App\Models\PayeeInvoice;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class InvoicesSheet implements FromQuery, WithMapping, WithHeadings
{
    use Exportable;

    const TITLE = 'Invoices data';

    public function __construct(
        public Team $team,
        public Carbon $from,
        public Carbon $to,
    ) {
    }

    public function title(): string
    {
        return self::TITLE;
    }

    public function query(): Builder
    {
        return PayeeInvoice::query()
            ->whereTeamId($this->team->id)
            ->where('issued_at', '>=', $this->from)
            ->where('issued_at', '<', $this->to)
            ->with('issuerPayee', 'receiverPayee');
    }

    public function map($row): array
    {
        /** @var PayeeInvoice $row */
        return [
            $row->invoice_id,
            $row->issued_at,
            $row->sub_total,
            $row->vat_amount,
            $row->total,
            $row->others_total,
            $row->issuer,
            $row->issuerPayee->name,
            $row->receiver,
            $row->receiverPayee->name,
        ];
    }

    public function headings(): array
    {
        return [
            'Invoice ID',
            'Date',
            'Sub Total',
            'VAT Amount',
            'Total',
            'Others Total',
            'Issuer',
            'Issuer Payee',
            'Receiver',
            'Receiver Payee',
        ];
    }
}
