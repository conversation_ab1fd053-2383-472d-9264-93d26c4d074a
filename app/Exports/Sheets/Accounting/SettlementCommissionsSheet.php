<?php

namespace App\Exports\Sheets\Accounting;

class SettlementCommissionsSheet extends ExcelExporterSheet
{
    const TITLE = 'Commissions Breakdown';

    const HEADERS = ['Commissions Breakdown', 'Commission Price'];

    const COLUMNS_CURRENCY_FORMAT = ['B'];

    public function __construct(array $rows, string $formatter)
    {
        parent::__construct(
            $rows,
            self::TITLE,
            self::HEADERS,
            self::COLUMNS_CURRENCY_FORMAT,
            $formatter
        );
    }

    public function getRowValues(): array
    {
        $rows = [];
        foreach ($this->rows as $channel => $commission) {
            $rows[$channel]['name'] = $channel;
            $rows[$channel]['amount'] = $commission;
        }

        return $rows;
    }
}
