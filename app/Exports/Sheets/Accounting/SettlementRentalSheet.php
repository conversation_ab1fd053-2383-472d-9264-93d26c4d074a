<?php

namespace App\Exports\Sheets\Accounting;

class SettlementRentalSheet extends ExcelExporterSheet
{
    const TITLE = 'Rental Breakdown';

    const HEADERS = ['Rental Breakdown', 'Bookings', 'Nights', 'Guests', 'Rental price', 'Fees', 'Taxes', 'Channel', 'Income'];

    const ROW_KEYS = ['rental_name', 'bookings', 'nights', 'guests', 'final_rental_prices_total', 'fees', 'taxes', 'commissions', 'bookings_income'];

    const COLUMNS_CURRENCY_FORMAT = ['E', 'F', 'G', 'H', 'I'];

    public function __construct(array $rows, string $formatter)
    {
        parent::__construct(
            $rows,
            self::TITLE,
            self::HEADERS,
            self::COLUMNS_CURRENCY_FORMAT,
            $formatter
        );
    }

    public function getRowValues(): array
    {
        $rows = [];
        foreach ($this->rows as $rental) {
            $rows[] = array_map(function ($key) use ($rental) {
                return array_key_exists($key, $rental) ? $rental[$key] : '';
            }, self::ROW_KEYS);
        }

        return $rows;
    }
}
