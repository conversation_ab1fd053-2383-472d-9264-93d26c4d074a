<?php

namespace App\Jobs;

use App\Actions\Alerts\UpdateBookingPaymentAlertsAction;
use App\Actions\Providers\Generic\IncreaseWebhookInvocationCountAction;
use App\Actions\Tasks\ScheduledTask\HandleTasksForBookingAction;
use App\DataProviders\ApiConnectors\ProviderConnector;
use App\DataProviders\Providers\Smoobu;
use App\DTO\Bookings\BookingDto;
use App\Enum\BookingStatusEnum;
use App\Events\Booking\BookingBlockEvent;
use App\Events\Booking\BookingCancelledEvent;
use App\Events\Booking\BookingConfirmedEvent;
use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Psr\SimpleCache\InvalidArgumentException;

class HandleSmoobuWebhook extends GenericProviderWebhookHandler
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public const EVENT_NEW_RESERVATION = 'newReservation';

    public const EVENT_CANCEL_RESERVATION = 'cancelReservation';

    public const EVENT_UPDATE_RESERVATION = 'updateReservation';

    public const EVENT_DELETE_RESERVATION = 'deleteReservation';

    /**
     * Returns true only if the action given as a param is part of the accepted list of actions we can take.
     */
    public static function isValidAction(string $action): bool
    {
        return in_array($action, [
            self::EVENT_NEW_RESERVATION,
            self::EVENT_CANCEL_RESERVATION,
            self::EVENT_UPDATE_RESERVATION,
            self::EVENT_DELETE_RESERVATION,
        ]);
    }

    /**
     * Execute the job. This job should be sent to the queue after checking the team is subscribed or on trial.
     *
     *
     * @throws InvalidArgumentException
     */
    public function handle(): void
    {
        $team = $this->team;
        if (empty($team->getProviderAccount(Smoobu::ID)->oauth_refresh_token)) {
            pnLog('[HandleSmoobuWebhook] Ignoring webhook from a team that cannot be synced.', $team);

            return;
        }

        pnLog("[SM] Received a {$this->get('action')}  webhook", $team);

        // action	string	action of the webhook (updateRates, newReservation, cancelReservation, updateReservation)
        if ($this->isBooking()) {
            $this->handleBooking();
        }
        if ($this->isDeleteBooking()) {
            $this->deleteBooking();
        }

        IncreaseWebhookInvocationCountAction::run($team);
    }

    private function isBooking(): bool
    {
        return in_array($this->get('action'), [
            self::EVENT_NEW_RESERVATION,
            self::EVENT_CANCEL_RESERVATION,
            self::EVENT_UPDATE_RESERVATION,
        ]);
    }

    private function isDeleteBooking(): bool
    {
        return $this->get('action') == self::EVENT_DELETE_RESERVATION;
    }

    private function get(string $string)
    {
        return Arr::get($this->payload, $string);
    }

    /**
     * @throws InvalidArgumentException
     */
    private function handleBooking(): void
    {
        $bookingId = $this->get('data.id');
        $oldBooking = Booking::query()
            ->withoutGlobalScopes()
            ->onTeam($this->team)
            ->whereId($bookingId)
            ->first();
        $oldBookingData = BookingDto::fromModel($oldBooking);

        $connector = ProviderConnector::from($this->team, Smoobu::ID);
        $connector->updateBooking($bookingId);

        $booking = Booking::getBookingModel($this->team, $bookingId); // $booking must not be null otherwise will fail
        $updatedBookingData = BookingDto::fromModel($booking);

        if (is_null($booking->rental)) {
            // This can happen if the booking is not linked to a rental, for example if the rental was deleted.
            pnLog("[SM] Booking $bookingId has no rental", $this->team);

            return;
        }

        UpdateBookingPaymentAlertsAction::run(booking: $booking, issuer: 0);
        $booking->rental->updateAvailability();

        HandleTasksForBookingAction::run($this->team, $booking);

        $this->triggerBookingEvents($oldBookingData, $updatedBookingData);
    }

    private function deleteBooking(): void
    {
        $bookingId = $this->get('data.id');
        $booking = Booking::query()
            ->withoutGlobalScopes()
            ->onTeam($this->team)
            ->whereId($bookingId)
            ->first();
        $booking->canceled_at = now();

        UpdateBookingPaymentAlertsAction::run(booking: $booking, issuer: 0);
        $booking->rental->updateAvailability();

        HandleTasksForBookingAction::run($this->team, $booking);

        if ($this->team->shouldNotify()) {
            event(new BookingCancelledEvent($this->team, $bookingId));
        }
    }

    private function triggerBookingEvents(?BookingDto $oldBookingData, BookingDto $updatedBookingData): void
    {
        if ($this->team->shouldNotify()) {
            switch ($this->getAction()) {
                case self::EVENT_CANCEL_RESERVATION:
                    event(new BookingCancelledEvent($this->team, $updatedBookingData->id));
                    break;
                case self::EVENT_NEW_RESERVATION:
                    if ($updatedBookingData->status == BookingStatusEnum::BOOKED) {
                        event(new BookingConfirmedEvent($this->team, $updatedBookingData->id));
                    } elseif ($updatedBookingData->status == BookingStatusEnum::UNAVAILABLE) {
                        event(new BookingBlockEvent($this->team, $updatedBookingData->id));
                    }
                    break;
                case self::EVENT_UPDATE_RESERVATION:
                    $this->triggerBookingUpdatedEvents($oldBookingData, $updatedBookingData);
                    // FIXME: Is there any case in which we should "restore" a cancelled booking and notify?
                    break;
            }
        }
    }

    /**
     * Returns the current action for the webhook received.
     */
    private function getAction(): string
    {
        return $this->get('action');
    }
}
