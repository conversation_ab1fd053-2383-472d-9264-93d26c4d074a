<?php

namespace App\Jobs;

use App\Actions\Users\ShouldNotifyUserAction;
use App\Enum\UserSettingsEnum;
use App\Models\Task;
use App\Models\TeamSettings;
use App\Models\User;
use App\Models\UserNotificationToken;
use App\Notifications\NinjaFcmNotification;
use App\Notifications\TaskNotification;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

/**
 * NOTIFY: Everyone except the requester. Insert in Timeline and Notify through Slack.
 * Email: none.
 *
 * Class TaskCompletedJob
 */
class TaskCompletedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /** @var \App\Models\Task */
    public $task;

    /** @var User */
    public $requester;

    /**
     * Create a new event instance.
     */
    public function __construct(Task $task, ?User $requester = null)
    {
        $this->task = $task;
        $this->requester = $requester;
    }

    /**
     * Execute the job.
     *
     *
     * @throws Exception
     */
    public function handle(): bool
    {
        if ($this->task == null) {
            return false;
        }

        $team = $this->task->team;

        if (! $team->subscribed() && ! $team->onGenericTrial()) {
            return false;
        }

        $task = $this->task;

        $rental = $task->rental;

        if ($rental == null) {
            return false;
        }

        $users = 0;
        foreach ($task->relatedUserTokens($this->requester) as $userNotificationToken) {
            if (! ShouldNotifyUserAction::run($userNotificationToken->user, UserSettingsEnum::TASK_NOTIFICATION)) {
                continue;
            }
            $locale = substr($userNotificationToken->user->locale, 0, 2);
            $title = __('messages.task.completed', [], $locale);
            if ($this->requester != null) {
                $body = __('messages.task.completed.body.by', [
                    'title' => $task->title,
                    'rental' => $rental?->name,
                    'requester' => $this->requester->name,
                ], $locale);
            } else {
                $body = __('messages.task.completed.body', [
                    'title' => $task->title,
                    'rental' => $rental?->name,
                ], $locale);
            }
            $data = [
                'task' => $task->id,
                'title' => $task->title,
                'rental' => $rental?->id,
                'rental_name' => $rental?->name,
            ];

            $users++;
            $userNotificationToken->notify(new NinjaFcmNotification($userNotificationToken, $title, $body, UserNotificationToken::TYPE_TASK, $data));
        }

        if ($team->canReceiveSlackNotifications(TeamSettings::TASK)) {
            $title = __('messages.task.completed', []);
            if ($this->requester != null) {
                $body = __('messages.task.completed.body.by', [
                    'title' => $task->title,
                    'rental' => $rental?->name,
                    'requester' => $this->requester->name,
                ]);
            } else {
                $body = __('messages.task.completed.body', [
                    'title' => $task->title,
                    'rental' => $rental?->name,
                ]);
            }

            $team->notify((new TaskNotification($title, $body, $task, $users))->locale($team->getTeamLocale()));
        }

        return true;
    }
}
