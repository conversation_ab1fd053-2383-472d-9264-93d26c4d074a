<?php

namespace App\Jobs;

use App\Actions\Bookings\Comments\AddBookingCommentAction;
use App\Models\Booking;
use App\Models\PreCheckInForm;
use App\Models\ProviderEvent;
use App\Models\Team;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class MarkAppInstalledJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Team $team;

    public int $bookingId;

    /**
     * Create a new job instance.
     */
    public function __construct(Team $team, int $bookingId)
    {
        $this->team = $team;
        $this->bookingId = $bookingId;
    }

    /**
     * Execute the job.
     *
     *
     * @throws Throwable
     */
    public function handle(): void
    {
        $booking = Booking::getBookingModel($this->team, $this->bookingId);

        /** @var \App\Models\PreCheckInForm $form */
        $form = PreCheckInForm::query()
            ->fromBooking($booking->id)
            ->onTeam($booking->team_id);

        if ($form->exists()) {
            $model = $form->first();
            if ($model->app_installed) {
                return;
            }
        }

        /** @var \App\Models\Team $team */
        $team = Team::query()
            ->whereId($booking->team_id)
            ->first();

        // If it doesn't exist... we should create it, right?
        if (! $form->exists()) {
            PreCheckInForm::createAndSaveFor($team, $booking);
        }

        $form = PreCheckInForm::query()
            ->fromBooking($booking->id)
            ->onTeam($team->id)
            ->first();

        $form->app_installed = true;
        $form->save();

        ProviderEvent::handleGuestAppInstalled($team, $form);
        $this->appInstalledComment($team, $booking);
    }

    /**
     * Adds the comment.
     */
    private function appInstalledComment(Team $team, Booking $booking)
    {
        $now = now();
        $comment = "Guest(s) logged into the Guest Portal on: $now UTC";
        AddBookingCommentAction::run(team: $team, booking: $booking, comment: $comment, authorId: 0);
    }
}
