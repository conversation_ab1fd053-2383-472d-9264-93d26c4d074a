<?php

namespace App\Http\Middleware;

use App\Flavors\NinjaFlavor;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * This class will assign different configurations given a domain name.
 *
 *
 * Class AssignConfigPerDomain
 */
class AssignConfigPerDomain
{
    /**
     * Handle an incoming request.
     *
     *
     * @throws Exception
     */
    public function handle(Request $request, Closure $next): Response
    {
        $config = NinjaFlavor::getConfigurationFromRequest(request: $request);
        NinjaFlavor::setConfiguration($config);

        return $next($request);
    }
}
