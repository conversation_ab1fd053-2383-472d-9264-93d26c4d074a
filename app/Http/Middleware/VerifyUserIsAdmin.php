<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VerifyUserIsAdmin
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        /** @var User $user */
        $user = $request->user();

        if (! is_null($user) && ! $user->isAdminOrAbove()) {
            if ($request->ajax() || $request->wantsJson()) {
                return response('{ error: "This user has no access" }', 401);
            } else {
                abort(401);
            }
        }

        return $next($request);
    }
}
