<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VerifyUserHasAccessToAccounting
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        /** @var User $user */
        $user = $request->user();

        if (! is_null($user) && ! $user->canSeeAccounting()) {
            if ($request->ajax() || $request->wantsJson()) {
                return response('{ error: "This user has no access to the website" }', 401);
            } else {
                abort(401);
            }
        }

        return $next($request);
    }
}
