<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RegisterMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        // Allow for our teams
        if ($request->hasHeader('Ninja-Register-Token')) {
            return $next($request);
        }

        abort(Response::HTTP_NOT_ACCEPTABLE, 'Restart the registration process');
    }
}
