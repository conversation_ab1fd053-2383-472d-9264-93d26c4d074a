<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;

class SetLocale
{
    public function handle($request, Closure $next)
    {
        if ($request->route('locale')) {
            $locale = $request->route('locale');

            abort_unless(strlen($locale) === 2 || strlen($locale) === 5, 400);

            App::setLocale($locale);
        }

        return $next($request);
    }
}
