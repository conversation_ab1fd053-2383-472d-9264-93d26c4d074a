<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VerifyTeamIsVerified
{
    public function handle(Request $request, Closure $next): Response
    {
        /** @var User $user */
        $user = $request->user();

        abort_unless($user->team->verified, 401, 'Your team is not verified. All teams must go through a verification process.');

        return $next($request);
    }
}
