<?php

namespace App\Http\Middleware;

use Closure;
use Fruitcake\Cors\CorsService;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CachePreflightRequestsMiddleware
{
    /**
     * Create a new middleware instance.
     *
     * @return void
     */
    public function __construct(protected CorsService $cors)
    {
    }

    public function handle(Request $request, Closure $next): Response
    {
        if ($this->cors->isPreflightRequest($request)) {
            /** @var Response $response */
            $response = $next($request);
            $maxAge = config('cors.max_age');
            $response->headers->set('Vary', 'Origin');
            $response->headers->set('Cache-Control', "public, max-age=$maxAge");

            return $response;
        }

        return $next($request);
    }
}
