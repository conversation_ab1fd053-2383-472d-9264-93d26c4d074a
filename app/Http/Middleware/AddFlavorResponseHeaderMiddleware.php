<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AddFlavorResponseHeaderMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     */
    public function handle(Request $request, Closure $next): \Symfony\Component\HttpFoundation\Response
    {
        $response = $next($request);
        $response->headers->set('X-Rn-Flavor', config('ninja.firebase_owner', 'rental-ninja'));

        return $response;
    }
}
