<?php

namespace App\Http\Middleware;

use App\Actions\Support\Postman\RegisterRequestAttributesForPostmanAction;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RegisterRequestAttributes
{
    public function handle(Request $request, Closure $next): Response
    {
        RegisterRequestAttributesForPostmanAction::run($request);

        return $next($request);
    }
}
