<?php

namespace App\Http\Middleware;

use App\Actions\Support\GetAllLanguagesAction;
use App\DataProviders\Providers\NoProvider;
use App\DTO\Website\WebsiteConfigData;
use App\Models\DistributionWebsite;
use App\Models\Locale;
use App\Models\Rental;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Session;
use Str;

class WebsiteBuilderMiddleware
{
    private string $previewDomain = 'preview.rental-ninja.com';
    private string $embedUrl = 'embed';
    // private string $directStaysDomain = 'preview-dario.rental-ninja.com';
    private string $directStaysDomain = 'directstays.io';

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Support\Facades\Response)  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $domain = $request->header('Apx-Incoming-Host');
        abort_if(is_null($domain), 404, 'Domain not found');
        $isDirectStays = $domain === $this->directStaysDomain;

        // Check if we're on the direct stays domain
        if ($isDirectStays) {
            $path = $request->path();
            if (! Str::contains($path, ['property/', 'info'])) {
                return redirect('https://directstays.io/info');
            }
            if (Str::contains($path, 'info')) {
                view()->share('robotsTag', 'noindex, nofollow');
                view()->share('favicon', asset('/img/directstays/favicon.png'));
                view()->share('fathomId');
                view()->share('googleAdsTagId');
                view()->share('googleAdsConversionLabel');

                return $next($request);
            }
            $segments = explode('/', $path);

            // Check if we're on a property page
            if (count($segments) >= 3 && $segments[1] === 'property') {
                $externalId = (int) $segments[2];

                // Find the rental by external ID
                $rental = Rental::query()
                    ->whereIsRentalNinja(true)
                    ->whereExternalId($externalId)
                    ->first();

                if ($rental) {
                    // Store both the original URL and the internal rental ID
                    $request->attributes->set('original_url', $request->fullUrl());
                    $request->attributes->set('internal_rental_id', $rental->id);

                    // Map the query parameters
                    $mappedQuery = [
                        'start' => $request->query('arrival'),
                        'end' => $request->query('departure'),
                        'guests' => $request->query('guests'),
                    ];

                    // Filter out null values
                    $mappedQuery = array_filter($mappedQuery);

                    // Create the new path
                    $newPath = '/website/property/'.$rental->id;
                    if (! empty($mappedQuery)) {
                        $newPath .= '?'.http_build_query($mappedQuery);
                    }

                    // Rewrite the request path
                    $request->server->set('REQUEST_URI', $newPath);
                }
            }
        }

        $isPreview = false;
        $isEmbed = Str::contains($request->getRequestUri(), $this->embedUrl);

        $distributionWebsite = $this->getDistributionWebsite($request, $domain, $isPreview, $isDirectStays);
        $team = $distributionWebsite->team;

        if (! $isPreview) {
            $this->checkAccountStatus($team, $distributionWebsite);
        }
        if (! $isPreview && ! $isDirectStays) {
            $this->checkWebsiteStatus($team, $distributionWebsite);
        }

        $supportedLanguages = $distributionWebsite->languages();
        $languageSwitcherLanguages = $this->getLanguageSwitcherLanguages($supportedLanguages);

        // if direct access to /en redirect to '/'
        if ($request->segment(2) === 'en' && ! $this->isAssetRequest($request)) {
            return redirect('https://'.$domain);
        }

        // If only one language is supported, use it directly and is not 'en'
        if (count($supportedLanguages) === 1 && $supportedLanguages[0] !== 'en') {
            $locale = $supportedLanguages[0];
            App::setLocale($locale);
            Session::put('locale', $locale);

            // Redirect to the single supported language if not already there
            if ($request->segment(2) !== $locale && ! $this->isAssetRequest($request)) {
                return $this->redirectWithLocale($request, $domain, $locale);
            }
        } else {
            // Get locale from different sources
            $localeFromUrl = $request->segment(2);
            $localeFromSession = Session::get('locale');
            $localeFromCookie = $this->getLocaleFromCookie($supportedLanguages);
            $localeFromBrowser = $this->getLocaleFromBrowser($request, $supportedLanguages);

            // Determine which locale to use
            $locale = $this->determineLocale(
                $localeFromUrl,
                $localeFromSession,
                $localeFromCookie,
                $localeFromBrowser,
                $supportedLanguages
            );

            App::setLocale($locale);
            Session::put('locale', $locale);

            // Handle redirections if necessary
            if (! $isDirectStays) {
                if ($this->needsRedirection($request, $localeFromUrl, $locale)) {
                    return $this->handleRedirection($request, $domain, $localeFromUrl, $locale);
                }

                if (! $localeFromUrl && $locale !== $this->getDefaultLocale()) {
                    return $this->redirectWithLocale($request, $domain, $locale);
                }
            }
        }

        $websiteConfig = $this->toConfig(
            $distributionWebsite,
            $locale,
            $distributionWebsite->team->name,
            str_replace('/website', '', $request->getRequestUri()),
            $isPreview,
            $isEmbed,
            $isDirectStays
        )->toArray();

        $request->merge([
            'distribution' => $websiteConfig,
            'navigation_languages' => $languageSwitcherLanguages,
            'locale' => $locale,
        ]);

        $this->addHeaders($distributionWebsite, $isPreview, $isDirectStays);
        $this->setFathomAnalyticsSiteId($distributionWebsite, $isPreview, $isDirectStays);

        if ($isEmbed) {
            $embedLang = $request->query('lang');
            if ($embedLang && in_array($embedLang, $supportedLanguages)) {
                App::setLocale($embedLang);
            }
        }

        return $next($request);
    }

    /**
     * Get locale from browser's Accept-Language header.
     */
    private function getLocaleFromBrowser(Request $request, array $supportedLanguages): ?string
    {
        $browserLocales = $request->getLanguages();

        foreach ($browserLocales as $browserLocale) {
            // Extract the primary language tag (e.g., 'en' from 'en-US')
            $primaryTag = substr($browserLocale, 0, 2);

            if (in_array($primaryTag, $supportedLanguages)) {
                return $primaryTag;
            }
        }

        return null;
    }

    /**
     * Determine which locale to use based on various sources.
     */
    private function determineLocale(
        ?string $urlLocale,
        ?string $sessionLocale,
        ?string $cookieLocale,
        ?string $browserLocale,
        array $supportedLanguages
    ): string {
        // Validate URL locale
        $urlLocale = $this->validateLocaleFromUrl($urlLocale, $supportedLanguages);

        // Return first valid locale in priority order
        return $urlLocale ??
            $cookieLocale ??
            $sessionLocale ??
            $browserLocale ??
            $this->getDefaultLocale();
    }

    private function getDistributionWebsite(Request $request, string $domain, bool &$isPreview, bool $isDirectStays): DistributionWebsite
    {
        if ($isDirectStays) {
            // "website/property/26849"
            $path = trim($request->path(), '/');
            $externalId = explode('/', $path)[2]; // Get the ID before any query parameters

            // Find the rental first
            $rental = Rental::query()
                ->whereIsRentalNinja(true)
                ->whereExternalId((int) $externalId)
                ->firstOrFail();

            // Find the distribution website that contains this rental
            $distributionWebsite = DistributionWebsite::query()
                ->whereTeamId($rental->team_id)
                ->whereJsonContains('rentals', $rental->id)
                ->first();

            if (is_null($distributionWebsite)) {
                $distributionWebsite = new DistributionWebsite();
                $distributionWebsite->id = 0;
                $distributionWebsite->rentals = [$rental->id];
                $distributionWebsite->team_id = $rental->team_id;
                $distributionWebsite->domain = $this->directStaysDomain;
                $distributionWebsite->allow_bookings = true;
                $distributionWebsite->source_id = $rental->team->sources()
                    ->firstOrCreate(
                        ['name' => 'Direct Stays'],
                        ['created_at' => now()->timestamp, 'updated_at' => now()->timestamp, 'provider_id' => NoProvider::ID]
                    )->id;
            }

            $googleTravel = $rental->channelRentals()->where('channel', '=', 'Google Travel')->first();
            $markup = $googleTravel?->markup ?? 0;
            $distributionWebsite->markup = $markup;

            return $distributionWebsite;
        }
        if ($domain === $this->previewDomain) {
            $previewCookie = $request->cookie('website-id');
            try {
                $decryptedId = decrypt($request->query('website-id') ?? $previewCookie);
            } catch (\Exception $e) {
                abort(404, 'Website not found');
            }
            $websiteId = intval($decryptedId);
            if (! $websiteId) {
                abort(404, 'Website not found');
            }
            $distributionWebsite = DistributionWebsite::whereId($websiteId)->firstOrFail();
            $encryptedId = encrypt($distributionWebsite->id);
            Cookie::queue(Cookie::make('website-id', $encryptedId, 60 * 24 * 30));
            $isPreview = true;
        } else {
            $distributionWebsite = DistributionWebsite::whereDomain($domain)->firstOrFail();
        }

        return $distributionWebsite;
    }

    private function checkAccountStatus($team, DistributionWebsite $distributionWebsite): void
    {
        abort_unless($team->isSubscribed(), 403, 'Account is not active');
    }

    private function checkWebsiteStatus($team, DistributionWebsite $distributionWebsite): void
    {
        abort_unless($distributionWebsite->isActiveAndReady(), 403, 'Website is not active');
    }

    private function getLanguageSwitcherLanguages($supportedLanguages)
    {
        return GetAllLanguagesAction::run()
            ->whereIn('id', $supportedLanguages)
            ->mapWithKeys(fn (Locale $lang) => [$lang->id => $lang->name]);
    }

    private function validateLocaleFromUrl(?string $localeFromUrl, array $supportedLanguages): ?string
    {
        return in_array($localeFromUrl, $supportedLanguages) ? $localeFromUrl : null;
    }

    private function needsRedirection(Request $request, ?string $localeFromUrl, ?string $lang): bool
    {
        // Don't redirect asset requests
        if ($this->isAssetRequest($request)) {
            return false;
        }

        // If we're on the default locale ('en')
        if ($lang === 'en' || $localeFromUrl === 'en') {
            // Remove '/en' from URLs when it's the default locale
            return $request->getRequestUri() !== '/' &&
                ($localeFromUrl === 'en' || str_starts_with($request->getRequestUri(), '/en'));
        }

        // For non-default locales, ensure the URL has the correct locale prefix
        if ($lang && $lang !== 'en' && $localeFromUrl !== $lang) {
            return true;
        }

        return false;
    }

    private function isAssetRequest(Request $request): bool
    {
        return preg_match('/\.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$/i', $request->path());
    }

    private function handleRedirection(Request $request, string $domain, ?string $localeFromUrl, ?string $lang)
    {
        if ($lang === 'en' || $localeFromUrl === 'en') {
            return redirect($request->getRequestUri());
        }

        return $this->redirectWithLocale($request, $domain, $lang);
    }

    private function redirectWithLocale(Request $request, string $domain, ?string $lang)
    {
        $query = $request->query();
        $url = "https://$domain/$lang";

        if (count($query) > 0) {
            $url .= '?'.http_build_query($query);
        }

        return redirect($url);
    }

    private function addHeaders(?DistributionWebsite $distributionWebsite = null, bool $isPreview = false, bool $isDirectStays = false): void
    {
        $robots = ($isPreview || $isDirectStays) ? 'noindex, nofollow' : 'index, follow';
        view()->share('robotsTag', $robots);
        $logo = $distributionWebsite->logo ?? 'https://via.placeholder.com/150';
        $favicon = $logo.'?fit=crop&h=32&w=32&fm=png';
        view()->share('favicon', $favicon);
        view()->share('googleAdsTagId', $distributionWebsite->google_ads_tag_id);
        view()->share('googleAdsConversionLabel', $distributionWebsite->google_ads_conversion_label);
    }

    private function setFathomAnalyticsSiteId(DistributionWebsite $distributionWebsite, bool $isPreview): void
    {
        $fathomId = $distributionWebsite->fathom_id;
        view()->share('fathomId', $fathomId);
    }

    protected function getLocaleFromCookie(array $supportedLanguages): ?string
    {
        $cookieLocale = request()->cookie('locale');

        return $cookieLocale && in_array($cookieLocale, $supportedLanguages) ? $cookieLocale : null;
    }

    protected function getDefaultLocale(): string
    {
        return 'en';
    }

    private function getDomainForConfig(bool $isPreviewMode, bool $isDirectStays, DistributionWebsite $distributionWebsite): string
    {
        if ($isDirectStays) {
            return $this->directStaysDomain;
        }

        if ($isPreviewMode) {
            return $this->previewDomain;
        }

        return $distributionWebsite->domain;
    }

    protected function toConfig(DistributionWebsite $distributionWebsite, string $locale, string $teamName, string $currentUrl, bool $isPreviewMode = false, bool $isEmbed = false, bool $isDirectStays): WebsiteConfigData
    {
        $domain = $this->getDomainForConfig($isPreviewMode, $isDirectStays, $distributionWebsite);

        $logo = $distributionWebsite->logo ?? 'https://via.placeholder.com/150';

        if ($isDirectStays) {
            $logo = asset('/img/directstays/favicon.png');
        }

        return new WebsiteConfigData(
            id: $distributionWebsite->id,
            teamId: $distributionWebsite->team_id,
            currentUrl: $currentUrl,
            domain: $domain,
            searchDomain: $distributionWebsite->domain,
            logo: $logo,
            mainImage: $distributionWebsite->main_image ?? 'https://via.placeholder.com/1280x720',
            rentals: $distributionWebsite->rentals,
            markup: $distributionWebsite->markup,
            sourceId: $distributionWebsite->source_id,
            headline: $distributionWebsite->headline?->getLocaledText($locale) ?? 'Change me Headline',
            about: $distributionWebsite->about?->getLocaledText($locale) ?? 'Change me About',
            faqs: $distributionWebsite->faqs?->getLocaledText($locale) ?? 'Change me FAQs',
            websiteSeoTitle: $distributionWebsite->website_seo_title?->getLocaledText($locale) ?? 'Change me SEO Title',
            websiteSeoDescription: $distributionWebsite->website_seo_description?->getLocaledText($locale) ?? 'Change me SEO Description',
            mainImageTitle: $distributionWebsite->main_image_title?->getLocaledText($locale) ?? 'Change me Main Image Title',
            mainImageDescription: $distributionWebsite->main_image_description?->getLocaledText($locale) ?? 'Change me Main Image Description',
            keywords: $distributionWebsite->keywords?->getLocaledText($locale) ?? 'Change me Keywords',
            googleAdsTagId: $distributionWebsite->google_ads_tag_id,
            googleAdsConversionLabel: $distributionWebsite->google_ads_conversion_label,
            teamName: $teamName,
            contactName: $distributionWebsite->contact_name ?? 'Change me Contact Name',
            contactEmail: $distributionWebsite->contact_email ?? 'Change me Contact Email',
            contactSupportPhone: $distributionWebsite->contact_support_phone,
            contactBookingPhone: $distributionWebsite->contact_booking_phone,
            contactStreet: $distributionWebsite->contact_street,
            contactRegion: $distributionWebsite->contact_region,
            contactCity: $distributionWebsite->contact_city,
            contactZip: $distributionWebsite->contact_zip,
            contactCountryCode: $distributionWebsite->contact_country_code,
            socialX: $distributionWebsite->social_x,
            socialFacebook: $distributionWebsite->social_facebook,
            socialInstagram: $distributionWebsite->social_instagram,
            socialLinkedin: $distributionWebsite->social_linkedin,
            socialYoutube: $distributionWebsite->social_youtube,
            socialPinterest: $distributionWebsite->social_pinterest,
            socialBlog: $distributionWebsite->social_blog,
            allowBookings: $distributionWebsite->allow_bookings,
            isPreviewMode: $isPreviewMode,
            isEmbed: $isEmbed,
            isDirectStays: $isDirectStays,
            locations: $distributionWebsite->locations(),
            rentalsWithLocation: $distributionWebsite->rentalsWithLocation(),
        );
    }
}
