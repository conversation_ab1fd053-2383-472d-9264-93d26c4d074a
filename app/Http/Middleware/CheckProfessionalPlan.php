<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use RealRashid\SweetAlert\Facades\Alert;

class CheckProfessionalPlan
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        if ($this->professionalAccess($request->user())) {
            return $next($request);
        }

        if ($request->ajax() || $request->wantsJson()) {
            return response('{ error: "Upgrade Required." }', 402);
        }

        $redirect_url = '/accounting/professional-plan-required';

        // Notify the user when trying to access.
        Alert::warning('Your team does not have access to this module', 'Wrong Subscription Level');

        return redirect($redirect_url);
    }

    /**
     * Returns true only if user is on generic trial or subscribed to the premium or enterprise plans.
     */
    private function professionalAccess(?User $user): bool
    {
        if (! $user) {
            return false;
        }

        $team = $user->team;

        if (! $team) {
            return false;
        }

        return $team->isProfessional();
    }
}
