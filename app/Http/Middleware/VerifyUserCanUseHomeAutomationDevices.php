<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VerifyUserCanUseHomeAutomationDevices
{
    public function handle(Request $request, Closure $next): Response
    {
        /** @var User $user */
        $user = $request->user();

        abort_unless($user->permissionFor('use_home_automation_devices'), 401, 'Not enough permissions');

        return $next($request);
    }
}
