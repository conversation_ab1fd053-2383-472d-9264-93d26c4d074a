<?php

namespace App\Http\Middleware;

use Alert;
use App\Http\Resources\InvitationResource;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VerifyUserHasTeam
{
    /**
     * Verify the incoming request's user belongs to team.
     */
    public function handle(Request $request, Closure $next): Response
    {
        /** @var User $user */
        $user = $request->user();
        if ($user->current_team_id == null) { // This must fail if there is no user. Use 'auth' middleware to verify if there is auth user
            if ($user->invitations()->count() > 0) {
                Alert::info('You have pending Invitations', 'Pending Invites');

                return response()->json(
                    data: new InvitationResource($user->invitations->first()),
                    status: Response::HTTP_PRECONDITION_REQUIRED,
                );
            }

            return response()->json(
                data: 'User has no team',
                status: Response::HTTP_PRECONDITION_FAILED,
            );
        }

        return $next($request);
    }
}
