<?php

namespace App\Http\Middleware;

use App\Flavors\NinjaFlavor;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DefaultConfigPerHost
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $config = NinjaFlavor::getConfigurationFromRequest(request: $request, checkUser: false);
        NinjaFlavor::setConfiguration($config);

        return $next($request);
    }
}
