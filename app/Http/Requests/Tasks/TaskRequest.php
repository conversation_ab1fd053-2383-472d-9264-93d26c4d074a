<?php

namespace App\Http\Requests\Tasks;

use App\DTO\Tasks\TaskDto;
use App\Enum\TeamRolesEnum;
use App\Models\Task;
use App\Models\Team;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TaskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'team_id' => 'required|exists:teams,id',
            'title' => 'required|string|max:255',
            'description' => 'string|nullable',
            'task_items' => ['nullable', 'array'],
            'task_items.*.team_id' => [Rule::when(! empty($this->input('task_items')), 'required', 'nullable'), 'exists:teams,id'],
            'task_items.*.order' => [Rule::when(! empty($this->input('task_items')), 'required', 'nullable'), 'numeric'],
            'task_items.*.picture_required' => [Rule::when(! empty($this->input('task_items')), 'required', 'nullable'), 'boolean'],
            'task_items.*.allow_multiple_pictures' => [Rule::when(! empty($this->input('task_items')), 'required', 'nullable'), 'boolean'],
            'role' => ['nullable', Rule::in(TeamRolesEnum::all()->map(fn (TeamRolesEnum $role) => $role->value))],
            'assignee_id' => ['nullable', 'exists:users,id', Rule::when($this->input('role') != null, 'prohibited')],
            'supervisor_id' => 'nullable|exists:users,id',
            'can_start' => 'nullable|in:'.implode(',', Task::START_OPTIONS),
            'due_date' => 'nullable|in:'.implode(',', Task::END_OPTIONS),
            'start_from' => 'nullable|date|before:finish_before',
            'finish_before' => 'nullable|date|after:start_from',
            'priority' => 'nullable|integer|min:1|max:5',
            'remove_on_cancellation' => 'nullable|boolean',
            'has_timer' => 'nullable|boolean',
            'max_time' => 'nullable|integer|min:1',
            'rental_id' => 'nullable|exists:rental,id',
            'booking_id' => 'nullable|exists:booking,id',
            'scheduled_task_id' => 'nullable|exists:scheduled_tasks,id',
            'recurrent_task_id' => 'nullable|exists:recurrent_tasks,id',
        ];
    }

    public function toDto(Team $team): TaskDto
    {
        return TaskDto::from(array_merge($this->toArray(), [
            'team_id' => $team->id, // Important: this ensures data within payload corresponds to current team
            'start_from' => $this->date('start_from'),
            'finish_before' => $this->date('finish_before'),
            'completed_at' => $this->date('completed_at'),
            'supervised_at' => $this->date('supervised_at'),
            'can_start' => $this->input('can_start') != null ? $this->input('can_start') : 'custom',
            'due_date' => $this->input('due_date') != null ? $this->input('due_date') : 'custom',
            'role' => TeamRolesEnum::tryFrom($this->input('role')),
            'assignee_id' => $this->input('assignee_id'),
        ]));
    }
}
