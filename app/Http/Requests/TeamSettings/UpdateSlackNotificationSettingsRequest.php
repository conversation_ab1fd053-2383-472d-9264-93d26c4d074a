<?php

namespace App\Http\Requests\TeamSettings;

use App\DTO\TeamSettings\TeamSettingsRequestDto;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSlackNotificationSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'active' => ['required', 'boolean'],
            'slack_webhook_url' => [
                Rule::requiredIf($this->active),
                'nullable',
                'url',
            ],
            'alert_notifications' => $this->getBooleanTeamSettingsRules(),
            'booking_created_notifications' => $this->getBooleanTeamSettingsRules(),
            'booking_canceled_notifications' => $this->getBooleanTeamSettingsRules(),
            'payment_created_notification' => $this->getBooleanTeamSettingsRules(),
            'picture_added_notification' => $this->getBooleanTeamSettingsRules(),
            'comment_created_notification' => $this->getBooleanTeamSettingsRules(),
            'check_in_out_time_modified_notification' => $this->getBooleanTeamSettingsRules(),
            'task_notification' => $this->getBooleanTeamSettingsRules(),
            'lead_created_notification' => $this->getBooleanTeamSettingsRules(),
            'new_message_notification' => $this->getBooleanTeamSettingsRules(),
            'upsale_purchased_notification' => $this->getBooleanTeamSettingsRules(),
        ];
    }

    private function getBooleanTeamSettingsRules(): array
    {
        return [
            Rule::requiredIf($this->active),
            'boolean',
        ];
    }

    public function asDto(): TeamSettingsRequestDto
    {
        return TeamSettingsRequestDto::from($this->validated());
    }
}
