<?php

namespace App\Http\Requests\ScheduledSettlement;

use App\DTO\Accounting\ScheduledSettlementDto;
use App\Enum\ScheduledSettlementFrequencyEnum;
use App\Enum\ScheduledSettlementPeriodEnum;
use App\Models\Payee;
use App\Models\Rental;
use App\Models\ScheduledSettlement;
use App\Models\User;
use App\Query\ScheduledSettlementQuery;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class ScheduledSettlementRequest extends FormRequest
{
    /**
     * Who has access to this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        /** @var User $user */
        $user = $this->user();

        $this->merge([
            'author_id' => $user->id,
            'team_id' => $user->current_team_id,
        ]);

        $this->mergeIfMissing(['on_net_income' => true]);
        // TODO: remove after a while. Done only to avoid conflicts with frontend not sending the parameter
    }

    public function rules(): array
    {
        return [
            'team_id' => ['required', 'integer'],
            'name' => ['required', 'string', 'max:255'],
            'include_date_with_name' => ['required', 'boolean'],
            'frequency' => ['required', new Enum(ScheduledSettlementFrequencyEnum::class)],
            'schedule_day' => ['required', 'in:first,last,'.implode(',', range(1, 31))],
            'period' => ['required', new Enum(ScheduledSettlementPeriodEnum::class)],
            'currency' => ['required', 'string'],
            'is_active' => ['boolean'],
            'author_id' => ['required', 'integer'],
            // Rentals
            'rentals.*' => ['integer'],
            'rentals' => [
                'required',
                'array',
                // Validate if rentals belong to the team. This is important here to avoid performance issues if checking it on the cron job creating the settlements
                function ($attribute, $value, $fail) {
                    $teamRentals = Rental::query()
                        ->withTrashed()
                        ->onTeam($this->team_id)
                        ->whereId($value)
                        ->count();
                    abort_unless(count($value) == $teamRentals, 404, "We couldn't find all those rentals in your team.");
                },
            ],
            'sources' => ['nullable', 'array'],
            'sources.*' => ['integer'],
            'on_check_out' => ['required', 'boolean'],
            'on_net_income' => ['required', 'boolean'],
            // Payees
            'payees.*' => ['integer'],
            'payees' => [
                'array',
                // Validate if payees belong to the team. This is important here to avoid performance issues if checking it on the cron job creating the settlements
                function ($attribute, $value, $fail) {
                    $teamPayees = Payee::query()
                        ->whereTeamId($this->team_id)
                        ->whereIn('id', $value)
                        ->count();
                    abort_unless(count($value) == $teamPayees, 404, "We couldn't find all those recipients in your team.");
                },
            ],
        ];
    }

    /**
     * This method is executed after validation of the input.
     */
    public function withValidator(Validator $validator): void
    {
        // If we have another active ScheduledSettlement with at least one of the rentals we are trying to save AND the same currency,
        // we should not allow it.
        $repeatedRentalsId = collect();

        ScheduledSettlement::query()
            ->select('rentals')
            ->onTeam($this->team_id)
            ->whereActive()
            ->whereCurrency($this->currency)
            ->where('on_check_out', $this->on_check_out)
            ->when($this->id,
                fn (ScheduledSettlementQuery $query) => $query->where('id', '<>', $this->id)
            )->get()
            ->filter(function (ScheduledSettlement $scheduledSettlement) use (&$repeatedRentalsId) {
                $repeatedRentals = array_intersect($this->rentals, $scheduledSettlement->rentals);

                if (! empty($repeatedRentals)) {
                    $repeatedRentalsId->push(...$repeatedRentals);
                }

                return count($repeatedRentals) > 0;
            })
            ->whenNotEmpty(function ($collection) use ($validator, $repeatedRentalsId) {
                $validator->after(function (Validator $validator) use ($repeatedRentalsId) {
                    $validator->errors()
                        ->add('rentals',
                            __('messages.errors.accounting.scheduled_settlements.already_exists',
                                ['rental_ids' => $repeatedRentalsId->implode('-')])
                        );
                });
            });
    }

    public function toDto(): ScheduledSettlementDto
    {
        return ScheduledSettlementDto::from($this->toArray());
    }
}
