<?php

namespace App\Http\Resources;

use App\Actions\ChannelManager\GetAmenitiesPerRoomRentalsUnitedAction;
use App\Actions\Providers\RentalsUnited\GetCountriesRentalsUnitedAction;
use App\Enum\ImageTypeEnum;
use App\Models\Amenity;
use App\Models\ChannelManager\LanguagesCM;
use App\Models\ChannelManager\LicenseExtraInfo;
use App\Models\ChannelManager\OTAPropertyType;
use App\Models\ChannelManager\PropertyType;
use App\Models\Rental;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;

/**
 * Class RentalResource.
 *
 * @mixin Rental
 */
class ExtendedRentalDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        // Set locale using: app locale, user locale, input locale
        $appLocale = App::getLocale();
        $userLocale = Str::before($request->user()->locale, '_');
        $inputLocale = request()->input('locale');
        $locale = $userLocale ?: $inputLocale ?: $appLocale;

        // Format images types
        $imageTypes = $this->getImagesTypes($locale);

        // Format amenities
        $listOfAmenities = $this->getListOfAmenities($locale);
        $enabledAmenitiesId = collect($listOfAmenities)->pluck('id');

        //  License
        $licenseFields = collect(LicenseExtraInfo::ROWS)
            ->map(fn (array $group) => [
                'name' => __("channel_manager.license_groups.{$group['group_key']}"),
                'fields' => collect($group['fields'])
                    ->map(fn (array $field) => $this->getLicenseGroup($field)),
            ]);

        return [
            'license_fields' => $licenseFields,
            'image_types' => $imageTypes,
            'property_types' => PropertyType::query()->get()->toArray(),
            'ota_property_types' => OTAPropertyType::query()->get(['id', 'name'])->toArray(),
            'amenities' => $listOfAmenities,
            'amenities_groups' => $this->getAmenitiesGroups($enabledAmenitiesId),
            'amenities_no_group' => $this->getAmenitiesWithoutGroup($enabledAmenitiesId),
            'amenities_per_room' => GetAmenitiesPerRoomRentalsUnitedAction::run($enabledAmenitiesId),
            'languages' => LanguagesCM::query()->get()->toArray(),
        ];
    }

    private function getAmenitiesGroups(Collection $enabledAmenitiesId): array
    {
        return collect([
            'bedroom_amenity' => [257],
            'bathroom_amenity' => [81],
            'toilet_amenity' => [37],
            'living_room' => [72, 97, 98, 99, 106, 161, 163, 182, 188, 192, 193, 197, 200, 203, 204, 237, 248, 249, 277, 344, 349, 353, 363, 364, 372, 382, 388, 397, 428, 432, 454, 455, 456, 459, 460, 473, 481, 483, 500, 503, 508, 509, 514, 517, 629, 850, 890, 893, 954, 984, 1000, 1044, 1068, 1255, 1257, 1266, 1272, 1273, 1277, 1327, 1720, 1721, 1724, 1727, 1745, 1751, 1831, 1834, 1842, 1860, 1861, 1862],
            'bedroom_and_laundry' => [7, 11, 21, 55, 61, 66, 69, 70, 71, 78, 87, 134, 137, 201, 209, 210, 234, 257, 311, 323, 324, 341, 360, 384, 396, 412, 440, 444, 453, 485, 499, 501, 515, 589, 599, 600, 613, 614, 624, 655, 658, 699, 715, 767, 778, 939, 957, 972, 974, 988, 1210, 1215, 1256, 1263, 1264, 1268, 1271, 1276, 1322, 1344, 1345, 1346, 1347, 1348, 1349, 1396, 1397, 1436, 1437, 1439, 1441, 1442, 1443, 1444, 1821, 1825, 1841, 1849, 1865, 1866],
            'bathroom' => [6, 8, 27, 29, 33, 35, 36, 37, 46, 50, 52, 53, 79, 81, 82, 239, 245, 252, 315, 321, 351, 395, 409, 414, 422, 434, 446, 447, 458, 482, 505, 516, 607, 654, 760, 775, 940, 941, 956, 971, 973, 996, 1056, 1061, 1063, 1073, 1089, 1201, 1212, 1220, 1222, 1226, 1229, 1240, 1258, 1259, 1260, 1261, 1321, 1406, 1407, 1408, 1409, 1410, 1411, 1412, 1413, 1414, 1415, 1417, 1418, 1419, 1420, 1421, 1422, 1424, 1425, 1426, 1427, 1429, 1430, 1431, 1432, 1489, 1495, 1528, 1839, 1840, 1857, 1858, 1859],
            'entertainment' => [19, 22, 23, 24, 25, 74, 166, 167, 198, 294, 325, 326, 327, 345, 359, 371, 379, 389, 418, 419, 421, 445, 468, 480, 634, 647, 650, 657, 660, 683, 691, 707, 719, 720, 726, 730, 738, 742, 749, 758, 762, 770, 774, 784, 788, 790, 797, 817, 832, 847, 851, 852, 884, 889, 897, 901, 945, 946, 947, 1019, 1040, 1041, 1043, 1045, 1046, 1047, 1049, 1050, 1051, 1052, 1057, 1058, 1064, 1065, 1067, 1069, 1072, 1076, 1088, 1092, 1097, 1098, 1099, 1102, 1103, 1104, 1106, 1107, 1110, 1111, 1112, 1113, 1114, 1116, 1120, 1121, 1123, 1124, 1141, 1154, 1157, 1161, 1177, 1188, 1191, 1291, 1331, 1332, 1333, 1334, 1335, 1336, 1366, 1367, 1368, 1369, 1371, 1372, 1373, 1374, 1375, 1376, 1377, 1383, 1386, 1387, 1388, 1389, 1390, 1391, 1392, 1404, 1447, 1459, 1460, 1461, 1468, 1548, 1671, 1695, 1705, 1843],
            'kitchen_and_dining' => [2, 3, 13, 17, 32, 94, 101, 102, 110, 114, 115, 119, 123, 124, 125, 128, 129, 130, 131, 135, 140, 142, 144, 146, 147, 150, 151, 152, 157, 189, 235, 250, 258, 331, 342, 347, 348, 361, 362, 373, 376, 380, 390, 413, 435, 436, 437, 439, 462, 463, 464, 506, 507, 617, 619, 632, 639, 651, 653, 659, 665, 667, 671, 680, 681, 692, 697, 698, 716, 717, 798, 800, 818, 822, 825, 830, 838, 845, 877, 1048, 1108, 1262, 1270, 1274, 1288, 1289, 1292, 1293, 1295, 1296, 1299, 1300, 1302, 1307, 1308, 1311, 1313, 1315, 1319, 1324, 1338, 1340, 1352, 1353, 1354, 1355, 1356, 1357, 1358, 1359, 1360, 1361, 1362, 1363, 1364, 1594, 1595, 1596, 1597, 1598, 1599, 1600, 1602, 1603, 1604, 1605, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615, 1616, 1617, 1618, 1619, 1620, 1621, 1622, 1623, 1624, 1625, 1626, 1627, 1628, 1629, 1630, 1631, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644, 1645, 1646, 1647, 1648, 1649, 1650, 1651, 1652, 1653, 1654, 1655, 1656, 1657, 1658, 1659, 1660, 1661, 1662, 1663, 1664, 1665, 1666, 1667, 1668, 1669, 1670, 1680, 1683, 1686, 1688, 1691, 1692, 1693, 1694, 1697, 1699, 1768, 1789, 1844, 1845, 1846, 1847, 1848],
            'internet_and_office' => [73, 174, 199, 205, 308, 309, 312, 314, 330, 339, 354, 368, 374, 375, 417, 426, 429, 433, 438, 461, 592, 593, 630, 668, 672, 673, 678, 687, 688, 690, 694, 703, 723, 734, 750, 761, 766, 777, 792, 807, 808, 809, 829, 846, 849, 855, 879, 885, 887, 891, 997, 1235, 1239, 1245, 1265, 1267, 1269, 1370, 1378, 1379, 1380, 1382, 1384, 1385, 1393, 1398, 1399, 1403, 1729, 1730, 1731, 1732, 1782, 1826, 1827, 1828],
            'parking_and_transportation' => [215, 295, 296, 302, 320, 450, 478, 504, 633, 645, 656, 675, 676, 686, 689, 702, 704, 753, 793, 794, 795, 803, 804, 805, 806, 827, 836, 853, 909, 913, 931, 932, 933, 991, 1156, 1180, 1219, 1227, 1394, 1467, 1471, 1475, 1538, 1539, 1540, 1541, 1542, 1543, 1545, 1546, 1549, 1550, 1551, 1552, 1553, 1554, 1555, 1556, 1557, 1558, 1559, 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1568, 1569, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 1577, 1578, 1579, 1580, 1581, 1582, 1583, 1584, 1585, 1586, 1587, 1588, 1589, 1590, 1592, 1593, 1728, 1743, 1746, 1749, 1787, 1833, 1855],
            'family_amenities' => [56, 63, 208, 385, 466, 596, 597, 622, 635, 640, 674, 713, 724, 828, 833, 839, 840, 841, 857, 858, 859, 860, 861, 862, 864, 868, 869, 870, 876, 899, 914, 929, 930, 942, 1323, 1337, 1341, 1342, 1706, 1736, 1823],
            'outdoor_spaces' => [89, 91, 92, 93, 96, 100, 227, 253, 322, 346, 391, 408, 415, 449, 451, 457, 465, 479, 510, 511, 512, 513, 594, 603, 604, 609, 611, 612, 623, 625, 626, 637, 638, 641, 642, 648, 662, 700, 705, 709, 710, 711, 714, 729, 731, 732, 735, 736, 737, 739, 740, 744, 748, 754, 755, 756, 757, 763, 764, 773, 782, 801, 811, 815, 816, 820, 823, 824, 826, 837, 854, 894, 895, 902, 905, 906, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 937, 938, 944, 948, 949, 950, 951, 952, 989, 998, 999, 1002, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1020, 1021, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030, 1032, 1033, 1035, 1036, 1037, 1038, 1039, 1059, 1060, 1062, 1071, 1075, 1077, 1079, 1080, 1081, 1083, 1084, 1085, 1086, 1087, 1090, 1094, 1095, 1096, 1100, 1105, 1115, 1119, 1122, 1125, 1126, 1127, 1128, 1129, 1130, 1131, 1132, 1133, 1134, 1135, 1136, 1137, 1138, 1139, 1140, 1142, 1143, 1144, 1145, 1148, 1149, 1150, 1151, 1152, 1153, 1155, 1159, 1162, 1163, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1172, 1173, 1174, 1175, 1176, 1178, 1179, 1181, 1182, 1183, 1184, 1185, 1186, 1189, 1192, 1193, 1194, 1195, 1196, 1197, 1198, 1199, 1217, 1244, 1339, 1401, 1446, 1448, 1449, 1450, 1451, 1452, 1453, 1454, 1455, 1456, 1457, 1458, 1466, 1472, 1476, 1477, 1478, 1487, 1492, 1547, 1601, 1672, 1673, 1674, 1675, 1676, 1677, 1678, 1703, 1708, 1734, 1744, 1750, 1851, 1852, 1853, 1854, 1867, 1868, 1869],
            'healthy_safety' => [365, 601, 725, 781, 783, 799, 802, 821, 883, 943, 955, 976, 981, 1055, 1070, 1074, 1093, 1211, 1221, 1231, 1232, 1233, 1234, 1241, 1243, 1246, 1251, 1252, 1253, 1325, 1326, 1328, 1329, 1330, 1402, 1480, 1485, 1488, 1490, 1491, 1494, 1496, 1503, 1504, 1515, 1516, 1518, 1527, 1529, 1735, 1748, 1756, 1757, 1758, 1759, 1760, 1761, 1772, 1773, 1779, 1781, 1785, 1796, 1797, 1799, 1801, 1802, 1803, 1807, 1809, 1811, 1812, 1813, 1815, 1837],
            'accessibility' => [281, 652, 682, 745, 814, 953, 1202, 1203, 1204, 1205, 1206, 1207, 1208, 1209, 1213, 1214, 1216, 1218, 1223, 1224, 1225, 1228, 1230, 1237, 1238, 1248, 1249, 1250, 1350, 1381, 1830],
            'cleaning_services' => [143, 225, 231, 232, 233, 355, 448, 628, 819, 848, 900, 911, 960, 966, 978, 992, 1278, 1282, 1283, 1284, 1285, 1433, 1435, 1753, 1754, 1755, 1766, 1767, 1769, 1770, 1771, 1780, 1798],
            'check_in_and_support' => [627, 679, 693, 695, 771, 865, 875, 908, 961, 962, 963, 965, 1499, 1544, 1685, 1741, 1747, 1762, 1763, 1764, 1817, 1818, 1819, 1820, 1829],
            'staff_and_services' => [298, 420, 472, 598, 608, 616, 644, 669, 684, 746, 765, 769, 910, 915, 916, 1286, 1287, 1395, 1784, 1795, 1871],
            'property_vacancy' => [747, 863, 1003, 1031, 1034, 1242, 1774, 1775, 1776, 1786, 1850, 1856],
            'safety_policies' => [751, 856, 1742, 1765, 1783, 1804, 1805, 1836, 1863],
            'dining_safety' => [1298, 1303, 1305, 1312, 1314, 1788, 1790],
            'country_specific_requirements' => [1777, 1778, 1810, 1814],
            'general_policies' => [595, 733, 813, 842, 843, 812, 871, 872, 874, 936, 959, 995, 1290, 1733, 1873, 1874, 1875, 1876, 1877],
            'other_amenities' => [4, 118, 127, 133, 332, 334, 336, 337, 338, 352, 404, 452, 467, 476, 484, 491, 497, 590, 591, 602, 605, 606, 615, 618, 620, 621, 631, 636, 643, 646, 649, 663, 664, 666, 670, 677, 685, 696, 701, 706, 708, 712, 718, 721, 722, 727, 728, 741, 743, 752, 759, 768, 772, 779, 780, 786, 791, 796, 810, 834, 835, 844, 866, 867, 873, 878, 880, 882, 886, 888, 892, 898, 903, 904, 907, 912, 917, 918, 934, 935, 958, 964, 967, 968, 969, 970, 977, 979, 980, 986, 990, 993, 994, 1018, 1042, 1053, 1054, 1066, 1078, 1091, 1101, 1109, 1117, 1118, 1146, 1147, 1158, 1160, 1187, 1254, 1297, 1301, 1304, 1306, 1309, 1310, 1320, 1400, 1428, 1434, 1440, 1445, 1462, 1463, 1464, 1465, 1469, 1470, 1473, 1479, 1481, 1483, 1486, 1493, 1497, 1500, 1501, 1502, 1505, 1506, 1507, 1508, 1509, 1510, 1511, 1512, 1513, 1514, 1517, 1519, 1520, 1521, 1522, 1523, 1524, 1525, 1526, 1530, 1687, 1689, 1698, 1700, 1701, 1702, 1704, 1707, 1709, 1710, 1711, 1712, 1713, 1714, 1715, 1716, 1717, 1718, 1719, 1722, 1723, 1725, 1726, 1752, 1791, 1792, 1793, 1794, 1832, 1835, 1864, 1870],
            'climate_control_and_confort' => [9, 58, 180, 181, 186, 187, 261, 328, 490, 661, 776, 785, 789, 881, 896, 985, 987, 1438, 1838],
        ])->map(fn (array $rows) => collect($rows)->intersect($enabledAmenitiesId)->toArray())
            ->toArray();
    }

    private function getAmenitiesWithoutGroup(Collection $enabledAmenitiesId): array
    {
        $amenitiesWithGroup = collect($this->getAmenitiesGroups($enabledAmenitiesId))->flatten();

        return $enabledAmenitiesId->diff($amenitiesWithGroup)->toArray();
    }

    protected function getImagesTypes(mixed $locale): Collection
    {
        return collect(ImageTypeEnum::cases())->map(fn ($e) => [
            'id' => $e->value,
            'name' => __(key: 'channel_manager.image_type.'.$e->name, locale: $locale),
        ]);
    }

    protected function getListOfAmenities(mixed $locale)
    {
        return Amenity::query()
            ->where('public', '=', true)
            ->get()
            ->map(
                fn (Amenity $amenity) => [
                    'id' => $amenity->external_id,
                    'popular' => $amenity->popular,
                    'default_name' => $amenity->default_name,
                    'max_units' => $amenity->max_units,
                    'name' => __(key: 'channel_manager.amenity.'.$amenity->key, locale: $locale),
                ]
            )->toArray();
    }

    public function getLicenseGroup(array $array): array
    {
        $key = $array['key'];

        $values = data_get($array, 'values');
        if (! is_null($values)) {
            $values = collect($values)->map(function (array $array) use ($key) {
                $valKey = $array['key'];

                return [
                    'name' => __("channel_manager.license.{$key}_{$valKey}_name"),
                    'description' => trans()->has("channel_manager.license.{$key}_{$valKey}_description") ? __("channel_manager.license.{$key}_{$valKey}_description") : null,
                    'field' => $array['field'],
                    'country' => data_get($array, 'country'),
                ];
            });
        }
        $type = $array['type'];
        if ($type == 'country') {
            $values = collect(GetCountriesRentalsUnitedAction::run())->values()
                ->map(fn (array $country) => ['name' => $country['name'], 'field' => $country['name']]);
            // Should be this according to doc: 'field' => strval($country['id'])]);
            $type = 'enum';
        }

        if ($type == 'class') {
            $values = collect(call_user_func([$array['class_name'], $array['class_method']]))
                ->map(fn ($value, $key) => ['name' => $value, 'field' => is_numeric($key) ? strval(intval($key)) : $key])
                ->values();
            $type = 'enum';
        }

        return [
            'name' => __("channel_manager.license.{$key}_name"),
            'description' => trans()->has("channel_manager.license.{$key}_description") ? __("channel_manager.license.{$key}_description") : null,
            'field' => $array['field'],
            'type' => $type,
            'country' => data_get($array, 'country'),
            'depends_on' => data_get($array, 'depends_on'),
            'depends_value' => data_get($array, 'depends_value'),
            'values' => $values,
        ];
    }
}
