<?php

namespace App\Http\Resources;

use App\Models\BookingFee;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class BookingFee.
 *
 * @mixin BookingFee
 *
 * @extends JsonResource<BookingFee>
 */
class BookingFeeContextResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request
     */
    public function toArray($request): array
    {
        return [
            'price' => $this->price,
            'kind' => $this->kind,
            'name' => $this->fee_name,
        ];
    }
}
