<?php

namespace App\Http\Resources;

use App\DTO\UserSettingsData;
use App\Models\ApiUser;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Laravel\Cashier\Subscription;

/**
 * Class UserResource.
 *
 * @mixin ApiUser
 */
class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'external_id' => $this->external_id,
            'name' => $this->name,
            'email' => $this->email,
            'locale' => $this->locale,
            'photo_url' => $this->photo_url,
            'photo' => $this->photo_url,
            'country_code' => $this->country_code,
            'phone' => $this->phone,
            'current_team_id' => $this->current_team_id,
            'role' => $this->ninja_role,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            'alert_count' => $this->alert_count,
            'permissions' => new TeamUsersPermissionResource($this->getPermissions()),
            'settings' => UserSettingsData::from($this->settings),
            'provider_id' => $this->provider(),
            'subscription' => $this->subscription(),
            'is_professional' => $this->isProfessional(),
            'can_use_professional' => $this->isProfessional(),  // To remove
            'pre_check_in_enabled' => $this->isProfessional(),  // To remove
        ];
    }

    private function isProfessional(): bool
    {
        if ($this?->current_team_id) {
            return false;
        }

        return $this->team->isProfessional();
    }

    private function provider(): int
    {
        if ($this != null && $this->current_team_id != null) {
            return $this->team->provider_id;
        }

        return 0;
    }

    private function subscription(): ?Subscription
    {
        if ($this != null && $this->current_team_id != null) {
            return $this->team->subscription();
        }

        return null;
    }
}
