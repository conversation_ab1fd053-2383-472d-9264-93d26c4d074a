<?php

namespace App\Http\Resources\AllowedActions;

use App\Actions\Bookings\Actions\GetModalForRejectBookingRequestAction;
use App\DataProviders\Providers\NoProvider;
use App\Enum\BookingStatusEnum;
use App\Models\Booking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Booking Allowed Actions.
 *
 * @mixin Booking
 */
class AllowedBookingActions extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        /** @var User $user */
        $user = request()->user();
        if (is_null($user)) {
            return [];
        }

        $access = collect($user->getRentalsForUser(true))->contains($this->rental_id);

        $noProviderBooking = $this->provider_id == NoProvider::ID;
        $internalBooking = $this->provider()->allowsBookingUpdates();
        $block = $this->status == BookingStatusEnum::UNAVAILABLE;

        $accessAndInternalBooking = $access && $internalBooking;
        $accessAndNoProviderBooking = $access && $noProviderBooking;

        $userCanSend = $access && $user->permissionFor('can_send_inbox_messages');
        $userCanManage = $access && $user->permissionFor('can_manage_bookings');
        $canUseHomeAutomationDevices = $access && $user->permissionFor('use_home_automation_devices');

        return [
            'can_see' => $access,
            'can_edit' => $this->isEditable() && (
                ($accessAndNoProviderBooking && $user->permissionFor('can_manage_bookings')) ||
                ($accessAndInternalBooking && $user->permissionFor('edit_fees_and_taxes'))
            ),
            // Booking actions
            // Leads or Tentatives.
            'can_convert_tentative' => $userCanManage && $this->canBeConvertedToTentative(),
            'can_confirm' => $userCanManage && $this->canBeConfirmed() && ($this->status->blocksAvailability() || $this->isPeriodAvailable()),
            'can_accept' => $userCanManage && $this->canBeAccepted() && $this->isPeriodAvailable(),
            'can_reject' => $userCanManage && $this->canBeRejected(),
            'can_cancel' => $access && $this->canBeCanceled() && (
                (! $block && $user->permissionFor('can_manage_bookings')) || // Bookings
                ($block && $user->permissionFor('block_dates') && ($user->isRentalManagerOrAbove() || $this->created_by === $user->id)) // Blocks
            ),
            'can_archive' => $access && $this->canBeArchived(),

            'reject_modal' => ($userCanManage && $this->canBeRejected()) ? $this->getRejectModal() : null,
            // Old namings, deprecated
            'can_accept_request' => $access && $this->status->isRequest() && $user->permissionFor('can_manage_bookings') && $this->isPeriodAvailable(),
            'can_reject_request' => $access && $this->status->isRequest() && $user->permissionFor('can_manage_bookings'),

            // Other
            'can_view_comments' => $access && $user->permissionFor('view_booking_comments'),
            'can_add_comments' => $access && $user->permissionFor('view_booking_comments'),
            'can_edit_expected_checkin_time' => $access && $user->permissionFor('edit_time_of_arrival_and_departure'),
            'can_edit_expected_checkout_time' => $access && $user->permissionFor('edit_time_of_arrival_and_departure'),
            'can_add_fees' => $accessAndInternalBooking && ! $block && $user->permissionFor('edit_fees_and_taxes'),
            'can_add_payments' => $access && ! $block && $user->permissionFor('add_payment'),
            'can_add_tags' => $access && $user->permissionFor('edit_booking_tags'),
            // The inbox should be accessible to the connected rentals (provider of the rental not from the booking).
            // As you have multiple OTA, we centralize comms for you. This must include direct bookings provider_id = 0
            'can_access_inbox' => $access && $user->permissionFor('can_access_inbox') && $this->rental->hasCentralizedInbox(),
            'can_send_inbox_messages' => $userCanSend && $this->rental->hasCentralizedInbox(),
            'cant_send_inbox_message_in_source' => false, // From now on we will allow messages from all sources and send it accordingly
            'can_use_home_automation_devices' => $canUseHomeAutomationDevices,
            'can_create_authorisations' => $canUseHomeAutomationDevices && $this->status->canCreateAuthorisations() && $this->client,

            // Deprecated
            'can_edit_guest' => $accessAndNoProviderBooking && $user->isRentalManagerOrAbove(),
            'can_edit_guest_count' => $accessAndNoProviderBooking && $user->isRentalManagerOrAbove(),
            'can_edit_dates' => $accessAndNoProviderBooking && $user->isRentalManagerOrAbove() && $this->rental_ical_id === null,
            'can_edit_source' => $accessAndNoProviderBooking && $user->isRentalManagerOrAbove(),
            'can_edit_rental_price' => $accessAndNoProviderBooking && $user->isRentalManagerOrAbove(),
            'can_edit_damage_deposit' => $accessAndNoProviderBooking && $user->isRentalManagerOrAbove(),
            'can_edit_notes' => $access && $user->canEditInternalNote(),
        ];
    }

    private function getRejectModal()
    {
        return GetModalForRejectBookingRequestAction::run($this->resource);
    }
}
