<?php

namespace App\Http\Resources\AllowedActions;

use App\DataProviders\Providers\NoProvider;
use App\Models\Rental;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Rental Allowed Actions.
 *
 * @mixin Rental
 */
class AllowedRentalActions extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        /** @var User $user */
        $user = request()->user();
        if (is_null($user)) {
            return [];
        }

        $access = collect($user->getRentalsForUser(true))->contains($this->id);

        return [
            'status' => $this->getStatus(),
            'can_see' => $access,
            'can_manage_export_icals' => $access && $user->isAdminOrAbove() && $user->team->config()->canExportIcal(),
            'can_manage_import_icals' => $access && $user->isAdminOrAbove() && $this->canUseInputIcal(),

            'can_create_booking' => $access && $this->is_rental_ninja && $user->permissionFor('can_manage_bookings'),
            'can_create_blocking' => $access && $user->permissionFor('block_dates'),

            'can_manage_channel_manager_connections' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja && $this->team->cm_enabled,

            // TO DELETE
            'can_edit_pms_features' => $access && $user->isAdminOrAbove() && $this->provider_id == NoProvider::ID,
            'can_edit_channel_manager_features' => $access && $user->isAdminOrAbove() && $this->provider_id == NoProvider::ID && $this->team->cm_enabled,
            // END TO DELETE

            'can_view_rental_base_info' => $access,

            'can_edit_rental_base_info' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja,
            'can_edit_rental_legal_details' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja,
            'can_edit_rental_pictures' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja,
            'can_edit_rental_rates' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja,
            'can_edit_rental_amenities' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja,
            'can_edit_rental_description' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja,
            'can_edit_rental_location' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja && ! $this->has_ever_been_in_ota,

            'can_edit_rental_guest_info' => ($user->getPermissions()->guest_app_general_settings > 0) || ($access && $user->isAdminOrAbove() && $this->is_rental_ninja),

            'can_edit_guest_module_features' => $user->getPermissions()->guest_app_general_settings > 0,
            'can_edit_pcif_features' => $user->getPermissions()->guest_app_pre_check_in_form_settings > 0,

            'can_delete_rental' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja,
            'can_edit_upsales' => $access && $user->isAdminOrAbove() && $this->is_rental_ninja && $user->canManageGuestApp(),
        ];
    }

    private function getStatus(): string
    {
        if (! $this->is_rental_ninja) {
            return 'provider';
        }
        if (! $this->cm_active) {
            return 'internal';
        }
        if (! $this->external_id) {
            return 'uncompleted';
        }
        if (! $this->has_ever_been_in_ota) {
            return 'ready';
        }

        return 'connected';
    }
}
