<?php

namespace App\Http\Resources;

use App\Models\TaskItem;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class TaskItemResource.
 *
 * @mixin TaskItem
 */
class TaskItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return array_merge(parent::toArray($request), [
            'description' => $this->description,
            'completed_at' => apiDateFromCarbon($this->completed_at),
            'pictures' => TaskItemPictureResource::collection($this->pictures),
            'allow_multiple_pictures' => true,
        ]);
    }
}
