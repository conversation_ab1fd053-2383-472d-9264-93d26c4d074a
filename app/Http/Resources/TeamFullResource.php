<?php

namespace App\Http\Resources;

use App\DataProviders\Providers\RentalsUnited;
use App\Models\PaymentGatewayAccount;
use App\Models\Team;
use App\Models\TeamSettings;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * Class UserResource: TeamCompleteDetails FE model.
 *
 * @mixin Team
 */
class TeamFullResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        // Lazy eager loading posters for slim_rental and SlimRentalResource
        $this->teamRentals->load('innerPoster');
        $this->ensureTeamSettingsExists();

        $gatewayValidationPending = $this->paymentGatewayAccounts
            ->where(fn (PaymentGatewayAccount $account) => ! empty($account->gateway_account_id) && ! $account->charges_enabled)
            ->isNotEmpty();

        return [
            'id' => $this->id,
            'owner_id' => $this->owner_id,
            'name' => $this->name,
            'slug' => Str::slug($this->name),
            'photo_url' => $this->photo_url,
            'email' => $this->email,
            'can_see_distribution_in_menu' => $this->config()->canUseChannelManager() && $this->provider_id != RentalsUnited::ID,
            'can_access_distribution' => $this->cm_enabled && $this->config()->canUseChannelManager(),
            'verified' => $this->verified,
            'communication_locate' => $this->getTeamLocale(),
            'app_locate' => $this->app_locale,
            'website' => $this->website,
            'currency' => $this->currency,
            'country' => $this->country,
            'trial_ends_at' => $this->trial_ends_at,
            'has_ever_been_subscribed' => $this->subscriptions_count > 0,
            'provider_account_id' => 0,
            'account_id' => 0,
            'rentals' => $this->rentals,
            'slim_rentals' => SlimRentalResource::collection($this->teamRentals),
            'arrival_time' => $this->arrival_time,
            'departure_time' => $this->departure_time,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'synced_at' => $this->synced_at,
            'force_synced_at' => $this->force_synced_at,
            'provider_id' => $this->provider_id,
            'owner' => new SlimUserResource($this->owner),
            'users' => SlimUserResource::collection($this->users->load(['userRentals', 'permissions'])),
            'sms_price' => ($this->teamSettings?->getSmsPriceInCents() ?? 0) / 100,
            'scan_price' => ($this->teamSettings?->getScanPriceInCents() ?? 0) / 100,
            'upscale_price' => ($this->teamSettings?->getUpscalePriceInCents() ?? 0) / 100,
            'ai_price' => ($this->teamSettings?->getAiPriceInCents() ?? 0) / 100,
            'content_ai_price' => ($this->teamSettings?->getContentAiPriceInCents() ?? 0) / 100,
            'booking_engine_commission_percentage' => $this->teamSettings->getBookingEngineCommissionPercentage(),
            'upsales_commission_percentage' => $this->teamSettings->getUpsalesCommissionPercentage(),
            'white_label_id' => $this->teamSettings?->white_label_id,
            'provider' => $this->mainProvider()->providerConfiguration(),
            'flavor' => $this->config()->flavorConfiguration(),
            'is_gateway_validation_pending' => $gatewayValidationPending,
        ];
    }

    // Team Settings should be created on TeamCreatedListener, however it is not created on some teams.
    private function ensureTeamSettingsExists()
    {
        if ($this->teamSettings) {
            return;
        }
        TeamSettings::create(['team_id' => $this->id]);
        $this->load('teamSettings');
        pnLog("[WARNING] Team $this->id had not team settings created. Created in TeamFullResource.php");
    }
}
