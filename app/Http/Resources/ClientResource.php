<?php

namespace App\Http\Resources;

use App\DataProviders\Providers\NoProvider;
use App\Models\Client;
use Illuminate\Http\Resources\Json\JsonResource;
use Throwable;

/**
 * Class ClientResource.
 *
 * @mixin Client
 *
 * @extends JsonResource<Client>
 */
class ClientResource extends JsonResource
{
    public function toArray($request): array
    {
        $primary = $this->getPrimaryEmail();

        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'is_external' => $this->provider_id !== NoProvider::ID,
            'fullname' => $this->fullname ?? '',
            'firstname' => $this->firstname,
            'lastname' => $this->lastname,
            'notes' => $this->notes,
            'passport' => $this->passport,
            'preferred_locale' => $this->preferred_locale ?? 'en',
            'addresses' => $this->safeJsonToArray($this->addresses),
            'emails' => $this->safeJsonToArray($this->emails),
            'primary_email' => $primary,
            'phones' => array_values($this->getPhonesCollection()->toArray()),
            'bookings' => SlimBookingResource::collection($this->whenLoaded('bookings')),
            'created_at' => apiDateFromTimestamp($this->created_at),
            'updated_at' => apiDateFromTimestamp($this->updated_at),
        ];
    }

    private function safeJsonToArray(?string $json): ?array
    {
        try {
            return array_values(json_decode($json, true));
        } catch (Throwable) {
            return null;
        }
    }
}
