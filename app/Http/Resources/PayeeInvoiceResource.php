<?php

namespace App\Http\Resources;

use App\Models\PayeeInvoice;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class PayeeInvoiceResource.
 *
 * @mixin PayeeInvoice
 *
 * @extends JsonResource<PayeeInvoice>
 */
class PayeeInvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $tr = array_merge(parent::toArray($request), [
            'created_at' => $this->created_at->toIso8601ZuluString(),
            'updated_at' => $this->updated_at->toIso8601ZuluString(),
            'issued_at' => $this->issued_at->toIso8601ZuluString(),
            'url' => route('ninjaDownloader', ['hash' => $this->downloadHash?->hash]),
        ]
        );
        unset($tr['download_hash']);

        return $tr;
    }
}
