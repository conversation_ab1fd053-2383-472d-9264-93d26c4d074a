<?php

namespace App\Http\Resources;

use App\Models\BookingPayment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class BookingPaymentResource.
 *
 * @mixin BookingPayment
 *
 * @extends JsonResource<BookingPayment>
 */
class BookingPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'booking_id' => $this->booking_id,
            'provider_id' => $this->provider_id,
            'currency' => $this->currency,
            'amount_in_cents' => $this->amount_in_cents,
            'kind' => strtolower($this->kind) == 'bookingsync' ? 'smily' : $this->kind,
            'stripe_payment_intent_id' => $this->stripe_payment_intent_id,
            'hold_until' => $this->hold_until,
            'stripe_captured' => $this->stripe_captured,
            'stripe_cents_fee' => $this->stripe_cents_fee,
            'stripe_fee_currency' => $this->stripe_fee_currency,
            'notes' => $this->notes,
            'email' => $this->email,
            'author' => $this->authorName(),
            'author_id' => $this->author_id,
            'locked' => $this->locked,
            'paid_at' => $this->paid_at,
            'canceled_at' => $this->canceled_at,
            'pictures' => PictureResource::collection($this->pictures),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
