<?php

namespace App\Http\Resources;

use App\Actions\Inbox\GetAttachmentPathAction;
use App\Models\MessageAttachment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin MessageAttachment
 */
class MessageAttachmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'message_id' => $this->message_id,
            'file_name' => GetAttachmentPathAction::getFileName($this->url),
            // I don't want to share s3 urls, to prevent showing internal organization of files to attackers
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
