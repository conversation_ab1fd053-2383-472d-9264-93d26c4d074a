<?php

namespace App\Http\Resources;

use App\Models\AccountingAdvancedSettings;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class AccountingAdvancedSettingsResource.
 *
 * @mixin AccountingAdvancedSettings
 *
 * @extends JsonResource<AccountingAdvancedSettings>
 */
class AccountingAdvancedSettingsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'enabled' => $this->enabled,
            'calculate_payments_on_net_income' => true, //TODO: delete in the future. Not used. We pass true because now all DB entries are true
            'sources_commission_strategies' => SourceCommissionStrategyResource::collection($this->sourceCommissionStrategies),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
