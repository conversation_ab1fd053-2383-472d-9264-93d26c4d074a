<?php

namespace App\Http\Resources;

use App\Enum\PayoutDetailOriginalTypeEnum;
use App\Models\PartialPayout;
use App\Models\PayeeInvoice;
use App\Models\Payout;
use App\Models\PayoutDetail;
use App\Models\Settlement;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class PayoutResource.
 *
 * @mixin Payout
 */
class PayoutResource extends JsonResource
{
    public $maps;

    public function __construct(Payout $resource, bool $make_maps = false)
    {
        $this->maps = $make_maps;
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $settlement = Settlement::query()
            ->withTrashed()
            ->whereId($this->settlement_id)
            ->first();

        $payee = $this->payee;

        $partial_payouts = PartialPayout::query()
            ->wherePayoutId($this->id)
            ->orderBy('paid_at')
            ->get();

        $invoices = PayeeInvoice::query()
            ->wherePayoutId($this->id)
            ->whereSettlementId($this->settlement_id)
            ->whereIssuer($this->payee_id)
            ->orderBy('id')
            ->get();

        $payout_details = PayoutDetail::query()
            ->wherePayoutId($this->id)
            ->orderBy('rental_id', 'desc')
            ->orderBy('sort_value')
            ->orderBy('name')
            ->get();

        // Let's sort out properly. We want to sort ascending by sort_value (which is the check-in timestamp),
        // but manually introduced items are given -1. We don't want them at the beginning, but at the end.
        // Although this is a little bit verbose, we think it's the most efficient way.
        $manual_payout_details = $payout_details->where('sort_value', '<=', 0);
        if ($manual_payout_details->isNotEmpty()) {
            $payout_details = $payout_details->where('sort_value', '>', 0);
            $manual_payout_details->each(fn ($manual_detail) => $payout_details->push($manual_detail));
        }

        $details_initial_value = [
            PayoutDetailOriginalTypeEnum::BOOKING->value => 0.0,
            PayoutDetailOriginalTypeEnum::FEE->value => 0.0,
            PayoutDetailOriginalTypeEnum::TAX->value => 0.0,
            PayoutDetailOriginalTypeEnum::OTHER->value => 0.0,
            PayoutDetailOriginalTypeEnum::EXPENSE->value => 0.0,
        ];

        $details_summary = $payout_details->groupBy('original_type')
            ->map(fn (Collection $item, string $key) => $item->sum(fn (PayoutDetail $detail) => round($detail->value, 2)))
            // Round before sum: we show each payoutDetail rounded at 2, if we don't round before sum, sum amount may not match + do the same as we do in invoice generation
            ->toArray();

        $details_summary = array_merge($details_initial_value, $details_summary);

        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'settlement_id' => $this->settlement_id,
            'payee_id' => $this->payee_id,
            'bookings_value' => $details_summary['booking'],
            'fees_value' => $details_summary['fee'],
            'taxes_value' => $details_summary['tax'],
            'others_value' => $details_summary['other'],
            'expenses_value' => $details_summary['expense'],
            'value' => array_sum($details_summary),
            'currency' => $settlement->currency,
            'settlement_name' => $settlement->name,
            'settlement_start' => $settlement->start->toIso8601ZuluString(),
            'settlement_end' => $settlement->end->toIso8601ZuluString(),
            'payee_name' => $payee->name,
            'payee_email' => $payee->email,
            'payee_information' => $payee->payout_information,
            'payee_notes' => $payee->payout_notes,
            'payee_invoices_footer_information' => $payee->invoices_footer_information,
            'created_at' => $this->created_at->toIso8601ZuluString(),
            'updated_at' => $this->updated_at->toIso8601ZuluString(),
            'deleted_at' => $this->deleted_at?->toIso8601ZuluString(),
            'details' => PayoutDetailResource::collection($payout_details),
            'rentals' => $payout_details->pluck('rental_id')->unique(),
            'paid_amount' => $partial_payouts->sum(fn (PartialPayout $partialPayout) => round($partialPayout->amount, 2)) ?? 0.0, // round before sum
            'partial_payouts' => PartialPayoutResource::collection($partial_payouts),
            'invoices' => ! empty($invoices) ? PayeeInvoiceResource::collection($invoices) : null,

            $this->mergeWhen($this->maps == true, $this->getMap($payout_details, $settlement)),
        ];
    }

    private function getMap(Collection $payout_details, Settlement $settlement): array
    {
        return [
            'bookings' => $payout_details->where('original_type', PayoutDetailOriginalTypeEnum::BOOKING->value)
                ->groupBy('rental_id'),
            'fees' => $payout_details->where('original_type', PayoutDetailOriginalTypeEnum::FEE->value)
                ->groupBy('rental_id'),
            'taxes' => $payout_details->where('original_type', PayoutDetailOriginalTypeEnum::TAX->value)
                ->groupBy('rental_id'),
            'others' => $payout_details->where('original_type', PayoutDetailOriginalTypeEnum::OTHER->value)
                ->groupBy('rental_id'),
            'expenses' => $payout_details->where('original_type', PayoutDetailOriginalTypeEnum::EXPENSE->value)
                ->groupBy('rental_id'),
            'settlement' => $settlement,
        ];
    }
}
