<?php

namespace App\Http\Resources;

use App\Actions\Sync\UploadRentalPhotosToS3Action;
use App\Models\RentalPicture;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class RentalPictureResource.
 *
 * @mixin RentalPicture
 *
 * @extends JsonResource<RentalPicture>
 */
class RentalPictureResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $url = $this->url;
        // Old pictures have not been uploaded to S3. If all of them are migrated, this can be deleted.
        if (is_null($url) && ! is_null($this->provider_url)) {
            pnLog('[WARNING] We are still uploading rental pictures to S3 on the fly. Where does this comes from? This should not happen.', $this->team_id);
            // TODO: remove this in a while if doesn't happen
            UploadRentalPhotosToS3Action::dispatch($this->resource);
        }

        $url = $this->getPublicImageUrl();
        $sep = str_contains($url, '?') ? '&' : '?';

        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'rental_id' => $this->rental_id,
            'url' => $url,
            'giant_url' => $url.$sep.'q=90&w=2400',
            'grande_url' => $url.$sep.'q=90&w=1536',
            'large_url' => $url.$sep.'q=90&w=1200',
            'medium_url' => $url.$sep.'q=90&w=768',
            'compact_url' => $url.$sep.'q=90&w=384',
            'small_url' => $url.$sep.'q=90&w=192',
            'thumb_url' => $url.$sep.'q=90&w=96',
            'micro_url' => $url.$sep.'q=90&w=48',
            'order' => $this->order,
            'description' => $this->description,
            'image_type' => $this->image_type,
        ];
    }
}
