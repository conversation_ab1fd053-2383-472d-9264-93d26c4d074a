<?php

namespace App\Http\Resources;

use App\Models\NinjaPicture;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

/**
 * Class Picture.
 *
 * @mixin NinjaPicture
 */
class Picture extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request
     */
    public function toArray($request): array
    {
        return [
            'team_id' => $this->team_id,
            'rental_id' => $this->rental_id,
            'rental_name' => $this->rental_name,
            'booking_id' => $this->booking_id,
            'payment_id' => $this->payment_id,
            'start_at' => $this->start_at != null ? Carbon::createFromTimestampUTC($this->start_at) : null,
            'end_at' => $this->end_at != null ? Carbon::createFromTimestampUTC($this->end_at) : null,
            'full_size_url' => $this->full_size_url,
            'medium_size_url' => $this->medium_size_url,
            'small_size_url' => $this->small_size_url,
            'thumbnail_size_url' => substr_replace($this->thumbnail_size_url, '?w=250&h=250&fit=crop', strpos($this->thumbnail_size_url, '?')),
            'description' => $this->description,
            'author_id' => $this->author_id,
            $this->mergeWhen($this->author_id !== null, [
                'author' => User::whereId($this->author_id)
                    ->select(['id', 'name', 'email'])
                    ->first(),
            ]),

        ];
    }
}
