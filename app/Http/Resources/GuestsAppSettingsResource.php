<?php

namespace App\Http\Resources;

use App\Models\GuestsApplicationSettings;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class PreCheckInFormResource.
 *
 * @mixin  GuestsApplicationSettings
 */
class GuestsAppSettingsResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'team_id' => $this->team_id,
            'include_checkin_details_email' => $this->include_checkin_details_email,
            'include_checkin_details_view' => $this->include_checkin_details_view,
            'company_about_text' => $this->company_about_text,
            'emergency_phone' => $this->emergency_phone,
            'rental_settings' => GuestsAppRentalSettingsResource::collection($this->guestApplicationRentalSettings),
        ];
    }
}
