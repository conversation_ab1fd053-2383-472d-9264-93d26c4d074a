<?php

namespace App\Http\Resources;

use App\Models\Alert;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class AlertsResource.
 *
 * @mixin Alert
 *
 * @extends JsonResource<Alert>
 */
class AlertResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'rental_id' => $this->rental_id,
            'rental_name' => $this->rental?->name,
            'booking_id' => $this->booking_id,
            'alertable_type' => $this->alertable_type,
            'alertable_id' => $this->alertable_id,
            'start_at' => apiDateFromTimestamp($this->booking?->start_at),
            'end_at' => apiDateFromTimestamp($this->booking?->end_at),
            'alert_type' => $this->alert_type,
            'emoji' => $this->getEmoji(), // TODO removed from the frontend, remove in a while
            'alert_name' => $this->getAlertName(),
            'real_value' => $this->real_value, // TODO removed from the frontend, remove in a while
            'alert_description' => $this->getDescription(),
            'alert_value' => $this->getDetails(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'snoozed_until' => $this->snoozed->firstWhere('user_id', Auth::user()->id)?->snoozed_until,
        ];
    }
}
