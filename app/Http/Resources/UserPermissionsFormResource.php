<?php

namespace App\Http\Resources;

use App\Models\TeamUserRental;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class UserPermissionsFormResource.
 *
 * @mixin \App\Models\User
 */
class UserPermissionsFormResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->current_team_id,
            'role' => $this->ninja_role,
            'rentals' => TeamUserRental::whereUserId($this->id)
                ->select('rental_id')
                ->pluck('rental_id'),
            'permissions' => new TeamUserPermissionsBoolResource($this->getPermissions()),
        ];
    }
}
