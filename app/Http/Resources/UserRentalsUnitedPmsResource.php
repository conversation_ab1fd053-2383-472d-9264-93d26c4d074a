<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin User
 */
class UserRentalsUnitedPmsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->ninja_role,
            'locale' => $this->short_locale,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'external_id' => intval($this->external_id),
            'token' => $this->customUserToken?->token,
            'expires_at' => $this->customUserToken?->expires_at,
        ];
    }
}
