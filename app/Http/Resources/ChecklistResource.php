<?php

namespace App\Http\Resources;

use App\Models\Checklist;
use App\Models\ChecklistItem;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

/**
 * Class CheckListResource.
 *
 * @mixin Checklist
 *
 * @extends JsonResource<Checklist>
 */
class ChecklistResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'title' => $this->title,
            'description' => $this->description,
            'items' => CheckListItemResource::collection($this->whenLoaded('items')),
            'item_count' => ChecklistItem::whereChecklistId($this->id)->count() ?? 0,
            'created_at' => $this->created_at,
            'updated_at' => $this->getLastUpdatedAt(),
        ];
    }

    private function getLastUpdatedAt(): Carbon
    {
        if ($this->relationLoaded('items')) {
            $lastItem = $this->items->max('updated_at');
        } else {
            $lastItem = $this->items()->latest('updated_at')->first()?->updated_at;
        }
        $updatedAt = $this->updated_at;

        return $lastItem > $updatedAt ? $lastItem : $updatedAt;
    }
}
