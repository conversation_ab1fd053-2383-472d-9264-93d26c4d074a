<?php

namespace App\Http\Resources;

use App\Models\PayoutAttachment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @extends JsonResource<PayoutAttachment>
 *
 * @mixin PayoutAttachment
 */
class PayoutAttachmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return parent::toArray($request);
    }
}
