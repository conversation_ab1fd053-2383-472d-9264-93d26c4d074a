<?php

namespace App\Http\Resources;

use App\DataProviders\Providers\NoProvider;
use App\Models\Client;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class ClientResource.
 *
 * @mixin Client
 *
 * @extends JsonResource<Client>
 */
class SlimClientResource extends JsonResource
{
    public function toArray($request): array
    {
        $primary = $this->getPrimaryEmail();

        return [
            'id' => $this->id,
            'firstname' => $this->firstname,
            'fullname' => $this->fullname,
            'preferred_locale' => $this->preferred_locale,
            'is_external' => $this->provider_id !== NoProvider::ID, // This just allows the frontend to modify the client or not
            'primary_email' => $primary,
            'bookings' => SlimBookingResource::collection($this->whenLoaded('allBookings', fn () => $this->allBookings)),
        ];
    }
}
