<?php

namespace App\Http\Resources;

use App\Models\Invitation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class UserResource.
 *
 * @mixin Invitation
 */
class InvitationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        /** @var User $user */
        $user = $this->user;

        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'user_id' => $this->user_id,
            'email' => $this->email,
            'rentals' => $this->rentals,
            'token' => $this->token,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'role' => $this->role,
            'user' => is_null($user) ? null : [
                'current_team_id' => $user->current_team_id,
            ], // The frontend doesn't work if we pass a user resource
            'team' => new SlimTeamResource($this->team),
        ];
    }
}
