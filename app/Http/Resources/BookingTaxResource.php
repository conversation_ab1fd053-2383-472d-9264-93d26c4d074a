<?php

namespace App\Http\Resources;

use App\Models\BookingTax;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class BookingTax.
 *
 * @mixin BookingTax
 *
 * @extends JsonResource<BookingTax>
 */
class BookingTaxResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param Request
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'booking_id' => $this->booking_id,
            'tax_id' => $this->tax_id,
            'amount' => $this->amount ?? 0.0,
            'name' => $this->name,
            'percentage' => $this->percentage,
            'tax_included_in_price' => $this->tax_included_in_price,
        ];
    }
}
