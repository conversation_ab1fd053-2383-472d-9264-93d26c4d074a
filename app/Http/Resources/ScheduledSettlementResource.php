<?php

namespace App\Http\Resources;

use App\Models\ScheduledSettlement;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ScheduledSettlement
 */
class ScheduledSettlementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'name' => $this->name,
            'include_date_with_name' => $this->include_date_with_name,
            'frequency' => $this->frequency,
            'schedule_day' => $this->schedule_day,
            'period' => $this->period,
            'currency' => $this->currency,
            'rentals' => $this->rentals,
            'sources' => $this->sources,
            'on_check_out' => $this->on_check_out,
            'on_net_income' => $this->on_net_income,
            'payees' => $this->payees,
            'is_active' => $this->is_active,
            'author_id' => $this->author_id,
            'last_processed_at' => $this->last_processed_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
