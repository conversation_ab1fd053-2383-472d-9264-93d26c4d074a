<?php

namespace App\Http\Resources;

use App\Models\NinjaPicture;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class PictureResource.
 *
 * @mixin NinjaPicture
 */
class PictureResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'rental_id' => $this->rental_id,
            'rental_name' => $this->rental_name,
            'booking_id' => $this->booking_id,
            'payment_id' => $this->payment_id,
            'start_at' => apiDateFromTimestamp($this->start_at),
            'end_at' => apiDateFromTimestamp($this->end_at),
            'order' => $this->order,
            'full_size_url' => $this->full_size_url,
            'medium_size_url' => $this->medium_size_url,
            'small_size_url' => $this->small_size_url,
            'thumbnail_size_url' => $this->thumbnail_size_url,
            'description' => $this->description,
            'author_id' => $this->author_id,
            'author' => $this->author->name ?? '',
            'created_at' => $this->created_at->toIso8601ZuluString(),
            'updated_at' => $this->updated_at->toIso8601ZuluString(),
            'favorite' => boolval($this->favorite),
            'task_id' => $this->whenLoaded('taskItem', fn () => $this->taskItem?->task?->id),
            'task_item_id' => $this->whenLoaded('taskItem', fn () => $this->taskItem?->id),

        ];
    }
}
