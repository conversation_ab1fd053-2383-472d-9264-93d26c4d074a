<?php

namespace App\Http\Resources;

use App\Models\TeamSettings;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TeamSettings
 */
class TeamSettingsResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'active' => $this->active,
            'sms_price' => $this->getSmsPriceInCents() * 100,
            'scan_price' => $this->getScanPriceInCents() * 100,
            'upscale_price' => $this->getUpscalePriceInCents() * 100,
            'ai_price' => $this->getAiPriceInCents() * 100,
            'content_ai_price' => $this->getContentAiPriceInCents() * 100,
            'booking_engine_commission_percentage' => $this->getBookingEngineCommissionPercentage(),
            'upsales_commission_percentage' => $this->getUpsalesCommissionPercentage(),
            'slack_webhook_url' => $this->slack_webhook_url,
            'alert_notifications' => $this->alert_notifications,
            'booking_created_notifications' => $this->booking_created_notifications,
            'booking_canceled_notifications' => $this->booking_canceled_notifications,
            'payment_created_notification' => $this->payment_created_notification,
            'picture_added_notification' => $this->picture_added_notification,
            'comment_created_notification' => $this->comment_created_notification,
            'check_in_out_time_modified_notification' => $this->check_in_out_time_modified_notification,
            'task_notification' => $this->task_notification,
            'lead_created_notification' => $this->lead_created_notification,
            'new_message_notification' => $this->new_message_notification,
            'upsale_purchased_notification' => $this->upsale_purchased_notification,
            'model_179' => $this->model_179,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
