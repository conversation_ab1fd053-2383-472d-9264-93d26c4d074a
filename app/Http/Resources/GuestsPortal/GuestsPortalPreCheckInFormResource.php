<?php

namespace App\Http\Resources\GuestsPortal;

use App\Http\Resources\PreCheckInFormPassportResource;
use App\Models\PreCheckInForm;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

/**
 * @mixin  PreCheckInForm
 */
class GuestsPortalPreCheckInFormResource extends JsonResource
{
    public function toArray($request): array
    {
        $communications = collect($this->communications_sent);

        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'booking_id' => $this->booking_id,
            'completed' => $this->completed,
            'privacy_policy_accepted' => $this->privacy_policy_accepted,
            'first_email' => $this->first_email?->toIso8601ZuluString(),
            'second_email' => $this->second_email?->toIso8601ZuluString(),
            'third_email' => $this->third_email?->toIso8601ZuluString(),
            'last_reminder' => $this->last_reminder?->toIso8601ZuluString(),
            'emails_sent' => $this->emails_sent,
            'sms_sent' => $this->sms_sent,
            'communications_sent' => $this->communications_sent,
            'sms_sent_count' => $communications->where('type', '=', PreCheckInForm::SMS)->count(),
            'email_sent_count' => $communications->where('type', '=', PreCheckInForm::EMAIL)->count(),
            'provider_sent_count' => $communications->where('type', '=', PreCheckInForm::PROVIDER)->count(),
            'adults' => $this->adults,
            'children' => $this->children,
            'babies' => $this->babies,
            'email' => $this->email,
            'phone_country' => $this->phone_country,
            'phone_prefix' => $this->phone_prefix,
            'phone' => $this->phone,
            'address' => $this->address,
            'zip' => $this->zip,
            'city' => $this->city,
            'signature' => $this->signature,
            'locale' => $this->locale,
            'arrival_type' => $this->arrival_type,
            'arrival_details' => $this->arrival_details,
            'estimated_arrival_time' => $this->estimated_arrival_time?->toIso8601ZuluString(),
            'created_at' => $this->created_at?->toIso8601ZuluString(),
            'updated_at' => $this->updated_at?->toIso8601ZuluString(),
            'default_time' => $this->arrival(),
            'passports_count' => $this->when(! is_null($this->passports_count), $this->passports_count),
            'passports' => PreCheckInFormPassportResource::collection($this->whenLoaded('passports')),
            'provider_messages_enabled' => $this->getProviderMessagesEnabled($this->resource),
        ];
    }

    /**
     * Get the administrator flag for the user.
     */
    private function arrival(): string
    {
        $booking = $this->booking;

        if (is_null($booking)) {
            return now()->toIso8601ZuluString();
        }

        $time = $booking->getCheckInTime();
        $hour = intval(substr($time, 0, strpos($time, ':')));
        $minutes = intval(substr($time, strpos($time, ':') + 1, strlen($time)));
        $start = Carbon::createFromTimestamp($booking->start_at);
        $time = Carbon::create($start->year, $start->month, $start->day, $hour, $minutes);

        return $time->toIso8601ZuluString();
    }

    private function getProviderMessagesEnabled(PreCheckInForm $form): bool
    {
        $isAirbnb = Str::of($form->booking->source?->name)->contains('airbnb', true);

        return ($form->team_id == 1 || $form->team_id == 5) && $isAirbnb;
    }
}
