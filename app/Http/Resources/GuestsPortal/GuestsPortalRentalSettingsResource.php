<?php

namespace App\Http\Resources\GuestsPortal;

use App\Models\GuestsApplicationRentalSettings;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class GuestsApplicationRentalSettingsResource.
 *
 * @mixin GuestsApplicationRentalSettings
 *
 * @extends JsonResource<GuestsApplicationRentalSettings>
 */
class GuestsPortalRentalSettingsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'rental_id' => $this->rental_id,
            'active' => $this->guest_app,
            'upsales_enabled' => $this->upsales_enabled && $this->team->gatewayActivated($this->rental_id),
            'guest_app' => $this->guest_app,
            'pre_checkin' => $this->pre_checkin,
            'use_rn_settings' => $this->use_rn_settings,
            'require_passport_number' => $this->require_passport_number,
            'require_passport_upload' => $this->require_passport_upload,
            'require_passports_all_guests' => $this->require_passports_all_guests,
            'accept_any_document' => $this->accept_any_document,
            'scan_documents' => $this->scan_documents,
            'extra_information_guests' => $this->extra_information_guests,
            'main_postal_address' => $this->main_postal_address,
            'guests_postal_address' => $this->guests_postal_address,
            'request_signature' => $this->request_signature,
            'guide' => $this->guide,
            'wifi' => $this->show_wifi,
            'wifi_network' => $this->rental->wifi_network,
            'wifi_password' => $this->rental->wifi_password,
            'contact_person_name' => $this->rental->contact_name,
            'guest_portal_images' => $this->guest_portal_images,
            'maintenance_contact' => $this->show_maintenance_contact,
            'contact_person_phone' => $this->rental->contact_phone,
            'maintenance_person_name' => $this->rental->maintenance_contact,
            'maintenance_person_phone' => $this->rental->maintenance_phone,
            'show_image_on_guest_portal' => $this->show_image_on_guest_portal,
            'show_door_time' => $this->show_door_time,
            'rn_settings' => config('ninja.rn_settings'),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
