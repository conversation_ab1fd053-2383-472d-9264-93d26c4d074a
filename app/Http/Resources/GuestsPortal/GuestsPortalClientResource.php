<?php

namespace App\Http\Resources\GuestsPortal;

use App\Models\Client;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class ClientResource.
 *
 * @mixin Client
 *
 * @extends JsonResource<Client>
 */
class GuestsPortalClientResource extends JsonResource
{
    public function toArray($request): array
    {
        $primary = $this->getPrimaryEmail();

        return [
            'id' => $this->id,
            'firstname' => $this->firstname,
            'fullname' => $this->fullname,
            'preferred_locale' => $this->preferred_locale,
            'primary_email' => $primary,
        ];
    }
}
