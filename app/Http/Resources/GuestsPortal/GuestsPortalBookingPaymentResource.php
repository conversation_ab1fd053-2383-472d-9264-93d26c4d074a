<?php

namespace App\Http\Resources\GuestsPortal;

use App\Models\BookingPayment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class PaymentResource.
 *
 * @mixin BookingPayment
 *
 * @extends JsonResource<BookingPayment>
 */
class GuestsPortalBookingPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'booking_id' => $this->booking_id,
            'currency' => $this->currency,
            'amount_in_cents' => $this->amount_in_cents,
            'kind' => strtolower($this->kind) == 'bookingsync' ? 'smily' : $this->kind,
            'paid_at' => $this->paid_at,
            'canceled_at' => $this->canceled_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
