<?php

namespace App\Http\Resources;

use App\Models\Payee;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class PayeeListingResource.
 *
 * @mixin Payee
 *
 * @extends JsonResource<Payee>
 */
class PayeeSuperSlimResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
        ];
    }
}
