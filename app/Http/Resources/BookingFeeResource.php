<?php

namespace App\Http\Resources;

use App\Models\BookingFee;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class BookingFee.
 *
 * @mixin BookingFee
 *
 * @extends JsonResource<BookingFee>
 */
class BookingFeeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'booking_id' => $this->booking_id,
            'fee_id' => $this->fee_id,
            'price' => $this->price,
            'required' => boolval($this->required),
            'times_booked' => $this->times_booked,
            'kind' => $this->kind,
            'rate_kind' => $this->rate_kind,
            'name' => $this->fee_name,
            'created_at' => apiDateFromTimestamp($this->created_at),
            'updated_at' => apiDateFromTimestamp($this->updated_at),
            'times_bookable' => 1,
            'has_taxes' => $this->tax != null,
            'actions' => [
                'can_edit' => $this->internal,
                'can_delete' => $this->internal,
            ],
        ];
    }
}
