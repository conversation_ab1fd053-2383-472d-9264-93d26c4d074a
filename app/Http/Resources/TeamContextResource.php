<?php

namespace App\Http\Resources;

use App\Models\ProviderAccount;
use App\Models\Rental;
use App\Models\Team;
use App\Models\TeamSubscription;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

/**
 * Class TeamContextResource.
 *
 * @mixin Team
 */
class TeamContextResource extends JsonResource
{
    public function __construct(Team $resource, public string $locale = 'en')
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        /** @var TeamSubscription $subscription */
        $subscription = $this->subscription();

        $providerAccount = $this->getProviderAccount($this->provider_id);

        return [
            'name' => trim($this->name),
            'current_rn_plan' => $this->current_rn_plan,
            'current_rn_plan_name' => $subscription?->billing_cycle,
            'main_provider' => $this->mainProvider()->fullName(),
            'main_provider_oauth' => $this->providerOauth($providerAccount),
            'using_channel_manager' => $this->hasCentralizedInbox(),
            'rentals' => $this->teamRentals->map(fn (Rental $rental) => $this->rentalAiContext($rental)),
        ];
    }

    private function providerOauth(ProviderAccount $providerAccount): string
    {
        if (is_null($providerAccount->oauth_access_token)) {
            return 'Not using oauth';
        } elseif (is_null($providerAccount->oauth_refresh_token)) {
            return 'Invalid refresh';
        } elseif (Carbon::createFromTimestamp($providerAccount->oauth_expires_at)->addHours(3)->isPast()) {
            return 'Unable to refresh';
        } else {
            return 'Valid';
        }
    }

    private function rentalAiContext(Rental $rental): array
    {
        return [
            'internal_id' => $rental->id,
            'external_id' => $rental->external_id,
            'internal_name' => trim($rental->name),
            'public_name' => $rental->channel_name,
            'provider' => $rental->provider()->fullName(),
            'import_ical_count' => $rental->iCalInputs->count(),
            'export_ical_count' => $rental->iCalOutputs->count(),
            'channels' => $rental->channelRentals->map(fn ($channelRental) => [
                'name' => $channelRental->channel,
                'status' => $channelRental->active,
                'content_status' => $channelRental->content_status,
                'ari_status' => $channelRental->ari_status,
            ]),
        ];
    }
}
