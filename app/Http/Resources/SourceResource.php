<?php

namespace App\Http\Resources;

use App\Models\Source;
use Illuminate\Http\Resources\Json\JsonResource;
use JetBrains\PhpStorm\ArrayShape;

/**
 * Class AccountingAdvancedSettingsResource.
 *
 * @mixin Source
 *
 * @extends JsonResource<SourceCommissionStrategy>
 */
class SourceResource extends JsonResource
{
    #[ArrayShape(['id' => 'int', 'team_id' => 'int', 'name' => 'null|string', 'position' => 'int|null', 'is_external' => 'bool'])]
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'name' => $this->name,
            'position' => $this->position,
            'is_external' => $this->is_external,
        ];
    }
}
