<?php

namespace App\Http\Resources;

use App\Actions\Sources\GetSourceNameAction;
use App\Models\SourceCommissionStrategy;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class AccountingAdvancedSettingsResource.
 *
 * @mixin SourceCommissionStrategy
 *
 * @extends JsonResource<SourceCommissionStrategy>
 */
class SourceCommissionStrategyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'enabled' => $this->enabled,
            'source_id' => $this->source_id,
            'source_name' => $this->source ?
                $this->source->name : // In most cases, we will have sources as we define commissions for sources
                GetSourceNameAction::run($this->source, $this->team->provider_id), // Avoid loading the team unless necessary (for source_id = 0)
            'calculate_from_final_price' => $this->calculate_from_final_price,
            'final_price_rules' => $this->final_price_rules ?? [],
            'rental_price_rules' => $this->rental_price_rules ?? [],
            'fees_rules' => $this->fees_rules ?? [],
            'taxes_rules' => $this->taxes_rules ?? [],
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
