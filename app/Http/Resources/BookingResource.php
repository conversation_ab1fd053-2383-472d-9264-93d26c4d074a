<?php

namespace App\Http\Resources;

use App\Actions\Bookings\Comments\HandleCommentModificationsForApiAction;
use App\Actions\Bookings\Payment\GetBookingPaymentUrlAction;
use App\Actions\Bookings\Sources\HandleAirbnbModificationsAction;
use App\DataProviders\Providers\BookingSync;
use App\DataProviders\Providers\RentalsUnited;
use App\DataProviders\Providers\RentalsUnitedPms;
use App\DataProviders\Providers\Smoobu;
use App\Domains\HomeAutomation\Models\HomeAutomationDevice;
use App\Domains\HomeAutomation\Resources\HomeAutomationDeviceResource;
use App\Enum\AlertTypeEnum;
use App\Http\Resources\AllowedActions\AllowedBookingActions;
use App\Models\Booking;
use App\Models\User;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

/**
 * Class BookingResource.
 * This resource services to Booking.dart.
 *
 * @mixin Booking
 *
 * @extends JsonResource<Booking>
 */
class BookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $user = Auth::user();

        // Make modifications to the booking pricing, payments, etc to show it correctly to the user
        $this->resource = HandleAirbnbModificationsAction::run($this->resource);

        // Modify comments and notes to show them correctly to the user
        $original_note_is_empty = empty($this->notes);
        $notes = HandleCommentModificationsForApiAction::run($this->resource, $user);

        $payment_url = GetBookingPaymentUrlAction::run($this->resource);
        $is_ninja_payment_gateway = ! empty($payment_url) && strcmp($payment_url, $this->payment_url) !== 0;
        $checkIn = $this->getCheckInDay();
        $gatewayIsActive = $this->team->gatewayActivated($this->rental_id);

        // The inbox should be accessible to the connected rentals (provider of the rental not from the booking).
        // As you have multiple OTA, we centralize comms for you. This must include direct bookings provider_id = 0
        $shouldLoadInboxStuff = $this->rental->hasCentralizedInbox() && $user->permissionFor('can_access_inbox');
        /** @var Carbon|null $last_guest_message_sent_at */
        $last_guest_message_sent_at = null;
        if ($shouldLoadInboxStuff) {
            $last_guest_message_sent_at = $this->messages->where('guest_message', true)->sortByDesc('sent_at')->first()?->sent_at;
        }

        // Home automation
        if ($canUseDevices = $user->permissionFor('use_home_automation_devices')) {
            $devices = $this->getRentalSmartlocks()
                ->map(function (HomeAutomationDevice $device) {
                    $device->smartlockAuthorisations = $device->smartlockAuthorisations->where('booking_id', $this->id);

                    return $device;
                });
        }

        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'rental_id' => $this->rental_id,
            'client_id' => $this->client_id,
            'provider_id' => $this->provider_id,
            'rental_ical_id' => $this->rental_ical_id,
            'client' => new ClientResource($this->client),
            'adults' => $this->adults ?? 0,
            'children' => $this->children ?? 0,
            'guests' => $this->getPax(),
            'discount' => $this->discountAttr(),
            'final_rental_price' => round(floatval($this->final_rental_price), 2),
            'final_price' => round(floatval($this->final_price), 2),
            'paid_amount' => round(floatval($this->paid_amount), 2, PHP_ROUND_HALF_DOWN),
            'expected_paid_amount' => $this->getExpectedPaidAmount(),
            'percent_paid_booking' => $this->getPercentPaidBooking(),
            'unpaid_rental' => $this->getLeftToPayFinalPrice(false),
            'paid_rental' => round($this->getFinalPricePaidAmount(), 2, PHP_ROUND_HALF_DOWN),
            'initial_price' => round(floatval($this->initial_price), 2),
            'down_payment' => $this->downpayment,
            'locked' => $this->locked === 'true' || $this->locked === '1',
            'initial_rental_price' => round(floatval($this->initial_rental_price), 2),
            'channel_price' => round(floatval($this->channel_price), 2),
            'commission' => round(floatval($this->commission), 2),
            'net_rental_price' => round(floatval($this->final_rental_price - $this->commission), 2, PHP_ROUND_HALF_DOWN),
            'net_final_price' => round(floatval($this->final_price - $this->commission), 2, PHP_ROUND_HALF_DOWN),
            'currency' => $this->currency,
            'nights' => $this->nights,
            'damage_deposit' => $this->damage_deposit,
            'unpaid_damage_deposit' => $this->getLeftToPayDeposit(false),
            'paid_damage_deposit' => round($this->getDamageDepositPaidAmount(), 2, PHP_ROUND_HALF_DOWN),
            'start_at' => apiDateFromTimestamp($this->start_at),
            'end_at' => apiDateFromTimestamp($this->end_at),
            'created_at' => apiDateFromTimestamp($this->created_at),
            'updated_at' => apiDateFromTimestamp($this->updated_at),
            'canceled_at' => apiDateFromTimestamp($this->canceled_at),
            'reference' => $this->reference,
            'source_id' => $this->source_id,
            'created_by' => $this->created_by,
            'source' => $this->source_public_name,
            'conversation_read_by_user' => $this->when($shouldLoadInboxStuff, fn () => $this->isReadByUser($user, $last_guest_message_sent_at)), // Note: when using ->when() the $value must be a callable, to avoid it's execution when condition is false
            'notes' => $user->canSeeBookingNotice() && $user->canSeeInternalNote() ? $notes : null,
            'booking_notice' => $user->canSeeBookingNotice() ? $this->booking_notice : null,
            'internal_note' => $user->canSeeInternalNote() ? $this->internal_note : null,
            'payments' => BookingPaymentResource::collection($this->bookingPayments->sortByDesc('paid_at')),
            'fees' => BookingFeeResource::collection($this->bookingFees),
            'taxes' => BookingTaxResource::collection($this->bookingTaxes),
            'payment_url' => $payment_url,
            'is_ninja_payment_gateway' => $is_ninja_payment_gateway,
            'expected_checkin_time' => $this->getCheckInTime(),
            'expected_checkout_time' => $this->getCheckOutTime(),
            'alert' => AlertResource::collection($this->alerts),
            'missing_authorisation_alert' => $this->alerts->where('alert_type', AlertTypeEnum::MISSING_AUTHORISATIONS)->isNotEmpty(),
            'tasks' => $this->relationLoaded('tasks') ?
                SlimTaskResource::collection($this->filterBookingTasksByUser($user)) :
                null,
            'guide' => $this->guestApplicationRentalSettings != null && $this->guestApplicationRentalSettings->guest_app,
            'pre_check_in_filled' => $this->formIsCompleted(),
            'rental' => new RentalResource($this->rental),
            'status' => $this->status,
            'status_public_name' => $this->status->getStatusPublicName(),
            'tags' => $this->relationLoaded('tagsPivot') ? BookingTagResource::collection($this->tagsCollection()) : null,
            'firstNoteReal' => ! ($original_note_is_empty && $notes->isNotEmpty()),
            'rental_guide_url' => $this->team->config()->rnBackendTargetDomain().route('pdfRentalGuide', $this->reference, false)."?ci=$checkIn",
            'external_url' => $this->getExternalUrl(),
            'external_name' => $this->getExternalName(),
            'has_available_upsales' => $this->rental->guestsApplicationRentalSettings?->upsales_enabled && $gatewayIsActive,
            'gateway_is_active' => $gatewayIsActive,
            'devices' => $this->when($canUseDevices, fn () => HomeAutomationDeviceResource::collection($devices)),
            'actions' => new AllowedBookingActions($this->resource),
        ];
    }

    private function getExternalUrl(): ?string
    {
        if (is_null(request())) {
            return null;
        }

        return match ($this->provider_id) {
            1 => BookingSync::get()->providerDomain().'/bookings/'.$this->external_id,
            3 => Smoobu::get()->providerDomain().'/booking/detail/'.$this->external_id.'/',
            4 => RentalsUnited::get()->providerDomain().'/MyBookings/Edit/'.Str::before($this->external_id, '_'),
            7 => RentalsUnitedPms::get()->providerDomain().'/My/GuestPlanner/BigCard/'.Str::before($this->external_id, '_'),
            default => null,
        };
    }

    private function getExternalName(): ?string
    {
        if (is_null(request())) {
            return null;
        }

        return match ($this->provider_id) {
            1 => BookingSync::get()->fullName(),
            3 => Smoobu::get()->fullName(),
            4 => RentalsUnited::get()->fullName(),
            7 => 'Guest Planner',
            default => null,
        };
    }

    private function isReadByUser(User $user, ?Carbon $last_guest_message_sent_at): bool
    {
        // If no message was sent by the guest, last_message_sent_at will be null. We can say conversation is read by the user
        if ($last_guest_message_sent_at == null) {
            return true;
        }

        return $this->bookingUsers->firstWhere('user_id', $user->id)?->conversation_read_at?->greaterThanOrEqualTo($last_guest_message_sent_at) ?? false;
    }
}
