<?php

namespace App\Http;

use App\Domains\Hostboost\Middlewares\HostboostMiddleware;
use App\Http\Middleware\AddFlavorResponseHeaderMiddleware;
use App\Http\Middleware\AddUserModifiedAtHeaderMiddleware;
use App\Http\Middleware\CachePreflightRequestsMiddleware;
use App\Http\Middleware\CheckDomainPerUser;
use App\Http\Middleware\CheckProfessionalPlan;
use App\Http\Middleware\EncryptCookies;
use App\Http\Middleware\NinjaVerifyUserHasNovaAccess;
use App\Http\Middleware\RedirectIfAuthenticated;
use App\Http\Middleware\RegisterRequestAttributes;
use App\Http\Middleware\SetApiRequestLocaleMiddleware;
use App\Http\Middleware\SetLocale;
use App\Http\Middleware\SetWebRequestLocaleMiddleware;
use App\Http\Middleware\TrimStrings;
use App\Http\Middleware\TrustProxies;
use App\Http\Middleware\VerifyCsrfToken;
use App\Http\Middleware\VerifyIsLocalEnvMiddleware;
use App\Http\Middleware\VerifyRequestIsSameTeam;
use App\Http\Middleware\VerifyTeamIsSubscribed;
use App\Http\Middleware\VerifyUserHasAccess;
use App\Http\Middleware\VerifyUserHasAccessToAccounting;
use App\Http\Middleware\VerifyUserHasAccessToGuestApp;
use App\Http\Middleware\VerifyUserHasTeam;
use App\Http\Middleware\VerifyUserIsAdmin;
use App\Http\Middleware\VerifyUserIsRentalManagerOrAbove;
use App\Http\Middleware\VerifyUserIsTeamOwner;
use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Auth\Middleware\AuthenticateWithBasicAuth;
use Illuminate\Auth\Middleware\Authorize;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Foundation\Http\Kernel as HttpKernel;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
use Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance;
use Illuminate\Foundation\Http\Middleware\ValidatePostSize;
use Illuminate\Http\Middleware\HandleCors;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Routing\Middleware\ThrottleRequestsWithRedis;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Spatie\ResponseCache\Middlewares\CacheResponse;

class Kernel extends HttpKernel
{
    protected $middlewarePriority = [
        StartSession::class,
        ShareErrorsFromSession::class,
        Authenticate::class,
        AuthenticateSession::class,
        SubstituteBindings::class,
        Authorize::class,
        RegisterRequestAttributes::class,
    ];

    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        TrustProxies::class,
        CachePreflightRequestsMiddleware::class,
        HandleCors::class,
        PreventRequestsDuringMaintenance::class,
        ValidatePostSize::class,
        TrimStrings::class,
        ConvertEmptyStringsToNull::class,
        RegisterRequestAttributes::class,
        AddFlavorResponseHeaderMiddleware::class,
        AddUserModifiedAtHeaderMiddleware::class,
        // LogRequest::class, -> Enable if we want all requests to be logged.
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            ShareErrorsFromSession::class,
            VerifyCsrfToken::class,
            SubstituteBindings::class,
            SetWebRequestLocaleMiddleware::class,
            CheckDomainPerUser::class,
            SetLocale::class,
        ],

        'api' => [
            SubstituteBindings::class,
            CheckDomainPerUser::class,
        ],

        'lodgify' => [],
        'smoobu' => [],
        'bookingsync' => [],
        'rentals-united' => [],
        'wheelhouse' => [],
        'tokeet' => [],
        'home-automation' => [],
        'hostboost' => [
            HostboostMiddleware::class,
        ],

    ];

    /**
     * The application's middleware aliases.
     *
     * Aliases may be used to conveniently assign middleware to routes and groups.
     *
     * @var array
     */
    protected $middlewareAliases = [
        'auth' => Authenticate::class,
        'auth.basic' => AuthenticateWithBasicAuth::class,
        'can' => Authorize::class,
        'novaAccess' => NinjaVerifyUserHasNovaAccess::class,
        'guest' => RedirectIfAuthenticated::class,
        'hasTeam' => VerifyUserHasTeam::class,
        'throttle' => ThrottleRequestsWithRedis::class,
        'teamSubscribed' => VerifyTeamIsSubscribed::class,
        'userHasAccess' => VerifyUserHasAccess::class,
        'setApiRequestLocale' => SetApiRequestLocaleMiddleware::class,
        'setWebRequestLocale' => SetWebRequestLocaleMiddleware::class,
        'verifyTeamIsProfessional' => CheckProfessionalPlan::class,
        'verifyUserHasAccessToAccounting' => VerifyUserHasAccessToAccounting::class,
        'VerifyUserHasAccessToGuestApp' => VerifyUserHasAccessToGuestApp::class,
        'verifyUserIsAdmin' => VerifyUserIsAdmin::class,
        'verifyUserIsTeamOwner' => VerifyUserIsTeamOwner::class,
        'cacheResponse' => CacheResponse::class,
        'checkRequestIsSameTeam' => VerifyRequestIsSameTeam::class,
        'userIsRentalManagerOrAbove' => VerifyUserIsRentalManagerOrAbove::class,
        'registerAttributes' => RegisterRequestAttributes::class,
        'isLocalEnv' => VerifyIsLocalEnvMiddleware::class,
    ];
}
