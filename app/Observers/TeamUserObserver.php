<?php

namespace App\Observers;

use App\Models\TeamUser;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class TeamUserObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the team user "created" event.
     */
    public function created(TeamUser $teamUser): void
    {
        $teamUser->user->ninja_role = $teamUser->role;
        $teamUser->user->save();
    }

    /**
     * Handle the team user "updated" event.
     */
    public function updated(TeamUser $teamUser): void
    {
        $teamUser->user->ninja_role = $teamUser->role;
        $teamUser->user->save();
    }
}
