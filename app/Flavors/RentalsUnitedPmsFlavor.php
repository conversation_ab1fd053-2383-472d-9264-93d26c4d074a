<?php

namespace App\Flavors;

use App\Actions\Pictures\GetDicebearAvatarAction;
use App\Exceptions\NinjaNotImplementedException;

class RentalsUnitedPmsFlavor extends DefaultFlavor
{
    const FLAVOR_ID = 5;

    public function name(): string
    {
        return 'Rentals United PMS';
    }

    public function rnAppName(): string
    {
        return 'Rentals United Operations';
    }

    public function rnAppLogo(): string
    {
        return 'img/rentals-united/rentals_united_icon_small.png';
    }

    public function rnAppTargetDomain(?string $path = null): string
    {
        return 'https://new.rentalsunited.com/MyOperations/#'; // They don't differentiate between production and staging. They told us, just production.
    }

    public function impersonationTargetDomain(): string
    {
        // We can't impersonate users in the RU domain, as all users use the embedded solution
        if (isProduction()) {
            return 'https://ru-pms-app.rental-ninja.com';
        } else {
            return config('white_labels.null.app_domain');
        }
    }

    public function guestPortalDomain(?string $path = null): string
    {
        return 'https://ops.thepowerbooking.com'.$path;
    }

    public function billedThroughRentalNinja(): bool
    {
        return false;
    }

    public function canSwitchToDefaultFlavor(): bool
    {
        return false;
    }

    public function canManageExtraRentals(): bool
    {
        return false;
    }

    public function flavorId(): int
    {
        return self::FLAVOR_ID;
    }

    public function useSegment(): bool
    {
        return false;
    }

    public function invoicePrefix(): string
    {
        return 'RU';
    }

    public function mailFromAddress(): string
    {
        return '<EMAIL>';
    }

    public function mailFromName(): string
    {
        return $this->rnAppName();
    }

    public function inboxMailFromDomain(): string
    {
        throw new NinjaNotImplementedException();
    }

    public function isWhiteLabel(): bool
    {
        return true;
    }

    public function ninjaPaymentGatewayEnabled(): bool
    {
        return true;
    }

    public function paymentGatewayBookingPaymentsName(): string
    {
        return 'Payment Gateway';
    }

    public function paymentGatewayBookingPaymentsKind(): string
    {
        return 'payment-gateway';
    }

    public function canUseChannelManager(): bool
    {
        return false;
    }

    public function canExportIcal(): bool
    {
        return false;
    }

    public function defaultTeamAndUserPhotoUrl(string $avatarInitials): string
    {
        return GetDicebearAvatarAction::run($avatarInitials);
    }

    public function canUseHomeAutomation(): bool
    {
        return false;
    }
}
