<?php

namespace App\Flavors;

use App\DataProviders\Providers\NinjaProvider;
use App\DataProviders\Providers\NoProvider;

class DefaultFlavor extends NinjaFlavor
{
    const FLAVOR_ID = 1;

    /** This is the Flavour name. Is only used internally */
    public function name(): string
    {
        return 'Rental Ninja';
    }

    public function rnAppName(): string
    {
        return config("white_labels.$this->franchiseId.app_name",
            config('white_labels.null.app_name')
        );
    }

    public function isDebug(): bool
    {
        return config('app.debug');
    }

    public function rnAppLogo(): string
    {
        return config("white_labels.$this->franchiseId.logo",
            config('white_labels.null.logo')
        );
    }

    public function mailFromAddress(): string
    {
        return config("white_labels.$this->franchiseId.mail_from",
            config('white_labels.null.mail_from')
        );
    }

    public function mailFromName(): string
    {
        return config("white_labels.$this->franchiseId.mail_from_name",
            config('white_labels.null.mail_from_name')
        );
    }

    public function inboxMailFromDomain(): string
    {
        return config("white_labels.$this->franchiseId.inbox_domain",
            config('white_labels.null.inbox_domain')
        );
    }

    public function sessionDomain(): ?string
    {
        return null;
    }

    public function sessionCookieName(): string
    {
        return 'rn_session_'.config('app.env');
    }

    public function useSegment(): bool
    {
        return config('ninja.use_segment');
    }

    public function segmentKey(): ?string
    {
        return config('ninja.segment_key');
    }

    public function isIframe(): bool
    {
        return false;
    }

    public function guestPortalDomain(?string $path = null): string
    {
        return config("white_labels.$this->franchiseId.guest_domain",
            config('white_labels.null.guest_domain')
        ).$path;
    }

    public function landingPage(): string
    {
        return config('ninja.landing_page', 'https://try.rental-ninja.com');
    }

    public function supportEmail(): string
    {
        return config('<EMAIL>');
    }

    public function shouldShowSupportEmail(): string
    {
        return true;
    }

    public function supportUrl(): string
    {
        return 'https://help.rental-ninja.com';
    }

    public function invitationSupportUrl(): string
    {
        return 'https://help.rental-ninja.com/faq/team-members-roles-and-permissions/re-invite-users-to-your-team';
    }

    public function rnBackendTargetDomain(?string $path = null): string
    {
        $domain = config('ninja.target_domain');

        return $domain.$path;
    }

    public function rnAppTargetDomain(?string $path = null): string
    {
        if (isset($this->franchiseId) && isProduction()) { // Whenever it is not production, we want to make sure we don't get to a real production environment
            return config("white_labels.$this->franchiseId.app_domain").$path;
        } else {
            return config('white_labels.null.app_domain').$path; // Impersonating in local env should fall here
        }
    }

    public function impersonationTargetDomain(): string
    {
        return $this->rnAppTargetDomain();
    }

    public function androidAppLink(): string
    {
        return 'https://play.google.com/store/apps/details?id=com.quickgiraffe';
    }

    public function iosAppLink(): string
    {
        return 'https://itunes.apple.com/us/app/rental-ninja/id1259904740';
    }

    public function articleTeamMembers(): string
    {
        return 'https://help.rental-ninja.com/rental-ninja-website-manual/home-view/team-members';
    }

    public function articleRoles(): string
    {
        return 'https://help.rental-ninja.com/en/articles/4924155-roles-access-permissions';
    }

    public function articleTestUser(): string
    {
        return 'https://help.rental-ninja.com/faq/team-members-roles-and-permissions/test-user';
    }

    public function articleGuestModule(): string
    {
        return 'https://help.rental-ninja.com/rental-ninja-website-manual/guest-module/guest-module';
    }

    public function articleTasksModule(): string
    {
        return 'https://help.rental-ninja.com/rental-ninja-website-manual/tasks-module/tasks-module-website-starters-guide-for-rental-managers-part-1';
    }

    public function articleAccountingModule(): string
    {
        return 'https://help.rental-ninja.com/rental-ninja-website-manual/accounting-module/accounting-module-tutorial';
    }

    public function articleWhatIsRentalNinja(): string
    {
        return 'https://help.rental-ninja.com/faq/what-rental-ninja-is';
    }

    public function articleMobileTasksForCleaners(): string
    {
        return 'https://help.rental-ninja.com/rental-ninja-mobile-app/tasks/tasks-module-starters-guide-for-cleaning-staff-check-in-agents';
    }

    public function articleMobileBookingsView(): string
    {
        return 'https://help.rental-ninja.com/rental-ninja-mobile-app/bookings-area/bookings-view';
    }

    public function articleMobileRentalsView(): string
    {
        return 'https://help.rental-ninja.com/rental-ninja-mobile-app/rentals-area/rentals-view';
    }

    public function articlePictureTypes(): string
    {
        return 'https://help.rental-ninja.com/faq/picture-types';
    }

    public function articleAlertsSymbology(): string
    {
        return 'https://help.rental-ninja.com/faq/alerts-symbology';
    }

    public function moneyBackGuaranteeTerms(string $locale): string
    {
        return match ($locale) {
            'fr' => 'https://help.rental-ninja.com/en/articles/6375972-periode-de-garantie-de-remboursement-de-60-jours-termes-et-conditions',
            default => 'https://help.rental-ninja.com/en/articles/6375933-60-day-money-back-guarantee-period-terms-conditions',
        };
    }

    public function tasksCreationCalculationDays(): int
    {
        return 30;
    }

    public function tasksDeletionCalculationDays(): int
    {
        return 3;
    }

    public function billedThroughRentalNinja(): bool
    {
        return true;
    }

    public function canSwitchToDefaultFlavor(): bool
    {
        return false;
    }

    public function canManageExtraRentals(): bool
    {
        return true;
    }

    public function flavorId(): int
    {
        return self::FLAVOR_ID;
    }

    public function usaNumberForSMSs(): string
    {
        return '13152774561';
    }

    /** WARNING! If you change this, invoice series of our customers will be broken! */
    public function invoicePrefix(): string
    {
        return 'RN';
    }

    public function preCheckInEMail(): string
    {
        return 'Form Email';
    }

    public function preCheckInPhone(): string
    {
        return 'Form Phone';
    }

    public function paymentGatewayBookingPaymentsName(): string
    {
        if ($this->franchiseId) {
            return 'Payment Gateway';
        }

        return 'Ninja Payment Gateway';
    }

    public function paymentGatewayBookingPaymentsKind(): string
    {
        if ($this->franchiseId) {
            return 'payment-gateway';
        }

        return 'ninja-payment-gateway';
    }

    public function isWhiteLabel(): bool
    {
        return false;
    }

    public function ninjaPaymentGatewayEnabled(): bool
    {
        return true;
    }

    // Channel manager related stuff
    public function canUseChannelManager(): bool
    {
        return true;
    }

    public function canChooseDistributedRentals(): bool
    {
        return true;
    }

    public function defaultProvider(): NinjaProvider
    {
        return NoProvider::get();
    }

    public function canExportIcal(): bool
    {
        return true;
    }

    public function defaultTeamAndUserPhotoUrl(string $avatarInitials): string
    {
        return url($this->rnAppLogo());
    }

    public function canUseHomeAutomation(): bool
    {
        return true;
    }
}
