<?php

namespace App\Flavors;

class SmilyPlansFlavor extends DefaultFlavor
{
    const FLAVOR_ID = 3;

    public function name(): string
    {
        return 'Smily plans';
    }

    public function billedThroughRentalNinja(): bool
    {
        return false;
    }

    public function canSwitchToDefaultFlavor(): bool
    {
        return true;
    }

    public function canManageExtraRentals(): bool
    {
        return false;
    }

    public function flavorId(): int
    {
        return self::FLAVOR_ID;
    }

    public function canUseChannelManager(): bool
    {
        return false;
    }

    public function canExportIcal(): bool
    {
        return false;
    }

    public function canUseHomeAutomation(): bool
    {
        return false;
    }
}
