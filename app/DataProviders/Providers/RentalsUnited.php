<?php

namespace App\DataProviders\Providers;

use App\DataProviders\ApiConnectors\ProviderConnector;
use App\DataProviders\ApiConnectors\RentalsUnitedConnector;
use App\DataProviders\ProviderApi\ProviderApi;
use App\DataProviders\ProviderApi\RentalsUnitedApi;
use App\DataProviders\ProviderConstants;
use App\DTO\Providers\RentalsUnited\RentalsUnitedResponseDto;
use App\DTO\Team\TeamRegistrationData;
use App\Enum\BookingStatusEnum;
use App\Flavors\DefaultFlavor;
use App\Models\Booking;
use App\Models\Team;
use DOMException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Str;
use Spatie\ArrayToXml\ArrayToXml;

/**
 * https://developer.rentalsunited.com/
 * Username: <EMAIL>
 * Password: Rental-Ninja1.
 *
 * This package might be useful:
 * https://github.com/jasekz/rentals-united-caching
 *
 * Class RentalsUnited
 */
class RentalsUnited extends NinjaProvider
{
    const ID = 4;

    const INTERNAL_NAME = 'RentalsUnited';

    // This is something RU specific
    const THREAD_COMMUNICATION_CHANNELS_WE_POST_TO = ['Airbnb', 'BookingCom', 'Vrbo', 'Expedia'];

    public function getProviderConnector(Team $team): ProviderConnector
    {
        return new RentalsUnitedConnector($team);
    }

    public function getProviderApi(): ?ProviderApi
    {
        return RentalsUnitedApi::instance();
    }

    public function shortIdentifier(): string
    {
        return 'RU';
    }

    public function fullName(): string
    {
        return 'Rentals United';
    }

    public function internalFullName(): string
    {
        return 'rentals-united';
    }

    public function enabled(): bool
    {
        return config('rentals-united.enabled');
    }

    public function hasWebsiteSignup(): bool
    {
        return config('rentals-united.enable-signup');
    }

    public function providerFlavor(): string
    {
        return DefaultFlavor::class;
    }

    public function configFile(string $string, mixed $default = null): mixed
    {
        return config('rentals-united.'.$string, $default);
    }

    public function providerDomain(): string
    {
        return 'https://new.rentalsunited.com';
    }

    public function providerLogoPath(): ?string
    {
        return 'img/rentals-united/rentals_united_icon_small.png';
    }

    public function providerFrontendLogoPath(): ?string
    {
        return 'assets/images/ota/rentalsunited.png';
    }

    public function registerUrl(): ?string
    {
        return 'https://new.rentalsunited.com/GenericChecklist/GenericChecklist/Index/569312/5';
    }

    /**
     * @throws RequestException
     * @throws DOMException
     */
    public function getRegistrationData(string $authorization): TeamRegistrationData
    {
        // Get Owner ID
        /** @var RentalsUnitedApi $ruApi */
        $ruApi = $this->getProviderApi();
        $response = $ruApi->pullAllParentUsers();
        $userId = 0;
        $providerAccount = '';
        foreach ($response->ParentUsers->children() as $user) {
            if ($user->UserId == $authorization) {
                $userId = (int) $user->UserId;
                $providerAccount = (string) $user->Username;
                break;
            }
        }

        // Get Rentals
        $endpoint = ProviderConstants::ENDPOINT_RENTALS;
        $action = config("rentals-united.endpoints_configuration.$endpoint.url");
        $array = [
            'Authentication' => [
                'UserName' => $this->configFile('username'),
                'Password' => $this->configFile('password'),
            ],
            'Username' => $providerAccount,
            'IncludeNLA' => 'false',
        ];

        $body = (new ArrayToXml($array, $action))->dropXmlDeclaration()->prettify()->toXml();
        $rentals = $ruApi->postXmlRaw(ProviderConstants::ENDPOINT_RENTALS, $body);
        nLog($rentals);
        $rentalsCount = 0;
        foreach ($rentals?->Properties?->Property as $property) {
            if ($property->LastMod->attributes()['Active'] == 'true') {
                $rentalsCount++;
            }
        }

        // Get user information
        $action = 'Pull_ListAllOwners_RQ';
        $array = [
            'Authentication' => [
                'UserName' => $this->configFile('username'),
                'Password' => $this->configFile('password'),
            ],
            'OwnerID' => $userId,
        ];
        $body = (new ArrayToXml($array, $action))->dropXmlDeclaration()->prettify()->toXml();
        $users = $ruApi->postXmlRaw($action, $body);
        // Search user and get data
        $userName = "rentals-united-$userId";
        $email = $providerAccount;
        foreach ($users->Owners->children() as $owner) {
            if ((int) $owner->attributes()['OwnerID'] == $userId) {
                $email = Str::of($owner?->Email)->before(',');
                $userName = $owner?->FirstName.' '.$owner?->SurName;
                break;
            }
        }

        return new TeamRegistrationData(
            providerId: $this->id(),
            flavorId: ($this->providerFlavor())::FLAVOR_ID,
            teamName: $userName,
            teamEmail: $email,
            rentalCount: $rentalsCount,
            providerRentalCount: $rentalsCount,
            providerAccountId: $providerAccount,
            providerSecondaryId: $userId,
            userEmail: $email,
            userName: $userName,
        );
    }

    public function id(): int
    {
        return self::ID;
    }

    public function httpReadTimeout(): int
    {
        return 120;
    }

    public function getHttpResponseDtoClass(): string
    {
        return RentalsUnitedResponseDto::class;
    }

    public function getHttpResponseHeaders(): array
    {
        return [];
    }

    public function allowsDownPaymentEmail(): bool
    {
        // Although this provider is used for both flavour RN and flavour RU, could be open for both and in the end, will work only if the button to collect down payment at a rental level is visible
        return true;
    }

    public function canBeCanceled(Booking $b): bool
    {
        return $b->status === BookingStatusEnum::UNAVAILABLE;
    }

    public function teamVerified(): bool
    {
        return true;
    }

    public function needsProxyInLocal(): bool
    {
        return true;
    }
}
