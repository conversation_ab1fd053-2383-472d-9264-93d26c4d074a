<?php

namespace App\DataProviders\Providers;

use App\DataProviders\ApiConnectors\ProviderConnector;
use App\DataProviders\ApiConnectors\SmoobuConnector;
use App\DataProviders\ProviderApi\ProviderApi;
use App\DataProviders\ProviderApi\SmoobuApi;
use App\DTO\Providers\Smoobu\SmoobuResponseDto;
use App\DTO\Team\TeamRegistrationData;
use App\Enum\BookingStatusEnum;
use App\Flavors\DefaultFlavor;
use App\Models\Booking;
use App\Models\Team;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Str;

/**
 * Class Smoobu.
 */
class Smoobu extends NinjaProvider
{
    const ID = 3;

    const INTERNAL_NAME = 'Smoobu';

    public function getProviderConnector(Team $team): ProviderConnector
    {
        return new SmoobuConnector($team);
    }

    public function getProviderApi(): ?ProviderApi
    {
        return SmoobuApi::instance();
    }

    public function fullName(): string
    {
        return 'Smoobu';
    }

    public function internalFullName(): string
    {
        return self::INTERNAL_NAME;
    }

    public function providerLogoPath(): ?string
    {
        return 'img/smoobu/smoobu_icon.png';
    }

    public function providerFrontendLogoPath(): ?string
    {
        return 'assets/images/ota/smoobu.png';
    }

    public function shortIdentifier(): string
    {
        return 'SM';
    }

    public function hasWebsiteSignup(): bool
    {
        return true;
    }

    public function registerUrl(): ?string
    {
        return SmoobuApi::instance()->oauthConnectUrl();
    }

    /**
     * @throws RequestException
     */
    public function getRegistrationData(string $authorization): TeamRegistrationData
    {
        $api = SmoobuApi::instance();
        $token = $api->getNewOauthToken(auth_code: $authorization);
        $res = $api->rawGet(endpoint: 'me', authorization: $token->access_token);
        $firstName = $res->get('firstName');
        $lastName = $res->get('lastName');
        $accountId = $res->get('id');
        $name = trim("$firstName $lastName");
        $email = $res->get('email');
        $rentals = $api->rawGet(endpoint: 'apartments', authorization: $token->access_token);
        $rentalCount = count($rentals->get('apartments', []));

        return new TeamRegistrationData(
            providerId: $this->id(),
            flavorId: ($this->providerFlavor())::FLAVOR_ID,
            teamName: $name,
            teamEmail: $email,
            accountData: $res->toArray(),
            rentalCount: $rentalCount,
            providerRentalCount: $rentalCount,
            oauth: $token,
            providerAccountId: $accountId,

            userEmail: $email,
            userName: $name,
        );
    }

    public function id(): int
    {
        return self::ID;
    }

    public function providerFlavor(): string
    {
        return DefaultFlavor::class;
    }

    public function configFile(string $string, mixed $default = null): mixed
    {
        return config('smoobu.'.$string, $default);
    }

    public function providerDomain(): string
    {
        return 'https://login.smoobu.com';
    }

    public function enabled(): bool
    {
        return config('smoobu.enabled');
    }

    public function getHttpResponseDtoClass(): string
    {
        return SmoobuResponseDto::class;
    }

    public function getHttpResponseHeaders(): array
    {
        return [];
    }

    public function canBeCanceled(Booking $b): bool
    {
        return $b->status === BookingStatusEnum::UNAVAILABLE;
    }

    public function hasCentralizedInbox(): bool
    {
        return true;
    }

    public function usingOurInboxUiIsAllowed(): bool
    {
        return false;
        // We hasCentralizedInbox() is true because we send precheckin form for airbnb through API,
        // but we don't need to allow UI messages, as they can use smoobu's inbox
    }

    public function canSendApiMessageForBooking(Booking $booking): bool
    {
        return Str::of($booking->source_public_name)->contains('airbnb', true);
    }

    public function teamVerified(): bool
    {
        return true;
    }
}
