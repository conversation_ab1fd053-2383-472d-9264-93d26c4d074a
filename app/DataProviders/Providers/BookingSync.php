<?php

namespace App\DataProviders\Providers;

use App\DataProviders\ApiConnectors\BookingSyncConnector;
use App\DataProviders\ApiConnectors\ProviderConnector;
use App\DataProviders\ProviderApi\BookingSyncApi;
use App\DataProviders\ProviderApi\ProviderApi;
use App\DTO\Providers\BookingSync\BookingSyncResponseDto;
use App\DTO\Team\TeamRegistrationData;
use App\Enum\BookingStatusEnum;
use App\Flavors\DefaultFlavor;
use App\Flavors\SmilyPlansFlavor;
use App\Models\Booking;
use App\Models\Team;
use Exception;

class BookingSync extends NinjaProvider
{
    const ID = 1;

    const INTERNAL_NAME = 'BookingSync';

    public function getProviderConnector(Team $team): ProviderConnector
    {
        return new BookingSyncConnector($team);
    }

    public function getProviderApi(): ?ProviderApi
    {
        return BookingSyncApi::instance();
    }

    public function fullName(): string
    {
        return 'Smily';
    }

    public function internalFullName(): string
    {
        return self::INTERNAL_NAME;
    }

    public function providerLogoPath(): ?string
    {
        return 'img/bookingsync/bs_icon.png';
    }

    public function providerFrontendLogoPath(): ?string
    {
        return 'assets/images/ota/smily.png';
    }

    public function shortIdentifier(): string
    {
        return 'BS';
    }

    public function hasWebsiteSignup(): bool
    {
        return true;
    }

    public function logoHeight(): int
    {
        return 5;
    }

    public function registerUrl(): ?string
    {
        return BookingSyncApi::instance()->oauthConnectUrl();
    }

    /**
     * @throws Exception
     */
    public function getRegistrationData(string $authorization): TeamRegistrationData
    {
        $api = BookingSyncApi::instance();
        $token = $api->getNewOauthToken(auth_code: $authorization);
        $res = $api->rawGet(endpoint: 'accounts', authorization: $token->access_token);
        $account = $res->get('accounts')[0];
        $params = ['per_page' => 1, 'fields' => 'id,name'];
        $apiData = $api->rawGet(endpoint: 'rentals', params: $params, authorization: $token->access_token);
        $rentalCount = data_get(target: $apiData, key: 'meta.X-Total-Count', default: 0);

        // Identify flavor
        $res = $api->rawGet(endpoint: 'me', authorization: $token->access_token);
        $invoice = data_get(target: $res, key: 'accounts_applications.0.partnership_invoice_recipient');
        $flavorId = $invoice == 'account' ? DefaultFlavor::FLAVOR_ID : SmilyPlansFlavor::FLAVOR_ID;

        return new TeamRegistrationData(
            providerId: $this->id(),
            flavorId: $flavorId,
            teamName: $account['business_name'],
            teamEmail: $account['email'],
            arrivalTime: data_get($account, 'preferences.bookings.default_arrival_time'),
            departureTime: data_get($account, 'preferences.bookings.default_departure_time'),
            appLocate: $account['default_locale'],
            communicationLocale: data_get($account, 'preferences.bookings.default_communication_locale'),
            providerRentalCount: $rentalCount,
            oauth: $token,
            providerAccountId: $account['id'],
            userEmail: $account['email'],
            userPhone: data_get($account, 'phones.phone'),
            userLocale: $account['default_locale'],
        );
    }

    public function id(): int
    {
        return self::ID;
    }

    // The flavour is determined by the provider
    public function providerFlavor(): string
    {
        return DefaultFlavor::class;
    }

    public function configFile(string $string, mixed $default = null): mixed
    {
        return config('bookingsync.'.$string, $default);
    }

    public function providerDomain(): string
    {
        return 'https://www.bookingsync.com';
    }

    public function paymentGatewayBookingPaymentsKind(Team $team): string
    {
        // BS allowed payment kinds https://developers.bookingsync.com/reference/enums/#payment-kinds
        return 'online';
    }

    public function enabled(): bool
    {
        return config('bookingsync.enabled');
    }

    public function getHttpResponseDtoClass(): string
    {
        return BookingSyncResponseDto::class;
    }

    public function getHttpResponseHeaders(): array
    {
        return [
            BookingSyncApi::UPDATED_SINCE_RESPONSE_HEADER => [now()->toIso8601String()],
        ];
    }

    public function canBeCanceled(Booking $b): bool
    {
        return $b->status === BookingStatusEnum::UNAVAILABLE && $b->locked != 'true';
    }

    public function teamVerified(): bool
    {
        return true;
    }
}
