<?php

namespace App\DataProviders\Providers\Traits;

use App\DTO\Providers\Oauth\OAuthConfig;
use App\DTO\Providers\Oauth\OAuthToken;
use App\Models\ProviderAccount;
use App\Models\RentalNinjaTeam;
use App\Models\Team;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Psr\Log\LogLevel;

trait ProviderApiOauthTrait
{
    public function ensureLoggedIn(ProviderAccount $account): void
    {
        $token_expiry = $account->oauth_expires_at - 40;
        $time = Carbon::now()->timestamp;
        if ($token_expiry < $time) {
            $SN = $this->provider->shortIdentifier();
            $debugData = ['expires' => $account->oauth_expires_at, 'current_token' => substr($account->oauth_access_token, 16, 8)];
            pnLog("[$SN refreshing token]", $account->team, LogLevel::INFO, $debugData);
            $this->refreshOauthToken($account);
        }
    }

    public function oauthConfig(bool $reconnect = false): OAuthConfig
    {
        $redirect = $reconnect ? $this->config('reconnect_redirect_url') : $this->config('register_redirect_url');

        return new OAuthConfig(
            clientId: $this->config('client_id'),
            clientSecret: $this->config('client_secret'),
            reconnectUrl: $this->config('reconnect_redirect_url'),
            registerUrl: $this->config('register_redirect_url'),
            redirectUrl: $redirect,
            scopes: $this->config('scope'),
        );
    }

    abstract protected function getOauthConnectParams(): array;

    protected function getOauthRefreshParams(string $refreshToken): array
    {
        $config = $this->oauthConfig();

        return [
            'client_id' => $config->clientId,
            'client_secret' => $config->clientSecret,
            'refresh_token' => $refreshToken,
            'grant_type' => 'refresh_token',
        ];
    }

    public function oauthConnectUrl(bool $reconnect = false, ?string $state = null): string
    {
        $url = $this->config('oauth_authorize');
        $params = $this->getOauthConnectParams($reconnect, $state);

        return $url.'?'.http_build_query($params);
    }

    public function getNewOauthToken($auth_code, bool $reconnect = false): OAuthToken
    {
        $conf = $this->oauthConfig($reconnect);
        $params = [
            'client_id' => $conf->clientId,
            'client_secret' => $conf->clientSecret,
            'code' => $auth_code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $conf->redirectUrl,
        ];

        return $this->requestNewToken(params: $params);
    }

    public function refreshOauthToken(ProviderAccount $account): bool
    {
        if (is_null($account->oauth_refresh_token)) {
            return false;
        }
        $team = $account->team;

        $date = Carbon::createFromTimestampUTC($account->oauth_expires_at)->toDateTimeString();

        nLog(string: "Issuing new Tokens for Team {$team->name}. Expiry time: $date", team: $team);

        $params = $this->getOauthRefreshParams($account->oauth_refresh_token);

        try {
            $token = $this->requestNewToken(params: $params);
            $account->setNewOauthToken($token);
        } catch (Exception $e) {
            // Refresh team, since oauth could have been updated in parallel.
            $account->refresh();
            if ($account->oauth_expires_at > now()->addMinute()->timestamp) {
                return true;
            }
            // Otherwise:
            report($e);

            $SN = $this->provider->shortIdentifier();
            if (Str::contains($e->getMessage(), 'invalid_grant')) {
                pnLog("[$SN Oauth] Invalid Grant: Could not refresh team token", $team, LogLevel::WARNING);

                // Team has already cancelled their subscription and removed our access to the account,
                if ($team->subscription()?->canceled()) {
                    pnLog("[$SN Oauth] Team has already cancelled their subscription. Stop trying to renew tokens", $team, LogLevel::WARNING);
                    RentalNinjaTeam::slackNotification("Team $team->name (id $team->id) has already cancelled RN subscription and revoked our access");

                    $account->oauth_refresh_token = null;
                    $account->save();
                }
                // Should we ask our client to reconnect?
            } else {
                pnLog("[$SN Oauth] Error: Could not refresh team token", $team, LogLevel::ERROR, ['error' => $e->getMessage()]);
            }

            return false;
        }

        return true;
    }

    protected function requestNewToken(array $params): OAuthToken
    {
        $url = $this->config('oauth_endpoint');
        $response = Http::asForm()->post($url, $params);

        if ($response->failed()) {
            throw new Exception("Error while requesting new token: {$response->body()}", $response->status());
        }
        $response = $response->collect();

        abort_unless($response->has('access_token'), 403);
        $tokenCreatedAt = $response->get('created_at', now()->getTimestamp());
        $expires = Carbon::createFromTimestampUTC($tokenCreatedAt)
            ->addSeconds($response->get('expires_in') - 10)
            ->getTimestamp();
        $response->offsetSet('expires', $expires);

        return new OAuthToken(
            access_token: $response->get('access_token'),
            expires: $expires,
            refresh_token: $response->get('refresh_token')
        );
    }

    public function oauthRandomState(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }
}
