<?php

namespace App\DataProviders\Providers;

use App\DataProviders\ApiConnectors\ProviderConnector;
use App\DataProviders\ApiConnectors\TokeetConnector;
use App\DataProviders\ProviderApi\ProviderApi;
use App\DataProviders\ProviderApi\TokeetApi;
use App\DTO\Providers\Smoobu\SmoobuResponseDto;
use App\DTO\Team\TeamRegistrationData;
use App\Enum\TeamStatusEnum;
use App\Flavors\TokeetFlavor;
use App\Models\Booking;
use App\Models\Team;
use Exception;

class Tokeet extends NinjaProvider
{
    const ID = 5;

    const INTERNAL_NAME = 'Tokeet';

    public function getProviderConnector(Team $team): ProviderConnector
    {
        return new TokeetConnector($team);
    }

    public function getProviderApi(): ?ProviderApi
    {
        return TokeetApi::instance();
    }

    public function fullName(): string
    {
        return 'Tokeet';
    }

    public function internalFullName(): string
    {
        return self::INTERNAL_NAME;
    }

    public function providerLogoPath(): ?string
    {
        return 'img/tokeet/tokeet_icon.png';
    }

    public function providerFrontendLogoPath(): ?string
    {
        return 'assets/images/ota/tokeet.png';
    }

    public function shortIdentifier(): string
    {
        return 'TK';
    }

    public function hasWebsiteSignup(): bool
    {
        return false;
    }

    public function getRegistrationData(string $authorization): TeamRegistrationData
    {
        // Account
        $api = new TokeetApi;
        $token = $api->getNewOauthToken(auth_code: $authorization);
        $res = $api->rawGet(endpoint: 'auth/account', authorization: $token->access_token);
        $name = $res->get('name');
        $accountId = $res->get('account');
        $email = $res->get('email');
        $rentalCount = $res->get('rentalsAmount');

        // Owner
        $owner = $api->rawGet(endpoint: 'v1/user/me', authorization: $token->access_token)->get('data');

        return new TeamRegistrationData(
            providerId: Tokeet::ID,
            flavorId: TokeetFlavor::FLAVOR_ID,
            teamName: $name,
            teamEmail: $email,
            rentalCount: $rentalCount,
            status: TeamStatusEnum::enabled,
            oauth: $token,
            providerAccountId: $accountId,
            userExternalId: $owner['pkey'],
            userEmail: $owner['primaryemail'],
            userName: trim($owner['firstname'].' '.$owner['lastname']),
        );
    }

    public function id(): int
    {
        return self::ID;
    }

    /**
     * @throws Exception
     */
    public function registerUrl(): ?string
    {
        return '';
    }

    public function providerFlavor(): string
    {
        return TokeetFlavor::class;
    }

    public function configFile(string $string, mixed $default = null): mixed
    {
        return config('tokeet.'.$string, $default);
    }

    public function providerDomain(): string
    {
        return config('tokeet.login_redirect_url');
    }

    public function enabled(): bool
    {
        return config('tokeet.enabled');
    }

    public function getHttpResponseDtoClass(): string
    {
        return SmoobuResponseDto::class;
    }

    public function getHttpResponseHeaders(): array
    {
        return [];
    }

    public function canBeCanceled(Booking $b): bool
    {
        return false;
    }

    public function teamVerified(): bool
    {
        return true;
    }
}
