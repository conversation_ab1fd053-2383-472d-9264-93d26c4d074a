<?php

namespace App\DataProviders\ApiResolvers\RentalsUnited\GuestCommunications;

use App\DataProviders\ApiResolvers\ProviderResourceResolver;
use App\Enum\BookingStatusEnum;
use App\Exceptions\NinjaAddContextException;
use App\Models\Booking;
use App\Models\Team;
use App\Models\Thread;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class AbstractRUThreadsResolver extends ProviderResourceResolver
{
    protected int $providerId;

    /**
     * @param  $singleId:  is the string of the booking external id
     */
    protected function callback(Team $team, Collection $response, bool $force = false, $singleId = 0, $extraInfo = [])
    {
        if (! empty($response->get('ErrorMessage'))) {
            pnLog("Received a wrong response for Threads Endpoint in booking $singleId", $team);
            report(new NinjaAddContextException(team: $team, context: ['external_id' => $singleId], message: $response->get('ErrorMessage')));

            return;
        }

        $booking = Booking::query()
            ->withoutGlobalScopes()
            ->onTeam($team)
            ->whereExternalIdStartsWith($singleId)
            ->onProvider($this->providerId)
            ->with('threads')
            ->firstOrFail();

        /* We are no longer deleting threads as RU will now delete threads older than 2 years and we want to keep them
        // Delete threads not present in the current response
        if (! array_key_exists('is_webhook', $extraInfo) || ! $extraInfo['is_webhook']) {
            $toDelete = $booking->threads?->pluck('external_id')->diff($response->pluck('ID'));
            if ($toDelete && $toDelete->isNotEmpty()) {
                Thread::query()
                      ->where('team_id', $team->id)
                      ->where('booking_id', $booking->id)
                      ->whereIn('external_id', $toDelete)
                      ->delete();
            }
        }*/

        $response->each(function (array $data) use ($team, $booking) {
            $thread = $this->getThread($team, $booking, $data);
            $thread->communication_channel = $data['CommunicationChannel'];
            $thread->last_message_date = Carbon::parse($data['LastMessageDate']);
            $thread->provider_id = $this->providerId;

            // TODO: Temporal patch to avoid threads pointing to the wrong booking
            if ($thread->booking_id !== $booking->id && $booking->status != BookingStatusEnum::LEAD) {
                $thread->booking_id = $booking->id;
            }

            $thread->save();
        });
    }

    private function getThread(Team $team, Booking $booking, array $data): Thread
    {
        $externalId = $data['ID'];
        $thread = $booking->threads?->firstWhere('external_id', $externalId);

        if ($thread) {
            return $thread;
        }

        $thread = new Thread();
        $thread->team_id = $team->id;
        $thread->external_id = $externalId;
        $thread->booking_id = $booking->id;

        return $thread;
    }
}
