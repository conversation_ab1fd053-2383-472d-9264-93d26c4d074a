<?php

namespace App\DataProviders\ApiResolvers\RentalsUnited\Rentals;

use App\Actions\Rentals\CreateRentalsForNewTeamAction;
use App\Actions\Rentals\SeasonalPrices\Import\FetchRatesFromRUImportDataAction;
use App\Actions\Sync\UploadRentalPhotosToS3Action;
use App\DataProviders\ApiConnectors\ProviderConnector;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\DataProviders\Providers\NoProvider;
use App\DTO\ChannelManager\TranslationArray;
use App\Enum\FeeKindEnum;
use App\Enum\FeeRateKindEnum;
use App\Models\ChannelManager\LanguagesCM;
use App\Models\Fee;
use App\Models\Rental;
use App\Models\RentalPicture;
use App\Models\Team;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

/**
 * This was used for the setup before having the DualSetupRentalAction. I think this can be now deleted.
 */
class CMRentalResolver extends AbstractRURentalResolver
{
    public function __construct()
    {
        $this->provider = ChannelManagerProvider::ID;
    }

    protected function getRental(Team $team, int $ruId): Rental
    {
        return Rental::withTrashed()->firstOrNew(['team_id' => $team->id, 'external_id' => $ruId]);
    }

    protected function resolveCollection(Team $team, Collection $response, bool $force): void
    {
        // Take properties from the response and ignore 'Example Rental to replace';
        $properties = collect(data_get($response, 'Properties.Property'));
        $properties = $properties
            ->filter(fn (array $r) => ! Str::of($r['Name'])->contains(CreateRentalsForNewTeamAction::NAMES));
        // We do not allow multiple imports.
        $ruPropertiesIds = data_get($properties, '*.ID.data');

        if (is_null($ruPropertiesIds)) { // If the user has no properties, we do not need to sync.
            return;
        }

        $existingRentals = Rental::withTrashed()
            ->onProvider(ChannelManagerProvider::ID)
            ->where('team_id', '=', $team->id)
            ->pluck('external_id');

        // TODO: QUICK FIX FOR TEAM 11764: Add external_id 3866454 to existingRentals
        if ($team->id === 11764) {
            $existingRentals = $existingRentals->push(3866454);
        }

        collect($ruPropertiesIds)
            ->diff($existingRentals)
            ->each(fn (int $importedPropertyId) => ProviderConnector::from($team, ChannelManagerProvider::ID)->updateRental($importedPropertyId)
            );
    }

    protected function updateOrCreatePhotos(array $resp, Rental $rental): void
    {
        $images = collect(data_get($resp, 'Images.Image'));
        $captions = collect(data_get($resp, 'ImageCaptions.ImageCaption'));

        if (is_null($images) || $rental->photos->isNotEmpty()) {
            return;
        }
        $order = 1;
        foreach ($images as $rentalPhoto) {
            $data = $captions
                ->where('attr_ImageReferenceID', '=', data_get($rentalPhoto, 'attr_ImageReferenceID'))
                ->mapWithKeys(fn (array $caption) => [
                    LanguagesCM::firstWhere('id', $caption['attr_LanguageID'])?->code => $caption['data'],
                ])
                ->toArray();
            $descriptions = new TranslationArray($data);

            $url = $rentalPhoto['data'];

            $rnPhoto = RentalPicture::create([
                'team_id' => $rental->team_id,
                'rental_id' => $rental->id,
                'provider_url_hash' => null,
                'provider_url' => $url,
                'order' => $order,
                'image_type' => $rentalPhoto['attr_ImageTypeID'],
                'description' => $descriptions,
            ]);

            // If we run this action in Nova, it is run inside a transaction. We need to make sure that data is stored when the job is run.
            UploadRentalPhotosToS3Action::dispatch($rnPhoto, true)->delay(5);
            $order += 1;
        }
    }

    protected function fetchRates(Team $team, Rental $rental, array $resp): void
    {
        // 1: Process fees
        collect(data_get($resp, 'AdditionalFees.AdditionalFee'))
            ->each(fn (array $fee) => $this->addRentalFee($team, $rental, $fee));

        // 2: Process rates
        FetchRatesFromRUImportDataAction::run($team, $rental);
    }

    private function addRentalFee(Team $team, Rental $rental, array $fee)
    {
        try {
            $rate = round(data_get($fee, 'Value'), 2);
            $name = data_get($fee, 'attr_Name');
            $intVal = ninjaIntval($rate);
            $feeModel = Fee::firstOrCreate([
                'team_id' => $team->id,
                'kind' => $this->getFeeKindEnum(data_get($fee, 'attr_FeeTaxType')),
                'rate_kind' => $this->getRateKindEnum(data_get($fee, 'attr_KindID')),
                'rate' => $rate,
            ], [
                'name' => "$name $intVal",
                'provider_id' => NoProvider::ID,
            ]);

            $rental->rentalFees()->updateOrCreate(['fee_id' => $feeModel->id], [
                'order' => intval(data_get($fee, 'attr_Order')),
                'required' => data_get($fee, 'attr_Optional') == 'false',
                'public' => true,
                'rate' => $rate,
                'rate_kind' => $this->getRateKindEnum(data_get($fee, 'attr_KindID')),
                'collect_at_booking_time' => data_get($fee, 'attr_CollectTime') === '1',
            ]);
        } catch (Exception $e) {
            pnLog($e->getMessage(), $team);

            return;
        }
    }

    private function getRateKindEnum(int $rate): FeeRateKindEnum
    {
        return match ($rate) {
            1 => FeeRateKindEnum::fixed,
            5 => FeeRateKindEnum::fixedPerPerson,
            2 => FeeRateKindEnum::fixedPerNight,
            6 => FeeRateKindEnum::fixedPerPersonPerNight,
            3 => FeeRateKindEnum::independentPercentage,
            4 => FeeRateKindEnum::cumulativePercentage,
            14 => FeeRateKindEnum::percentagePerPerson,
            default => throw new Exception("Invalid rate code: $rate"),
        };
    }

    private function getFeeKindEnum(int $type): FeeKindEnum
    {
        return match ($type) {
            2 => FeeKindEnum::vat,
            0 => FeeKindEnum::other,
            41 => FeeKindEnum::cleaning,
            5 => FeeKindEnum::cityTax,
            3 => FeeKindEnum::touristTax,
            18 => FeeKindEnum::housekeeping,
            38 => FeeKindEnum::extraGuest,
            29 => FeeKindEnum::pet,
            31 => FeeKindEnum::parking,
            33 => FeeKindEnum::service,
            34 => FeeKindEnum::resort,
            8 => FeeKindEnum::bedLinen,
            17 => FeeKindEnum::kitchenLinen,
            14 => FeeKindEnum::linenPackage,
            6 => FeeKindEnum::towel,
            7 => FeeKindEnum::electricity, // Assuming 'utilities' maps to 7 as well
            12 => FeeKindEnum::waterUsage,
            30 => FeeKindEnum::internet,
            9 => FeeKindEnum::gas,
            10 => FeeKindEnum::oil,
            11 => FeeKindEnum::wood,
            15 => FeeKindEnum::heating,
            16 => FeeKindEnum::airConditioning,
            13 => FeeKindEnum::transfer,
            19 => FeeKindEnum::airportShuttle,
            20 => FeeKindEnum::boatShuttle,
            21 => FeeKindEnum::seaPlane,
            28 => FeeKindEnum::creditCard,
            default => throw new Exception("Invalid fee type code: $type"),
        };
    }
}
