<?php

namespace App\DataProviders\ApiResolvers;

use App\Models\Team;
use App\Support\CustomBatch;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * All resolvers should extend this class which is responsible for handling
 * call backs from api, modifying their response and storing the data in our database.
 *
 * Additionally classes that extend this abstract class should emit events or dispatch jobs
 * whenever required.
 *
 * Class ProviderResourceResolver
 */
abstract class ProviderResourceResolver
{
    public int $batchNumber;

    protected array $additionalInformation = [];

    private string $endpoint;

    /**
     * Save the value and return the id of the first value.
     *
     * @return int
     */
    public static function runAndGetId(Team $team, array|Collection|null $data, bool $force = false, $singleId = 0, $extraInfo = [])
    {
        if (is_array($data)) {
            $data = collect($data);
        }
        // Save
        (new static())->callback($team, $data, $force, $singleId, $extraInfo);

        // Return the id.
        return $data->first()['id'];
    }

    public static function runSingle(Team $team, array|Collection|null $data, bool $force = false, $singleId = 0, $extraInfo = []): mixed
    {
        if (is_array($data)) {
            $data = collect($data);
        }

        // Save
        return (new static())->callback($team, $data, $force, $singleId, $extraInfo);
    }

    /**
     * Takes care of the parsing and storing of External APIs from providers.
     */
    abstract protected function callback(Team $team, Collection $response, bool $force = false, $singleId = 0, $extraInfo = []);

    protected static function nowToStore(): ?int
    {
        return self::date(now());
    }

    protected static function date($date, bool $allow_null = false): ?int
    {
        if ($allow_null === true && $date == null) {
            return null;
        }

        if (! isset($date)) {
            return 0;
        }

        if ($date instanceof Carbon) {
            return $date->getTimestamp();
        }

        if (is_int($date)) {
            return Carbon::createFromTimestamp($date)
                ->getTimestamp();
        }

        return ($date == null || $date <= 0
            ? 0
            : (new Carbon($date))->getTimestamp() >= 0)
            ? (new Carbon($date))->getTimestamp()
            : 0;
    }

    protected static function getDateTimeToStore($date): ?Carbon
    {
        return $date == null ? null : Carbon::parse($date);
    }

    public function setAdditionalInformation(?array $additionalInformation): void
    {
        $this->additionalInformation = $additionalInformation;
    }

    public function runWithContext(
        Team $team,
        Collection $response,
        string $endpoint,
        int $batchNumber,
        bool $force = false,
        int|string $singleId = 0,
        array $extraInfo = []
    ): void {
        $this->setContext($endpoint, $batchNumber);
        $this->run($team, $response, $force, $singleId, $extraInfo);
    }

    public function setContext(string $endpoint, int $batchNumber): self
    {
        $this->endpoint = $endpoint;
        $this->batchNumber = $batchNumber;

        return $this;
    }

    public function run(Team $team, Collection $response, bool $force = false, int|string $single_id = 0, array $headers = []): ?Collection
    {
        return $this->callback($team, $response, $force, $single_id, $headers);
    }

    public function getEndpoint(): ?string
    {
        return $this->endpoint;
    }

    protected function ensureValidStringOrNull($value): ?string
    {
        return $value === '' ? null : $value;
    }

    /**
     * Ensures a boolean value is valid for storage in database.
     */
    protected function bool(bool $value): int
    {
        return intval($value);
    }

    /**
     * Inserts or updates the records in the database.
     */
    public function updateInDatabase(Model $model, array $parsedRecordsArray, $idCol = 'id'): void
    {
        $parsedRecordsArray = $this->filterDuplicates($parsedRecordsArray);
        $ids = array_unique(array_column($parsedRecordsArray, $idCol));

        // I am assuming we always get here from BookingSyncRequester for a single team_id, I must validate with Pol.
        $teamId = -1;
        if ($parsedRecordsArray && count($parsedRecordsArray) > 0) {
            $teamId = $parsedRecordsArray[0]['team_id'];
        }

        $existing = DB::table($model->getTable())
            ->whereIn($idCol, $ids)
            ->where('team_id', $teamId)
            ->select($idCol)
            ->pluck($idCol)
            ->toArray();

        $toUpdate = ! empty($existing) ? Arr::where($parsedRecordsArray, fn ($value) => in_array($value[$idCol], $existing)) : [];
        $databaseManager = app()->db;
        $batch = new CustomBatch($databaseManager);

        if (! empty($toUpdate)) {
            $batch->update($model, $toUpdate, $idCol, false, 'team_id');
        }

        $toUpdate = null;
        $new = array_unique(array_diff($ids, $existing));
        $existing = null;
        $ids = null;
        $toInsert = ! empty($new) ? Arr::where($parsedRecordsArray, fn ($value) => in_array($value[$idCol], $new)) : [];

        if ($idCol == 'id ') {
            $noId = Arr::where($parsedRecordsArray, fn ($value) => ! array_key_exists('id', $value) || is_null($value['id']));
            $toInsert = array_merge($toInsert, $noId);
        }

        if (! empty($toInsert)) {
            $batch->insert($model, array_keys(Arr::first($toInsert)), array_values($toInsert), 500);
        }
        // Set back the default connection

        $batch = null;
        $toInsert = null;
    }

    /**
     * Filter duplicated values from an array given the identifier.
     */
    private function filterDuplicates(array $parsedRecordsArray): array
    {
        $toAvoid = Arr::where($parsedRecordsArray,
            fn ($value) => ! array_key_exists('id', $value) || is_null($value['id']));

        // Handle possible duplicates.
        $parsedRecordsArray = Arr::where($parsedRecordsArray,
            fn ($value) => array_key_exists('id', $value) && ! is_null($value['id']));

        $ids = Arr::sort(Arr::pluck($parsedRecordsArray, 'id'));
        $duplicates = array_keys(Arr::where(array_count_values($ids), fn ($value) => $value > 1));

        $cleanOfDuplicates = Arr::where($parsedRecordsArray, fn ($value) => ! in_array($value['id'], $duplicates));

        $duplicates = Arr::where($parsedRecordsArray, fn ($value) => in_array($value['id'], $duplicates));

        $duplicateIds = array_unique(Arr::pluck($duplicates, 'id'));

        $cleanedDuplicates = [];
        if (! empty($duplicateIds)) {
            foreach ($duplicateIds as $duplicateId) {
                $cleanedDuplicates[] = Arr::collapse(Arr::where($duplicates,
                    fn ($value) => $value['id'] == $duplicateId));
            }

            return array_merge($cleanOfDuplicates, $cleanedDuplicates, $toAvoid);
        }

        return array_merge($parsedRecordsArray, $toAvoid);
    }

    public function cleanCollection($data, array $columns): array
    {
        $colKeys = array_flip($columns);

        if ($data instanceof Collection) {
            $data = $data->toArray();
        }

        return collect($data)->map(fn ($item) => array_intersect_key($item, $colKeys))->all();
    }
}
