<?php

namespace App\DataProviders\ApiResolvers\Bookingsync;

use App\Actions\Bookings\GetNameForFeeOrTaxAction;
use App\DataProviders\ApiResolvers\ProviderResourceResolver;
use App\Models\BookingTag;
use App\Models\Team;
use Illuminate\Support\Collection;

class BSBookingTagResolver extends ProviderResourceResolver
{
    /** @noinspection PhpUnhandledExceptionInspection */
    protected function callback(Team $team, Collection $response, bool $force = false, $singleId = 0, $extraInfo = [])
    {
        $deletedTags = collect(data_get($response, 'meta.deleted_ids', []));

        $response = $response->get('bookings_tags', $response);

        $existingTags = BookingTag::query()
            ->withTrashed()
            ->where('team_id', '=', $team->id)
            ->get();

        foreach ($response as $tags) {
            $id = $tags['id'];
            /** @var BookingTag $r */
            $r = $existingTags->firstWhere('id', '=', $id) ?? new BookingTag();
            $r->id = $id;
            $r->team_id = $team->id;
            $r->name = GetNameForFeeOrTaxAction::run($tags['name'], $team);
            $r->color = $tags['color'];
            $r->logo = $tags['logo'];
            $r->created_at = self::date($tags['created_at']);
            $r->updated_at = self::date($tags['updated_at']);
            if ($r->trashed()) {
                $r->restore();
            }
            $r->saveOrFail();
        }

        if ($deletedTags->isNotEmpty()) {
            $existingTags
                ->whereNotIn('id', $deletedTags)
                ->each(fn (BookingTag $bt) => $bt->delete());
        }
    }
}
