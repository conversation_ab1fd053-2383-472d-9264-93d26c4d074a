<?php

namespace App\DataProviders\ApiResolvers\Bookingsync;

use App\DataProviders\ApiResolvers\ProviderResourceResolver;
use App\DataProviders\Providers\BookingSync;
use App\Models\Client;
use App\Models\Team;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Schema;

class BSClientResolver extends ProviderResourceResolver
{
    /**
     * @throws Exception
     */
    protected function callback(Team $team, Collection $response, bool $force = false, $singleId = 0, $extraInfo = [])
    {
        $response = $response->get('clients', $response);

        $cols = Schema::getColumnListing('client');
        $batch = [];
        foreach ($response as $client) {
            $client['id'] = intval($client['id']);
            $client['team_id'] = $team->id;
            $client['external_id'] = $client['id'];
            $client['provider_id'] = BookingSync::ID;
            $client['created_at'] = self::date($client['created_at']);
            $client['updated_at'] = self::date($client['updated_at']);
            $client['phones'] = json_encode($this->cleanPhones($client['phones'] ?? []));
            $client['addresses'] = json_encode($client['addresses']);
            $client['emails'] = json_encode($this->cleanEmails($client['emails'] ?? []));
            $batch[] = $client;
        }
        $data = Client::hydrate($batch)
            ->toArray();
        $data = $this->cleanCollection($data, $cols);
        $this->updateInDatabase(new Client(), $data);
    }

    protected function cleanEmails(array $emails): array
    {
        foreach ($emails as $i => $email) {
            if (! array_key_exists('email', $email) || empty($email['email'])) {
                unset($emails[$i]);

                continue;
            }
            $emails[$i] = Arr::only($email, Client::EMAIL_KEYS);
        }

        return $emails;
    }

    protected function cleanPhones(array $phones): array
    {
        foreach ($phones as $i => $phone) {
            if (! array_key_exists('number', $phone) || empty($phone['number'])) {
                unset($phones[$i]);

                continue;
            }
            $phones[$i] = Arr::only($phone, Client::PHONE_KEYS);
        }

        return $phones;
    }
}
