<?php

namespace App\DataProviders\ApiResolvers\Bookingsync;

use App\Actions\Sync\UploadRentalPhotosToS3Action;
use App\DataProviders\ApiResolvers\ProviderResourceResolver;
use App\DataProviders\Providers\BookingSync;
use App\Models\Rental;
use App\Models\RentalPicture;
use App\Models\Team;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class BSPhotoResolver extends ProviderResourceResolver
{
    protected function callback(Team $team, Collection $response, bool $force = false, $singleId = 0, $extraInfo = [])
    {
        $deletedIds = Arr::get($response->get('meta'), 'deleted_ids');

        $response = collect($response->get('photos', $response));

        $rentals = $response->pluck('links.rental')->unique();
        $externalIds = $response->pluck('id')->unique();
        $numToStore = config('ninja-sync.num_rental_photos');

        if ($team->migrating) {
            $numToStore = 99;
        }

        // Get existing rental ids from received rentals
        $existingRentals = Rental::query()
            ->onProvider(BookingSync::ID)
            ->onTeam($team)
            ->whereIn('external_id', $rentals)
            ->pluck('external_id', 'id');

        // Filter received photos for deleted rentals
        $response = $response
            ->filter(fn (array $p) => ! empty($existingRentals->get($p['links']['rental'])));

        // Load existing models from the database
        $existingPictures = RentalPicture::query()
            ->where('team_id', $team->id)
            ->whereIn('rental_id', $existingRentals->values())
            ->whereIn('external_id', $externalIds)
            ->get();

        // Remove deleted RentalPicture. Each is used to delete the image from Storage.
        $this->removeDeletedRentalPicture($deletedIds, $team);

        foreach ($response as $photo) {
            if ($photo['position'] > $numToStore) {
                // If the image is repositioned and it was saved, we can delete it.
                $existingPictures->firstWhere('external_id', '=', $photo['id'])?->delete();

                continue;
            }

            $rPicture = $existingPictures->firstWhere('external_id', '=', $photo['id']) ?? new RentalPicture();
            $existingPictures = $existingPictures->where('external_id', '!=', $photo['id']);

            $rPicture->external_id = intval($photo['id']);
            $rPicture->order = $photo['position'];
            $rPicture->rental_id = $photo['links']['rental'];
            $rPicture->team_id = $team->id;
            $rPicture->provider_url = $photo['giant_url'];
            $rPicture->provider_url_hash = sha1($photo['giant_url']);
            $rPicture->created_at = self::getDateTimeToStore($photo['created_at']);
            $rPicture->updated_at = self::getDateTimeToStore($photo['updated_at']);
            $shouldDispatchUpload = $rPicture->isDirty('provider_url_hash') || is_null($rPicture->url);
            $rPicture->save();

            UploadRentalPhotosToS3Action::dispatchIf($shouldDispatchUpload, $rPicture);
        }
    }

    protected function removeDeletedRentalPicture(?array $deletedIds, Team $team): void
    {
        if (is_null($deletedIds)) {
            return;
        }

        RentalPicture::query()
            ->where('team_id', $team->id)
            ->whereIn('external_id', $deletedIds)
            ->get()
            ->each(fn (RentalPicture $rp) => $rp->delete());
    }
}
