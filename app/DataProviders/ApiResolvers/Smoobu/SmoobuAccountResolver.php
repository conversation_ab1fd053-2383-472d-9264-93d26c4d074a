<?php

namespace App\DataProviders\ApiResolvers\Smoobu;

use App\DataProviders\ApiResolvers\ProviderResourceResolver;
use App\DataProviders\Providers\Smoobu;
use App\Models\Team;
use Exception;
use Illuminate\Support\Collection;
use Throwable;

class SmoobuAccountResolver extends ProviderResourceResolver
{
    /**
     * @throws Exception|Throwable
     */
    protected function callback(Team $team, Collection $response, bool $force = false, $singleId = 0, $extraInfo = [])
    {
        $account = $team->getProviderAccount(Smoobu::ID);
        $account->provider_id = Smoobu::ID;
        $account->account_id = $response->get('id');
        $account->saveOrFail();
        nLog("Smoobu Account {$account->provider_id} Saved", $team);
    }
}
