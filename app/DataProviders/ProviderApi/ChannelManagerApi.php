<?php

namespace App\DataProviders\ProviderApi;

use App\DataProviders\ProviderConstants;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\DTO\Providers\ProviderSyncRequestData;
use App\Models\Booking;
use App\Models\ProviderAccount;
use Exception;
use Illuminate\Support\Carbon;
use Spatie\ArrayToXml\ArrayToXml;
use Str;

class ChannelManagerApi extends RentalsUnitedApi
{
    const ENDPOINT_AVAILABILITY = 'availability';
    const ENDPOINT_PRICES = 'prices';
    const ENDPOINT_CM_SALES_CHANNELS = 'sales_channels';
    const ENDPOINT_CM_RENTALS = 'cm_rentals';
    const ENDPOINT_BOOKING_PRICE = 'cm_booking_price';

    public function __construct()
    {
        parent::__construct();
        $this->provider = ChannelManagerProvider::get();
    }

    protected function getBody(ProviderSyncRequestData $requestData): array|string
    {
        $endpoint = $requestData->endpoint;
        $team = $requestData->team;
        $providerAccount = $team->getProviderAccount($this->provider->id());

        $array = [];

        if ($endpoint == ProviderConstants::ENDPOINT_RENTALS) {
            $action = $this->getRentalAction($requestData);
            $array = $this->getRentalBody($requestData, $providerAccount, $array);
        } elseif ($endpoint == ProviderConstants::ENDPOINT_BOOKINGS) {
            $action = $this->getBookingAction($requestData);
            $array = $this->getBookingBody($requestData, $providerAccount, $array);
        } elseif ($endpoint == ProviderConstants::ENDPOINT_THREADS || $endpoint == ProviderConstants::ENDPOINT_MESSAGES) {
            return [];
        } else {
            $action = $this->getCMEndpointAction($requestData);
            $array = $this->getCMEndpointBody($requestData, $providerAccount, $array);
        }

        return $this->getApiCallBody($action, $array, $providerAccount);
    }

    public function getHeaders(?ProviderAccount $account = null, ?string $authorization = null): array
    {
        $user = $account->account_id;
        $password = $account->api_key;

        return [
            'content-type' => 'application/json',
            'cache-control' => 'no-cache',
            'X-Ninja-Provider' => $account->provider_id,
            'authorization' => 'Basic '.base64_encode("$user:$password"),
            'X-Ninja-Team' => $account->team_id,
            'X-Ninja-Provider-Id' => $account->account_id,
        ];
    }

    protected function getQueryParams(ProviderSyncRequestData $requestData): array
    {
        $params = $this->config('endpoints_configuration.'.$requestData->endpoint.'.extra_params', []);

        if ($requestData->endpoint === ProviderConstants::ENDPOINT_THREADS || $requestData->endpoint === ProviderConstants::ENDPOINT_MESSAGES) {
            $params['parentId'] = $requestData->singleId;
        }

        return $params;
    }

    public function acceptRequest(ProviderAccount $account, Booking $booking): bool
    {
        $endpoint = 'Push_ConfirmReservation_RQ';
        $extra = [
            'ReservationID' => Str::of($booking->external_id)->before('_'),
        ];

        return $this->pushApiMethod($account, $endpoint, $booking, $extra);
    }

    public function rejectRequest(ProviderAccount $account, Booking $booking, ?string $reason, ?string $messageToGuest, ?string $messageToChannel): bool
    {
        $endpoint = 'Push_RejectRequest_RQ';
        $extra = [
            'ReservationID' => Str::of($booking->external_id)->before('_'),
            'Reason' => $reason,
            'MessageToGuest' => $messageToGuest,
            'MessageToChannel' => $messageToChannel,
        ];
        $extra = array_filter($extra); // Remove null values

        return $this->pushApiMethod($account, $endpoint, $booking, $extra);
    }

    public function cancelReservation(ProviderAccount $account, Booking $booking): bool
    {
        $endpoint = 'Push_CancelReservation_RQ';
        $extra = [
            'ReservationID' => Str::of($booking->external_id)->before('_'),
            'CancelTypeID' => 2,
        ];

        return $this->pushApiMethod($account, $endpoint, $booking, $extra);
    }

    private function pushApiMethod(ProviderAccount $account, string $method, Booking $booking, array $extraBody): bool
    {
        $body = $this->getApiCallBody($method, $extraBody, $account);
        $response = $this->postXmlRaw($method, $body);

        return $response->Status == 'Success';
    }

    public function getApiCallBody(string $action, array $extra = [], ?ProviderAccount $account = null): string
    {
        if (is_null($account)) {
            $username = config('channel_manager.username');
            $password = config('channel_manager.password');
        } else {
            $username = $account->account_id;
            $password = $account->api_key;
        }

        $array = [
            'Authentication' => [
                'UserName' => $username,
                'Password' => $password,
            ],
        ];
        if (! empty($extra)) {
            $array = array_merge($array, $extra);
        }

        /** @noinspection PhpUnhandledExceptionInspection */
        $arrayToXml = new ArrayToXml($array, $action);

        return $arrayToXml->dropXmlDeclaration()->prettify()->toXml();
    }

    protected function getBookingBody(ProviderSyncRequestData $requestData, ProviderAccount $providerAccount, array $array): array
    {
        if ($requestData->singleId > 0) {
            $array['ReservationID'] = $requestData->singleId;

            return $array;
        }

        $array['LocationID'] = 0;
        $array['Statuses'] = ['StatusID' => [1, 2, 4, 6, 7, 8]]; // If we don't specify, we get 1 & 2. But we need to add Statuses to particularly ask for 4.
        $array['User'] = $providerAccount->account_id;

        if ($requestData->force) {
            $array['Extended'] = 'true';
            // "Custom" pagination asks bookings of one month. Uses 'page' as a counter.
            $array['DateFrom'] = Carbon::today()->endOfDay()->subMonths($requestData->page)->format('Y-m-d H:i:s');
            $array['DateTo'] = Carbon::today()->endOfDay()->subMonths($requestData->page - 1)->format('Y-m-d H:i:s');
        } else {
            $array['DateFrom'] = $requestData->updatedSince?->format('Y-m-d H:i:s') ?? Carbon::today()->subMonth()->format('Y-m-d H:i:s');
            $array['DateTo'] = Carbon::now()->addHour()->format('Y-m-d H:i:s');
        }

        return $array;
    }

    protected function getCMEndpointAction(ProviderSyncRequestData $requestData): string
    {
        return match ($requestData->endpoint) {
            self::ENDPOINT_AVAILABILITY => 'Pull_ListPropertyAvailabilityCalendar_RQ',
            self::ENDPOINT_PRICES => 'Pull_ListPropertyPrices_RQ',
            self::ENDPOINT_CM_SALES_CHANNELS => 'Pull_ListSalesChannels_RQ',
            self::ENDPOINT_CM_RENTALS => 'CM_Pull_PropertiesStatus_RQ',
            self::ENDPOINT_BOOKING_PRICE => 'Pull_GetPropertyAvbPrice_RQ',
            ProviderConstants::ENDPOINT_LEADS => $this->config('endpoints_configuration.'.ProviderConstants::ENDPOINT_LEADS.'.url'),
            default => throw new Exception('Unknown action'),
        };
    }

    protected function getCMEndpointBody(ProviderSyncRequestData $requestData, ProviderAccount $providerAccount, array $array): array
    {
        if ($requestData->endpoint == self::ENDPOINT_AVAILABILITY || $requestData->endpoint == self::ENDPOINT_PRICES) {
            $array['PropertyID'] = $requestData->singleId;
            $array['DateFrom'] = Carbon::today()->startOfDay()->toDateString();
            $array['DateTo'] = Carbon::today()->startOfDay()->addYears(3)->toDateString();

            return $array;
        }
        if ($requestData->endpoint == self::ENDPOINT_CM_SALES_CHANNELS) {
            return [];
        }
        if ($requestData->endpoint == self::ENDPOINT_CM_RENTALS) {
            $array['ChannelId'] = $requestData->singleId;

            return $array;
        }
        if ($requestData->endpoint == self::ENDPOINT_BOOKING_PRICE) {
            $array['PropertyID'] = $requestData->singleId;
            /** @noinspection PhpUndefinedMethodInspection */
            $array['DateFrom'] = $requestData->data->get('from')->toDateString();
            /** @noinspection PhpUndefinedMethodInspection */
            $array['DateTo'] = $requestData->data->get('to')->toDateString();
            $array['NOP'] = $requestData->data->get('guests');
            $array['AllowLongstays'] = 'true';

            return $array;
        }
        if ($requestData->endpoint == ProviderConstants::ENDPOINT_LEADS) {
            $array['LocationID'] = 0;
            // Set dates will be used to filter from DateEntered API response parameter
            if ($requestData->force) {
                $array['Extended'] = 'true'; // In case it's needed
                $array['DateFrom'] = Carbon::now()->subYear()->format('Y-m-d H:i:s'); // However, as per RU docs, only last 7 days are returned
                $array['DateTo'] = Carbon::now()->addHour()->format('Y-m-d H:i:s');
            } else {
                $array['DateFrom'] = $requestData->updatedSince?->toDateTimeString() ?? Carbon::today()->endOfDay()->subWeek()->format('Y-m-d H:i:s');
                // As per RU API doc, only last 7 days are returned
                $array['DateTo'] = Carbon::now()->addHour()->format('Y-m-d H:i:s');
            }

            return $array;
        }
        throw new Exception('Unknown action');
    }
}
