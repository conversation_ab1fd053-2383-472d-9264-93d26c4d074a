<?php

/** @noinspection PhpUnhandledExceptionInspection */

namespace App\DataProviders;

/**
 * This class is responsible for the whole sync process for a team.
 * Class ProviderSyncManager.
 */
class ProviderConstants
{
    const TYPE_NORMAL = 'normal';

    const TYPE_FULL = 'full';

    const TYPE_FORCE = 'force';

    const ENDPOINT_ACCOUNTS = 'accounts';

    const ENDPOINT_RENTALS = 'rentals';

    const ENDPOINT_DELETED_PROPS = 'deleted_properties';

    const ENDPOINT_PHOTOS = 'photos';

    const ENDPOINT_CLIENTS = 'clients';

    const ENDPOINT_AVAILABILITIES = 'availabilities';

    const ENDPOINT_FEES = 'fees';

    const ENDPOINT_RENTALS_FEES = 'rentals_fees';

    const ENDPOINT_TAXES = 'taxes';

    const ENDPOINT_PAYMENTS = 'payments';

    const ENDPOINT_BOOKINGS = 'bookings';

    const ENDPOINT_LEADS = 'leads';

    const ENDPOINT_RENTAL_ROOMS = 'rental_rooms';

    const ENDPOINT_SOURCES = 'sources';

    const ENDPOINT_BOOKING_TAGS = 'booking_tags';

    const ENDPOINT_USERS = 'users';

    const ENDPOINT_THREADS = 'threads';

    const ENDPOINT_MESSAGES = 'messages';

    const ENDPOINT_RATES = 'rates';
}
