<?php

namespace App\DataProviders\ApiConnectors;

use App\Actions\Alerts\UpdateBookingPaymentAlertsAction;
use App\Actions\Bookings\GenerateBookingReferenceAction;
use App\Actions\Tasks\ScheduledTask\HandleTasksForBookingAction;
use App\DataProviders\ApiConnectors\Interfaces\CentralizedInboxApi;
use App\DataProviders\ApiConnectors\Interfaces\GuestNotificationsApi;
use App\DataProviders\ApiConnectors\Interfaces\ManageRequestsApi;
use App\DataProviders\ChannelManager\ChannelManagerPusher;
use App\DataProviders\ProviderApi\ChannelManagerApi;
use App\DataProviders\ProviderConstants;
use App\DataProviders\Providers\ChannelManagerProvider;
use App\DataProviders\Providers\NoProvider;
use App\DataProviders\Providers\RentalsUnited;
use App\Enum\BookingStatusEnum;
use App\Exceptions\NinjaAddContextException;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Message;
use App\Models\MessageAttachment;
use App\Models\Source;
use App\Models\Team;
use App\Models\Thread;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Psr\Log\LogLevel;
use Throwable;

class ChannelManagerConnector extends ProviderConnector implements GuestNotificationsApi, CentralizedInboxApi, ManageRequestsApi
{
    public function __construct(public Team $team)
    {
        $this->api = ChannelManagerApi::instance();
        $this->provider = ChannelManagerProvider::get();
        $this->account = $team->getProviderAccount(ChannelManagerProvider::ID);
    }

    public function createBlockInRental(int $rentalId, Carbon $start_date, Carbon $end_date, string $notes): int|Booking
    {
        $serviceName = $this->team->config()->rnAppName();
        $source = Source::firstOrCreate([
            'name' => 'Block in '.$serviceName,
            'team_id' => $this->team->id,
        ], [
            'is_external' => false,
            'provider_id' => 0,
            'created_at' => now()->timestamp,
            'updated_at' => now()->timestamp,
        ]);
        $nights = $start_date->copy()->startOfDay()->diffInDays($end_date);

        $booking = Booking::create([
            'id' => Booking::getRandomId($this->team),
            'provider_id' => NoProvider::ID,
            'team_id' => $this->team->id,
            'rental_id' => $rentalId,
            'start_at' => $start_date->timestamp,
            'end_at' => $end_date->timestamp,
            'canceled_at' => 0,
            'expected_checkin_time' => $start_date->hour,
            'expected_checkout_time' => $end_date->hour,
            'status' => BookingStatusEnum::UNAVAILABLE,
            'reference' => GenerateBookingReferenceAction::run(),
            'nights' => $nights,
            'source_id' => $source->id,
            'notes' => $notes,
            'created_at' => now()->timestamp,
            'updated_at' => now()->timestamp,
        ]);
        ChannelManagerPusher::putCompleteAvailability($this->team, $booking->rental);

        return $booking;
    }

    public function updateBooking(int|string|null $externalId): void
    {
        $ruId = Str::of($externalId)->before('_');
        $this->syncEndpointAndResolve(ProviderConstants::ENDPOINT_BOOKINGS, $ruId);

        // TODO Since payments are not synchronized with RU, we need to recalculate total paid
        $booking = Booking::query()
            ->withoutGlobalScopes()
            ->onProvider(ChannelManagerProvider::ID)
            ->whereExternalIdStartsWith($externalId)
            ->firstOrFail();

        // Update booking
        $totalPaid = BookingPayment::query()
                ->onTeam($booking->team_id)
                ->onBooking($booking->id)
                ->whereNotCancelled()
                ->sum('amount_in_cents') / 100;

        $booking->paid_amount = $totalPaid;
        $booking->save();
    }

    public function deleteBookingById(Booking $booking): bool
    {
        if ($booking->canBeCanceled()) {
            // Booked bookings are canceled in the provider.
            if ($booking->isBooked()) {
                pnLog("[ChannelManagerConnector] Canceling booking $booking->id.", $this->team);
                $success = $this->api->cancelReservation($this->account, $booking);
                if (! $success) {
                    pnLog("[ChannelManagerConnector] Failed to cancel booking $booking->id.", $this->team);

                    return false;
                }
                pnLog("[ChannelManagerConnector] Booking $booking->id cancelled.", $this->team);
            }
            $booking->status = BookingStatusEnum::CANCELED;
            $booking->canceled_at = now()->timestamp;
            $booking->save();

            $booking->rental->updateAvailability();
            ChannelManagerPusher::putCompleteAvailability($this->team, $booking->rental);
            UpdateBookingPaymentAlertsAction::run($booking);
            HandleTasksForBookingAction::run($booking->team, $booking);

            pnLog("[ChannelManagerConnector] Booking $booking->id internally cancelled.", $this->team);

            return true;
        }

        return false;
    }

    public function subscribeToNotificationsWebhooks(): void
    {
        $body = ['Url' => config('app.url').route('cm-webhook-rest-api', absolute: false)];
        $this->api->put($this->account, 'notifications_subscription', $body);
    }

    /** Use this method to send async messages, we do not need to wait for the response and ensure a Message is created. Use sendMessage for sync instead */
    public function sendNotification(Booking $booking, string $content, string $subject): bool
    {
        pnLog('[Send API Notification] Channel Manager', $this->team, LogLevel::INFO, [
            'booking_id' => $booking->id,
            'subject' => $subject,
        ]);

        $message = "$subject\n\n$content";

        $response = $this->postMessage($booking, $message);
        if (data_get($response, 'error_code')) {
            pnLog('[ChannelManagerConnector] Sending a notification, not storing message', $this->team);

            return false;
        }

        return ! is_null($response);
    }

    /** Use this method to send sync messages, thus wait for the response and ensure we have a Message created.
     * Use sendNotification for async instead.
     *
     * @param  Collection<MessageAttachment>|null  $attachments
     */
    public function sendMessage(Booking $booking, Message $message, ?Collection $attachments = null): bool
    {
        $response = $this->postMessage($booking, $message->message, $attachments);

        $message->external_id = data_get($response, 'ID');
        $message->thread_id = data_get($response, 'rn_thead_id');
        $message->external_sender_id = data_get($response, 'Sender.ID');
        $message->sender_name = data_get($response, 'SenderName');
        $message->provider_id = ChannelManagerProvider::ID;

        // Error handling
        $errorCode = data_get($response, 'error_code');
        $errorMessage = data_get($response, 'error_message');
        try {
            [$error, $errorDetails] = $this->parseError($errorCode, $errorMessage);
            $message->error = $error;
            $message->error_details = $errorDetails;
        } catch (Throwable $e) {
            report($e);
            pnLog("[ChannelManagerConnector] Failed to parse error: $errorCode, $errorMessage", $this->team);
        }

        /* It looks like the message can be received from the webhook before this point. Interesting...
         * To solve that, and avoid problems with race conditions, we have delayed the processing of new host message webhooks.
         * Thus, we can guarantee, we will be faster here than the processing of the webhook.
         * Race condition problems:
         * - Who saves first the message, and if we save first here, will the webhook save the attachments first or here?
         * - If the webhook wins, will it have the attachments ready before we return OK to the frontend so the user can see the sent attachments?
         */
        try {
            $message->save();
        } catch (UniqueConstraintViolationException $e) { // This error triggers because we have a unique index in the DB
            // Webhook has won. This situation should never happen. Report to us to increase webhook delay.
            // Just ensure the message is saved correctly, but for attachments, I don't care if they are duplicated. This should be a weird situation.
            report(new NinjaAddContextException(
                $e,
                $booking->team,
                ['message_id' => $message->id],
                "WARNING! New Message webhook processing has been faster than processing the response of our Post. Go to HandleRentalsUnitedRestWebhookAction and increase delay when it's a new message from the Host.",
            ));
            pnLog('[ChannelManagerConnector] Message already saved in the database.', $this->team);
            Message::query()
                ->whereTeamId($this->team->id)
                ->whereProviderId(ChannelManagerProvider::ID)
                ->whereBookingId($message->booking_id)
                ->whereExternalId($message->external_id)
                ->update(['author_id' => $message->author_id]); // We wish to keep the author_id, which doesn't come from the webhook
            $message->delete();
        }

        if (empty($message->error)) { //If the message was not sent, the response is empty and we can't map the attachments
            // Handle attachments
            $responseAttachments = collect(data_get($response, 'Attachments'));
            foreach ($attachments as $attachment) {
                $attachment->external_id = $responseAttachments->firstWhere('Name', $attachment->name)['ID'];
                $attachment->provider_id = ChannelManagerProvider::ID;
                $attachment->save();
            }
        }

        return true;
    }

    protected function getOrFetchBookingThread(Booking $booking): Thread
    {
        $thread = Thread::query()
            ->whereTeamId($this->team->id)
            ->whereBookingId($booking->id)
            ->whereIn('communication_channel', RentalsUnited::THREAD_COMMUNICATION_CHANNELS_WE_POST_TO)
            ->first();

        if (is_null($thread)) {
            pnLog('[ChannelManagerConnector] Thread not found, fetching from Channel Manager', $this->team);
            $this->syncEndpointAndResolve(ProviderConstants::ENDPOINT_THREADS, $booking->getExternalId());
            $thread = Thread::query()
                ->whereTeamId($this->team->id)
                ->whereBookingId($booking->id)
                ->whereIn('communication_channel', RentalsUnited::THREAD_COMMUNICATION_CHANNELS_WE_POST_TO)
                ->firstOrFail();
        }

        return $thread;
    }

    /**
     * @param  Collection<MessageAttachment>|null  $attachments
     */
    private function postMessage(Booking $booking, string $content, ?Collection $attachments = null): ?array
    {
        $thread = $this->getOrFetchBookingThread($booking);

        $requestBody = ['Body' => $content];
        if ($attachments !== null && $attachments->isNotEmpty()) {
            $attachments = $attachments->map(function (MessageAttachment $att) {
                // No need to check mime type as it is checked in the Controller level as well as by RU's API
                return [
                    'Name' => $att->name, // Must contain extension. Also, may reject too long names
                    'Content' => base64_encode(Storage::disk('s3')->get($att->url)),
                ];
            });
            $requestBody['AddAttachmentsPayload'] = $attachments->toArray();
        }

        $response = $this->api->post($this->account,
            "/messaging/threads/$thread->external_id/messages",
            $requestBody,
        );

        // Process errors
        if (is_null(data_get($response, 'ID'))) {
            $errorCode = data_get($response, 'ErrorCode');
            $errorMessage = data_get($response, 'ErrorMessage');
            pnLog("[ChannelManagerConnector] Failed to send message to Channel Manager, error code: $errorCode, error message: $errorMessage", $this->team);

            return [
                'rn_thead_id' => $thread->id,
                'error_code' => $errorCode,
                'error_message' => $errorMessage,
            ];
        }

        // Process normal response
        $response['rn_thead_id'] = $thread->id;

        return $response;
    }

    public function acceptRequest(Booking $booking): bool
    {
        if (! $booking->status->isRequest()) {
            return false;
        }

        $success = $this->api->acceptRequest($this->account, $booking);

        if (! $success) {
            pnLog("[ChannelManagerConnector] Failed to accept booking request $booking->id.", $this->team);

            return false;
        }

        $booking->status = BookingStatusEnum::BOOKED; // TODO: B.com bookings must be confirmed by the guest (Status: Approved)
        $booking->save();
        $this->updateBooking($booking->external_id);

        pnLog("[ChannelManagerConnector] Booking request $booking->id accepted.", $this->team);

        return true;
    }

    public function rejectRequest(Booking $booking, ?string $reason, ?string $messageToGuest, ?string $messageToChannel): bool
    {
        if (! $booking->status->isRequest()) {
            return false;
        }

        $success = $this->api->rejectRequest($this->account, $booking, $reason, $messageToGuest, $messageToChannel);

        if (! $success) {
            pnLog("[ChannelManagerConnector] Failed to cancel booking request $booking->id.", $this->team);

            return false;
        }

        $booking->status = BookingStatusEnum::REJECTED;
        $booking->save();
        $this->updateBooking($booking->external_id);

        pnLog("[ChannelManagerConnector] Booking request $booking->id canceled.", $this->team);

        return true;
    }

    private function parseError(?int $errorCode, ?string $errorDetails): array
    {
        if (is_null($errorCode)) {
            return [null, null];
        }

        return match ($errorCode) {
            28 => ['invalid_host', Str::of($errorDetails)->after('Airbnb:')->trim()],
            29 => ['booking_com_reject', null],
            34 => ['airbnb_reject', Str::of($errorDetails)->after('Airbnb:')->trim()],
            default => ['unknown', $errorDetails],
        };
    }
}
