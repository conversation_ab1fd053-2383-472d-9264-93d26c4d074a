<?php

namespace App\DataProviders\ApiConnectors;

use App\Actions\Bookings\CheckIns\FormatCheckInOutHourAction;
use App\Actions\Bookings\Times\TriggerCheckInOutTimeModifiedEventAction;
use App\Actions\Providers\Generic\ShouldPostToProviderInLocalEnvAction;
use App\Actions\Tasks\ScheduledTask\HandleTasksForBookingAction;
use App\DataProviders\ApiConnectors\Interfaces\BookingCommentsApi;
use App\DataProviders\ApiConnectors\Interfaces\BookingTagsApi;
use App\DataProviders\ApiConnectors\Interfaces\ClientsApi;
use App\DataProviders\ApiConnectors\Interfaces\GuestNotificationsApi;
use App\DataProviders\ApiConnectors\Interfaces\PaymentsApi;
use App\DataProviders\ProviderApi\BookingSyncApi;
use App\DataProviders\Providers\BookingSync;
use App\DTO\Bookings\BookingDto;
use App\Events\CommentCreatedEvent;
use App\Exceptions\NinjaProviderIntegrationException;
use App\Models\Booking;
use App\Models\BookingComment;
use App\Models\BookingPayment;
use App\Models\Client;
use App\Models\Team;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Psr\Log\LogLevel;

class BookingSyncConnector extends ProviderConnector implements BookingCommentsApi, BookingTagsApi, ClientsApi, GuestNotificationsApi, PaymentsApi
{
    public BookingSyncApi $papi;

    public function __construct(public Team $team)
    {
        $this->api = $this->papi = BookingSyncApi::instance();
        $this->provider = BookingSync::get();
        $this->account = $team->getProviderAccount(BookingSync::ID);
    }

    public function updateClient(Client $client): bool
    {
        $request = [
            'clients' => [
                [
                    'phones' => json_decode($client->phones, true),
                    'emails' => json_decode($client->emails, true),
                ],
            ],
        ];
        $this->api->put($this->account, 'clients', $request, $client->id);

        return true;
    }

    public function createBlockInRental(int $rentalId, Carbon $start_date, Carbon $end_date, string $notes): int|Booking
    {
        $body_request = [
            'bookings' => [
                [
                    'unavailable' => true,
                    'start_at' => $start_date->toIso8601ZuluString(),
                    'expected_checkin_time' => $start_date->format('H:i'),
                    'end_at' => $end_date->toIso8601ZuluString(),
                    'expected_checkout_time' => $end_date->format('H:i'),
                    'notes' => $notes,
                ],
            ],
        ];

        $result = $this->api->post($this->account, "rentals/$rentalId/bookings", $body_request, 0, 'bookings');

        if (empty($result[0])) {
            nLog('Trying to add new block: Bad response from '.$this->getChannelName(), $this->team, LogLevel::ERROR, $result);
            abort(500, 'Bad Response from '.$this->getChannelName());
        }

        if (! isset($result[0]['id']) && is_int($result[0]['id'])) {
            nLog('Trying to add new block: Missing booking id from '.$this->getChannelName(), $this->team, LogLevel::ERROR, $result);
            abort(500, 'Bad Response from '.$this->getChannelName());
        }

        return $result[0]['id'];
    }

    public function updateBookingByModel(Booking $booking, BookingDto $booking_data): Booking|array|null
    {
        // Add here each attribute to be updated in this booking:
        $data_to_send = collect()
            ->when(isset($booking_data->expected_checkin_time), function (Collection $data) use ($booking, $booking_data) {
                $parsed_time = FormatCheckInOutHourAction::run($booking_data->expected_checkin_time);
                $booking->expected_checkin_time = $parsed_time;
                $booking_data->check_in_out_time_modified = true;

                return $data->put('expected_checkin_time', $parsed_time);
            })
            ->when(isset($booking_data->expected_checkout_time), function (Collection $data) use ($booking, $booking_data) {
                $parsed_time = FormatCheckInOutHourAction::run($booking_data->expected_checkout_time);
                $booking->expected_checkout_time = $parsed_time;
                $booking_data->check_in_out_time_modified = true;

                return $data->put('expected_checkout_time', $parsed_time);
            })
            ->when(isset($booking_data->notes), function (Collection $data) use ($booking, $booking_data) {
                // No need to save, we receive a booking_updated immediately with the new note. However, for local purposes:
                $booking->notes = $booking_data->notes;

                return $data->put('notes', $booking_data->notes);
            })
            ->when(isset($booking_data->final_price), function (Collection $data) use ($booking_data) {
                return $data->put('final_price', $booking_data->final_price);
                // I don't want to save the booking, I want it to come back properly once saved in BS
            })
            ->when(isset($booking_data->initial_price), function (Collection $data) use ($booking_data) {
                return $data->put('initial_price', $booking_data->initial_price);
                // I don't want to save the booking, I want it to come back properly once saved in BS
            })
            ->when(isset($booking_data->discount), function (Collection $data) use ($booking_data) {
                return $data->put('discount', $booking_data->discount);
                // I don't want to save the booking, I want it to come back properly once saved in BS
            })
            ->when(isset($booking_data->bookingFees), function (Collection $data) use ($booking_data) {
                return $data->put('bookings_fees', $booking_data->bookingFees->toArray());
                // I don't want to save the booking, I want it to come back properly once saved in BS
            })
            ->when(isset($booking_data->bookingTaxes), function (Collection $data) use ($booking_data) {
                return $data->put('bookings_taxes', $booking_data->bookingTaxes->toArray());
                // I don't want to save the booking, I want it to come back properly once saved in BS
            });

        if (ShouldPostToProviderInLocalEnvAction::run()) {
            if (! $data_to_send->isEmpty()) {
                $request = ['bookings' => [$data_to_send->toArray()]];
                $response = $this->api->put($this->account, 'bookings', $request, $booking->id);
                // This will trigger a booking_updated webhook which will, in turn, trigger the needed events
                // In case we see many webhooks failing to respond back, we may save the booking here and trigger the notify, as webhook won't do.
                nLog("Saving booking with id $booking->id to BookingSync", $this->team->id);

                return $response;
            }
        } else {
            // For local cases where you don't want to send the booking to BS:
            if ($booking->isDirty()) {
                $booking->save();
                nLog("Saved booking with id $booking->id in DB", $this->team->id);
            }

            // Notify, as we will not receive a webhook
            if ($booking_data->check_in_out_time_modified === true) {
                $updated_booking_data = BookingDto::fromModel($booking);
                TriggerCheckInOutTimeModifiedEventAction::run($this->team, $updated_booking_data);
            }
        }

        return $booking;
    }

    public function deleteBookingById(Booking $booking): bool
    {
        $success = $this->api->delete($this->account, 'bookings', $booking->id);

        if ($success) {
            HandleTasksForBookingAction::run($booking->team, $booking);
        }

        return $success;
    }

    public function deleteComment(int $id): bool
    {
        return $this->api->delete($this->account, 'booking_comments', $id);
    }

    public function deletePayment(Booking $booking, BookingPayment $payment): void
    {
        $this->api->delete($this->account, 'payments', $payment->external_id);
    }

    public function createComment(Booking $booking, string $comment, int $authorId, array $mentions): void
    {
        $request = ['booking_id' => $booking->id, 'booking_comments' => [['content' => $comment]]];
        $result = $this->api->post($this->account, 'booking_comments', $request);
        $this->updateBookingComments(
            booking_id: $booking->id,
            data: $result, // This only contains the current saved booking, not all booking comments
            author_id: $authorId,
            mentions: $mentions
        );
    }

    public function updateBookingComments($booking_id, $data, int $author_id = 0, bool $delete_not_updated = false, array $mentions = [])
    {
        $updated = [];
        foreach ($data as $booking_comment) {
            $booking_query = Booking::query()
                ->whereId($booking_id)
                ->onTeam($this->team->id);
            if (! $booking_query->exists()) {
                continue;
            }

            $id = $booking_comment['id'];
            $r = BookingComment::query()
                ->firstOrNew([
                    'id' => $id,
                    'team_id' => $this->team->id,
                ]);

            $xB = $booking_query->select('created_at')->first();
            $booking_creation_time = Carbon::createFromTimestamp($xB->created_at);

            // TODO: check SaveBookingCommentInDbAction and BSBookingResolver to reuse code
            // If the team was created less than some short time ago
            if ($this->team->shouldNotNotify()) {
                $must_notify = false;
            } // If the content did not change.. do not notify.
            elseif ($r->content == $booking_comment['content']) {
                $must_notify = false;
            } // the booking must be older than 15 minutes ago
            elseif (
                $booking_creation_time->isAfter(Carbon::now()
                    ->subMinutes(15))
            ) {
                $must_notify = false;
            } // If it is an airbnb comment, do not notify.
            elseif (ninja_str_contains($booking_comment['content'], 'Airbnb to pay back')) {
                $must_notify = false;
            } // Price details, do not notify.
            elseif (ninja_str_contains($booking_comment['content'], '### Price Details')) {
                $must_notify = false;
            } else {
                $must_notify = true;
            }

            // SET STUFF.
            $r->fill($booking_comment);
            $r->id = $id;
            $r->team_id = $this->team->id;
            $r->created_at = self::getDateToStore($booking_comment['created_at']);
            $r->updated_at = self::getDateToStore($booking_comment['updated_at']);
            $r->booking_id = $booking_id;
            $r->editable = $booking_comment['editable'];
            $r->mentions = $mentions ?? [];

            $a_id = $author_id;
            // If a_id id is 0 or bool and the comment already had an author.
            if ($a_id == 0 && $r->author_id != null && $r->author_id != 0) {
                $a_id = $r->author_id;
            }
            if ($a_id != 0) {
                $r->author_id = $a_id;
            }

            if (Booking::query()->whereId($booking_id)->onTeam($this->team->id)->exists()) {
                $r->saveOrFail();

                //If it has been correctly saved (not an error above) and $must_notify = true, then notify:
                if ($must_notify) {
                    // Rise an Event.
                    // For the moment, author_id gets the value of true if the modification comes from BS & default value is 0.
                    // So, notify to all users in exception of the author if author_id is an int higher than 1. Otherwise notify everyone:
                    $issuer = is_int($r->author_id) && $r->author_id >= 0 ? $r->author_id : null;
                    event(new CommentCreatedEvent(
                        team: $this->team,
                        bookingId: $r->booking_id,
                        commentId: $r->id,
                        issuer: $issuer,
                        mentions: $r->mentions
                    ));
                }
            } elseif ($r->exists) {
                $r->delete();
            }

            $updated[] = $id;
        }
        //  Delete not updated comments for this booking.
        if ($delete_not_updated) {
            $deleted = BookingComment::query()
                ->where('team_id', $this->team->id)
                ->where('booking_id', '=', $booking_id)
                ->whereNotIn('id', $updated)
                ->delete();

            if ($deleted > 0) {
                nLog("Deleted a total of $deleted booking comments for booking id $booking_id", $this->team);
            }
        }
    }

    /**
     * Push the payment to bookingsync, load it and get the results.
     */
    public function createPayment(Booking $booking, BookingPayment $payment): void
    {
        $request = [
            'payments' => [
                [
                    'amount_in_cents' => $payment->amount_in_cents,
                    'currency' => $payment->currency,
                    'kind' => $payment->kind,
                    'paid_at' => $payment->paid_at,
                    'notes' => $payment->notes,
                    'locale' => 'en', // TODO Test
                    'email' => $payment->email,
                ],
            ],
        ];
        $result = $this->api->post($this->account, "bookings/$booking->id/payments", $request, 0, 'payments');
        // If the body is null or has failed, abort.
        $paymentId = data_get($result, '0.id');
        if (empty($paymentId)) {
            pnLog('Trying to add payment: Bad response from bookingsync:', $this->team, LogLevel::ERROR, $result);

            throw new NinjaProviderIntegrationException('Bad Response from BookingSync');
        } else {
            nLog('Trying to add payment: Correct response from bookingsync . Saving Payment', $this->team->id);
        }

        // Save the new payment on the database and save the external id
        try {
            $payment->external_id = $paymentId;
            $payment->save();
        } catch (QueryException $e) {
            // We may fail when trying to save in case the payment created webhook has reached our DB before (faster than this code)
            report($e);
            $payment->forceDelete();
        }
    }

    // Bookings tagging
    public function attachTagToBooking(int $bookingId, int $tagId): void
    {
        $this->api->post($this->account, "bookings/$bookingId/add_tag", [], $tagId);
    }

    public function removeTagFromBooking(int $bookingId, int $tagId): void
    {
        $this->api->delete($this->account, "bookings/$bookingId/remove_tag", $tagId);
    }

    public function createBookingTag(string|array $name, ?string $color, ?string $logo): ?int
    {
        $data = $this->getTagData($color, $logo, $name);
        $response = $this->api->post($this->account, 'bookings_tags', $data);

        return data_get($response, '0.id');
    }

    public function updateBookingTag(int $tagId, string|array $name, ?string $color, ?string $logo): void
    {
        $data = $this->getTagData($color, $logo, $name);
        $this->api->put($this->account, 'bookings_tags', $data, $tagId);
    }

    public function deleteBookingTag(int $tagId): void
    {
        $this->api->delete($this->account, 'bookings_tags', $tagId);
    }

    private function getTagData(?string $color, ?string $logo, array|string $name): array
    {
        // Prepare data except localized name
        $data = [
            'bookings_tags' => [
                'color' => $color,
                'logo' => $logo,
            ],
        ];
        // Prepare localized name
        $locale = $this->team->app_locale;
        $nameLocale = 'name_'.$locale;
        $data['bookings_tags'][$nameLocale] = $name;

        return $data;
    }

    public function sendNotification(Booking $booking, string $content, string $subject): bool
    {
        pnLog('[Send API Notification] BookingSync', $this->team, LogLevel::INFO, [
            'booking_id' => $booking->id,
            'subject' => $subject,
        ]);

        // NOTE: This is currently implemented to work only with Airbnb.

        // First: Fetch the conversations associated with the booking and get the airbnb one.
        $url = "https://www.bookingsync.com/api/v3/bookings/$booking->id";
        $data = $this->api->get($this->account, $url, ['include' => 'conversations']);
        $conversations = collect(data_get($data, 'bookings.0.conversations'));
        $otaConversation = $conversations->whereIn('default_channel', ['airbnb'])->first();
        if (is_null($otaConversation)) {
            return false;
        }
        $otaConversationId = data_get($otaConversation, 'id');

        // Second: Fetch the participants and get the host
        $url = "https://www.bookingsync.com/api/v3/inbox/conversations/$otaConversationId";
        try {
            $data = $this->api->get($this->account, $url, ['include' => 'participants']);
        } catch (RequestException $e) {
            if ($e->getCode() == 403) {
                report(new Exception("Not valid scope for team {$this->team->id}"));
                abort(403, 'Provider do not allow sending the message. Please, refresh your connection to update your scope.');
            }
        }
        $participants = collect(data_get($data, 'conversations.0.participants'));
        $host = $participants->where('links.member.type', '=', 'Host')->first();
        if (is_null($host)) {
            return false;
        }
        $hostId = data_get($host, 'id');

        // Third: Send the message in the selected conversation as the host.
        $body = [
            'conversation_id' => $otaConversationId,
            'sender_id' => $hostId,
            'content' => 'TEST',
            'origin' => 'airbnb_message',
            'visibility' => 'external',
            'sent_at' => now()->toIso8601ZuluString(),
        ];
        $this->api->post($this->account, '/inbox/messages', $body);

        return true;

        /*
         * https://developers.bookingsync.com/guides/understanding-inbox-messaging/
         * https://developers.bookingsync.com/reference/enums/#inbox-message-channels
         * https://developers.bookingsync.com/reference/endpoints/inbox_conversations/#get-a-single-conversation
         * https://developers.bookingsync.com/reference/endpoints/inbox_messages/#create-a-new-message
         * https://developers.bookingsync.com/reference/endpoints/inbox_participants/#list-participants
         */
    }
}
