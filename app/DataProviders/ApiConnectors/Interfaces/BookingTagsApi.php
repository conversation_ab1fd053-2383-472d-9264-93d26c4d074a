<?php

namespace App\DataProviders\ApiConnectors\Interfaces;

interface BookingTagsApi
{
    /**
     * Attach a tag to a booking.
     */
    public function attachTagToBooking(int $bookingId, int $tagId): void;

    /**
     * Attach a tag to a booking.
     */
    public function removeTagFromBooking(int $bookingId, int $tagId): void;

    /**
     * Create a new booking tag.
     */
    public function createBookingTag(string|array $name, ?string $color, ?string $logo): ?int;

    /**
     * Update a booking tag.
     */
    public function updateBookingTag(int $tagId, string|array $name, ?string $color, ?string $logo): void;

    /**
     * Delete a booking tag.
     */
    public function deleteBookingTag(int $tagId): void;
}
