<?php

namespace App\DataProviders\ApiConnectors;

use App\Actions\Bookings\CheckIns\FormatCheckInOutHourAction;
use App\Actions\Bookings\Times\TriggerCheckInOutTimeModifiedEventAction;
use App\Actions\Providers\Generic\ShouldPostToProviderInLocalEnvAction;
use App\Actions\Providers\Smoobu\CancelSmoobuPaymentAction;
use App\Actions\Providers\Smoobu\CreateSmoobuPaymentAction;
use App\DataProviders\ApiConnectors\Interfaces\GuestNotificationsApi;
use App\DataProviders\ApiConnectors\Interfaces\PaymentsApi;
use App\DataProviders\ProviderApi\SmoobuApi;
use App\DataProviders\ProviderConstants;
use App\DataProviders\Providers\Smoobu;
use App\DTO\Bookings\BookingDto;
use App\DTO\Clients\ClientDto;
use App\Exceptions\NinjaNotImplementedException;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Team;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Psr\Log\LogLevel;

class SmoobuConnector extends ProviderConnector implements GuestNotificationsApi, PaymentsApi
{
    const CHANNEL_ID_FOR_BLOCKING_RENTALS = 11;

    public function __construct(public Team $team)
    {
        $this->api = SmoobuApi::instance();
        $this->provider = Smoobu::get();
        $this->account = $team->getProviderAccount(Smoobu::ID);
    }

    /**
     * Create new booking into provider.
     */
    public function createBlockInRental(int $rentalId, Carbon $start_date, Carbon $end_date, string $notes): int|Booking
    {
        // Generate the data needed
        $body_request = collect(ClientDto::fromRequest(request()))
            // Merge in the required values
            ->merge([
                'apartmentId' => $rentalId,
                'channelId' => self::CHANNEL_ID_FOR_BLOCKING_RENTALS,
                'arrivalDate' => $start_date->format('Y-m-d'),
                'arrivalTime' => $end_date->format(Carbon::getTimeFormatByPrecision('minute')),
                'departureDate' => $end_date->format('Y-m-d'),
                'departureTime' => $end_date->format(Carbon::getTimeFormatByPrecision('minute')),
                'notice' => $notes,
            ])
            // Filter empty values
            ->filter()
            // Convert back to array
            ->toArray();

        $result = SmoobuApi::instance()->post(account: $this->account, endpoint: 'reservations', body: $body_request);
        if (empty($result)) {
            nLog('Trying to add new block: Bad response from '.$this->getChannelName(), $this->team, LogLevel::ERROR, $result);
            abort(500, 'Bad Response from '.$this->getChannelName());
        }
        $id = Arr::get($result, 'id');
        if (! isset($id) && is_int($id)) {
            nLog('Trying to add new block: Missing booking id from '.$this->getChannelName(), $this->team, LogLevel::ERROR, $result);
            abort(500, 'Bad Response from '.$this->getChannelName());
        }

        return $id;
    }

    public function updateBookingByModel(Booking $booking, BookingDto $booking_data): Booking|array|null
    {
        // Add here each attribute to be updated in this booking:
        $body = collect()
            ->when(isset($booking_data->expected_checkin_time), function (Collection $data) use ($booking, $booking_data) {
                $parsed_time = FormatCheckInOutHourAction::run($booking_data->expected_checkin_time);
                $booking->expected_checkin_time = $parsed_time;
                $booking_data->check_in_out_time_modified = true;

                return $data->put('arrivalTime', $parsed_time);
            })
            ->when(isset($booking_data->expected_checkout_time), function (Collection $data) use ($booking, $booking_data) {
                $parsed_time = FormatCheckInOutHourAction::run($booking_data->expected_checkout_time);
                $booking->expected_checkout_time = $parsed_time;
                $booking_data->check_in_out_time_modified = true;

                return $data->put('departureTime', $parsed_time);
            })
            ->when(isset($booking_data->notes), function (Collection $data) use ($booking, $booking_data) {
                // No need to save, we receive a booking_updated immediately with the new note. However, for local purposes:
                $booking->internal_note = $booking_data->notes;

                return $data->put('assistantNotice', $booking_data->notes);
            })
            ->when(isset($booking_data->final_price), function (Collection $data) use ($booking_data) {
                return $data->put('price', $booking_data->final_price);
                // I don't want to save the booking, I want it to come back properly once saved in BS
            })
            ->when(isset($booking_data->initial_price), fn () => throw new NinjaNotImplementedException())
            ->when(isset($booking_data->discount), fn () => throw new NinjaNotImplementedException())
            ->when(isset($booking_data->bookingFees), fn () => throw new NinjaNotImplementedException())
            ->when(isset($booking_data->bookingTaxes), fn () => throw new NinjaNotImplementedException());

        if (ShouldPostToProviderInLocalEnvAction::run()) {
            if (! $body->isEmpty()) {
                $request = $body->toArray();
                $response = $this->api->put($this->account, ProviderConstants::ENDPOINT_BOOKINGS, $request, $booking->id);
                // This will trigger a booking_updated webhook which will, in turn, trigger the needed events
                // In case we see many webhooks failing to respond back, we may save the booking here and trigger the notify, as webhook won't do.
                nLog("Saving booking with id $booking->id to Smoobu", $this->team->id);

                return $response;
            }
        } else {
            // For local cases where you don't want to send the booking to BS:
            if ($booking->isDirty()) {
                $booking->save();
                nLog("Saved booking with id $booking->id in DB", $this->team->id);
            }

            // Notify, as we will not receive a webhook
            if ($booking_data->check_in_out_time_modified === true) {
                $updated_booking_data = BookingDto::fromModel($booking);
                TriggerCheckInOutTimeModifiedEventAction::run($this->team, $updated_booking_data);
            }
        }

        return $booking;
    }

    public function deleteBookingById(Booking $booking): bool
    {
        if ($booking->isUnavailable()) {
            $url = 'reservations';
            $singleId = $booking->external_id;

            return SmoobuApi::instance()->delete(account: $this->account, endpoint: $url, single_id: $singleId);
        }

        throw new NinjaNotImplementedException();
    }

    /**
     * Create a Payment for Smoobu.
     * And generate the required updates to smoobu if needed.
     */
    public function createPayment(Booking $booking, BookingPayment $payment): void
    {
        CreateSmoobuPaymentAction::run($this->team, $booking, $payment);
    }

    public function deletePayment(Booking $booking, BookingPayment $payment): void
    {
        CancelSmoobuPaymentAction::run($this->team, $booking, $payment);
    }

    public function sendNotification(Booking $booking, string $content, string $subject): bool
    {
        // Provider communication is only available with Airbnb or booking.com. Other providers uses emails.
        if (! Str::of($booking->source->name)->contains(['airbnb', 'booking.com'], true)) {
            return false;
        }

        $externalId = $booking->external_id;
        // Generate the data needed
        $body_request = [
            'subject' => $subject,
            'messageBody' => $content,
        ];

        $url = "reservations/$externalId/messages/send-message-to-guest";
        $result = SmoobuApi::instance()->post(account: $this->account, endpoint: $url, body: $body_request);

        if (empty($result)) {
            pnLog('[Smoobu] Error sending guest communication message', $this->team, LogLevel::ERROR, $result);

            // abort(500, 'Bad Response from '.$this->getChannelName());
            return false;
        }

        return true;
    }
}
