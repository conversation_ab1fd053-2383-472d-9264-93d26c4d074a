<?php

namespace App\DataProviders\ApiConnectors;

use App\Actions\Alerts\UpdateBookingPaymentAlertsAction;
use App\Actions\Bookings\GenerateBookingReferenceAction;
use App\Actions\Tasks\ScheduledTask\HandleTasksForBookingAction;
use App\DataProviders\ApiConnectors\Interfaces\GuestNotificationsApi;
use App\DataProviders\ProviderApi\RentalsUnitedApi;
use App\DataProviders\ProviderConstants;
use App\DataProviders\Providers\NoProvider;
use App\DataProviders\Providers\RentalsUnited;
use App\Enum\BookingStatusEnum;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Source;
use App\Models\Team;
use App\Models\Thread;
use Illuminate\Support\Carbon;
use Psr\Log\LogLevel;

class RentalsUnitedConnector extends ProviderConnector implements GuestNotificationsApi
{
    public function __construct(public Team $team)
    {
        $this->api = RentalsUnitedApi::instance();
        $this->provider = RentalsUnited::get();
        $this->account = $team->getProviderAccount(RentalsUnited::ID);
    }

    public function createBlockInRental(int $rentalId, Carbon $start_date, Carbon $end_date, string $notes): int|Booking
    {
        $serviceName = $this->team->config()->rnAppName();
        $source = Source::firstOrCreate([
            'name' => 'Block in '.$serviceName,
            'team_id' => $this->team->id,
        ], [
            'is_external' => false,
            'provider_id' => 0,
            'created_at' => now()->timestamp,
            'updated_at' => now()->timestamp,
        ]);
        $nights = $start_date->copy()->startOfDay()->diffInDays($end_date);

        return Booking::create([
            'id' => Booking::getRandomId($this->team),
            'provider_id' => NoProvider::ID,
            'team_id' => $this->team->id,
            'rental_id' => $rentalId,
            'start_at' => $start_date->timestamp,
            'end_at' => $end_date->timestamp,
            'canceled_at' => 0,
            'expected_checkin_time' => $start_date->hour,
            'expected_checkout_time' => $end_date->hour,
            'status' => BookingStatusEnum::UNAVAILABLE,
            'reference' => GenerateBookingReferenceAction::run(),
            'nights' => $nights,
            'source_id' => $source->id,
            'notes' => $notes,
            'created_at' => now()->timestamp,
            'updated_at' => now()->timestamp,
        ]);
    }

    public function updateBooking(int|string|null $externalId): void
    {
        // TODO Since payments are not synchronized with RU, we need to recalculate total paid
        $booking = Booking::query()
            ->onProvider($this->provider->id())
            ->whereExternalIdStartsWith($externalId)
            ->firstOrFail();

        // Update booking
        $totalPaid = BookingPayment::query()
                ->onTeam($booking->team_id)
                ->onBooking($booking->id)
                ->whereNotCancelled()
                ->sum('amount_in_cents') / 100;

        $booking->paid_amount = $totalPaid;
        $booking->save();
    }

    public function deleteBookingById(Booking $booking): bool
    {
        if ($booking->status == BookingStatusEnum::UNAVAILABLE || $booking->status == BookingStatusEnum::TENTATIVE) {
            $booking->status = BookingStatusEnum::CANCELED;
            $booking->canceled_at = now()->timestamp;
            $booking->save();

            $booking->rental->updateAvailability();
            UpdateBookingPaymentAlertsAction::run($booking);
            HandleTasksForBookingAction::run($booking->team, $booking);

            return true;
        }

        return false;
    }

    public function sendNotification(Booking $booking, string $content, string $subject): bool
    {
        pnLog('[Send API Notification] Rentals United', $this->team, LogLevel::INFO, [
            'booking_id' => $booking->id,
            'subject' => $subject,
        ]);
        $message = "$subject\n\n$content";

        $response = $this->postMessage($booking, $message);

        return ! is_null($response);
    }

    protected function getOrFetchBookingThread(Booking $booking): Thread
    {
        $thread = Thread::query()
            ->whereTeamId($this->team->id)
            ->whereBookingId($booking->id)
            ->whereIn('communication_channel', RentalsUnited::THREAD_COMMUNICATION_CHANNELS_WE_POST_TO)
            ->first();

        if (is_null($thread)) {
            pnLog('[RentalsUnitedConnector] Thread not found, fetching from Rentals United', $this->team);
            $this->syncEndpointAndResolve(ProviderConstants::ENDPOINT_THREADS, $booking->getExternalId());
            $thread = Thread::query()
                ->whereTeamId($this->team->id)
                ->whereBookingId($booking->id)
                ->whereIn('communication_channel', RentalsUnited::THREAD_COMMUNICATION_CHANNELS_WE_POST_TO)
                ->firstOrFail();
        }

        return $thread;
    }

    private function postMessage(Booking $booking, $content): ?array
    {
        $thread = $this->getOrFetchBookingThread($booking);

        return $this->api->post($this->account,
            "/api/messaging/threads/$thread->external_id/messages/by-service-for-user",
            ['Body' => $content]
        );
    }
}
