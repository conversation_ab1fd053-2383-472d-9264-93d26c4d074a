<?php

namespace App\Providers;

use App\Models\RentalNinjaTeam;
use Illuminate\Support\Facades\Gate;
use Laravel\Horizon\Horizon;
use Laravel\Horizon\HorizonApplicationServiceProvider;

class HorizonServiceProvider extends HorizonApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        parent::boot();

        if (isProduction()) {
            Horizon::routeMailNotificationsTo(RentalNinjaTeam::TECH_EMAIL);
            Horizon::routeSlackNotificationsTo(RentalNinjaTeam::SLACK_URL, '#rental-ninja-prod');
        }
    }

    /**
     * Register the Horizon gate.
     *
     * This gate determines who can access Horizon in non-local environments.
     */
    protected function gate(): void
    {
        Gate::define('viewHorizon', function ($user = null) {
            if (! empty(config('services.horizon.secret')) || strlen(config('services.horizon.secret')) >= 10) {
                if (request()->bearerToken() === config('services.horizon.secret')) {
                    return true;
                }
            }

            return RentalNinjaTeam::userHasNovaAccess($user);
        });
    }
}
