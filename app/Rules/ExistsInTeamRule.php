<?php

namespace App\Rules;

use Attribute;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Database\Eloquent\Model;

#[Attribute]
class ExistsInTeamRule implements ValidationRule
{
    public function __construct(public string $model, public string $column = 'id', public ?int $except = null)
    {
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $teamId = request()->route()->team->id;
        $model = $this->model;
        if (! is_null($this->except) && $value === $this->except) {
            return;
        }
        /** @var Model $model */
        if (! $model::query()->withoutGlobalScopes()->where('team_id', '=', $teamId)->where($this->column, '=', $value)->exists()) {
            $fail('The :attribute is not part of the team.');
        }
    }
}
